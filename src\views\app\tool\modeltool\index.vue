<template>
  <d2-container class="modeltool">
    <el-tabs v-model="activeName" type="card" style="height:99%">
      <el-tab-pane label="版型特体" name="modelbodylist">
        <model-body-list-tool />
      </el-tab-pane>
      <!-- <el-tab-pane label="配置管理" name="second">配置管理</el-tab-pane>
      <el-tab-pane label="角色管理" name="third">角色管理</el-tab-pane>
      <el-tab-pane label="定时任务补偿" name="fourth">定时任务补偿</el-tab-pane> -->
    </el-tabs>
  </d2-container>
</template>

<script>
// import masterTableMixins from '@/mixins/master_table_mixins/index'
import ModelBodyListTool from './components/modelbodylist'
export default {
  name: 'modeltool',
  // mixins: [masterTableMixins],
  components: {
    ModelBodyListTool
  },
  data () {
    return {
      activeName: 'modelbodylist'
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    }
  }
}
</script>

<style lang="scss" >
.modeltool {
  .el-tabs__content {
    min-height: calc(100vh - 150px);
  }
  .d2-container-full__body {
    overflow-x: hidden !important;
  }
  //处理vxe table 头和数据  上下不一致问题
  .vxe-cell {
    margin: 1px !important;
    // background-color: red;
  }
}
</style>
