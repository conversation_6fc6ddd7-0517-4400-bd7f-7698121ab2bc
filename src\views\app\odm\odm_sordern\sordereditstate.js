export default {
  // name: 'sorderEditState',
  data () {
    return {
      EditState: false
    }
  },
  watch: {
    // 'sorderForm.statusID': {
    //   deep: true,
    //   handler: function (newVal, oldVal) {
    //     this.setEditState(newVal)
    //   }
    // }
  },
  created () {
    switch (this.$options.name) {
      case 'odm_sorderbase':
      case 'sordermaster':
        this.sorderEditSet()
        break
      case 'OdmSorderDetailModel':
      case 'DetailModelIndex':
      case 'DetailModel':
        this.detailmodelEditSet()
        break
      case 'OdmSorderDetailElem':
      case 'SorderDetailImage':
      case 'FuLiaoCmp':
      case 'GongYiCmp':
      case 'KuanShiCmp':
        this.detailElemEditSet()
        break
      case 'SorderDetailBody':
        this.detailBodyEditSet()
        break
      case 'SorderDetailSize':
        this.detailSizeEditSet()
        break
      default:
        break
    }
  },

  methods: {
    setEditState (val) {
      if (this.sorderStore != null) {
        if (this.info.userType === 2 && val !== 0) {
          if (val !== 0 && val !== 22) {
            this.EditState = true
          } else {
            this.EditState = false
          }
        } else {
          var states = [0, 20]
          if (!this.$utils.includes(states, val)) {
            this.EditState = true
          }
        }
      }
    },
    sorderEditSet () {
      if (this.sorderStore !== null) {
        if (this.info.userType === 2) {
          if (this.sorderStore.statusID !== 0 && this.sorderStore.statusID !== 22) {
            this.EditState = true
          } else {
            this.EditState = false
          }
        } else {
          var states = [0, 20]
          if (this.sorderStore != null && !this.$utils.includes(states, this.sorderStore.statusID)) {
            this.EditState = true
          }
        }
      }
    },
    detailmodelEditSet () {
      if (this.sorderStore !== null) {
        if (this.info.userType === 2) {
          if (this.sorderStore.statusID !== 0 && this.sorderStore.statusID !== 22) {
            this.EditState = true
          } else {
            this.EditState = false
          }
        } else {
          var states = [0, 20]
          if (this.sorderStore != null && !this.$utils.includes(states, this.sorderStore.statusID)) {
            this.EditState = true
          }
        }
      }
    },
    detailElemEditSet () {
      if (this.sorderStore !== null) {
        if (this.info.userType === 2) {
          if (this.sorderStore.statusID !== 0 && this.sorderStore.statusID !== 22) {
            this.EditState = true
          } else {
            this.EditState = false
          }
        } else {
          var states = [0, 20]
          if (this.sorderStore != null && !this.$utils.includes(states, this.sorderStore.statusID)) {
            this.EditState = true
          }
        }
      }
    },
    detailBodyEditSet () {
      if (this.sorderStore !== null) {
        if (this.info.userType === 2) {
          if (this.sorderStore.statusID !== 0 && this.sorderStore.statusID !== 22) {
            this.EditState = true
          } else {
            this.EditState = false
          }
        } else {
          var states = [0, 20, 30]
          if (this.sorderStore != null && !this.$utils.includes(states, this.sorderStore.statusID)) {
            this.EditState = true
          }
        }
      }
    },
    detailSizeEditSet () {
      if (this.sorderStore !== null) {
        if (this.info.userType === 2) {
          if (this.sorderStore.statusID !== 0 && this.sorderStore.statusID !== 22) {
            this.EditState = true
          } else {
            this.EditState = false
          }
        } else {
          var states = [0, 20, 30]
          if (this.sorderStore != null && !this.$utils.includes(states, this.sorderStore.statusID)) {
            this.EditState = true
          }
        }
      }
    }
  }
}
