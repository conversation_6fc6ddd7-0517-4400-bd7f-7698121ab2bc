<template>
  <board-chart :data="data" :settings="settings ">
    <template slot="chartheader">
      <el-row>
        <el-col :span="8">
          <h2 style="margin-left: 20px;">工段占压统计数据</h2>
        </el-col>
        <el-col :span="16">
          <el-checkbox-group v-model="groupIDs" size="mini" @change="change" style="float: right; padding: 3px 0;padding-top: 8px;">
            <el-checkbox-button v-for="(item,index) in GroupComboStore" :label="item.value" :key="index">{{item.label}}</el-checkbox-button>
          </el-checkbox-group>
        </el-col>
      </el-row>
    </template>
  </board-chart>
</template>

<script>
import boardChart from '@/components/charts/chart'
export default {
  name: 'workSecationSetpUp', // 工段占压
  components: {
    boardChart
  },
  data () {
    return {
      data: {
        columns: ['label', 'count', 'tieUp'],
        rows: [

        ]
      },
      settings: {
        labelMap: {
          label: '工段',
          count: '订单压货数量',
          tieUp: '额定占压数量'
        },
        label: {
          normal: {
            show: true,
            position: 'top'
          }
        }
      },
      type: 'histogram',
      api: {
        get: '/mes/boardChart/workSecationStepUp',
        GroupComboStore: '/mtm/combo/groupComboStore'
      },
      groupIDs: [],
      GroupComboStore: []
    }
  },
  mounted () {
    this.timer = setInterval(this.get, 1000 * 60)// 毫秒
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  async created () {
    await this.getCombStore()
    this.get()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
        this.GroupComboStore.forEach(item => {
          this.groupIDs.push(item.value)
        })
      })
    },
    async get () {
      await this.$api.ActionRequest(this.api.get, { groupIDs: this.groupIDs }).then(result => {
        this.data.rows = result
      })
    },
    async change () {
      await this.get()
    }
  }
}
</script>

<style>
</style>
