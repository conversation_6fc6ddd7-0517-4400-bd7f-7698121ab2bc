<template>
  <d2-container class="sorderlist">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <el-button type="danger" size="mini" @click="deletesEvent" v-if="menuAction.allowDelete">彻底删除</el-button>
          <el-button type="warning" size="mini" v-if="menuAction.allowAdd" @click="deepCloneEvent">订单复制</el-button>
          <el-button type="danger" size="mini" v-if="menuAction.allowAdd" @click="recoverEvent">恢复订单</el-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom class="mtmtoolbar">
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="dates" :item-render="{}"><template #default>
                <!-- <el-date-picker size="mini" v-model="searchForm.dates" type="daterange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker> -->
                <el-date-picker v-model="searchForm.dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template></vxe-form-item>
            <vxe-form-item field="stateIDs" :item-render="{}"><template #default>
                <el-select v-model.trim="searchForm.stateIDs" placeholder="节点" multiple collapse-tags clearable size="mini" style="width:190px">
                  <el-option v-for="item in SorderStatusComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template></vxe-form-item>
            <template v-if="!(info.userType==2)">
              <vxe-form-item field="clientID" :item-render="{}"><template #default>
                  <el-select v-model.trim="searchForm.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                    <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template></vxe-form-item>
            </template>
            <vxe-form-item field="personID" :item-render="{}"><template #default>
                <el-select v-model.trim="searchForm.personID" filterable placeholder="顾客" size="mini" remote reserve-keyword :remote-method="remoteMethod5" clearable style="width:110px">
                  <el-option v-for="item in ClientPersonComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="itemID" :item-render="{}"><template #default>
                <el-select v-model.trim="searchForm.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable style="width:150px">
                  <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"><template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
              </template></vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomSewbase_master_table' ref='master_table' @cell-click='tableCellClick' :custom-config="{storage: true}" :stripe="false" :highlight-hover-row="false" :highlight-current-row="false" :highlight-current-column="false" :row-class-name="rowClassName" :loading="tableLoading" :height="TableHeight" :data="tableData">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column title="操作" width="50" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="el-icon-location-information" @click="showOrderState(row)"></vxe-button>
        </template>
      </vxe-table-column>
      <vxe-table-column field="statusText" title="订单状态" width="100"></vxe-table-column>
      <vxe-table-column field="isManualOrder" title="是否手工单?" width="100">
        <template v-slot="{ row }">
          <span v-if="row.isManualOrder" style="color:red;background-color: white;">手工单</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="code" title="订单号" width="100"></vxe-table-column>
      <vxe-table-column field="clientText" title="客户" width="100"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" width="100"> </vxe-table-column>
      <vxe-table-column field="tel" title="联系电话" width="100"></vxe-table-column>
      <vxe-table-column field="address" title="地址" width="100"></vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="业务类型" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="detailInfo" title="明细" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="clientPerson" title="顾客" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="clientShop" title="店铺" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="itemText" title="面料" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="currencyTypeText" title="货币" sortable width="100"></vxe-table-column>
      <vxe-table-column field="exchangeRate" title="汇率" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemConsumption" title="面料耗量" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="itemConsumptionL" title="里布耗量" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="finalTextureText" title="纹理" show-overflo width="100"></vxe-table-column>

      <vxe-table-column field="createBy" title="创单人" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="isUrgent" title="是否加急" width="100" sortable>
        <template v-slot="{ row }">
          <span v-if="row.isUrgent" style="color:red">加急</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="deliveryDate" title="期望交期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>

      <vxe-table-column field="remark" title="备注" width="100"></vxe-table-column>
      <vxe-table-column field="checkOn" title="客服审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="technicianCheckDate" title="技术审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="checkBy" title="审核人" width="100"></vxe-table-column>
      <vxe-table-column field="deleteOn" title="删除时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="deleteBy" title="删除人" sortable width="100px"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>

  </d2-container>
</template>

<script>

import masterTableMixins from '@/mixins/master_table_mixins/index'

import { mapState } from 'vuex'

export default {
  name: 'odm_sorderlist',
  mixins: [masterTableMixins],
  components: {
    // detailList
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        sequence: 9999
      },
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/odm_sorder/ListDeleted',
        delete: '/mtm/oDM_Sorder/deletesOrder',
        recover: '/mtm/oDM_Sorder/RecoversOrder',
        deepClone: '/mtm/oDM_Sorder/deepClone',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ClientPersonComboStoreByQuery: '/mtm/comboQuery/ClientPersonComboStoreByQuery',
        SorderStatusComboStore: '/mtm/combo/sorderStatusComboStore',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        productionPlanState: '/mes/PRD_ProductionPlan/GetProductionPlanStateToMTM'
      },
      detailForm: {
        sorderId: null

      },
      SorderStatusComboStore: [],
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      ClientPersonComboStoreByQuery: [],
      sorderStateDrawer: false,
      footerCompanyInfo: false,
      cadLayoutShow: false,
      cadTextShow: false,
      bomTableShow: false,
      Cad: {
        cadText: null,
        sorderNum: '',
        server: '',
        fileName: ''
      },
      searchForm: {
        stateIDs: [],
        sorderTypes: [1, 2, 0, 10]
      },
      salesBillShow: false,
      isAutoLoding: false

    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])

  },
  async created () {
    await this.getCombStore()
    this.setSorderState()
    this.loadData()
  },
  methods: {
    setSorderState () {
      if (this.info.userType === 2) {

      }
      var admin = [1, 20, 21, 22, 30, 31, 32, 40]
      if (this.info.userRoles.ElementExist('code', 'SuperAdmin')) {
        this.searchForm.stateIDs = this.$utils.union(this.searchForm.stateIDs, admin)
      }
    },
    tableCellClick ({ column, row }) {
      if (column && column.type === 'checkbox') {
        return
      } else {
        this.$refs.master_table.clearCheckboxRow()
        this.$refs.master_table.toggleCheckboxRow(row)
      }
      this.selectRow = row
    },
    getBomItem () {
      this.bomTableShow = !this.bomTableShow
    },
    async getProductionPlanState (data) {
      var sorderIDs = data.map((item) => {
        return item.id
      })
      if (sorderIDs.length <= 0) {
        return data
      }
      var list = await this.$api.ActionRequest(this.api.productionPlanState, { sorderId: sorderIDs }).then(async ({ totalCount, items }) => {
        return items
      })
      list.forEach(item => {
        var dto = this.$utils.find(data, it => it.id === item.sorderId)
        if (dto !== null) {
          dto.mEsLastFinishedProductionSchedule = item.lastFinishedProductionSchedule
          dto.mesProductionPlanStateText = item.productionPlanStateText
          dto.mesSchedulesDto = item.schedulesDto
        }
      })
      return data
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { isNotG: true }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SorderStatusComboStore).then(result => {
        this.SorderStatusComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
    },
    insertEvent (name) {
      this.$router.push({ name: name })
    },
    CadMake () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var ids = checks.map(item => { return item.id })
      this.$api.ActionRequest(this.api.sendCad, { sorderIDs: ids }).then(res => {
        console.log(res)
        // var groupList = this.$utils.groupBy(res, "type")
        // this.$utils.objectEach(groupList, (item, key) => {
        //   // console.log(key)
        //   // console.log(item)
        //   this.tableExport(item, key)
        // })
      })
    },
    async getCad () {
      await this.$api.ActionRequest(this.api.sorderCadlayout, { sorderID: this.selectRow.id }).then(async res => {
        this.cadLayoutList = res
      })
    },
    async createCad () {
      await this.$api.ActionRequest(this.api.createCad, { sorderID: this.selectRow.id, skipManual: true }).then(async res => {
        if (this.info.userType === 2) {
          this.$message({ type: 'success', message: '获取成功,请等待一分钟左右后刷新列表,如果未获取到请联系客服人员,请勿多次点击!', duration: 5000 })
        } else {
          this.$message({ type: 'success', message: '排料成功,请等待CAD返回耗量' })
        }

        await this.getCad()
      })
    },
    async DownloadETCad () {
      await this.$api.ActionRequest(this.api.getCadStr, { SorderID: this.selectRow.id }).then(res => {
        this.createJson(res)
      })
    },

    handleClose () {
      this.sorderStateDrawer = false
    },
    showOrderState (row) {
      this.selectRow = row
      this.sorderStateDrawer = true
    },
    removeEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      this.$XModal.confirm('您确定要删除该数据?').then(type => {
        if (type === 'confirm') {
          if (checks[0].id != null) {
            this.$api.ActionRequest(this.api.delete, checks).then(res => {
              this.$XModal.message({ message: '删除成功！', status: 'success' })
              this.loadData()
            })
          }
        }
      })
    },
    // 订单恢复
    recoverEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      this.$XModal.confirm('您确定要恢复该数据?').then(type => {
        if (type === 'confirm') {
          if (checks[0].id != null) {
            this.$api.ActionRequest(this.api.recover, checks).then(res => {
              this.$XModal.message({ message: '订单恢复成功！', status: 'success' })
              this.loadData()
            })
          }
        }
      })
    },
    // tableCellClick({ column, row }) {
    //   // if (column.title !== '操作') {
    //   //   this.gosorder(row.id, row.code)
    //   // }
    //   //   this.$refs.master_table.clearCheckboxRow()
    //   this.$refs.master_table.toggleCheckboxRow(row)
    //   // this.$refs.master_table.toggleRowExpand(row)
    //   this.selectRow = row
    // },

    editEvent (name) {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能编辑一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.gosorder(checks[0].id, checks[0].code, name)
    },

    gosorder (id, orderid, name) {
      this.$router.push({
        name: name,
        params: {
          id: id, sorderid: orderid
        }
      })
    },
    printEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能打印一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var url = this.mtmpai.replace('/api/', '')
      window.open(`${url}/fs/print/sorder/pdf?num=${checks[0].id}` + '&zoom=125', '_blank')
    },
    rowClassName ({ row, rowIndex }) {
      var stateClass = ''
      switch (row.statusID) {
        case 0: // 待定 ClientUndetermined
          stateClass = 'sorderstate-client'
          break
        case 1:// 已确认Confirmed
          stateClass = 'sorderstate-confirmed' //  background-color: #0598e1;
          break

        case 20:// 客服锁定CLock
          stateClass = 'sorderstate-clock'
          break
        case 21:// 客服审核完成 //CChecked
          stateClass = 'sorderstate-cchecked'
          break
        case 22:// 客服驳回 CReject
          stateClass = 'sorderstate-customer'
          break
        case 30:// 技术锁定 //MLock
          stateClass = 'sorderstate-mlock'
          break
        case 31:// 技术审核完成 MChecked
          stateClass = 'sorderstate-MChecked'
          break
        case 32:// 技术驳回 MReject
          stateClass = 'sorderstate-technology'
          break
        case 40:// 计划下单Planed
          stateClass = 'sorderstate-planed'
          break
        case 41:// 计划驳回 PReject
          stateClass = 'sorderstate-preject'
          break
        case 50: // 完成 Finished
          stateClass = 'sorderstate-finished'
          break
        default:
          stateClass = ''
          break
      }
      return stateClass
    },
    // 订单复制
    deepCloneEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能编辑一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.deepClone, checks[0]).then(res => {
        this.$message({ type: 'success', message: '复制成功' })
        this.loadData()
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { text: query }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
    }
  }
}
</script>

<style lang="scss" >
.sorderlist {
  // .mtmtoolbar {
  //   overflow-x: auto;
  //   overflow-y: hidden;
  // }
  // .el-drawer {
  //   overflow-y: auto !important;
  // }
  .expandclass {
    background-color: #e6f7ff;
  }
  .sorderstate-client {
    background-color: #909399;
    color: cornsilk;
  }
  .sorderstate-confirmed {
    background-color: #0598e1;
    color: cornsilk;
  }
  //客服驳回
  .sorderstate-customer {
    background-color: #0da468;
    color: cornsilk;
  }
  //客服锁定CLock
  .sorderstate-clock {
    background-color: #0d97a4;
    color: cornsilk;
  }
  //客服审核完成 //CChecked
  .sorderstate-cchecked {
    background-color: #0da410;
    color: cornsilk;
  }
  //技术 技术驳回
  .sorderstate-technology {
    background-color: #e6a23c;
    color: cornsilk;
  }
  //技术 技术锁定
  .sorderstate-mlock {
    background-color: #a68934;
    color: cornsilk;
  }
  //技术 技术审核完成
  .sorderstate-MChecked {
    background-color: #a47e0d;
    color: cornsilk;
  }
  //计划下单Planed
  .sorderstate-planed {
    background-color: #ea6157;
    color: cornsilk;
  }
  //计划驳回 PReject
  .sorderstate-preject {
    background-color: #de3327;
    color: cornsilk;
  }
  //完成 Finished
  .sorderstate-finished {
    background-color: #0d6aa4;
    color: cornsilk;
  }
}
</style>
