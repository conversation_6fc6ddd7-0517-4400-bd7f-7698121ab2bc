<template>
  <vxe-pulldown ref="sizeDown" size="mini" :transfer="true">
    <template v-slot>
      <vxe-input v-model="sizeText" suffix-icon="fa fa-search" placeholder="点击搜索号型" @keyup="sizekeyupEvent" @focus="sizeFocusEvent" @suffix-click="personSuffixClick" @clear="clear" clearable></vxe-input>
    </template>
    <template v-slot:dropdown>
      <div class="dropdownsize">
        <vxe-grid Id="sizeperson" keep-source highlight-hover-row auto-resize height="300" width="800" min-height="300px;" ref="personGrid" :loading="sizeLoading" :pager-config="sizeTablePage" :data="sizetableData" :columns="sizetableColumn" @cell-click="sizeCellClickEvent" @page-change="sizePageChange" :edit-config="{trigger: 'manual', mode: 'row', showStatus: true, icon: 'fa fa-pencil'}" :custom-config="{storage: false}">
        </vxe-grid>
      </div>
    </template>
  </vxe-pulldown>
</template>

<script>
// import { isEmpty } from 'lodash'
export default {
  name: 'clientpersoninsert',
  props: {
    detailrow: {
      type: Object
    },
    sorderStore: {
      type: Object
    }

  },
  data () {
    return {
      sizeText: this.detailrow.sizeIDText,
      api: {
        get: '/mtm/oDM_SorderDetailSize/getSize'
      },
      sizetableColumn: [
        { field: 'code', title: '编码', width: 120 },
        { field: 'codeName', title: '名称', width: 120 },
        { field: 'groupName', title: '类别', width: 150 }
      ],
      sizetableData: [],
      sizeTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        modelId: this.detailrow.modelId,
        text: null
      }
    }
  },
  created () {
  },
  methods: {
    personSuffixClick () {
      this.$refs.sizeDown.togglePanel()
    },
    clear () {
      this.sizeText = null
      this.$emit('setSize', { detailrow: this.detailrow, sizeRow: null })
    },
    sizeCellClickEvent ({ row, column }) {
      if (column.title !== '操作') {
        this.$emit('setSize', { detailrow: this.detailrow, sizeRow: row })
        this.sizeText = row.code
        this.$refs.sizeDown.hidePanel()
      }
    },
    async sizeFocusEvent () {
      if (this.sizetableData.length === 0) {
        await this.getSizebyQuery()
      }
      this.$refs.sizeDown.showPanel()
    },
    async sizekeyupEvent (query) {
      if (!query) return
      const { value } = query || ''
      this.sizeTablePage.currentPage = 1
      this.sizeTablePage.text = value

      if (this.sorderStore.clientID === null || this.sorderStore.clientID === '') {
        this.$notify({
          message: '请先选择客户',
          type: 'error'
        })
        return
      };
      await this.getSizebyQuery(value)
    },
    async getSizebyQuery (query = null, personId = null) {
      this.sizeLoading = true
      await this.$api.ActionRequest(this.api.get, this.sizeTablePage).then(result => {
        this.sizeTablePage.total = result.totalCount
        this.sizetableData = result.items
        this.sizeLoading = false
      })
    },
    async sizePageChange ({ currentPage, pageSize }) {
      this.sizeTablePage.pageSize = pageSize
      this.sizeTablePage.currentPage = currentPage
      await this.getSizebyQuery()
    }
  }
}
</script>

<style>
</style>
