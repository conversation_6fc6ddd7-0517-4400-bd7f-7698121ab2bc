<template>
  <d2-container class="prd_productionplanschedulelog">
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->
          <vxe-button @click="exportSelectEvent" v-if="menuAction.allowPrint">导出选中</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="dates" :item-render="{}"><template #default>
                <el-date-picker v-model="searchForm.dates" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod6" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="productionTeamID"><template #default>
                <el-select v-model="searchForm.productionTeamID" filterable placeholder="生产小组" size="mini" remote reserve-keyword plremoteaceholder="生产小组" clearable :remote-method="remoteMethod2">
                  <el-option v-for="item in productionTeamComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="productionStationID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.productionStationID" filterable placeholder="工位" size="mini" remote reserve-keyword plremoteaceholder="工位" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in productionStationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="hasQualityDetail" :item-render="{}">
              <template #default>
                <vxe-checkbox v-model="searchForm.hasQualityDetail" content="不合格"></vxe-checkbox>
              </template>
            </vxe-form-item>
            <vxe-form-item field="qualityBaseID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.qualityBaseID" filterable size="mini" remote reserve-keyword placeholder="质检部位" :remote-method="remoteMethod5" clearable>
                  <el-option v-for="item in qualityBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="qualityID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.qualityID" filterable size="mini" remote reserve-keyword placeholder="质检分类" :remote-method="remoteMethod3" clearable>
                  <el-option v-for="item in qualityComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="qualityDetailID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.qualityDetailID" filterable size="mini" remote reserve-keyword placeholder="质检明细" :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in qualityDetailComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PrdProductionplanschedulelogMasterTable' ref='master_table' height="auto" @cell-click='tableCellClick' :row-class-name="rowClassName" :loading="tableLoading" :data="tableData" :custom-config="{storage: true}">
      <!-- <vxe-table-column type="checkbox" width="60"></vxe-table-column> -->
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNum" title="订单号" sortable width="80"> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="80"> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupName" title="类别" sortable width="55"></vxe-table-column>
      <vxe-table-column field="modelName" title="版型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="workSecationName" title="工段" sortable width="90"></vxe-table-column>
      <vxe-table-column field="productionTeamName" title="小组" sortable width="90"></vxe-table-column>
      <vxe-table-column field="productionStationName" title="工位" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qualityBaseCodeName" title="质检部位" sortable width="100">
        <template v-slot="{ row }">
          {{row.qualityBaseCode}}{{row.qualityBaseCodeName===null ?"":(':'+row.qualityBaseCodeName)}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="qualityCodeName" title="质检分类" sortable width="100">
        <template v-slot="{ row }">
          {{row.qualityCode}} {{row.qualityCodeName===null ?"":(':'+row.qualityCodeName)}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="qualityDetailCodeName" title="质检明细" sortable width="100">
        <template v-slot="{ row }">
          {{row.qualityDetailCode}}{{row.qualityDetailCodeName===null ?"":(':'+row.qualityDetailCodeName)}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100">
        <template v-slot="{ row }">
          <template v-if="row.remark!==null&&row.remark!==''">
            <span style="color:red">{{row.remark}}</span>
          </template>
          <template v-else>
            {{row.remark}}
          </template>
        </template>
      </vxe-table-column>
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column> -->
      <!-- <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="createBy" title="扫描人" sortable width="80"></vxe-table-column>
      <vxe-table-column field="createOn" title="扫描时间" :formatter="val=>formatDate(val)" sortable width="150">
      </vxe-table-column>
      <!-- <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column> -->
      <!-- <vxe-table-column title="操作" width="150" :fixed='tableOptFixed'  align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
             <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"> </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>

  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'prd_productionplanschedulelog',
  mixins: [masterTableMixins],
  props: {
    productionPlanDetailID: {
      type: String,
      default: ''
    }
  },
  watch: {
    productionPlanDetailID: {
      handler (newval, oldval) {
        this.searchForm.productionPlanDetailID = newval
        this.loadData()
      }
    }
  },
  components: {
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        qualityDetailID: null,
        qualityID: null,
        qualityBaseID: null,
        hasQualityDetail: null,
        isActive: true
      },
      searchForm: {
        productionPlanDetailID: null,
        dates: [],
        productionTeamID: null,
        clientID: null
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mes/prd_productionplanschedulelog/get',
        productionStationComboStoreByQuery: '/mes/comboQuery/productionStationComboStoreByQuery',
        productionTeamComboStoreByQuery: '/mes/comboQuery/productionTeamComboStoreByQuery',
        qualityDetailComboStoreByQuery: '/mes/comboQuery/qualityDetailComboStoreByQuery',
        qualityBaseComboStoreByQuery: '/mes/comboQuery/qualityBaseComboStoreByQuery',
        qualityComboStoreByQuery: '/mes/comboQuery/qualityComboStoreByQuery',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      clientComboStoreByQuery: [],
      qualityDetailComboStoreByQuery: [],
      qualityBaseComboStoreByQuery: [],
      qualityComboStoreByQuery: [],
      productionStationComboStoreByQuery: [],
      productionTeamComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    if (this.productionPlanDetailID !== null) {
      this.searchForm.productionPlanDetailID = this.productionPlanDetailID
      this.loadData()
    }

    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.productionStationComboStoreByQuery).then(result => {
        this.productionStationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery).then(result => {
        this.productionTeamComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityBaseComboStoreByQuery).then(result => {
        this.qualityBaseComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityComboStoreByQuery).then(result => {
        this.qualityComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityDetailComboStoreByQuery).then(result => {
        this.qualityDetailComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery, { text: query }).then(result => {
        this.productionTeamComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.productionStationComboStoreByQuery, { text: query }).then(result => {
        this.productionStationComboStoreByQuery = result
      })
    },

    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.qualityComboStoreByQuery, { text: query }).then(result => {
        this.qualityComboStoreByQuery = result
      })
    },
    remoteMethod6 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.qualityDetailComboStoreByQuery, { text: query }).then(result => {
        this.qualityDetailComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.qualityBaseComboStoreByQuery, { text: query }).then(result => {
        this.qualityBaseComboStoreByQuery = result
      })
    }
  }
}
</script>

<style lang="scss">
.prd_productionplanschedulelog {
  .vxe-toolbar.size--mini.is--perfect {
    height: auto;
  }
  .is--perfect {
    // .vxe-toolbar.size--mini {
    //   height: auto;
    // }
    .vxe-tools--wrapper {
      width: 97%;
    }
  }
}
</style>
