<template>
  <d2-container class="page2">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button>
            <template>下拉按钮</template>
            <template v-slot:dropdowns>
              <vxe-button>删除</vxe-button>
              <vxe-button>保存</vxe-button>
            </template>
          </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-input v-model="input" placeholder="编码/名称" suffix-icon="fa fa-search"></vxe-input>
          <vxe-button status="success">查询</vxe-button>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-toolbar perfect custom>
      <template v-slot:buttons>
        <vxe-button @click="click()">下一页</vxe-button>
        <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent">新增</vxe-button>
        <vxe-button icon="fa fa-trash-o" status="perfect" @click="removeEvent">删除</vxe-button>
        <vxe-button icon="fa fa-save" status="perfect" @click="saveEvent">保存</vxe-button>
        <vxe-button icon="fa fa-mail-reply" status="perfect" @click="revertEvent">还原</vxe-button>
        <vxe-button>自定义模板</vxe-button>
        <vxe-button>按钮2</vxe-button>
        <vxe-button>按钮3</vxe-button>
        <vxe-button>
          <template>下拉按钮</template>
          <template v-slot:dropdowns>
            <vxe-button>按钮1</vxe-button>
            <vxe-button>按钮2</vxe-button>
            <vxe-button>按钮3</vxe-button>
          </template>
        </vxe-button>
      </template>
      <template v-slot:tools>
        <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
        <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
      </template>
    </vxe-toolbar>
    <div style="height:calc(90vh - 140px)">
      <vxe-table resizable border highlight-hover-row highlight-current-row highlight-hover-column highlight-current-column height="100%" :data="tableData" @cell-dblclick="celldblclick">
        <vxe-table-column type="seq" width="60"></vxe-table-column>
        <vxe-table-column field="name" title="Name" sortable></vxe-table-column>
        <vxe-table-column field="sex" title="Sex"></vxe-table-column>
        <vxe-table-column field="age" title="Age"></vxe-table-column>
        <vxe-table-column field="address" title="Address" show-overflow></vxe-table-column>
        <vxe-table-column title="操作" width="100" show-overflow>
          <template v-slot="{ row }">
            <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)"></vxe-button>
            <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)"></vxe-button>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <template slot="footer">
      <vxe-pager :current-page.sync="page6.currentPage" :page-size.sync="page6.pageSize" :total="page6.totalResult" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:left>
          <vxe-button size="small">
            <template>更多操作</template>
            <template v-slot:dropdowns>
              <vxe-button type="text">批量修改</vxe-button>
              <vxe-button type="text">批量管理</vxe-button>
              <vxe-button type="text">批量删除</vxe-button>
            </template>
          </vxe-button>
        </template>
        <template v-slot:right>
          <div style="width: 300px;" class=".d2-text-center">{{company}}&nbsp;&nbsp;&nbsp;{{company}}</div>
        </template>
      </vxe-pager>
    </template>

    <vxe-modal v-model="showEdit" :title="selectRow ? '编辑&保存' : '新增&保存'" width="800" :loading="submitLoading" resize destroy-on-close>
      <vxe-form :data="formData" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
// import tableMixinxs from '@/components/table/tablemixins/table-mixins.js'
import { cloneDeep } from 'lodash'
export default {
  name: 'master',
  //   mixins: [tableMixinxs],
  data () {
    return {
      company: process.env.VUE_APP_Development_Company,
      versions: process.env.VUE_APP_Development_Versions,
      input: '',
      tableData: [],
      tableHeight: 0,
      page6: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 300
      },
      formData: {
        name: '',
        role: '',
        sex: null,
        age: null,
        region: [],
        date: null,
        flag: false,
        rate: 0,
        flag1: '',
        checkedList: []
      }
    }
  },

  methods: {
    click () {
      this.$emit('nextpage', {
        pagename: 'detail',
        data: cloneDeep('row'),
        keepalive: true,
        action: 'edit'
      })
    },
    celldblclick (row) {
      this.$emit('nextpage', {
        pagename: 'detail',
        data: cloneDeep(row),
        keepalive: true,
        action: 'edit'
      })
    },
    createdata () {
      var data = []
      for (let index = 0; index < 100; index++) {
        data.push({
          id: index,
          name: 'name' + index,
          age: index,
          address: '地址' + index
        })
      }
      return data
    }
  },
  created () {
    this.tableData = this.createdata()
  }
}
</script>

<style lang='scss' >
.page2 {
  .d2-container-full__body {
    height: 50% !important;
    // overflow-y: hidden !important;
  }

  // padding: 5px;
  .d2-container-full__header {
    padding: 8px !important;
  }
  .d2-container-full__footer {
    padding: 8px !important;
  }
  //   .d2-container-full__body {
  //     padding: 0px !important;
  //     position: relative !important;
  //     height: calc(100vh - 100px) !important;
  //   }
}
</style>
