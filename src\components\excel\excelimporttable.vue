<template>
  <d2-container>
    <div>
      <input ref="excel-upload-input" class="excel-upload-input" type="file" accept=".xlsx, .csv,.xls" @change="handleClick">
      <div class="drop" @drop="handleDrop" @dragover="handleDragover" @dragenter="handleDragover">
        选择excel(.xlsx,.csv,.xls后缀的文件)文件
        <el-button :loading="loading" style="margin-left:16px;" size="mini" type="primary" @click="handleUpload">
          浏览
        </el-button>
      </div>
      <vxe-table border ref="xTable" height="800" :data="tableData">
        <vxe-table-column v-for="config in tableColumn" :key="config.key" :type="config.type" :field="config.field" :title="config.title" :fixed="config.fixed" :width="config.width" :filters="config.filters">
        </vxe-table-column>
      </vxe-table>
    </div>
  </d2-container>
</template>
<script>
import * as XLSX from 'xlsx'

export default {
  props: {
    beforeUpload: Function, // eslint-disable-line
    onSuccess: Function// eslint-disable-line
  },
  data () {
    return {
      loading: false,
      excelData: {
        header: null,
        results: null
      },
      tableData: [],
      tableColumn: [
        { key: 1, type: 'seq', width: 60, fixed: null }
      ]
    }
  },
  methods: {
    generateData ({ header, results }) {
      this.tableData = results
      this.createTableColumn(header)
      this.excelData.header = header
      this.excelData.results = results
      this.onSuccess && this.onSuccess(this.excelData)
    },
    createTableColumn (list) {
      list.forEach((item, index) => {
        this.tableColumn.push({ field: item, title: item, width: 100 })
      })
    },
    handleDrop (e) {
      e.stopPropagation()
      e.preventDefault()
      if (this.loading) return
      const files = e.dataTransfer.files
      if (files.length !== 1) {
        this.$message.error('只支持上传一个文件!')
        return
      }
      const rawFile = files[0] // only use files[0]
      if (!this.isExcel(rawFile)) {
        this.$message.error('只能上传 .xlsx, .xls,.csv   后缀的文件')
        return false
      }
      this.upload(rawFile)
      e.stopPropagation()
      e.preventDefault()
    },
    handleDragover (e) {
      e.stopPropagation()
      e.preventDefault()
      e.dataTransfer.dropEffect = 'copy'
    },
    handleUpload () {
      this.$refs['excel-upload-input'].click()
    },
    handleClick (e) {
      const files = e.target.files
      const rawFile = files[0] // only use files[0]
      if (!rawFile) return
      this.upload(rawFile)
    },
    upload (rawFile) {
      this.$refs['excel-upload-input'].value = null

      if (!this.beforeUpload) {
        this.readerData(rawFile)
        return
      }
      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerData(rawFile)
      }
    },
    readerData (rawFile) {
      this.loading = true
      // const fixdata = data => {
      //   let o = ''
      //   let l = 0
      //   const w = 10240
      //   for (; l < data.byteLength / w; ++l) o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w, l * w + w)))
      //   o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w)))
      //   return o
      // }
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          // const fixedData = fixdata(data)
          const workbook = XLSX.read(data, { type: 'array' })
          //  const workbook = XLSX.read(data, { type: 'array',cellDates: true })

          // const workbook = XLSX.read(btoa(fixedData), { type: 'base64'})
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          const header = this.getHeaderRow(worksheet)
          const sheet2JSONOpts = {
            /** Default value for null/undefined values */
            defval: '', // 给defval赋值为空的字符串,
            raw: false,
            rawNumbers: false
          }
          const results = XLSX.utils.sheet_to_json(worksheet, sheet2JSONOpts)
          //  var csv = XLSX.utils.sheet_to_csv(worksheet);
          // const results = XLSX.utils.sheet_to_json(worksheet)
          // console.log(csv)
          console.log(worksheet)
          this.generateData({ header, results })
          this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow (sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      const R = range.s.r
      /* start in the first row */
      for (C = range.s.c; C <= range.e.c; ++C) { /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        /* find the cell in the first row */
        let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        headers.push(hdr)
      }
      return headers
    },
    isExcel (file) {
      return /\.(xlsx|xls|csv)$/.test(file.name)
    }
  }
}
</script>

<style scoped>
.excel-upload-input {
  display: none;
  z-index: -9999;
}
.drop {
  border: 2px dashed #bbb;
  width: 600px;
  height: 60px;
  line-height: 60px;
  margin: 0 auto;
  font-size: 24px;
  border-radius: 5px;
  text-align: center;
  color: #bbb;
  position: relative;
}
</style>
