<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent1" v-if="menuAction.allowAdd">新增入库单</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent2" v-if="menuAction.allowAdd">新增出库单</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemStockInvoicesType" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.itemStockInvoicesType" placeholder="类型" clearable>
                  <vxe-option v-for="item in ItemStockInvoicesTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemStockInvoicesGroup" :item-render="{}">
              <template #default="{ data }">
                <vxe-select v-model="data.itemStockInvoicesGroup" placeholder="分类" clearable>
                  <vxe-option v-for="item in ItemStockInvoicesGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID" size="mini" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItemstockinvoicesMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="itemStockInvoicesGroupText" title="分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="itemStockInvoicesTypeText" title="类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="itemStockInvoicesStateText" title="状态" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="clientName" title="供货方" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="invoicesNumber" title="单据号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="department" title="部门" sortable width="100"></vxe-table-column>
      <vxe-table-column field="productionNumber" title="生产批号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button status="warning" @click="printEvent(row)" v-if="menuAction.allowPrint">打印</vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit" :disabled="row.itemStockInvoicesState===1"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存&入库单' : '新增&保存&入库单'" width="800" resize destroy-on-close show-zoom :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="供货方" field="clientID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="供货方" size="mini" remote reserve-keyword :remote-method="remoteMethod4">
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="业务属性" field="businessGroupID" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.businessGroupID" placeholder="业务类型" filterable clearable size="mini">
              <el-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="状态" field="itemStockInvoicesState" span="12" :item-render="{name: 'select',options: ItemStockInvoicesStateComboStore}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="showEdit2" :title="selectRow.id!=null? '编辑&保存&出库单' : '新增&保存&出库单'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules2" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="供货方" field="clientID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="供货方" size="mini" remote reserve-keyword :remote-method="remoteMethod4">
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="部门" field="department" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="生产批号" field="productionNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="状态" field="itemStockInvoicesState" span="12" :item-render="{name: 'select',options: ItemStockInvoicesStateComboStore}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep, isEmpty } from 'lodash'
export default {
  name: 'WarItemstockinvoicesMaster',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        itemStockInvoicesType: 1,
        clientID: null,
        invoicesNumber: null,
        department: null,
        productionNumber: null,
        remark: '',
        isActive: true,
        itemStockInvoicesState: 0,
        itemStockInvoicesGroup: 0
      },
      showEdit2: false,
      formRules: {
        clientID: [{ required: true, message: '请选择供货方' }],
        businessGroupID: [{ required: true, message: '请选择业务属性' }]

      },
      isAutoLoding: false,
      formRules2: {

      },
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/war_itemstockinvoices/get',
        add: '/mtm/war_itemstockinvoices/adds',
        edit: '/mtm/war_itemstockinvoices/updates',
        delete: '/mtm/war_itemstockinvoices/deletes',
        ItemStockInvoicesTypeComboStore: '/mtm/combo/ItemStockInvoicesTypeComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ItemStockInvoicesStateComboStore: '/mtm/combo/ItemStockInvoicesStateComboStore',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore',
        ItemStockInvoicesGroupComboStore: '/mtm/comboQuery/ItemStockInvoicesGroupComboStore'
      },
      ItemStockInvoicesGroupComboStore: [],
      ItemStockInvoicesTypeComboStore: [],
      BusinessGroupComboStore: [],
      clientComboStoreByQuery: [],
      ItemStockInvoicesStateComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    console.log(this.masterSeach)
    this.loadData(this.masterSeach)
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemStockInvoicesTypeComboStore).then(result => {
        this.ItemStockInvoicesTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemStockInvoicesStateComboStore).then(result => {
        this.ItemStockInvoicesStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemStockInvoicesGroupComboStore).then(result => {
        this.ItemStockInvoicesGroupComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },

    // 编辑
    editEvent (row) {
      this.selectRow = cloneDeep(row)
      if (this.selectRow.itemStockInvoicesType === 1) {
        this.showEdit = true
      } else {
        this.showEdit2 = true
      }
    },
    insertEvent1 () {
      this.selectRow = cloneDeep(this.formData)
      this.selectRow.itemStockInvoicesType = 1
      this.showEdit = true
    },
    insertEvent2 () {
      this.selectRow = cloneDeep(this.formData)
      this.selectRow.itemStockInvoicesType = 2
      this.showEdit2 = true
    },
    // 提交
    submitEvent () {
      if (isEmpty(this.selectRow.id) || this.selectRow.id === undefined || this.selectRow.id === null || this.selectRow.id === '00000000-0000-0000-0000-000000000000') {
        this.$api.ActionRequest(this.api.add, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '新增成功', status: 'success' })
          this.loadData()
          this.showEdit = false
          this.showEdit2 = false
        })
      } else {
        this.$api.ActionRequest(this.api.edit, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '保存成功', status: 'success' })
          this.loadData()
          this.showEdit = false
          this.showEdit2 = false
        })
      }
    },
    printEvent (row) {
      var url = this.mtmpai.replace('/api/', '')
      window.open(`${url}/fs/print/itemstockinvoices/pdf?num=${row.id}`, '_blank')
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (to.params.text) {
        vm.masterSeach.text = to.params.text
      }
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
