<template>
  <vxe-form title-align="right" title-width="100">
    <vxe-form-item title="表单操作列位置" span="12" :item-render="{}"> <template #default>
        <vxe-switch v-model="fixed" @change="fixedchange"></vxe-switch>{{fixed?"右侧":"左侧"}}
      </template>
    </vxe-form-item>
    <vxe-form-item span="12" :item-render="{}"> <template #default>
        <el-alert title="  配置完成后刷新页面既可生效" type="warning">
        </el-alert>
      </template>
    </vxe-form-item>
  </vxe-form>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { cloneDeep } from 'lodash'
export default {
  name: 'vxetableset',
  data () {
    return {
      fixed: true

    }
  },
  computed: {
    ...mapState('d2admin/vxetable', ['vxetableconfig'])
  },
  methods: {
    ...mapActions('d2admin/vxetable', [
      'set'
    ]),
    fixedchange ({ value }) {
      var str = value ? 'right' : 'left'
      var d = cloneDeep(this.vxetableconfig)
      d.tableOptFixed = str
      this.set(d)
      console.log(this.vxetableconfig.tableOptFixed)
    }
  },
  created () {
    if (this.vxetableconfig.tableOptFixed === 'right') {
      this.fixed = true
    } else {
      this.fixed = false
    }
  }
}
</script>

<style>
</style>
