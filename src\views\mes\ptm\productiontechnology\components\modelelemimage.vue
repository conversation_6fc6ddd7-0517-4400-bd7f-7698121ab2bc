<template>
  <d2-container type="ghost">
    <div class="text main-container">
      <div class="main-picture center-block">
        <template v-if="form!==null&&isGroupUp(form.groupID)">
          <div v-for="(item1,index1) in 4" v-bind:Key="'b'+index1" class="main-list shangyi">
            <div v-for="(item2,index2) in 3" :key="'c'+index2" class="item" :title="'位置 ' + (arrIntImgRule1[index1][index2])">
              <template v-for="(elem,elemindex) in ElemFilter(index1,index2,ModelElemImages)">
                <img v-if="DetailModelElem(elem,index1,index2)" :key="elemindex" :src="elem.imagePath" v-bind:imageSeq="elem.imageSeq" v-bind:tag-modelElemID='elem.modelElemID' v-bind:tag-modelElemID1='elem.modelElemID1' />
              </template>
            </div>
          </div>
        </template>
        <template v-else>
          <div v-for="(item1,index1) in 3" v-bind:Key="'b'+index1" class="main-list-pants xiayi">
            <div v-for="(item2,index2) in 4" :key="'c'+index2" class="item" :title="'位置 ' + (arrIntImgRule2[index1][index2])">
              <template v-for="(elem,elemindex) in ElemFilter(index1,index2,ModelElemImages)">
                <img v-if="DetailModelElem(elem,index1,index2)" :key="elemindex" :src="elem.imagePath" v-bind:imageSeq="elem.imageSeq" v-bind:tag-modelElemID='elem.modelElemID' v-bind:tag-modelElemID1='elem.modelElemID1' />
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="itemimage">
      <template v-for="(elem,elemindex) in ItemImages">
        <div class="item" :key="elemindex">
          <img style="width: 300px;height: 150px;display: inherit;" :key="elemindex" :src="elem.imageUrl" v-bind:tag-itemID='elem.itemID' />
        </div>
      </template>
    </div>
  </d2-container>
</template>

<script>
import config from '@/config.js'
export default {
  name: 'modelelemimages',
  props: {
    form: {
      type: Object
    },
    productstationconfigData: {
      type: Object
    }
  },
  mixins: [config],
  watch: {
    'form.elemImages': {
      deep: true,
      handler (newval, oldval) {
        if (newval !== null) {
          this.elemImages.id = newval.modelID
          this.elemImages.modelElemIDs = newval.modelElemIDs
          this.$nextTick(() => {
            this.get(this.elemImages)
          })
        } else {
          this.ModelElemImages = []
        }
      }
    },
    'form.modelElems': {
      deep: true,
      handler (newval, oldval) {
        if (newval !== null) {
          this.$nextTick(() => {
            this.getItemImages(newval)
          })
        } else {
          this.ItemImages = []
        }
      }
    }
  },
  data () {
    return {
      elemImages: {
        id: null,
        modelElemIDs: [],
        isRefresh: true
      },
      api: {
        get: '/mtm/oDM_SorderDetailModel/getModelElemImages',
        getitemimage: '/mtm/bAD_ItemImage/getByItemIDs'
      },
      ModelElemImages: [],
      ItemImages: [],
      arrIntImgRule1: [[1, 5, 6], [2, 4, 8], [10, 11, -1], [3, 9, 7]],
      arrIntImgRule2: [[1, 2, -1, -1], [3, 4, 5, 6], [7, 8, 9, 10]],
      imagePositions: []
    }
  },
  computed: {

  },
  created () {
    this.imagePositions = this.productstationconfigData.imagePositions
    if (this.form != null && this.form.elemImages.length > 0) {
      this.elemImages.id = this.form.elemImages.modelID
      this.elemImages.modelElemIDs = this.form.elemImages.modelElemIDs
      this.$nextTick(() => {
        this.get(this.elemImages)
      })
    }
    if (this.form != null && this.form.modelElems.length > 0) {
      this.$nextTick(() => {
        this.getItemImages(this.form.modelElems)
      })
    }
  },
  methods: {
    get (elems) {
      if (elems.id === null || elems.modelElemIDs.length <= 0) {
        this.ModelElemImages = []
        return
      }
      this.$api.ActionRequest(this.api.get, elems).then(result => {
        this.ModelElemImages = result
      })
    },
    getItemImages (modelElems) {
      if (modelElems.length <= 0) {
        this.ItemImages = []
        return
      }
      var items = modelElems.filter(item => { return item.itemID !== null })
      if (items.length <= 0) {
        return
      }
      var itemids = items.map(item => { return item.itemID })
      if (itemids.length <= 0) {
        return
      }
      this.$api.ActionRequest(this.api.getitemimage, { IDs: itemids }).then(result => {
        this.ItemImages = result
      })
    },
    ElemFilter (index1, index2, data) {
      if (data && data.length === 0) return
      var index = 0
      // index = this.arrIntImgRule1[index1][index2] // 上衣
      if (this.isGroupUp(this.form.groupID)) {
        index = this.arrIntImgRule1[index1][index2] // 上衣
      } else {
        index = this.arrIntImgRule2[index1][index2] // 裤子
      }
      // var index = this.arrIntImgRule3[index1][index2] //商标
      if (this.imagePositions.length > 0) {
        if (this.$utils.arrayIndexOf(this.imagePositions, index) < 0) {
          return []
        }
      }
      var array = []
      var elem = data.GetFirstElement('positionID', index)
      if (elem) {
        array = elem.list
      }
      // console.log(array)
      return array
    },
    // DetailModelElem (item, index1, index2) {
    //   if (item.fromType === 'Type') {
    //     return true
    //   }
    //   if (item.fromType === 'Base') {
    //     if (item.modelElemID1 !== null) {
    //       return (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID1)) > 0
    //     }
    //     return true
    //   }
    //   if (item.fromType === 'ModelElem') {
    //     return false
    //   } else {
    //     if (item.modelElemID === null) {
    //       return false
    //     }
    //     var b = false
    //     if (item.modelElemID1 !== null && item.modelElemID2 === null) {
    //       b = (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID) > 0) && (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID1) > 0)
    //     } else if (item.modelElemID1 !== null && item.modelElemID2 !== null) {
    //       b = (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID) > 0) && (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID1) > 0) && (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID2) > 0)
    //     } else {
    //       b = (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID)) > 0
    //     }
    //     return b
    //   }
    // },
    DetailModelElem (item, index1, index2) {
      if (item.fromType === 'Type') {
        return true
      }
      if (item.fromType === 'Base') {
        if (item.modelElemID1 !== null) {
          return (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID1)) > 0
        }
        return true
      }
      if (item.fromType === 'ModelElem') {
        return false
      } else {
        if (item.modelElemID === null) {
          return false
        }
        var b = false
        if (item.modelElemID1 !== null && item.modelElemID2 === null) {
          b = (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID) >= 0) && (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID1) >= 0)
        } else if (item.modelElemID1 !== null && item.modelElemID2 !== null) {
          b = (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID) >= 0) && (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID1) >= 0) && (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID2) >= 0)
        } else {
          b = (this.$utils.indexOf(this.elemImages.modelElemIDs, item.modelElemID)) >= 0
        }
        return b
      }
    }
    // DetailModelElem(item, index1, index2) {
    //   if (item.fromType === 'Type') {
    //     return true
    //   }

    //   if (item.fromType === 'Base') {
    //     if (item.modelElemID1 !== null) {
    //       return this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID1)
    //     }
    //     return true
    //   }
    //   if (this.SorderDetailElemData.length === 0) {
    //     return false
    //   }
    //   if (item.fromType === 'ModelElem') {
    //     return false
    //   } else {
    //     if (item.modelElemID === null) {
    //       return false
    //     }
    //     var b = false
    //     if (item.modelElemID1 !== null && item.modelElemID2 === null) {
    //       b = this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID) && this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID1)
    //     } else if (item.modelElemID1 !== null && item.modelElemID2 !== null) {
    //       b = this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID) && this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID1) && this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID2)
    //     } else {
    //       b = this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID)
    //     }
    //     return b
    //   }
    // },
  }

}
</script>

<style lang='scss' scoped>
@import "./index.css";
.itemimage {
  min-height: 300px;
  // text-align: center;
  .item {
    display: inline-block;
    // border: 1px solid red;
    width: 300px;
    height: 150px;
    img {
      position: absolute;
      display: block;
    }
    // display: block;
    // width: 100%;
    // height: 50%;
  }
}
</style>
