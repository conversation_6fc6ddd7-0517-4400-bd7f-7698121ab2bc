<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">保存</vxe-button>
          <vxe-button status="perfect" @click="checkall('selected',true)" v-if="menuAction.allowAdd">全选选择</vxe-button>
          <vxe-button status="perfect" @click="checkall('selected',false)" v-if="menuAction.allowAdd">取消全选</vxe-button>
          <vxe-button status="perfect" @click="checkall('default',true)" v-if="menuAction.allowAdd">全选默认</vxe-button>
          <vxe-button status="perfect" @click="checkall('default',false)" v-if="menuAction.allowAdd">取消默认</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelGroupID">
              <template #default="{ data }">
                <el-select v-model="data.modelGroupID" filterable placeholder="版型系列" size="mini" clearable>
                  <el-option v-for="item in ModelGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="MomModelelemDetailTable" ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="groupName" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelCode" title="版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelName" title="版型名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="selected" title="选择" sortable width="100px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.selected"></vxe-checkbox>
        </template>
      </vxe-table-column>
      <vxe-table-column field="default" title="默认" sortable width="100px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.default"></vxe-checkbox>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>

  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'MomModelelemDetail', // 款式明细关联的版型
  mixins: [detailTableMixins],

  data() {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      searchForm: {
        groupID: null,
        text: null
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codename', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch ' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modelmodelelem/GetModelByModelElem',
        edit: '/mtm/mom_modelmodelelem/UpdatesByModel',
        GroupComboStore: '/mtm/combo/groupComboStore',
        ModelGroupComboStore: '/mtm/combo/ModelGroupComboStore',
      },
      ModelGroupComboStore: [],
      GroupComboStore: []
    }
  },
  async created() {
    await this.setGroupId(this.form)
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    await this.getCombStore()
  },
  watch: {
    'form.id': {
      deep: true,
      async handler(newVal, oldVal) {
        // console.log(`newVal:${newVal},oldVal:${oldVal}`)
        if (newVal !== oldVal) {
          await this.setGroupId(this.form)
          await this.loadData(this.form)
        }
      }
    }
  },
  methods: {
    async getCombStore() {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelGroupComboStore).then(result => {
        this.ModelGroupComboStore = result
      })
    },
    checkall(type, b) {
      this.tableData.forEach(element => {
        switch (type) {
          case 'selected':
            if (b) {
              element.selected = true
            } else {
              element.selected = false
            }
            break
          case 'default':
            if (b) {
              element.default = true
            } else {
              element.default = false
            }
            break
        }
      })
    },
    async setGroupId(form) {
      this.searchForm.groupID = form.groupID
    },
    async insertEvent() {
      await this.$api.ActionRequest(this.api.edit, this.tableData).then(result => {
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.loadData({ id: this.form.id })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
