<template>
  <div>
    <vxe-form element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)" :data="sorderForm" :rules="sorderformRules" class="sorderformRules" title-width="100" title-align="right" ref="sorderform">
      <vxe-form-item title="商户信息" field="code" span="24" :title-prefix="{  icon: 'fa fa-address-card-o' }" title-align="left" :item-render="{}">
        <template #default>
          <span class="sordernum" style="font-size:20px;font-weight:600;color:#909399"> {{sorderForm.code}}</span>
        </template>
      </vxe-form-item>
      <vxe-form-item title="联系人" field="contact" span="8" :item-render="{}">
        <template #default>
          <el-input placeholder="请输入联系人" v-model="sorderForm.contact" size="mini" :disabled="EditState">
            <el-button slot="append" icon="el-icon-search" @click="personAddressShowEvent"></el-button>
          </el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="电话" field="tel" span="8" :item-render="{}">
        <vxe-input v-model="sorderForm.tel" placeholder="请输入地址" :size="size" :disabled="EditState"></vxe-input>
      </vxe-form-item>
      <vxe-form-item title="收货地址" field="address" span="6" :item-render="{}">
        <vxe-input v-model="sorderForm.address" placeholder="请输入地址" :size="size" :disabled="EditState"></vxe-input>
      </vxe-form-item>
      <vxe-form-item title="订单类型" field="sorderTypeID" :item-render="{}">
        <template #default="{ data }">
          <el-select v-model="data.sorderTypeID" placeholder="订单类型" :size="size" :disabled="EditState">
            <el-option v-for="(item,index) in SorderTypeComboStore" :key="index" v-show="item.value===3||item.value===50" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="期望交期" field="deliveryDate" :item-render="{}">
        <template #default>
          <el-date-picker v-model="sorderForm.deliveryDate" value-format="yyyy-MM-dd" type="date" placement="选择日期" clearable :size="size" :disabled="EditState">
          </el-date-picker>
        </template>
      </vxe-form-item>
      <vxe-form-item title="是否加急" field="isUrgent" :item-render="{}">
        <template #default>
          <vxe-switch v-model="sorderForm.isUrgent" open-label="是" close-label="否" :size="size" :disabled="EditState"></vxe-switch>
        </template>
      </vxe-form-item>
      <vxe-form-item title="团装版型数据" span="24" :item-render="{}" :title-prefix="{  icon: 'fa fa-info-circle' }" title-align="left"></vxe-form-item>
    </vxe-form>
    <person-address-select :sorderStore="sorderStore" />
    <!-- <client-person-insert /> -->
    <!-- <sorder-item-insert :sorderStore="sorderStore" /> -->

  </div>
</template>
<script>
// import sorderEditState from './sordereditstate'
import { mapState } from 'vuex'
// import { difference, cloneDeep, isEmpty } from 'lodash'
import config from '@/config.js'
import PersonAddressSelect from './components/personaddressselect'
// import ClientPersonInsert from './components/clientpersoninsert'
// import SorderItemInsert from './components/sorderiteminsert'
// import SorderDetailUpdateOrAdd from './components/sorderdetailupdateoradd'
export default {
  name: 'sorderinfro',
  mixins: [config],
  components: {
    // SorderDetailModel
    PersonAddressSelect
    // ClientPersonInsert,
    // SorderItemInsert,
    // SorderDetailUpdateOrAdd
  },
  props: {
    client: {
      type: Object,
      default: null
    },
    sorderStore: {
      type: Object,
      default: null
    },
    EditState: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      size: 'mini',
      sorderForm: {
        clientID: null,
        address: null,
        contact: null,
        tel: null,
        code: null,
        // itemID: null,
        // itemLength: null,
        // itemText: null,
        // finalWidth: 0,
        // finalTexture: null, // 纹理
        // finalComposition: null, // 面料成分
        // finalFabricLabel: null, // 客供面料表
        // finalFabric: null, // 客供面料号
        deliveryDate: null,
        // clientPersonID: null,
        // clientPersonText: null,
        // height: null,
        HalfFitting: false,
        isUrgent: false,
        remark: null,
        id: null,
        // sorderDetailID: null,
        statusID: 0,
        sorderDetailModels: [],
        sorderTypeID: 3
        // itemBusinessGroupID: null, // 面料业务归属

      },
      api: {
        SorderTypeComboStore: '/mtm/combo/SorderTypeComboStore'
      },
      SorderTypeComboStore: [],
      sorderformRules: {
        clientID: [{ required: true, message: '请输入客户信息' }],
        tel: [{ required: true, message: '联系电话不能为空' }],
        contact: [{ required: true, message: '联系人不能为空' }],
        address: [{ required: true, message: '收货地址不能为空' }]
      }
    }
  },
  watch: {
    sorderStore: {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== null) {
          this.sorderForm = Object.assign(this.sorderForm, newVal)
          // this.personAddress.tablePage.id = newVal.clientID
          // this.clientPsersonSelectRow.clientID = newVal.clientID
        }
      }
    },
    client: {
      deep: true,
      async handler (newVal, oldVal) {
        if (newVal !== null) {
          this.sorderForm.clientID = newVal.id
          // this.clientPsersonSelectRow.clientID = newVal.id
          this.sorderForm.contact = newVal.contact
          this.sorderForm.address = newVal.address
          this.sorderForm.tel = newVal.tel
        } else {
          this.sorderForm.clientID = null
          this.sorderForm.contact = null
          this.sorderForm.address = null
          this.sorderForm.tel = null
        }
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SorderTypeComboStore).then(result => {
        this.SorderTypeComboStore = result
      })
    },
    personAddressShowEvent () {
      if (this.sorderStore.clientID === null || this.sorderStore.clientID === '') {
        return
      }
      this.personAddress.Show = true
      this.personAddressGet()
    },
    setData (data) {
      this.sorderForm = Object.assign(this.sorderForm, data)
    }
  }
}
</script>

<style lang="scss">
.model-check-warning {
  color: #e6a23c;
}
.dropdownitem {
  z-index: 999;
}
.my-green {
  color: green;
}

.sorderformRules {
  padding: 10px;
}
</style>
