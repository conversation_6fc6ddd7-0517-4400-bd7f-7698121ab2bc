@echo off
setlocal enabledelayedexpansion
echo === Windows Compatible Build Test ===

cd ..

echo Step 1: Check package.json build scripts
echo.
echo Build scripts in package.json:
findstr /n "build" package.json | findstr ":"

echo.
echo Step 2: Clean previous build
echo.
if exist "dist" (
    echo Removing old dist folder...
    rmdir /s /q dist
)

echo.
echo Step 3: Build with staging mode (Windows compatible)
echo.
echo Building with staging environment...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    echo.
    echo Trying alternative build command...
    set NODE_OPTIONS=--openssl-legacy-provider
    call npx vue-cli-service build --mode staging
    if %errorlevel% neq 0 (
        echo ERROR: Alternative build also failed
        echo.
        echo Please check:
        echo 1. Node.js version compatibility
        echo 2. npm dependencies are installed
        echo 3. Try: npm install
        pause
        exit /b 1
    )
)

echo Build successful!

echo.
echo Step 4: Analyze built files
echo.
if exist "dist\\js" (
    echo Checking built JavaScript files for hardcoded values:
    echo.
    echo Files in dist/js:
    dir /b dist\\js\\*.js
    echo.
    
    echo Looking for API URLs in built files:
    for %%f in (dist\\js\\app.*.js) do (
        echo Checking %%f:
        findstr /i "kngintl.com\|localhost:21021\|api-sso-test\|api-mtm-test" "%%f" >nul
        if !errorlevel! equ 0 (
            echo   FOUND: Hardcoded API URLs in %%f
            findstr /i "kngintl.com\|localhost:21021" "%%f" | head -2
        ) else (
            echo   OK: No obvious hardcoded URLs found
        )
    )
) else (
    echo ERROR: dist/js folder not found
    exit /b 1
)

echo.
echo Step 5: Check built index.html
echo.
if exist "dist\\index.html" (
    echo Checking built index.html for config placeholders:
    findstr /i "VUE_APP\|config.js" dist\\index.html
    if %errorlevel% neq 0 (
        echo WARNING: No VUE_APP or config.js references found in built index.html
    )
) else (
    echo ERROR: dist/index.html not found
)

echo.
echo Step 6: Test Docker build and runtime config
echo.
docker stop windows-build-test >nul 2>&1
docker rm windows-build-test >nul 2>&1
docker rmi windows-build-test:latest >nul 2>&1
echo Building Docker image...
docker build -t windows-build-test:latest .
if %errorlevel% neq 0 (
    echo ERROR: Docker build failed
    pause
    exit /b 1
)

echo Starting container with test configuration...
docker run -d --name windows-build-test -p 8080:80 ^
  -e "VUE_APP_TITLE=WINDOWS-TEST-TITLE" ^
  windows-build-test:latest

timeout /t 5 /nobreak >nul

echo.
echo Step 7: Test results
echo.
echo Container logs:
docker logs windows-build-test

echo.
echo Generated config.js:
docker exec windows-build-test cat /usr/share/nginx/html/config.js 2>nul | findstr WINDOWS-TEST

echo.
echo Testing HTTP access:
curl -s http://localhost:8080/config.js 2>nul | findstr WINDOWS-TEST
if %errorlevel% neq 0 (
    echo WARNING: Could not access config.js via HTTP or no WINDOWS-TEST values found
)

echo.
echo === ANALYSIS ===
echo.
echo 1. If you see "FOUND: Hardcoded API URLs" above:
echo    ^=^> Vue compiled the environment variables into the JavaScript
echo    ^=^> Runtime configuration cannot override these values
echo    ^=^> You need to modify the source code to use runtime config
echo.
echo 2. If you see "OK: No obvious hardcoded URLs found":
echo    ^=^> The build looks good for runtime configuration
echo    ^=^> Check if the app code is using the runtime config correctly
echo.
echo 3. Next steps:
echo    - Open: http://localhost:8080/config-test.html
echo    - Look for WINDOWS-TEST values in green
echo    - If not working, run: analyze-code.bat
echo    - Then run: fix-with-runtime-config.bat
echo.

echo Cleanup: docker stop windows-build-test ^&^& docker rm windows-build-test
pause
