{"name": "mtm", "version": "2.0.0", "scripts": {"serve": "vue-cli-service serve --open", "start": "npm run serve", "dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:dev": "export  NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode staging", "build": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service build --mode development", "build:preview": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode preview", "lint": "vue-cli-service lint --fix", "test:unit": "vue-cli-service test:unit", "new": "plop"}, "dependencies": {"@d2-projects/vue-table-export": "^1.1.2", "@d2-projects/vue-table-import": "^1.0.1", "@jiaminghi/data-view": "^2.10.0", "autoprefixer": "^9.7.5", "axios": "^0.19.0", "axios-mock-adapter": "^1.18.1", "better-scroll": "^1.15.2", "core-js": "^3.4.3", "countup.js": "^2.0.5", "dayjs": "^1.8.17", "echarts": "^4.8.0", "element-resize-detector": "^1.2.4", "element-ui": "^2.13.1", "exceljs": "^4.4.0", "faker": "^4.1.0", "flex.css": "^1.1.7", "fuse.js": "^5.2.3", "hotkeys-js": "^3.7.3", "js-cookie": "^2.2.1", "jsbarcode": "^3.11.3", "json-editor-vue": "^0.16.0", "lodash": "^4.17.15", "lowdb": "^1.0.0", "nprogress": "^0.2.0", "qs": "^6.13.0", "screenfull": "^5.0.2", "sortablejs": "^1.10.1", "ua-parser-js": "^1.0.1", "vue": "^2.6.11", "vue-grid-layout": "^2.3.7", "vue-i18n": "^8.15.1", "vue-pdf": "^4.3.0", "vue-router": "^3.1.3", "vue-seamless-scroll": "^1.1.23", "vue-splitpane": "^1.0.6", "vuex": "^3.1.2", "vxe-table": "~3.8.24", "vxe-table-plugin-export-xlsx": "~3.3.4", "webpack-theme-color-replacer": "1.4.1", "xe-utils": "3.5.2", "xlsx": "~0.18.5"}, "devDependencies": {"@d2-projects/vue-filename-injector": "^1.1.0", "@kazupon/vue-i18n-loader": "^0.5.0", "@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-unit-jest": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "^1.0.2", "babel-eslint": "^10.0.3", "compression-webpack-plugin": "^3.0.1", "cz-conventional-changelog": "^3.2.0", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^6.2.2", "file-saver": "^2.0.5", "jszip": "^3.10.1", "plop": "^2.7.1", "sass": "~1.23.7", "sass-loader": "~8.0.0", "svg-sprite-loader": "^4.1.6", "text-loader": "^0.0.1", "vue-cli-plugin-i18n": "^1.0.1", "vue-template-compiler": "^2.6.10", "webpack-bundle-analyzer": "^3.6.0", "webpack-theme-color-replacer": "1.4.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "repository": {"type": "git", "url": "https://github.com/d2-projects/d2-admin.git"}}