<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table ref='master_table' class="sortable-column-demo" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="username" title="账号" sortable show-overflow width="100px"> </vxe-table-column>
      <vxe-table-column field="name" title="姓名" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="department" title="部门" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="gender" title="性别" :formatter="formatSex" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="birthday" title="生日" show-overflow :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100px"></vxe-table-column>
      <vxe-table-column field="email" title="邮箱" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="tel" title="手机号" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="mobile" title="手机" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="fax" title="传真" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="userType" title="用户类型" show-overflow :formatter="val=>formatSelect(val,userTypeList)" width="100px"> </vxe-table-column>
      <vxe-table-column field="position" title="职位" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="lastLoggingDate" title="最后登录时间" :formatter="val=>formatDate(val)" show-overflow width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="100px" show-overflow :fixed='tableOptFixed'>
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="账号" field="username" span="12">
          <vxe-input v-model="selectRow.username" placeholder="请输入昵称" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="密码" field="password" span="12">
          <vxe-input v-model="selectRow.password" placeholder="请输入密码" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="姓名" field="name" span="12">
          <vxe-input v-model="selectRow.name" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="部门" field="departmentID" span="12">
          <template #default>
            <vxe-select v-model="selectRow.departmentID" placeholder="部门">
              <vxe-option v-for="item in departmentCombStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="性别" field="gender" span="12">
          <template #default>
            <vxe-switch v-model="selectRow.gender" open-label="男" close-label="女" :on-value="true" :off-value="false"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="生日" field="birthday" span="12">
          <vxe-input v-model="selectRow.birthday" type="date" placeholder="请选择日期" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="邮箱" field="email" span="12">
          <vxe-input v-model="selectRow.email" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="手机号" field="tel" span="12">
          <vxe-input v-model="selectRow.tel" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="电话" field="mobile" span="12">
          <vxe-input v-model="selectRow.mobile" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="用户类型" field="userType" span="12">
          <template #default>
            <vxe-select v-model="selectRow.userType" placeholder="用户类型">
              <vxe-option v-for="item in userTypeList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="职位" field="position" span="12">
          <vxe-input v-model="selectRow.position" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="启用？" field="isActive" span="12">
          <template #default>
            <vxe-switch v-model="selectRow.isActive" open-label="是" close-label="否" on-icon="fa fa-check" off-icon="fa fa-close"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item span="24" align='center'>
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='35%'>
      <detail-table :footerCompanyInfo="footerCompanyInfo" :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import DetailTable from './detail'
export default {
  name: 'SymUserMaster',
  mixins: [masterTableMixins],
  components: {
    DetailTable
  },
  data () {
    return {
      formData: {
        id: null,
        username: '',
        name: '',
        password: '',
        department: '',
        gender: true,
        birthday: '',
        email: '',
        tel: '',
        mobile: '',
        date3: '',
        userType: 1,
        position: '',
        isActive: true,
        departmentID: ''
      },
      formRules: {
        username: [{ required: true, message: '请输入名称' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        password: [{ required: true, message: '请输入密码' }]
      },
      sexList: [
        { label: '女', value: false },
        { label: '男', value: true }
      ],
      userTypeList: [
        { label: '系统账号', value: 0 },
        { label: '内部账户', value: 1 },
        { label: '外部账户', value: 2 }
      ],

      api: {
        get: '/mtm/sym_user/get',
        add: '/mtm/sym_user/adds',
        edit: '/mtm/sym_user/updates',
        delete: '/mtm/sym_user/deletes',
        departmentCombStore: '/mtm/combo/departmentComboStore'
      },
      departmentCombStore: []
    }
  },

  async created () {
    await this.getCombStore()
    // this.formItems[4].itemRender.options = this.departmentCombStore
    // this.formItems[5].itemRender.options = this.sexList
    // this.formItems[10].itemRender.options = this.userTypeList
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
        this.departmentCombStore = result
      })
    }
  }
}
</script>

<style>
</style>
