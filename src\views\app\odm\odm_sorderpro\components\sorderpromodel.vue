<template>
  <div class="d2-panel-search-item" :class="hoverMode ? 'can-hover' : ''" flex>
    <div class="d2-panel-search-item__icon" flex-box="0">
      <div class="d2-panel-search-item__icon-box" flex="main:center cross:center">
        <d2-icon name="home" />
      </div>
    </div>
    <div class="d2-panel-search-item__info" flex-box="1" flex="dir:top">
      <div class="d2-panel-search-item__info-title" flex-box="1" flex="cross:center">
        <span>{{sorderProModelForm.modelText}}</span>
      </div>
      <div>
        <p style="font-size: 16px; font-weight: 800;">
          <span style="color:#E6A23C">顾客：{{sorderProModelForm.qty}}</span>&nbsp;&nbsp;&nbsp;
          <span style="color:#67C23A">已检验：{{sorderProModelForm.checkQty}}</span>&nbsp;&nbsp;&nbsp;
          <span style="color:#F56C6C">待检验{{ sorderProModelForm.qty-sorderProModelForm.checkQty }}</span></p>
      </div>
    </div>
    <div class="d2-panel-search-item__icon" flex-box="0" style="width:100px;">
      <!-- <slot name="checkall" :data="sorderProModelForm" /> -->
      <p>
        <slot name="checkall" :data="sorderProModelForm" />
        <!-- <template v-if="!sorderProModelForm.isChecked">
          <el-popover placement="top" width="400" trigger="click">
            <div>{{sorderProModelForm.message===""||sorderProModelForm.message===null?"请先检验规格":sorderProModelForm.message}}</div>
            <el-button slot="reference" type="warning" size="mini">规格<i class="el-icon-warning-outline el-icon--right"></i></el-button>
          </el-popover>
        </template>
        <template v-else>

          <el-button :type="sorderProModelForm.isChecked?'success':'warning'" size="mini">规格<i v-if="!sorderProModelForm.isChecked" class="el-icon-warning-outline el-icon--right"></i></el-button>
        </template> -->

      </p>
    </div>
    <div class="d2-panel-search-item__icon" flex-box="0">
      <div class="d2-panel-search-item__icon-box" flex="main:center cross:center">
        <slot name="next" :data="sorderProModelForm" />

      </div>
    </div>
    <div class="d2-panel-search-item__icon" flex-box="0">
      <div class="d2-panel-search-item__icon-box" flex="main:center cross:center">
        <slot name="remove" :data="sorderProModelForm" />
      </div>
    </div>

  </div>
</template>

<script>

import { cloneDeep } from 'lodash'
export default {
  name: 'SorderProModel',
  // mixins: [sorderEditState],
  // components: { UpLoadExcel },
  props: {
    sorderProModel: {
      type: Object,
      requerid: true
    },
    sorderProModels: {
      type: Array,
      requerid: true
    },
    hoverMode: {
      default: false
    },
    EditState: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {

      sorderProModelForm: {
        id: null, //
        modelId: null,
        sorderId: null
        // qty: '1', //
        // frontBowOrBackUp: null,
        // isFrontBow: false, //
        // isBackUp: false, //
        // customerSize: null, //
        // sorderDetailID: null, //
        // modelID: '', //
        // remark: null, //
        // designStyleID: null, //
        // modelText: null,
        // sorderSizeTypeID: '3',
        // sizeID: null,
        // sizeID1: null,
        // isChecked: false,
        // isCheckedElem: false,
        // message: null,
        // messageElem: null,
        // isRuleSize: '' // 算法支持
      }
    }
  },

  watch: {
    sorderProModel: {
      deep: true,
      immediate: true,
      handler: function (newVal, oldVal) {
        this.sorderProModelForm = Object.assign(this.sorderProModelForm, cloneDeep(newVal))
      }
    }
    // sorderProModels: {
    //   deep: true,
    //   immediate: true,
    //   handler: function (newVal, oldVal) {
    //     console.log(newVal)
    //   }
    // }

  },
  async created () {
    this.sorderProModelForm = Object.assign(this.sorderProModelForm, cloneDeep(this.sorderProModel))
    if (this.sorderProModelForm !== null) {
      if (this.sorderProModelForm.isFrontBow) {
        this.sorderProModelForm.frontBowOrBackUp = '1'
      }
      if (this.sorderProModelForm.isBackUp) {
        this.sorderProModelForm.frontBowOrBackUp = '2'
      }
    }
  },
  methods: {
    SorderDetailSizeChecksEvent () {

    },
    frontBowOrBackUpChange ({ value }) {
      if (value === '1') {
        this.sorderProModelForm.isFrontBow = true
        this.sorderProModelForm.isBackUp = false
      } else if (value === '2') {
        this.sorderProModelForm.isFrontBow = false
        this.sorderProModelForm.isBackUp = true
      } else {
        this.sorderProModelForm.isFrontBow = false
        this.sorderProModelForm.isBackUp = false
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.d2-panel-search-item {
  &.can-hover {
    @extend %unable-select;
    margin: 0px;
    &:hover {
      background-color: #f5f7fa;
      .d2-panel-search-item__icon {
        .d2-panel-search-item__icon-box {
          i {
            font-size: 24px;
            color: $color-primary;
          }
        }
      }
      .d2-panel-search-item__info {
        .d2-panel-search-item__info-title {
          color: $color-text-main;
        }
        .d2-panel-search-item__info-fullTitle {
          color: $color-text-normal;
        }
        .d2-panel-search-item__info-path {
          color: $color-text-normal;
        }
      }
    }
  }
  .d2-panel-search-item__icon {
    width: 64px;
    .d2-panel-search-item__icon-box {
      height: 64px;
      width: 64px;
      border-right: 1px solid $color-border-3;
      i {
        font-size: 20px;
        color: $color-text-sub;
      }
      svg {
        height: 20px;
        width: 20px;
      }
    }
  }
  .d2-panel-search-item__info {
    margin-left: 10px;
    .d2-panel-search-item__info-title {
      font-size: 16px;
      line-height: 16px;
      font-weight: bold;
      color: $color-text-normal;
    }
    .d2-panel-search-item__info-fullTitle {
      font-size: 10px;
      line-height: 14px;
      color: $color-text-placehoder;
    }
    .d2-panel-search-item__info-path {
      margin-bottom: 4px;
      font-size: 10px;
      line-height: 14px;
      color: $color-text-placehoder;
    }
  }
}
</style>
