<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" @click="insertEvent()" v-if="menuAction.allowEdit">新增</vxe-button> -->
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="warning" @click="RectifySorderBillEvent()" v-if="menuAction.allowAdd">账单统计</vxe-button>
          <vxe-button @click="exportSelectEvent({name:'账单'})" status="perfect" v-if="menuAction.allowPrint">导出选中</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates">
              <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="billType">
              <template #default="{ data }">
                <el-select v-model.trim="data.billType" filterable placeholder="类别" size="mini" clearable>
                  <el-option v-for="item in BillTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item> <vxe-form-item field="SorderBillTypes">
              <template #default="{ data }">
                <el-select v-model.trim="data.SorderBillTypes" placeholder="账单类型" size="mini" multiple collapse-tags clearable>
                  <el-option v-for="item in SorderBillTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="ModelElemPriceByClientDetailTable" ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" keep-source :footer-method="footerMethod" show-footer>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="订单类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderBillTypeText" title="账单类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="说明" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"></vxe-table-column>
      <vxe-table-column field="billTypeText" title="类别" sortable width="100">
        <template v-slot="{ row }">
          <el-tag v-if="row.billType===0" type="danger">{{row.billTypeText}}</el-tag>
          <el-tag v-else type="success">{{row.billTypeText}}</el-tag>
        </template>
      </vxe-table-column>

      <vxe-table-column field="clientPersonName" title="洗唛货号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="面料号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="businessGroupName" title="业务属性" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemTotal" title="面料成本" sortable width="100"></vxe-table-column>
      <vxe-table-column field="otherTotal" title="基本价格" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="count" title="合计" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="total" title="实际付款金额" sortable width="100">
        <template v-slot="{ row }">
          <span v-if="row.billType===0" style="color:red">{{row.total}}</span>
          <span v-else style="color:green">{{row.total}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="currentBalance" title="余额" sortable width="100"></vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100"></vxe-table-column>
      <vxe-table-column field="deliveryDate" title="要求交期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumberTime" title="发货日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="customerNumber" title="客户订单号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <template>
        <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
          <template v-slot="{ row }">
            <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
            <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
            <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
          </template>
        </vxe-table-column>
      </template>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-sizes="[50,200,500,1000,2000,3000,5000]" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="账单类型" field="sorderBillType" span="12" :item-render="{name: '$select', options: SorderBillTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="类别" field="billType" span="12" :item-render="{name: '$select', options: BillTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="时间" field="createOn" span="12">
          <template #default>
            <el-date-picker v-model="selectRow.createOn" type="datetime" placeholder="时间" size="mini">
            </el-date-picker>
          </template>
        </vxe-form-item>

        <vxe-form-item title="实际付款金额" field="total" span="12" :item-render="{name: 'input', attrs: {type: 'float',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="说明" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="RectifySorderBillShow" :title="this.form.codeName+'账单统计'" width="50%" resize destroy-on-close>
      <rectify-sorder-bill :client="this.form" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
import RectifySorderBill from './RectifySorderBill.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'SorderBill',
  mixins: [detailMixins],
  components: {
    RectifySorderBill
  },
  data () {
    return {
      formData: {
        sorderID: null,
        sorderBillType: 20,
        clientID: this.form.id,
        billType: 0,
        count: null,
        total: null,
        remark: '',
        isActive: true

      },
      RectifySorderBillShow: false,
      formRules: {
        billType: [{ required: true, message: '请选择类别' }],
        sorderBillType: [{ required: true, message: '请选择账单类型' }],
        total: [{ required: true, message: '清楚如实际金额' }]
      },
      api: {
        get: '/mtm/odm_sorderbill/get',
        add: '/mtm/odm_sorderbill/adds',
        edit: '/mtm/odm_sorderbill/updates',
        delete: '/mtm/odm_sorderbill/deletes',
        SorderBillTypeComboStore: '/mtm/combo/SorderBillTypeComboStore',
        BillTypeComboStore: '/mtm/combo/BillTypeComboStore'
        // ModelElemListComboStoreByQuery: '/mtm/comboQuery/modelElemListComboStoreByQuery'
      },
      BillTypeComboStore: [],
      SorderBillTypeComboStore: []
      // ModelElemListComboStoreByQuery: []

    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ ClientID: this.form.id })
    this.tableData = []
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.BillTypeComboStore).then(result => {
        this.BillTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.SorderBillTypeComboStore).then(result => {
        this.SorderBillTypeComboStore = result
      })
    },
    RectifySorderBillEvent () {
      this.RectifySorderBillShow = true
    },
    copyRowEvent (row, attributenames = [], code = false, codeName = false) {
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (this.$utils.has(this.selectRow, 'createBy')) {
        this.selectRow.createBy = null
      }
      if (this.$utils.has(this.selectRow, 'createID')) {
        this.selectRow.createID = null
      }
      if (this.$utils.has(this.selectRow, 'createOn')) {
        this.selectRow.createOn = null
      }
      if (this.$utils.has(this.selectRow, 'modifyBy')) {
        this.selectRow.modifyBy = null
      }
      if (this.$utils.has(this.selectRow, 'modifyID')) {
        this.selectRow.modifyID = null
      }
      if (this.$utils.has(this.selectRow, 'modifyOn')) {
        this.selectRow.modifyOn = null
      }
      // if (!code && XEUtils.has(this.selectRow, 'code')) {
      //   this.selectRow.code = null
      // }
      // if (!codeName && XEUtils.has(this.selectRow, 'codeName')) {
      //   this.selectRow.codeName = null
      // }
      if (attributenames.length > 1) {
        attributenames.forEach(name => {
          this.selectRow[name] = null
        })
      }
      this.selectRow.clientID = this.form.id
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      this.showEdit = true
    },
    // 编辑
    async editEvent (row) {
      // this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
      //   this.ModelElemComboStoreByQuery = result
      // })
      // await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
      //   this.ItemComboStore = result
      //   this.selectRow = cloneDeep(row)
      //   this.showEdit = true
      // })
      this.selectRow = cloneDeep(row)
      this.showEdit = true
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        if (item.billType === 1) {
          count += Number(item[field])
        }
        if (item.billType === 0) {
          count += Number(item[field])
        }
      })
      return count
    },
    footerMethod ({ columns, data }) {
      const footerData = [
        columns.map((column, _columnIndex) => {
          if (_columnIndex === 0) {
            return '合计'
          }
          if (['total'].includes(column.property)) {
            return this.sumNum(data, 'total')
          }
          return null
        })
      ]
      return footerData
    }
  }
}
</script>

<style>
</style>
