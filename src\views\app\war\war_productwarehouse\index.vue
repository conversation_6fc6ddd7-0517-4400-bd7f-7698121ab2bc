<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button status="success" @click="pickUpEvent(1)" v-if="menuAction.allowEdit">提货</vxe-button>
          <vxe-button status="warning" @click="pickUpEvent(2)" v-if="menuAction.allowEdit">(UPS)提货</vxe-button>
          <vxe-button status="danger" @click="SuitSupplyPickUpEvent" v-if="menuAction.allowEdit">SuitSupply取消订单汇总</vxe-button>
          <vxe-button @click="exportSelectEvent" v-if="menuAction.allowPrint" content="导出选中">

            <!-- <template #dropdowns>
              <vxe-button @click="exportDBSelectEvent" v-if="menuAction.allowPrint" type="text" content="(大宝)导出选中"></vxe-button>
            </template> -->
          </vxe-button>

          <vxe-button status="primary">准时交货率</vxe-button>
          <span><strong>{{outputStorageFinisedRatio}}%</strong></span>

          <el-radio-group v-model="outputStorageFinised" size="mini" @change="getOutputStorageFinisedRatio">
            <el-radio-button label="7">7天</el-radio-button>
            <el-radio-button label="30">30天</el-radio-button>
          </el-radio-group>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates" :item-render="{}">
              <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="productState" :item-render="{}">
              <template #default="{ data }">
                <vxe-select v-model="data.productState" placeholder="状态" clearable>
                  <vxe-option v-for="item in ProductStateComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default="{ data }">
                <vxe-input v-model.trim="data.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarProductwarehouseMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>

      <vxe-table-column field="sorderNumber" title="订单编号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="productStateText" title="状态" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipmentsNumber" title="发货单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="eoriNo" title="手册号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientShops" title="店铺合集" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientPersonName" title="顾客" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" sortable width="100"></vxe-table-column>
      <vxe-table-column field="address" title="地址" sortable width="100"></vxe-table-column>
      <vxe-table-column field="tel" title="电话" sortable width="100"></vxe-table-column>
      <vxe-table-column field="deliveryDate" title="期望交期" :formatter="val=>formatDate(val)" sortable width="100"></vxe-table-column>
      <vxe-table-column field="details" title="明细" cell-type="string" sortable width="100"></vxe-table-column>
      <vxe-table-column field="count" title="总数" sortable width="100"></vxe-table-column>
      <vxe-table-column field="country" title="国家" sortable width="100"></vxe-table-column>
      <vxe-table-column field="putInStorageCount" title="入库数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="outputStorageCount" title="出库数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column> -->
      <!-- <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="50" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button> -->
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',},props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="productWareHouseDetailShow" :title="(selectType===2?'(UPS)':'')+'选择要发货的订单数据'" width="60%" height="50%" resize destroy-on-close :loading="submitLoading">
      <product-ware-house-detail :selectRowIds="selectRowIds" :selectType="selectType" :success="success" />
    </vxe-modal>
    <vxe-modal v-model="SuitSupplyProductWareHouseDetailShow" :title="'SuitSypply取消订单汇总'" width="60%" height="50%" resize destroy-on-close :loading="submitLoading">
      <suitsupply-productwarehousedetail :selectRowIds="selectRowIds" :selectType="selectType" :success="success" />
    </vxe-modal>

  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import ProductWareHouseDetail from './productwarehousedetail.vue'
import SuitsupplyProductwarehousedetail from './suitsupply_productwarehousedetail.vue'

export default {
  name: 'war_productwarehouse',
  mixins: [masterTableMixins],
  components: {
    ProductWareHouseDetail,
    SuitsupplyProductwarehousedetail
  },
  data () {
    return {
      productWareHouseDetailShow: false,
      SuitSupplyProductWareHouseDetailShow: false,
      selectType: 1,
      ShipmentsShow: false,
      outputStorageFinised: '7',
      outputStorageFinisedRatio: 0,
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/war_productwarehouse/get',
        add: '/mtm/war_productwarehouse/adds',
        edit: '/mtm/war_productwarehouse/updates',
        delete: '/mtm/war_productwarehouse/deletes',
        ProductStateComboStore: '/mtm/combo/ProductStateComboStore',
        OutputStorageFinisedRatio: '/mtm/war_productwarehouse/OutputStorageFinisedRatio',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ShipmentTypeComboStore: '/mtm/combo/ShipmentTypeComboStore'

      },
      selectRowIds: [],
      ShipmentTypeComboStore: [],
      clientComboStoreByQuery: [],

      ProductStateComboStore: []
    }
  },
  mounted () {
    this.timer = setInterval(this.getOutputStorageFinisedRatio, 1000 * 60 * 10)// 毫秒*秒*分钟
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  async created () {
    await this.getCombStore()
    this.getOutputStorageFinisedRatio()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ProductStateComboStore).then(result => {
        this.ProductStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ShipmentTypeComboStore).then(result => {
        this.ShipmentTypeComboStore = result
      })
    },
    // 出库完成率
    async getOutputStorageFinisedRatio () {
      await this.$api.ActionRequest(this.api.OutputStorageFinisedRatio, { day: this.outputStorageFinised }).then(result => {
        this.outputStorageFinisedRatio = result.outputStorageFinisedRatio
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    SuitSupplyPickUpEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      this.selectRowIds = list.map((item) => { return item.id })
      this.SuitSupplyProductWareHouseDetailShow = true
    },
    pickUpEvent (type) {
      this.selectType = type
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要提货的订单', status: 'error' })
        return
      }

      this.selectRowIds = list.map((item) => { return item.id })
      this.productWareHouseDetailShow = true
    },
    success () {
      this.productWareHouseDetailShow = false
      this.SuitSupplyProductWareHouseDetailShow = false
      this.selectRowIds = []
      this.loadData()
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      console.log(to.params)
      if (to.params.text) {
        vm.searchForm.text = to.params.text
        to.params.text = null
      }
      if (to.params.refresh) {
        vm.loadData()
      }
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
