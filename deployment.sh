#!/bin/bash

#2021-12-22
#sunday

# 项目初始部署和发布docker镜像脚本
project_name=project_name1
tag=tag2
prot=prot3
project_environment=project_environment4 #环境变量
webhoohrul=webhoohrul5
committext=committext6
titlename=titlename7
httpspath=httpspath8
httpspwd=httpspwd9
project_namespace='shop'
harbor='harbor.xmmtm.com'
harbor_u='admin'
harbor_p='zhang123456'
messagetype='wechat'
state="0"

clear

d2=$(date "+%Y-%m-%d %H:%M:%S")
echo "--------------------开始部署--${project_name}:${tag}---${d2}------------"

echo "--------------------登陆 docker------------------"
docker login -u ${harbor_u} -p ${harbor_p} ${harbor} || ! echo '登录失败' || exit

d=$(date "+%Y-%m-%d %H:%M:%S")
echo "---------------拉取新版镜像--${tag}---${d}---------------------"
docker pull ${harbor}/${project_namespace}/${project_name}:${tag}

d1=$(date "+%Y-%m-%d %H:%M:%S")
echo "--------------------停止旧版容器${d1}---------------------"
docker stop $(docker ps -aq --filter name=${project_name})
docker rm $(docker ps -aq --filter name=${project_name})

echo "--------------------删除旧版镜像-----------------"
if [ "${project_environment}" == "Production" ]; then
  docker images -a | awk '/'$project_name'/&&$2!="'$tag'"&&$2 ~ /p/ {print $1,$2,$3}'
  docker image rmi $(docker images -a | awk '/'$project_name'/&&$2!="'$tag'"&&$2 ~ /p/ {print $3}')
fi
if [ "${project_environment}" == "Development" ]; then
  docker images -a | awk '/'$project_name'/&&$2!="'$tag'"&&$2 ~ /t/ {print $1,$2,$3}'
  docker image rmi $(docker images -a | awk '/'$project_name'/&&$2!="'$tag'"&&$2 ~ /t/ {print $3}')
fi

echo "-----------------部署新镜像---${project_name}---${tag}------------"
# 项目部署
# https
#-e ASPNETCORE_URLS='https://+;http://+' \
#-e ASPNETCORE_Kestrel__Certificates__Default__Password=${httpspwd} \
#-e ASPNETCORE_Kestrel__Certificates__Default__Path=${httpspath} \
docker run -d \
  --name ${project_name} \
  --restart=always \
  -p ${prot}:80 \
  -e "VUE_APP_TITLE=C2M 智尚工场" \
  #-e "VUE_APP_API=https://your-api.com/api/" \
  -e ASPNETCORE_ENVIRONMENT=${project_environment} \
  -v /etc/localtime:/etc/localtime \
  ${harbor}/${project_namespace}/${project_name}:${tag}
c=$?

if [ $c -ne 0 ]; then
  echo '部署失败'
  docker rmi ${project_name}
  state="1"

fi
finish_time=$(date "+%Y-%m-%d %H:%M:%S")
echo "--------------------脚本完成:${finish_time}------"
#docker ps  -a|awk   '/'$project_name'/ {print $1,$2 }'
chmod a+x ./messages_front.sh
./messages_front.sh "${project_name}" "${tag}" "${state}" "${webhoohrul}" "${titlename}" "${messagetype}" "${committext}"
rm -f deployment_front.sh
rm -f messages_front.sh

exit
