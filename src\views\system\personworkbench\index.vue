<template>
  <d2-container class="personWorkBench">
    <el-tabs type="border-card" v-model="activeName" style="height:99%">
      <el-tab-pane name="detail1">
        <span slot="label">
          客服订单<el-badge :value="detail1" class="item" ></el-badge>
        </span>
        <detail1 :Group="1" ref="detail1" @cuserCount="cuserCount"/>
      </el-tab-pane>
      <el-tab-pane name="detail2">
        <span slot="label">
          技术订单<el-badge :value="detail2" class="item"> </el-badge>
        </span>
        <detail1 :Group="2" ref="detail2" @cuserCount="cuserCount" />
      </el-tab-pane>
      <el-tab-pane name="detailmodelsizecolumn">
        <span slot="label">
          采购单<el-badge :value="detail3" class="item"> </el-badge>
        </span>
        <detail1 :Group="10" ref="detai3" @cuserCount="cuserCount" />
      </el-tab-pane>
      <el-tab-pane name="detailmodelbodylist">
        <span slot="label">
          客服提货<el-badge :value="detail4" class="item"> </el-badge>
        </span>
        <detail1 :Group="12" ref="detail4"  @cuserCount="cuserCount"/>
      </el-tab-pane>

    </el-tabs>
  </d2-container>
</template>

<script>
import Detail1 from './components/detail1.vue'
export default {
  name: 'personWorkBenchDetail1',
  mixins: [],
  components: {
    Detail1
  },
  data () {
    return {
      activeName: 'detail1',
      detail1: 0,
      detail2: 0,
      detail3: 0,
      detail4: 0
    }
  },
  computed: {

  },
  async created () {

  },
  methods: {
    cuserCount ({ type, count }) {
      if (type === 1) {
        this.detail1 = count
      }
      if (type === 2) {
        this.detail2 = count
      }
      if (type === 3) {
        this.detail3 = count
      }
      if (type === 4) {
        this.detail4 = count
      }
    }
  }
}
</script>

<style lang="scss" >
.personWorkBench {
  .el-tabs__content {
    height: 95%;
  }
  .d2-container-full__body {
    overflow-x: hidden !important;
  }
  //处理vxe table 头和数据  上下不一致问题
  .vxe-cell {
    margin: 1px !important;
    // background-color: red;
  }
}
</style>
