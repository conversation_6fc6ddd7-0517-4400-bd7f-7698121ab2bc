<template>
  <d2-container class="mom_modelelemlist_master">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <vxe-select v-model="data.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="item in GroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="genderID">
              <template #default="{ data }">
                <vxe-select v-model="data.genderID" placeholder="性别" clearable>
                  <vxe-option :key="true" value="true" label="男"></vxe-option>
                  <vxe-option :key="false" value="false" label="女"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <!-- <vxe-form-item field="modelElemBaseID"></vxe-form-item> -->
            <vxe-form-item field="modelElemBase">
              <vxe-pulldown ref="xDown1" placement="bottom">
                <template>
                  <vxe-input v-model="searchForm.modelElemBase" placeholder="款式部位" @focus="focusEvent1" @keyup="keyupEvent1" clearable @clear="modelElemBaseEvent"></vxe-input>
                </template>
                <template v-slot:dropdown>
                  <div class="my-dropdown1">
                    <div class="list-item1" v-for="item in ModelElemBaseComboStore" :key="item.value" @click="selectEvent1(item)">
                      <span>{{ item.label }}</span>
                    </div>
                  </div>
                </template>
              </vxe-pulldown>
            </vxe-form-item>
            <vxe-form-item field="modelElemTypeID">
              <template #default="{ data }">
                <vxe-select v-model="data.modelElemTypeID" placeholder="款式类别" clearable>
                  <vxe-option v-for="item in ModelElemTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="cadSeq">
              <template #default="{ data }">
                <vxe-select v-model="data.cadSeq" placeholder="CAD顺序" clearable>
                  <vxe-option :key="true" value="true" label="是"></vxe-option>
                  <vxe-option :key="false" value="false" label="否"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="marketSeq">
              <template #default="{ data }">
                <vxe-select v-model="data.marketSeq" placeholder="市场顺序" clearable>
                  <vxe-option :key="true" value="true" label="是"></vxe-option>
                  <vxe-option :key="false" value="false" label="否"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="shopSeq">
              <template #default="{ data }">
                <vxe-select v-model="data.shopSeq" placeholder="商城顺序" clearable>
                  <vxe-option :key="true" value="true" label="是"></vxe-option>
                  <vxe-option :key="false" value="false" label="否"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="elemSeq">
              <template #default="{ data }">
                <vxe-select v-model="data.elemSeq" placeholder="报表顺序" clearable>
                  <vxe-option :key="true" value="true" label="是"></vxe-option>
                  <vxe-option :key="false" value="false" label="否"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isPlanShow">
              <template #default="{ data }">
                <vxe-select v-model="data.isPlanShow" placeholder="计划显示?" clearable>
                  <vxe-option :key="true" value="true" label="是"></vxe-option>
                  <vxe-option :key="false" value="false" label="否"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isItem">
              <template #default="{ data }">
                <vxe-select v-model="data.isItem" placeholder="属于辅料?" clearable>
                  <vxe-option :key="true" value="true" label="是"></vxe-option>
                  <vxe-option :key="false" value="false" label="否"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isInput">
              <template #default="{ data }">
                <vxe-select v-model="data.isInput" placeholder="内容输入?" clearable>
                  <vxe-option :key="true" value="true" label="是"></vxe-option>
                  <vxe-option :key="false" value="false" label="否"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>

            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table ref='master_table' @cell-dblclick="cellDblClick" id="mom_modelelemlist_master_table" :loading="tableLoading" :custom-config="{storage: true}" :row-class-name="rowClassName" :height="TableHeight" :data="tableData">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>

      <vxe-colgroup title="款式部位" align="center">
        <vxe-table-column field="modelElemBaseGroup" title="款式部位类别" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelElemBaseCode" title="款式部位编码" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelElemBaseName" title="款式部位" sortable width="95px"></vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup title="款式" align="center">
        <vxe-table-column field="groupName" title="款式类别" sortable width="70px"></vxe-table-column>
        <vxe-table-column field="modelElemTypeName" title="款式类别" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="gender" title="性别" sortable width="70px"></vxe-table-column>
        <vxe-table-column field="code" title="款式编码" sortable width="70px"></vxe-table-column>
        <vxe-table-column field="codeName" title="款式名称" sortable width="70px"></vxe-table-column>
      </vxe-colgroup>
      <vxe-table-colgroup title="顺序" align="center">
        <vxe-table-column field="cadSeq" title="CAD顺序" width="70px" sortable :filters="[{label: '非空', value: 'notnull'}, {label: '为空', value: 'null'}]" :filter-method="({value, row, column})=>filterNameMethod(value, row, column,'cadSeq')" :filter-multiple="false">
        </vxe-table-column>
        <vxe-table-column field="marketSeq" title="市场顺序" width="70px" sortable :filters="[{label: '非空', value: 'notnull'}, {label: '为空', value: 'null'}]" :filter-method="({value, row, column})=>filterNameMethod(value, row, column,'marketSeq')" :filter-multiple="false"></vxe-table-column>
        <vxe-table-column field="shopSeq" title="商城排序" width="70px" sortable :filters="[{label: '非空', value: 'notnull'}, {label: '为空', value: 'null'}]" :filter-method="({value, row, column})=>filterNameMethod(value, row, column,'shopSeq')" :filter-multiple="false"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="报表" sortable>
        <vxe-table-column field="elemSeq" title="顺序" width="70px" sortable :filters="[{label: '非空', value: 'notnull'}, {label: '为空', value: 'null'}]" :filter-method="({value, row, column})=>filterNameMethod(value, row, column,'elemSeq')" :filter-multiple="false"></vxe-table-column>
        <vxe-table-column field="reportShowName" title="显示?" sortable width="70px"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="显示" sortable>
        <vxe-table-column field="isPlus" title="深定制?" :formatter="formatBool" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="isPlanShow" title="配色显示?" :formatter="formatBool" sortable width="95px"></vxe-table-column>
        <!-- <vxe-table-column field="isItem" title="属于辅料?" :formatter="formatBool" sortable width="95px"></vxe-table-column> -->
        <vxe-table-column field="isCustomerShow" title="基础显示?" :formatter="formatBool" sortable width="95px"></vxe-table-column>
        <!-- <vxe-table-column field="iscustomerEdit" title="客服编辑?" :formatter="formatBool" sortable width="95px"></vxe-table-column> -->
        <vxe-table-column field="isClientShow" title="客户端显示?" :formatter="formatBool" sortable width="95px"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="类别" field="groupID" span="8">
          <template #default="{ data }">
            <el-select v-model="data.groupID" filterable placeholder="请选择类别" size="mini" clearable>
              <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="工艺" field="modelElemTypeID" span="8" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.modelElemTypeID" filterable placeholder="请选择" size="mini">
              <el-option v-for="item in ModelElemTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式部位" field="modelElemBaseID" span="8" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.modelElemBaseID" filterable placeholder="请选择" size="mini">
              <el-option v-for="item in ModelElemBaseComboStore2" :key="item.value" :label="item.label" :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.otherValue }}</span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="编码" field="code" span="8" :item-render="{name: 'input', attrs: {placeholder: '请输入名称'}}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="8" :item-render="{name: 'input', attrs: {placeholder: '请输入名称'}}"></vxe-form-item>
        <vxe-form-item title="性别" field="genderID" span="8" :item-render="{name: '$select',options: sexList}"></vxe-form-item>
        <vxe-form-item title="CAD顺序" field="cadSeq" span="8" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="市场顺序" field="marketSeq" span="8" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="商城排序" field="shopSeq" span="8" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="报表顺序" field="elemSeq" span="8" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="深定制" field="isPlus" span="8">
          <template #default>
            <vxe-switch v-model="selectRow.isPlus" size="mini" @change="isPlusChange"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="配色显示" field="isPlanShow" span="8" :item-render="{name: '$switch', }"></vxe-form-item>
        <!-- <vxe-form-item title="配色编辑" field="isPlanEdit" span="8" :item-render="{name: '$switch',}"></vxe-form-item>
        <vxe-form-item title="属于辅料" field="isItem" span="8" :item-render="{name: '$switch', }"></vxe-form-item> -->
        <vxe-form-item title="客户端显示" field="isClientShow" span="8" :item-render="{name: '$switch', }"></vxe-form-item>
        <vxe-form-item title="基础显示" field="isCustomerShow" span="8" :item-render="{name: '$switch',}"></vxe-form-item>
        <!-- <vxe-form-item title="客服编辑" field="iscustomerEdit" span="8" :item-render="{name: '$switch', }"></vxe-form-item> -->
        <vxe-form-item title="报表显示" field="reportShow" span="8" :item-render="{name: 'select', options: ReportShowComboStore}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <!-- <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <detail-table :footerCompanyInfo="footerCompanyInfo" :form="selectRow" v-if="drawer" />
    </el-drawer> -->
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import DetailTable from './detail'
import { cloneDeep } from 'lodash'
export default {
  name: 'MomModelelemlistMaster',
  mixins: [masterTableMixins],
  components: {
    // DetailTable
  },
  watch: {
    'selectRow.isCustomerShow'(n, o) {
      if (!n) {
        this.selectRow.isPlus = n
      }
    }
    // 'selectRow.selectRow'(n, o) {
    //   if (n && !this.selectRow.isPlus) {
    //     this.selectRow.selectRow = false;
    //   }
    // },
  },
  data() {
    return {
      searchForm: {
        modelElemBase: null
      },
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        groupID: '',
        genderID: true,
        modelElemTypeID: null,
        modelElemBaseID: null,
        cadSeq: '',
        marketSeq: '',
        elemSeq: '',
        reportShow: 0,
        isPlanShow: 'false',
        isPlanEdit: 'false',
        isCustomerShow: 'false',
        iscustomerEdit: 'false',
        isItem: 'false',
        isClientShow: 'false',
        shopSeq: null,
        isPlus: true

      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }],
        codeName: [{ required: true, message: '请输入编码名称' }],
        // groupID: [{ required: true, message: '请选择类别' }],
        genderID: [{ required: true, message: '请选择性别' }],
        modelElemTypeID: [{ required: true, message: '请选择工艺' }],
        modelElemBaseID: [{ required: true, message: '请选择部位' }]
      },
      formItems: [
        { field: '', title: '', span: 8, itemRender: { name: '$select', options: [] } },
        { field: '', title: '', span: 8, itemRender: { name: '$select', options: [] } },
        { field: 'modelElemBaseID', title: '款式部位', slot: 'modelElemBaseID' },
        { field: 'code', title: '编码', span: 8, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 8, itemRender: { name: '$input' } },
        { field: 'genderID', title: '性别', span: 8, itemRender: { name: '$select', options: [] } },
        { field: 'cadSeq', title: 'CAD顺序', span: 8, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'marketSeq', title: '市场顺序', span: 8, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'elemSeq', title: '报表顺序', span: 8, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'isPlanShow', title: '配色显示', span: 6, itemRender: { name: '$switch' } },
        // { field: 'isPlanEdit', title: '配色编辑', span: 6, itemRender: { name: '$switch' } },
        // { field: 'isItem', title: '属于辅料', span: 6, itemRender: { name: '$switch' } },
        { field: 'isClientShow', title: '客户端显示', span: 6, itemRender: { name: '$switch' } },
        { field: 'isCustomerShow', title: '基础显示', span: 6, itemRender: { name: '$switch' } },
        // { field: 'iscustomerEdit', title: '客服编辑', span: 6, itemRender: { name: '$switch' } },
        { field: 'reportShow', title: '报表显示', span: 6, itemRender: { name: '$select', options: [] } },

        { field: 'isActive', title: '活动', span: 6, itemRender: { name: '$switch' } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$textarea' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modelelemlist/get',
        add: '/mtm/mom_modelelemlist/adds',
        edit: '/mtm/mom_modelelemlist/updates',
        delete: '/mtm/mom_modelelemlist/deletes',
        GroupComboStore: '/mtm/comboQuery/groupComboStoreByQuery',
        ModelElemBaseComboStore: '/mtm/combo/modelElemBaseComboStore',
        ModelElemTypeComboStore: '/mtm/combo/modelElemTypeComboStore'
      },
      footerCompanyInfo: false,
      GroupComboStore: [],
      ModelElemBaseComboStore: [],
      ModelElemBaseComboStore2: [],
      ModelElemListData: [],
      ModelElemTypeComboStore: [],
      sexList: [
        { label: '女', value: false },
        { label: '男', value: true }
      ],
      ReportShowComboStore: [{ value: 0, label: '不显示' }, { value: 1, label: '需要时显示' }, { value: 2, label: '永远显示' }]
    }
  },
  async created() {
    await this.getCombStore()
    this.ModelElemListData = this.ModelElemBaseComboStore
  },
  methods: {
    async getCombStore() {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemBaseComboStore).then(result => {
        this.ModelElemBaseComboStore = result
        this.ModelElemBaseComboStore2 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemTypeComboStore).then(result => {
        this.ModelElemTypeComboStore = result
      })
    },
    sortChange({ column, property, order, sortBy, sortList, $event }) {
      // debugger
    },
    modelElemBaseEvent({ value }) {
      this.searchForm.modelElemBaseID = null
    },
    filterNameMethod(value, row, column, name) {
      switch (value) {
        case 'notnull':
          return row[name] !== null && row[name] !== ''
        //  break
        case 'null':
          return row[name] === null || row[name] === ''
        // break
        default:
          break
      }
    },
    sortNameMethod(row) {
      return row.cadSeq
      // switch (name) {
      //   case '':
      //     break;
      //   case '':
      //     break;
      //   case '':
      //     break;
      // }
    },
    focusEvent1() {
      this.$refs.xDown1.showPanel()
    },
    keyupEvent1() {
      var value = this.searchForm.modelElemBase
      this.ModelElemBaseComboStore = value ? this.ModelElemListData.filter(item => item.label.indexOf(value) > -1) : this.ModelElemListData
    },
    selectEvent1(item) {
      this.searchForm.modelElemBase = item.label
      this.searchForm.modelElemBaseID = item.value
      this.$refs.xDown1.hidePanel().then(() => {
        this.ModelElemBaseComboStore = this.ModelElemListData
      })
    },
    isPlusChange({ value }) {
      if (value && !this.selectRow.isCustomerShow) {
        this.$XModal.message({ message: '基础信息不显示,深定制无法打开', status: 'error' })
        this.selectRow.isPlus = false
      }
    },
    cellDblClick({ row }) {
      this.$emit('nextpage', {
        pagename: 'detail',
        data: cloneDeep(row),
        keepalive: false,
        action: 'edit',
        masterSeach: this.searchForm
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.mom_modelelemlist_master {
  .vxe-select {
    width: 100px;
  }
  .vxe-input {
    width: 100px;
  }
}
.my-dropdown1 {
  height: 200px;
  overflow: auto;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
}
.list-item1:hover {
  background-color: #f5f7fa;
}
</style>
