<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="SysClientAddressTable" ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人"></vxe-table-column>
      <vxe-table-column field="tel" title="电话" width="100px"></vxe-table-column>
      <vxe-table-column field="address" title="地址" width="100px"></vxe-table-column>
      <vxe-table-column field="contactDesc" title="联系人描述" width="100px"></vxe-table-column>
      <vxe-table-column field="fax" title="传真" width="100px"></vxe-table-column>
      <vxe-table-column field="mobile" title="座机" width="100px"></vxe-table-column>
      <vxe-table-column field="email" title="邮件" width="100px"></vxe-table-column>
      <vxe-table-column field="state" title="国家" width="100px"></vxe-table-column>
      <vxe-table-column field="province" title="省" width="100px"></vxe-table-column>
      <vxe-table-column field="city" title="市" width="100px"></vxe-table-column>
      <vxe-table-column field="county" title="县" width="100px"></vxe-table-column>
      <vxe-table-column field="street" title="街道" width="100px"></vxe-table-column>
      <vxe-table-column field="port" title="港口" width="100px"></vxe-table-column>
      <vxe-table-column field="transport" title="运送方式" width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="100px" show-overflow :fixed='tableOptFixed'>
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import { mapState } from 'vuex'
// import masterTableMixins from '@/mixins/master_table_mixins/index'
import detailMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'clientaddress',
  computed: {
    ...mapState('d2admin/user', ['info'])
  },
  mixins: [detailMixins],
  data () {
    return {
      formData: {
        id: null,
        address: null,
        contact: null,
        tel: null,
        contactDesc: null,
        fax: null,
        mobile: null,
        email: null,
        state: null,
        city: null,
        province: null,
        county: null,
        street: null,
        port: null,
        transport: null,
        remark: null,
        clientID: null
        // isActive: true,
      },
      formItems: [
        { field: 'contact', title: '联系人', span: 12, itemRender: { name: '$input' } },
        { field: 'tel', title: '电话', span: 12, itemRender: { name: '$input' } },
        { field: 'address', title: '地址', span: 12, itemRender: { name: '$input' } },
        { field: 'contactDesc', title: '联系人描述', span: 12, itemRender: { name: '$input' } },
        { field: 'fax', title: '传真', span: 12, itemRender: { name: '$input' } },
        { field: 'mobile', title: '座机', span: 12, itemRender: { name: '$input' } },
        { field: 'email', title: '邮件', span: 12, itemRender: { name: '$input' } },
        { field: 'state', title: '国家', span: 12, itemRender: { name: '$input' } },
        { field: 'province', title: '省', span: 12, itemRender: { name: '$input' } },
        { field: 'city', title: '市', span: 12, itemRender: { name: '$input' } },
        { field: 'county', title: '县', span: 12, itemRender: { name: '$input' } },
        { field: 'street', title: '街道', span: 12, itemRender: { name: '$input' } },
        { field: 'port', title: '港口', span: 12, itemRender: { name: '$input' } },
        { field: 'transport', title: '运送方式', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        // { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/bAD_ClientAddress/get',
        add: '/mtm/bAD_ClientAddress/adds',
        edit: '/mtm/bAD_ClientAddress/updates',
        delete: '/mtm/bAD_ClientAddress/deletes'
      },
      formRules: {
        contact: [
          { required: true, message: '联系人必须填写' }
        ],
        address: [
          { required: true, message: '地址必须填写' }
        ],
        tel: [
          { required: true, message: '手机号必须填写' },
          { message: '请填写正确11位手机号', type: 'string', pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/ }
        ]
      }
    }
  },
  created () {
    this.formData.clientID = this.info.clientID
    this.loadData({ id: this.info.clientID })
  },
  methods: {

  }
}
</script>

<style>
</style>
