<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="success" @click="createEvent" v-if="menuAction.allowAdd">生成入库单</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="itemPurchaseState">
              <template #default="{ data }">
                <vxe-select v-model="data.itemPurchaseState" placeholder="状态" clearable>
                  <vxe-option v-for="item in ItemPurchaseStateComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item> <vxe-form-item field="Dates">
              <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItempurchaseMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="supplierItemName" title="供货商" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="invoicesNumber" title="单据号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="itemName" title="物料名称" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemClassIDText" title="物料类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemPurchaseStateText" title="状态" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemGroupIDText" title="物料分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="businessGroupText" title="业务归属" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemSize" title="规格型号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="purchaseQty" title="采购数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="实际到货数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="deliveryDate" title="预计到货实际" :formatter="val=>formatDate(val)" sortable width="100"></vxe-table-column>
      <vxe-table-column field="actualArrivalTime" title="实际到货时间" :formatter="val=>formatDate(val)" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <!-- <vxe-button status="primary" @click="itemPutIn(row)" v-if="menuAction.allowEdit&&row.itemPurchaseState===3">入库</vxe-button> -->
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit&&row.itemPurchaseState!==7"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->

        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="物料名称" field="itemID" span="12">
          <template #default="{ data }">
            <el-select v-model.trim="data.itemID" filterable placeholder="物料" size="mini" remote reserve-keyword :remote-method="remoteMethod" @change="itemchange">
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.name+':'+item.originalItemNo" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="供应商" field="supplierItemID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.supplierItemID" filterable placeholder="供应商" size="mini" remote reserve-keyword :remote-method="remoteMethod4">
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="原始货号" field="originalItemNo" span="12" :item-render="{name: '$input',props:{type:'input'}}"></vxe-form-item>
        <!-- <vxe-form-item title="原始货号" field="originalItemNo" span="12" :item-render="{name: '$input',props:{type:'input',disabled: selectRow.itemPurchaseState===7}}"></vxe-form-item> -->
        <vxe-form-item title="状态" field="itemPurchaseState" span="12" :item-render="{name: '$select', options: ItemPurchaseStateComboStore}">
          <template #default="{ data }">
            <el-select v-model.trim="data.itemPurchaseState" size="mini">
              <el-option v-for="item in ItemPurchaseStateComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="采购数量" field="purchaseQty" span="12" :item-render="{name: '$input', attrs: {type: 'number'},props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="预计到货日期" field="deliveryDate" span="12">
          <template #default>
            <el-date-picker v-model="selectRow.deliveryDate" type="date" placeholder="选择日期" size="mini">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="实际到货数量" field="qty" span="12" :item-render="{name: '$input', attrs: {type: 'number',},props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="实际到货日期" field="actualArrivalTime" span="12" :item-render="{}">
          <template #default>
            <el-date-picker v-model="selectRow.actualArrivalTime" type="date" placeholder="选择日期" size="mini">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="规格" field="itemSize" span="12" :item-render="{name: '$input',props:{type:'input',disabled:true}}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_itempurchase',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      formData: {
        itemID: null,
        itemStockID: null,
        clientID: null,
        supplierItemID: null,
        originalItemNo: null,
        itemPurchaseState: 1,
        purchaseQty: null,
        qty: null,
        deliveryDate: null,
        actualArrivalTime: null,
        itemSize: null,
        remark: '',
        isActive: true
      },
      formRules: {
        itemID: [{ required: true, message: '请出入采购货号' }],
        supplierItemID: [{ required: true, message: '请选择供应商' }],
        purchaseQty: [{ required: true, message: '请输入采购数量' }]
      },
      api: {
        get: '/mtm/war_itempurchase/get',
        add: '/mtm/war_itempurchase/adds',
        edit: '/mtm/war_itempurchase/updates',
        create: '/mtm/war_itempurchase/create',
        itemPutIn: '/mtm/war_itempurchase/itemPutIn',
        delete: '/mtm/war_itempurchase/deletes',
        ItemPurchaseStateComboStore: '/mtm/combo/ItemPurchaseStateComboStore',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
        // ItemMLGroupComboStore: '/mtm/combo/ItemMLGroupComboStore',//面料物料类别
        // ItemFLGroupComboStore: '/mtm/combo/ItemFLGroupComboStore',//辅料物料类别
        // ItemClassComboStore: '/mtm/combo/ItemClassComboStore',//分类

      },
      ItemPurchaseStateComboStore: [],
      clientComboStoreByQuery: [],
      ItemComboStore: [],

      ItemClassComboStore: []

    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemPurchaseStateComboStore).then(result => {
        this.ItemPurchaseStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    itemchange (id) {
      var elem = this.ItemComboStore.GetFirstElement('id', id)
      if (elem !== null) {
        this.selectRow.originalItemNo = elem.originalItemNo
        this.selectRow.itemSize = elem.itemSize
      }
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    itemPutIn (row) {
      var data = cloneDeep(row)
      data.itemPurchaseState = 7
      this.$api.ActionRequest(this.api.itemPutIn, [data]).then(result => {
        this.$XModal.message({ message: '入库成功', status: 'success' })
        this.loadData()
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    createEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要生成的数据', status: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.create, list).then(result => {
        this.$XModal.message({ message: '生成成功', status: 'success' })
        this.goItemstockinvoices(result)
      })
    },
    goItemstockinvoices (number) {
      if (number === null || number === '' || number === undefined) {
        return
      }
      this.$router.push({
        name: 'war_itemstockinvoices',
        params: {
          text: number
        }
      })
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (to.params.text) {
        vm.searchForm.text = to.params.text
        to.params.text = null
      }
      if (to.params.refresh) {
        vm.loadData()
      }
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
