<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="sizeColumnID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.sizeColumnID" filterable remote reserve-keyword placeholder="规格字段" :remote-method="remoteMethod1" size="mini" clearable>
                  <el-option v-for="item in SizeColumnComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID" :item-render="{name: '$select', options: GroupComboStore,props:{placeholder:'品类',clearable:true}}"></vxe-form-item>
            <vxe-form-item field="sorderSizeType" :item-render="{name: '$select', options: SorderSizeTypeComboStore,props:{placeholder:'算法',clearable:true,filterable:true}}"></vxe-form-item>
            <vxe-form-item field="sorderType" :item-render="{name: '$select', options: SorderTypeComboStore,props:{placeholder:'订单类型',clearable:true,filterable:true}}"></vxe-form-item>
            <vxe-form-item field="sizeRuleCheckType" :item-render="{name: '$select', options: SizeRuleCheckTypeComboStore,props:{placeholder:'算法类型',clearable:true,filterable:true}}"></vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomSizerulecheckMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderSizeTypeText" title="算法类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="订单类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sizeRuleCheckTypeText" title="算法类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isCheckNull" title="不可空" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="品类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sizeColumnNameText" title="规格" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sizeColumnNameText1" title="关联规格1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sizeColumnNameText2" title="关联规格2" sortable width="100"></vxe-table-column>
      <vxe-table-column field="bodyListText1" title="关联特体部位1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="bodyListText2" title="关联特体部位2" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value" title="系数" sortable width="100"></vxe-table-column>
      <vxe-table-column field="maxValue" title="最大值" sortable width="100"></vxe-table-column>
      <vxe-table-column field="minValue" title="最小值" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isManualMaxValue1" title="手工1最大值" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isManualMinValue1" title="手工1最小值" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isManualMaxValue2" title="手工2最大值" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isManualMinValue2" title="手工2最小值" sortable width="100"></vxe-table-column>
      <vxe-table-column field="hasSizeElemA" title="体型A身高" :formatter='formatBool1' sortable width="100"></vxe-table-column>
      <vxe-table-column field="hasSizeElemB" title="体型B胸围" :formatter='formatBool1' sortable width="100"></vxe-table-column>
      <vxe-table-column field="hasSizeElemC" title="体型C体型" :formatter='formatBool1' sortable width="100"></vxe-table-column>
      <vxe-table-column field="hasSizeElemD" title="体型D臀围" :formatter='formatBool1' sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent1(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close show-zoom :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="算法" field="sorderSizeType" span="12" :item-render="{name: '$select', options: SorderSizeTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="订单类型" field="sorderType" span="12" :item-render="{name: '$select', options: SorderTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="算法类型" field="sizeRuleCheckType" span="12" :item-render="{name: '$select', options: SizeRuleCheckTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="品类" field="groupID" span="12" :item-render="{name: '$select', options: GroupComboStore,props:{placeholder:'品类',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="规格字段" field="sizeColumnID" span="24" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.sizeColumnID" filterable remote reserve-keyword placeholder="规格字段" :remote-method="remoteMethod1" size="mini" clearable>
              <el-option v-for="item in SizeColumnComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="关联规格1" field="sizeColumnID1" span="12" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.sizeColumnID1" filterable remote reserve-keyword placeholder="关联规格" :remote-method="remoteMethod2" size="mini" clearable>
              <el-option v-for="item in SizeColumnComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="关联规格2" field="sizeColumnID2" span="12" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.sizeColumnID2" filterable remote reserve-keyword placeholder="关联规格" :remote-method="remoteMethod3" size="mini" clearable>
              <el-option v-for="item in SizeColumnComboStoreByQuery2" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="关联特体部位1" field="bodyListID1" span="12" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.bodyListID1" filterable remote reserve-keyword placeholder="特体部位" :remote-method="remoteMethod4" size="mini" style="width:100%" clearable>
              <el-option v-for="item in BodyListComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="关联特体部位2" field="bodyListID2" span="12" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.bodyListID2" filterable remote reserve-keyword placeholder="特体部位" :remote-method="remoteMethod5" size="mini" style="width:100%" clearable>
              <el-option v-for="item in BodyListComboStoreByQuery2" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="系数" field="value" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: '$input', props: { type: 'number',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="最大值" field="maxValue" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="最小值" field="minValue" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="手工1最大值" field="isManualMaxValue1" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="手工1最小值" field="isManualMinValue1" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="手工2最大值" field="isManualMaxValue2" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="手工2最小值" field="isManualMinValue2" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="体型A身高" field="hasSizeElemA" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="体型B胸围" field="hasSizeElemB" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="体型C体型" field="hasSizeElemC" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="体型D臀围" field="hasSizeElemD" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="不可空" field="isCheckNull" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>

        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_sizerulecheck',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        sorderSizeType: null,
        sorderType: null,
        sizeColumnID: null,
        groupID: null,
        sizeRuleCheckType: null
      },
      formData: {
        sorderSizeType: null,
        sorderType: null,
        isCheckNull: false,
        sizeColumnID: null,
        sizeColumnID1: null,
        sizeColumnID2: null,
        bodyListID1: null,
        bodyListID2: null,
        sizeRuleCheckType: null,
        sizeCloumnID1: null,
        value: null,
        maxValue: null,
        minValue: null,
        groupID: null,
        isManualMaxValue1: null,
        isManualMinValue1: null,
        isManualMaxValue2: null,
        isManualMinValue2: null,
        hasSizeElemA: false,
        hasSizeElemB: false,
        hasSizeElemC: false,
        hasSizeElemD: false,
        sort: 1,
        remark: null,
        isActive: true
      },
      formRules: {
        sorderSizeType: [{ required: true, message: '请选择算法' }],
        sorderType: [{ required: true, message: '请选择订单类型' }],
        sizeColumnID: [{ required: true, message: '请选择规格字段' }],
        sizeRuleCheckType: [{ required: true, message: '请选择算法类型' }]
      },
      api: {
        get: '/mtm/mom_sizerulecheck/get',
        add: '/mtm/mom_sizerulecheck/adds',
        edit: '/mtm/mom_sizerulecheck/updates',
        delete: '/mtm/mom_sizerulecheck/deletes',
        GroupComboStore: '/mtm/combo/groupComboStore',
        SorderSizeTypeComboStore: '/mtm/combo/SorderSizeTypeComboStore',
        SizeRuleCheckTypeComboStore: '/mtm/combo/SizeRuleCheckTypeComboStore',
        SizeColumnComboStoreByQuery: '/mtm/comboquery/SizeColumnComboStoreByQuery',
        SorderTypeComboStore: '/mtm/combo/SorderTypeComboStore',
        BodyListComboStoreByQuery: '/mtm/comboQuery/BodyListComboStoreByQuery'
      },
      GroupComboStore: [],
      SorderSizeTypeComboStore: [],
      SorderTypeComboStore: [],
      SizeRuleCheckTypeComboStore: [],
      SizeColumnComboStoreByQuery: [],
      SizeColumnComboStoreByQuery1: [],
      SizeColumnComboStoreByQuery2: [],
      BodyListComboStoreByQuery1: [],
      BodyListComboStoreByQuery2: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SorderSizeTypeComboStore).then(result => {
        this.SorderSizeTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.SizeRuleCheckTypeComboStore).then(result => {
        this.SizeRuleCheckTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SorderTypeComboStore).then(result => {
        this.SorderTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.BodyListComboStoreByQuery).then(result => {
        this.BodyListComboStoreByQuery1 = result
      })
      await this.$api.ActionRequest(this.api.BodyListComboStoreByQuery).then(result => {
        this.BodyListComboStoreByQuery2 = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { text: query }).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { text: query }).then(result => {
        this.SizeColumnComboStoreByQuery1 = result
      })
    },
    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { text: query }).then(result => {
        this.SizeColumnComboStoreByQuery2 = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.BodyListComboStoreByQuery, { text: query }).then(result => {
        this.BodyListComboStoreByQuery1 = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.BodyListComboStoreByQuery, { text: query }).then(result => {
        this.BodyListComboStoreByQuery2 = result
      })
    },
    copyRowEvent1 (row) {
      this.copyRowEvent(row).then(async (res) => {
        await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID1 }).then(result => {
          this.SizeColumnComboStoreByQuery1 = result
        })
        await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID2 }).then(result => {
          this.SizeColumnComboStoreByQuery2 = result
        })
        await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
          this.SizeColumnComboStoreByQuery = result
        })
        await this.$api.ActionRequest(this.api.BodyListComboStoreByQuery, { gid: row.bodyListID1 }).then(result => {
          this.BodyListComboStoreByQuery1 = result
        })
        await this.$api.ActionRequest(this.api.BodyListComboStoreByQuery, { gid: row.bodyListID2 }).then(result => {
          this.BodyListComboStoreByQuery2 = result
        })
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.BodyListComboStoreByQuery, { gid: row.bodyListID1 }).then(result => {
        this.BodyListComboStoreByQuery1 = result
      })
      await this.$api.ActionRequest(this.api.BodyListComboStoreByQuery, { gid: row.bodyListID2 }).then(result => {
        this.BodyListComboStoreByQuery2 = result
      })
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID2 }).then(result => {
        this.SizeColumnComboStoreByQuery2 = result
      })
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID1 }).then(result => {
        this.SizeColumnComboStoreByQuery1 = result
      })
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
        this.SizeColumnComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
