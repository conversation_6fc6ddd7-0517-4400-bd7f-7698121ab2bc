<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="ImportSorderDetailModelElemId" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.ImportSorderDetailModelElemId" filterable remote reserve-keyword placeholder="导入款式绑定" :remote-method="remoteMethod" size="mini" clearable>
                  <el-option v-for="item in ImportSorderDetailModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <!-- <vxe-form-item field="modelElemListID" :item-render="{name: '$select', options: ModelElemListComboStoreByQuery,props:{placeholder:'Type',clearable:true,filterable:true,filterMethod:remoteMethod}}" /> -->
            <vxe-form-item field="configurationType" :item-render="{name: '$select', options: SuitSupplyOptionConfigurationTypeComboStore,props:{placeholder:'Type',clearable:true}}" />
            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='OdmSuitsupplyoptionMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60" />
      <vxe-table-column field="configurationTypeText" title="ConfigurationTypeText" sortable width="100" />
      <vxe-table-column field="oldCode" title="OldCode" sortable width="100" />
      <vxe-table-column field="code" title="code" sortable width="100" />
      <vxe-table-column field="name" title="Name" sortable width="100" />
      <vxe-table-column field="description" title="Description" sortable width="100" />
      <vxe-table-column field="importSorderModelElemCode" title="对应编码" sortable width="100" />
      <vxe-table-column field="importSorderModelElemCodeName" title="对应名称" sortable width="100" />
      <vxe-table-column field="remark" title="备注" sortable width="100" />
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100" />
      <vxe-table-column field="createBy" title="创建人" sortable width="100px" />
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100" />
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row,editEventThen)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row,[],copyRowEventThen)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="configurationType" field="configurationType" span="12" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.configurationType" filterable placeholder="请选择" size="mini" clearable>
              <el-option v-for="item in SuitSupplyOptionConfigurationTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>

        </vxe-form-item>
        <vxe-form-item title="oldCode" field="oldCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="code" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="name" field="name" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="description" field="description" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <!-- <vxe-form-item title="name" field="name" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="name" field="name" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <!-- <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', props: { type: 'float',clearable:true}}"></vxe-form-item> -->
        <vxe-form-item title="绑定明细" field="importSorderModelElemId" span="24" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.importSorderModelElemId" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="绑定明细" clearable :remote-method="remoteMethod">
              <el-option v-for="item in ImportSorderDetailModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'odm_suitsupplyoption',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        configurationType: null
      },
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }]
      },
      api: {
        get: '/mtm/odm_suitsupplyoption/get',
        add: '/mtm/odm_suitsupplyoption/adds',
        edit: '/mtm/odm_suitsupplyoption/updates',
        delete: '/mtm/odm_suitsupplyoption/deletes',
        SuitSupplyOptionConfigurationTypeComboStore: '/mtm/combo/SuitSupplyOptionConfigurationTypeComboStore',
        ImportSorderDetailModelElemComboStoreByQuery: '/mtm/comboQuery/ImportSorderDetailModelElemComboStoreByQuery'
      },
      SuitSupplyOptionConfigurationTypeComboStore: [],
      ImportSorderDetailModelElemComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SuitSupplyOptionConfigurationTypeComboStore).then(result => {
        this.SuitSupplyOptionConfigurationTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ImportSorderDetailModelElemComboStoreByQuery).then(result => {
        this.ImportSorderDetailModelElemComboStoreByQuery = result
      })
    },
    remoteMethod (query, gid) {
      return new Promise(resolve => {
        this.$api.ActionRequest(this.api.ImportSorderDetailModelElemComboStoreByQuery, { text: query, gid: gid }).then(result => {
          this.ImportSorderDetailModelElemComboStoreByQuery = result
          return resolve(true)
        })
      })
    },
    copyRowEventThen (row) {
      this.remoteMethod(null, row.importSorderModelElemId).then(res => { this.showEdit = true })
    },
    editEventThen (row) {
      this.remoteMethod(null, row.importSorderModelElemId).then(res => { this.showEdit = true })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
