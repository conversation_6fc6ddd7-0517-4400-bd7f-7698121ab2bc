<template>
  <d2-container class="model">
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="danger" @click="deletesEvent" v-if="menuAction.allowDelete">删除</vxe-button>
          <vxe-button status="success" @click="createModelDesignEvent" v-if="menuAction.allowEdit">生成款式上下级关系</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">

            <vxe-form-item field="businessSubType">
              <template #default="{ data }">
                <el-select v-model="data.businessSubType" filterable placeholder="版型分类" size="mini" clearable class="businessSubType">
                  <el-option v-for="item in ModelBusinessSubTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelGroupID">
              <template #default="{ data }">
                <el-select v-model="data.modelGroupID" filterable placeholder="版型系列" size="mini" clearable>
                  <el-option v-for="item in ModelGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelBaseID">
              <template #default="{ data }">
                <el-select v-model="data.modelBaseID" filterable placeholder="基本版型图片" size="mini" clearable>
                  <el-option v-for="item in ModelBaseComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" placeholder="类别" clearable size="mini" style="width:110px">
                  <el-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="genderID">
              <template #default="{ data }">
                <el-select v-model="data.genderID" filterable placeholder="性别" clearable size="mini" style="width:70px">
                  <el-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isModelDesign">
              <template #default="{ data }">
                <el-select v-model="data.isModelDesign" filterable placeholder="层级" clearable size="mini" style="width:70px">
                  <el-option v-for="num in boolList" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sizeListID">
              <template #default="{ data }">
                <el-select v-model="data.sizeListID" filterable placeholder="规格单" clearable size="mini" class="sizeListID">
                  <el-option v-for="num in SizeListComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sewBaseID">
              <template #default="{ data }">
                <el-select v-model="data.sewBaseID" filterable placeholder="缝份类别" clearable size="mini" class="sewBaseID">
                  <el-option v-for="num in SewBaseComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isModelDesignByElemListDetail">
              <template #default="{ data }">
                <el-select v-model="data.isModelDesignByElemListDetail" filterable placeholder="款式上下级关系" clearable size="mini" class="sewBaseID">
                  <el-option v-for="num in boolList" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="designNo">
              <vxe-input v-model.trim="searchForm.designNo" placeholder="设计号" suffix-icon="fa fa-search" clearable class="designNo"></vxe-input>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModel_master_table' ref='master_table' :loading="tableLoading" @cell-dblclick="cellDblClick" :height="TableHeight" :data="tableData" :row-class-name="rowClassName" highlight-current-column :custom-config="{storage: true}" :checkbox-config="{checkRowKeys: defaultSelecteRows, highlight: true}">
      <vxe-table-column type="seq" width="40" title="#"></vxe-table-column>
      <vxe-table-column field="id" title="ID" :visible="false"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="businessSubTypeText" title="业务类型" sortable width="100px">
        <template v-slot="{ row }">
          <template v-if="row.businessSubType===0">
            <el-tag type="success">{{row.businessSubTypeText}}</el-tag>
          </template>
          <template v-else>
            <el-tag type="warning">{{row.businessSubTypeText}}</el-tag>
          </template>
        </template>
      </vxe-table-column>
      <vxe-table-column field="modelGroupText" title="版型系列" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelBaseText" title="基础版型图片" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="genderText" title="性别" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="issueDate" title="日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="code" title="版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="designNo" title="设计号" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="shortName" title="版型简称" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="codeName" title="款式描述" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="modelName" title="款式描述" sortable ></vxe-table-column> -->
      <!-- <vxe-table-column field="codeName" title="负责人" sortable ></vxe-table-column> -->
      <vxe-table-column field="sizeListText" title="规格单" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sewBaseText" title="缝份列表" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isShopDefault" title="商城默认版" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isModelDesignByElemListDetail" title="款式上下级" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isClientShow" title="客户端显示？" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="maxEase" title="最大加放量" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="minEase" title="最小加放量" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="prefix" title="排料前缀" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isRuleSize" title="支持算法" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="originalModelCode" title="来源版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="originalModelName" title="来源版型名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemName" title="物料名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemOriginalItemNo" title="物料原始货号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="soderNumber" title="来源订单" sortable width="100px">
        <template v-slot="{ row }">
          <template v-if="row.soderNumber!=null">
            <vxe-button type="text" status="success" icon="fa vxe-icon--print" @click="printEvent(row.sorderID)">{{row.soderNumber}}</vxe-button>
          </template>
        </template>
      </vxe-table-column>
      <!-- <vxe-table-column field="originalModelName" title="来源版型" sortable ></vxe-table-column> -->
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="120" :fixed='tableOptFixed' v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-files-o" @click="deepCopyEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" :height="selectRow.businessSubType===0?500:850" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="版型编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="版型描述" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>

        <vxe-form-item title="版型分类" field="businessSubType" span="12">
          <template #default="{ data }">
            <el-select v-model="data.businessSubType" filterable placeholder="请选择" size="mini">
              <el-option v-for="item in ModelBusinessSubTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="基础版型图片" field="modelBaseID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelBaseID" filterable placeholder="基础版型" size="mini">
              <el-option v-for="item in ModelBaseComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="类别" field="groupID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.groupID" filterable placeholder="类别" size="mini">
              <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="版型系列" field="modelGroupID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelGroupID" filterable placeholder="版型系列" size="mini">
              <el-option v-for="item in ModelGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="性别" field="genderID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.genderID" filterable placeholder="性别" size="mini">
              <el-option v-for="item in sexList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户端显示" field="isClientShow" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="缝份列表" field="sewBaseID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.sewBaseID" filterable placeholder="缝份列表" size="mini" clearable>
              <el-option v-for="item in SewBaseComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="规格单" field="sizeListID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.sizeListID" filterable placeholder="规格单" size="mini" clearable>
              <el-option v-for="item in SizeListComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料信息" v-if="selectRow.businessSubType!==0&&selectRow.businessSubType!==undefined&&selectRow.businessSubType!==null" field="itemID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.itemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="面料信息" clearable :remote-method="remoteMethod5">
              <el-option v-for="item in ItemComboStoreByQuery" :key="item.value" :label="item.code+'【'+item.label+'/'+item.name+'】'+'规格:'+item.itemSize" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="设计号" v-if="selectRow.businessSubType!==0&&selectRow.businessSubType!==undefined&&selectRow.businessSubType!==null" field="designNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <!-- <vxe-form-item title="简称" field="shortName" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <!-- <vxe-form-item title="日期" field="issueDate" span="12" :item-render=" { name: '$input', props: { type: 'date', placeholder: '请选择日期' } }"></vxe-form-item> -->

        <vxe-form-item title="最小加放量" field="minEase" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="最大加放量" field="maxEase" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>

        <vxe-form-item title="排料图前缀" field="prefix" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="支持算法" field="isRuleSize" span="12" :item-render="{name: '$switch'}"></vxe-form-item>

        <vxe-form-item title="商城默认版型" field="isShopDefault" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
        <vxe-form-item v-if="selectRow.businessSubType!==0&&selectRow.businessSubType!==undefined&&selectRow.businessSubType!==null" title="选择订单版型" span="24">
          <template #default>
            <select-order-model :getModel="getmodel" :model='selectRow' ref="selectordermodel" />
          </template>
        </vxe-form-item>

      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import SelectOrderModel from './components/selectordermodel'
import { cloneDeep, isEmpty } from 'lodash'
export default {
  name: 'MomModelMaster',
  mixins: [masterTableMixins],
  components: {
    SelectOrderModel
  },
  data () {
    return {
      isAutoLoding: false,
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        businessSubType: null,
        modelBaseID: null,
        groupID: null,
        genderID: true,
        // issueDate: null,
        sizeListID: null,
        sewBaseID: null,
        isClientShow: false,
        maxEase: null,
        minEase: null,
        prefix: null,
        isRuleSize: false,
        itemID: null,
        originalModelID: null,
        soderDetailModelSource: null,
        designNo: null

      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 26, message: '长度在 3 到 26 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 30, message: '长度在 2 到 30 个字符' }],
        businessSubType: [{ required: true, message: '请选择业务类型' }],
        modelBaseID: [{ required: true, message: '请选择基础版型' }],
        modelGroupID: [{ required: true, message: '请选择版型系列' }]
      },
      formItems: [
        { field: 'code', title: '版型编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '款式描述', span: 12, itemRender: { name: '$input' } },

        { field: 'businessSubType', title: '业务类型', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'modelBaseID', title: '基础版型', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'groupID', title: '类别', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'modelGroupID', title: '版型系列', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'genderID', title: '性别', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'sewBaseID', title: '缝份列表', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'sizeListID', title: '规格单', span: 12, itemRender: { name: '$select', option: [] } },

        { field: 'shortName', title: '简称', span: 12, itemRender: { name: '$input' } },
        { field: 'issueDate', title: '日期', span: 12, itemRender: { name: '$input', props: { type: 'date', placeholder: '请选择日期' } } },
        { field: 'minEase', title: '最小加放量', span: 12, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'maxEase', title: '最大加放量', span: 12, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'prefix', title: '排料图前缀', span: 12, itemRender: { name: '$input' } },
        { field: 'isRuleSize', title: '支持算法', span: 12, itemRender: { name: '$switch' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/mom_model/get',
        add: '/mtm/mom_model/adds',
        edit: '/mtm/mom_model/updates',
        delete: '/mtm/mom_model/deletes',
        deepclone: '/mtm/mom_model/deepClone',
        modelBaseComboStore: '/mtm/combo/modelBaseComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        SizeListComboStore: '/mtm/combo/sizeListComboStore',
        SewBaseComboStore: '/mtm/combo/sewBaseComboStore',
        ModelGroupComboStore: '/mtm/combo/ModelGroupComboStore',
        ModelBusinessSubTypeComboStore: '/mtm/combo/ModelBusinessSubTypeComboStore',
        ItemComboStoreByQuery: '/mtm/comboQuery/ItemComboStoreByQuery',
        CreateDetails: '/mtm/mom_modeldesignbyelemlistdetail/CreateDetails'
      },
      ModelBaseComboStore: [],
      ModelGroupComboStore: [],
      GroupComboStore: [],
      SizeListComboStore: [],
      SewBaseComboStore: [],
      ItemComboStoreByQuery: [],
      ModelBusinessSubTypeComboStore: '',
      sorderModelData: [],
      searchSorderModelForm: {
        text: ''
      }
    }
  },
  props: {
    masterSeach: {
      type: Object
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'businessSubType').itemRender.options = this.ModelBusinessSubTypeComboStore
    // this.$utils.find(this.formItems, item => item.field === 'modelBaseID').itemRender.options = this.ModelBaseComboStore
    // this.$utils.find(this.formItems, item => item.field === 'genderID').itemRender.options = this.sexList
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
    // this.$utils.find(this.formItems, item => item.field === 'groupID').itemRender.options = this.GroupComboStore
    // this.$utils.find(this.formItems, item => item.field === 'sizeListID').itemRender.options = this.SizeListComboStore
    if (this.masterSeach != null) {
      this.searchForm = Object.assign(this.searchForm, this.masterSeach)
    }
    this.loadData()
  },
  methods: {
    // 数据加载
    async loadData () {
      return new Promise((resolve, reject) => {
        this.tableLoading = true
        this.$api.ActionRequest(this.api.get, this.searchForm).then(result => {
          this.tableData = result.items
          this.searchForm.totalCount = result.totalCount
          this.tableLoading = false
          resolve({ data: result.items })
        })
        // this.tableLoading = false
      })
    },
    createModelDesignEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length === 0) {
        this.$XModal.message({ message: '请勾选要生成的版型', status: 'error' })
        return
      }
      if (rows.length > 100) {
        this.$XModal.message({ message: '最大只能选择50条数据', status: 'error' })
        return
      }
      var data = rows.map(item => { return { modelID: item.id } })
      this.$api.ActionRequest(this.api.CreateDetails, data).then(result => {
        this.$XModal.message({ message: '生成成功', status: 'success' })
        this.loadData()
      })
    },
    printEvent (sorderid) {
      var url = this.mtmpai.replace('/api/', '')
      window.open(`${url}/fs/print/sorder/pdf?num=${sorderid}`, '_blank')
    },
    async getmodel (data) {
      await this.$api.ActionRequest(this.api.get, { id: data.modelID }).then(result => {
        if (result.items.length > 0) {
          var dto = cloneDeep(result.items[0])
          dto.businessSubType = this.selectRow.businessSubType
          dto.code = this.selectRow.code
          dto.codeName = this.selectRow.codeName
          dto.id = this.selectRow.id
          this.selectRow = dto
          this.selectRow.itemID = data.itemID
          this.selectRow.originalModelID = data.modelID
          this.selectRow.soderDetailModelSource = data.sorderDetailModelID
          this.itemComBomGet(null, data.itemID)
        }
      })
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.modelBaseComboStore).then(result => {
        this.ModelBaseComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SizeListComboStore).then(result => {
        this.SizeListComboStore = result
      })
      await this.$api.ActionRequest(this.api.SewBaseComboStore).then(result => {
        this.SewBaseComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelBusinessSubTypeComboStore).then(result => {
        this.ModelBusinessSubTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelGroupComboStore).then(result => {
        this.ModelGroupComboStore = result
      })
    },
    remoteMethod5 (query) {
      this.itemComBomGet(query)
    },
    itemComBomGet (text, gid = null) {
      this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { text: text, gid: gid, itemClassID: 2 }).then(result => {
        this.ItemComboStoreByQuery = result
      })
    },
    // 编辑
    editEvent (row) {
      this.selectRow = cloneDeep(row)
      this.itemComBomGet(null, row.itemID)
      this.showEdit = true
    },
    // 提交
    submitEvent () {
      if (this.selectRow.businessSubType !== 0) {
        // //固化版选择
        // var selectordermodel = this.$refs.selectordermodel.selectRow
        // if (selectordermodel === null ||(selectordermodel.sorderDetailModelID === undefined || selectordermodel.itemID === null)) {
        //   this.$XModal.message({ message: '请选择历史订单版型数据', status: 'error' })
        //   return
        // }
      } else {
        this.selectRow.itemID = null
        // this.selectRow.originalModelID = null
        // this.selectRow.soderDetailModelSource = null
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (isEmpty(this.selectRow.id) || this.selectRow.id === undefined || this.selectRow.id === null) {
        this.$api.ActionRequest(this.api.add, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '新增成功', status: 'success' })
          this.loadData()
          this.showEdit = false
          loading.close()
        }).catch(() => {
          loading.close()
        })
      } else {
        this.$api.ActionRequest(this.api.edit, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '保存成功', status: 'success' })
          this.loadData()
          this.showEdit = false
          loading.close()
        }).catch(() => {
          loading.close()
        })
      }
    },
    cellDblClick ({ row }) {
      this.$emit('nextpage', {
        pagename: 'detail',
        data: cloneDeep(row),
        keepalive: false,
        action: 'edit',
        masterSeach: this.searchForm
      })
    }
  }
}
</script>

<style lang="scss" >
.model {
  .businessSubType {
    .el-input > input {
      width: 85px !important;
    }
  }
  .sizeListID {
    .el-input > input {
      width: 100px !important;
    }
  }
  .sewBaseID {
    .el-input > input {
      width: 100px !important;
    }
  }
  .designNo {
    width: 100px !important;
  }
}
</style>
