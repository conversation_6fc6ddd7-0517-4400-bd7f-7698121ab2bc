<template>
  <el-dropdown size="small" class="d2-mr">
    <span class="btn-text">{{info.name ? `你好 ${info.name}` : '未登录'}}</span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item @click.native="logOff">
        <d2-icon name="power-off" class="d2-mr-5" />退出
      </el-dropdown-item>
      <el-dropdown-item @click.native="goToUserInfo">

        <router-link :to="{name:'userinfo'}" class="userinfo">
          <d2-icon name="user-o" class="d2-mr-5" />个人中心
        </router-link>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { mapState, mapActions } from 'vuex'
export default {
  computed: {
    ...mapState('d2admin/user', ['info'])

  },
  methods: {
    ...mapActions('d2admin/page', [
      'closeAll'
    ]),
    ...mapActions('d2admin/account', ['logout']),
    /**
     * @description 登出
     */
    async logOff () {
      await this.logout({ confirm: true })
      // await this.closeAll()
    },
    goToUserInfo () { }
  }
}
</script>
<style lang="scss" scoped>
.userinfo {
  font-weight: 500;
  font-size: 14px;
  color: #606266;
  text-decoration: none;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  -webkit-user-select: none;
  -moz-user-focus: none;
  -moz-user-select: none;
  a,
  a:hover,
  a:active,
  a:visited,
  a:link,
  a:focus {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

    -webkit-tap-highlight-color: transparent;

    outline: none;

    background: none;

    text-decoration: none;
    -webkit-tap-highlight-color: #fff;
  }
}
</style>
