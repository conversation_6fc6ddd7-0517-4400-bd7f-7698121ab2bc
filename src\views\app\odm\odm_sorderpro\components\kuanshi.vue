<template>
  <div class="kuanshigongyi">
    <span class="kuanshispan">{{kuanshiData.modelElemListCode+":"+kuanshiData.modelElemListName+(kuanshiData.isPlanShow?"":"")}}</span>
    <el-select :class="elemIsReuired(kuanshiData,'elem')" v-model="kuanshiData.modelElemID" placeholder="请选择" @change="ModelElemChange(kuanshiData.modelElemID,kuanshiData.modelElem,kuanshiData)" filterable clearable slot="reference" size="mini" :disabled="EditState">
      <template>
        <div class="div-table">
          <div class="table-body">
            <div class="table-content">
              <el-option :disabled="true" :key="0" :value="0">
                <div class="table-tr">
                  <div class="table-td table-td1">
                    <span>款式明细</span>
                  </div>
                  <div class="table-td table-td2">
                    <span>货号</span>
                  </div>
                  <!-- <div class="table-td table-td3">
                    <span>单价</span>
                  </div> -->
                  <div class="table-td table-td4">
                    <span>数量</span>
                  </div>
                </div>
              </el-option>
              <el-option v-for="(item,itemindex) in kuanshiData.modelElem" :key="itemindex+item.modelElemID" :value="item.modelElemID" :label="item.modelElemCode+(item.modelElemName==null?'':':'+item.modelElemName)" :style="defaultStyle(kuanshiData.modelElemID,kuanshiData.modelElem)">
                <div class="table-tr">
                  <template v-if="item.imagePath!==null&&item.imagePath!==''">
                    <el-popover placement="right-end" :title="item.modelElemCode+':'+item.modelElemName" width="400" trigger="hover">
                      <el-image style="width: 400px; height: 400px" :src="item.imagePath" fit="scale-down"></el-image>
                      <div class="table-td table-td1" slot="reference">
                        <span>{{ item.modelElemCode }}:{{ item.modelElemName }}</span><i v-if="item.imagePath!==null&&item.imagePath!==''" style="red" class="el-icon-picture-outline"></i>
                      </div>
                    </el-popover>
                  </template>
                  <template v-else>
                    <el-tooltip :content="item.modelElemCode+':'+item.modelElemName" placement="top-start">
                      <div class="table-td table-td1">
                        <span>{{ item.modelElemCode }}:{{ item.modelElemName }}</span>
                      </div>
                    </el-tooltip>
                  </template>

                  <el-tooltip :content="item.itemText" placement="top" v-if="item.itemText!=null&&item.itemText!=''">
                    <div class="table-td table-td2">
                      <span>{{item.itemText}}</span>
                    </div>
                  </el-tooltip>
                  <div class="table-td table-td2" v-else>
                    <span>{{item.itemText}}</span>
                  </div>
                  <!-- <div class="table-td table-td3">
                    <span>{{item.price}}</span>
                  </div> -->
                  <div class="table-td table-td4">
                    <span>{{item.qty}}</span>
                  </div>
                </div>
              </el-option>
            </div>
          </div>
        </div>
      </template>
    </el-select>
     <el-input :class="elemIsReuired(kuanshiData,'input')" v-model="elemItem.input" style="width:120px" placeholder="输入内容" v-if="elemItem.isInput&&elemItem.modelElemID!==null" clearable size="mini" suffix-icon="el-icon-info" :disabled="EditState"></el-input>
    <!-- <el-tag type="warning" v-if="kuanshiData.input!=null&&kuanshiData.input!=''">{{kuanshiData.input}}</el-tag> -->
    <el-tag v-if="fomatFloat(kuanshiData.qty,2)!=0">*{{fomatFloat(kuanshiData.qty,2)}}</el-tag>
  </div>
</template>

<script>
import detailElemMixins from './detailElemCommon'
import { cloneDeep } from 'lodash'
import sorderEditState from '../sordereditstate'
export default {
  // 款式组件
  name: 'KuanShiCmp',
  mixins: [detailElemMixins, sorderEditState],
  //   watch: {
  //     elemItem: {
  //       deep: true,
  //       handler: (newVal, oldVal) => {
  //         console.log(this.kuanshiData)
  //         this.kuanshiData = cloneDeep(newVal)
  //       }
  //     }
  //   },
  data () {
    return {
      kuanshiData: {}
    }
  },
  watch: {
    elemItem: {
      deep: true,
      handler (newval, oldVal) {
        this.kuanshiData = cloneDeep(newval)
      }
    }
  },
  created () {
    this.kuanshiData = cloneDeep(this.elemItem)
  }
}
</script>

<style>
</style>
