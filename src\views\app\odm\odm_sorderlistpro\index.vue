<template>
  <d2-container class="sorderlist">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">快速下单</vxe-button>
          <!-- <el-button>打印</el-button> -->
          <el-button type="primary" size="mini" @click="printEvent">预览</el-button>
          <!-- <el-button type="success" size="mini" @click="editEvent('odm_sordermodify')">编辑</el-button> -->
          <el-button type="success" size="mini" @click="editEvent('odm_sorderpromodify')" v-if="menuAction.allowEdit">编辑(团装)</el-button>
          <!-- <el-button type="success" size="mini" @click="editEvent('odm_sordermodify')" v-if="menuAction.allowEdit">编辑</el-button> -->
          <!-- <el-button type="info" size="mini" v-if="info.userType!==2">驳回</el-button> -->
          <!-- <el-button type="danger" size="mini" @click="deletesEvent" v-if="menuAction.allowDelete">删除</el-button> -->
          <el-button icon="fa fa-trash-o" type="danger" v-if="menuAction.allowDelete" @click="deletesEvent" size="mini">批量删除</el-button>
          <!-- <el-button type="warning" size="mini" v-if="menuAction.allowAdd" @click="deepCloneEvent">订单复制</el-button> -->
          <!-- <vxe-button status="perfect" @click="CadMake" v-if="menuAction.allowAdd">CAD制版</vxe-button>-->
          <!-- <vxe-button  status="warning" @click="getSorderCadlayout()" v-if="menuAction.allowAdd"></vxe-button> -->
          <el-button type="warning" size="mini" @click="getSorderCadlayout()" v-if="menuAction.allowAdd">智能制版</el-button>
          <el-button type="warning" size="mini" @click="getBomItem()" v-if="menuAction.allowAdd&&info.userType!==2">Bom清单</el-button>
          <el-button type="warning" size="mini" @click="salesBillShow=!salesBillShow" v-if="info.userType!==2">账单明细</el-button>
          <el-button type="danger" size="mini" v-if="info.userType===0" @click="SorderStateChangeEvent()">订单流转</el-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom class="mtmtoolbar">
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="dates" :item-render="{}">
              <template #default>
                <el-date-picker size="mini" v-model="searchForm.dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="stateIDs" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model.trim="data.stateIDs" placeholder="节点" multiple collapse-tags clearable size="mini">
                  <el-option v-for="item in SorderStatusComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <template v-if="!(info.userType==2)">
              <vxe-form-item field="clientID" :item-render="{}">
                <template #default="{ data }">
                  <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                    <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>
            </template>

            <vxe-form-item field="itemID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model.trim="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
                  <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <!-- <vxe-form-item field="modelDesignNo">
              <vxe-input v-model.trim="searchForm.modelDesignNo" placeholder="设计号"  clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item field="customerNumber">
              <vxe-input v-model.trim="searchForm.customerNumber" placeholder="客户订单号"  clearable></vxe-input>
            </vxe-form-item> -->
            <vxe-form-item field="text" :item-render="{}">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomSewbase_master_table' ref='master_table' @cell-click='tableCellClick' :checkbox-config="{highlight:false}" @cell-dblclick="cellDblClick" :stripe="false" :highlight-hover-row="false" :highlight-current-row="false" :highlight-current-column="false" :row-class-name="rowClassName" :loading="tableLoading" :height="TableHeight" :data="tableData" :expand-config="{accordion: true,iconOpen: 'fa fa-minus-square', iconClose: 'fa fa-plus-square'}">
      <!-- <vxe-table-column type="expand" width="35" class-name="expandclass">
        <template v-slot:content="{ row, rowIndex }">
          <detail-list :sorder='row' :rowIndex='rowIndex' />
        </template>
      </vxe-table-column> -->
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column title="操作" width="120" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <!-- <vxe-button type="text" icon="fa fa-file-pdf-o" v-if="menuAction.allowEdit" @click="printEvent(row)"></vxe-button> -->
          <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)"></vxe-button> -->
          <!-- <vxe-button type="text" icon="el-icon-s-flag" ></vxe-button> -->
          <vxe-button type="text" icon="el-icon-location-information" @click="showOrderState(row)"></vxe-button>
          <!-- <el-button  size="mini">进度查看</el-button> -->
        </template>
      </vxe-table-column>
      <vxe-table-column field="statusText" title="订单状态" width="100"></vxe-table-column>
      <vxe-table-column field="code" title="订单号" width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" width="100"></vxe-table-column>

      <vxe-table-column field="contact" title="联系人" width="100"> </vxe-table-column>
      <vxe-table-column field="tel" title="联系电话" width="100"></vxe-table-column>
      <vxe-table-column field="address" title="地址" width="100"></vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="业务类型" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="currencyTypeText" title="货币" sortable width="100"></vxe-table-column>
      <vxe-table-column field="exchangeRate" title="汇率" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="clientPerson" title="顾客" show-overflo width="100"></vxe-table-column> -->
      <vxe-table-column field="itemText" title="面料" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="itemConsumption" title="面料耗量" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="itemConsumptionL" title="里布耗量" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="finalTextureText" title="纹理" show-overflo width="100"></vxe-table-column>
      <!-- <vxe-table-column field="halfFitting" title="半成品试衣" :formatter="formatBool" show-overflo width="100"></vxe-table-column> -->
      <vxe-table-column field="detailInfo" title="明细" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="customerNumber" title="客户订单号" width="100"></vxe-table-column>
      <vxe-table-column field="modelDesignNo" title="设计号" width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创单人" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="isUrgent" title="是否加急" width="100" sortable>
        <template v-slot="{ row }">
          <span v-if="row.isUrgent" style="color:red">加急</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="deliveryDate" title="期望交期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"></vxe-table-column>
      <vxe-table-column field="checkOn" title="客服审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="checkBy" title="客服审核人" width="100"></vxe-table-column>
      <vxe-table-column field="technicianCheckDate" title="技术审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="technicianCheckBy" title="技术审核人" width="100"></vxe-table-column>
      <vxe-table-column field="sorderFromText" title="订单来源" width="100"></vxe-table-column>
      <vxe-table-column field="shipmentText" title="发货状态" width="100"></vxe-table-column>
      <vxe-table-column field="mesProductionPlanStateText" title="MES状态" width="100"></vxe-table-column>

    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="cadLayoutShow" v-if="cadLayoutShow" title="CAD" width="60%" height="70%" resize remember destroy-on-close>
      <cad-lay-out :form="selectRow" :sorderNumber="selectRow.code" ref="cadlayout" />
    </vxe-modal>
    <vxe-modal v-model="bomTableShow" :title="'Bom清单'+selectRow.code" width="65%" height="60%" resize remember destroy-on-close>
      <bom-item :sorder="selectRow" />
    </vxe-modal>
    <vxe-modal v-model="salesBillShow" :title="selectRow.code+'账单明细'" width="65%" height="60%" resize remember destroy-on-close>
      <sorder-sales-bill-detail :sorder="selectRow" />
    </vxe-modal>
    <el-drawer :visible.sync="sorderStateDrawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <sorder-log v-if="sorderStateDrawer" :sorder="selectRow" />
    </el-drawer>
    <vxe-modal v-model="SorderStateChangeShow" title="订单流转" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formSorderChangeRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="注意" span="24"><template #default>
            <el-alert title="请谨慎操作,可能会造成未知风险,操作不可逆。" type="error" effect="dark" :closable="false">
            </el-alert>
          </template></vxe-form-item>
        <vxe-form-item title="订单号" field="code" span="12" :item-render="{name: 'input', attrs:{disabled:true}}"></vxe-form-item>
        <vxe-form-item title="目标状态" field="action" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.statusID" filterable placeholder="目标状态" size="mini">
              <el-option v-for="item in SorderStatusComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="修改原因" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"><template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
          </template></vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import Vue from 'vue'
import masterTableMixins from '@/mixins/master_table_mixins/index'
import pluginExport from '@d2-projects/vue-table-export'
import SorderSalesBillDetail from '@/components/mtm/sordersalesbilldetail/index'
import CadLayOut from '../components/cadlayout.vue'
// import detailList from './listdetail'
import BomItem from '../components/bomitem'
import SorderLog from './sorderlog'
import { mapState } from 'vuex'
Vue.use(pluginExport)
// import {groupBy } from "utils"
export default {
  name: 'odm_sorderlistpro',
  mixins: [masterTableMixins],
  components: {
    // detailList
    SorderLog,
    BomItem,
    SorderSalesBillDetail,
    CadLayOut
  },
  data () {
    return {
      SorderStateChangeShow: false,
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        sequence: 9999
      },
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/odm_sorder/listpro',
        delete: '/mtm/odm_sorder/deletes',
        print: '/mtm/oDM_Sorder/sorderPrint',
        editState: '/mtm/odm_sorder/ChangeSorderState',
        sendCad: '/mtm/oDM_SorderCadLayout/sendcad',
        deepClone: '/mtm/oDM_Sorder/deepClone',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ClientPersonComboStoreByQuery: '/mtm/comboQuery/ClientPersonComboStoreByQuery',
        SorderStatusComboStore: '/mtm/combo/sorderStatusComboStore',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        productionPlanState: '/mes/PRD_ProductionPlan/GetProductionPlanStateToMTM'
      },
      detailForm: {
        sorderId: null

      },
      formSorderChangeRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        remark: [{ required: true, message: '请输入操作原因' }, { min: 2, max: 200, message: '长度在 2 到 200 个字符' }]
      },
      bomTableShow: false,
      salesBillShow: false,
      SorderStatusComboStore: [],
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      ClientPersonComboStoreByQuery: [],
      sorderStateDrawer: false,
      footerCompanyInfo: false,
      cadLayoutShow: false,

      Cad: {
        cadText: null,
        sorderNum: '',
        server: '',
        fileName: ''
      },
      searchForm: {
        stateIDs: []
      },
      isAutoLoding: false

    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
    //   ...mapState('d2admin/page', [
    //     'state'
    //   ])
  },
  async created () {
    await this.getCombStore()
    this.setSorderState()
    this.loadData()
  },
  methods: {
    setSorderState () {
      if (this.info.userType === 2) {

      }
    },
    submitEvent () {
      if (this.info.userType !== 0) {
        this.$XModal.message({ message: '您没有权限操作！', status: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.editState, this.selectRow).then(result => {
        this.$XModal.message({ message: '修改成功', status: 'success' })
        this.loadData()
        this.SorderStateChangeShow = false
      })
    },
    SorderStateChangeEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能勾选一个订单', status: 'error' })
        return
      }
      this.SorderStateChangeShow = true
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { isNotG: true }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SorderStatusComboStore).then(result => {
        this.SorderStatusComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
    },
    insertEvent () {
      this.$router.push({ name: 'odm_sorderpro' })
    },
    CadMake () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var ids = checks.map(item => { return item.id })
      this.$api.ActionRequest(this.api.sendCad, { sorderIDs: ids }).then(res => {
        console.log(res)
      })
    },
    getBomItem () {
      this.bomTableShow = !this.bomTableShow
    },

    handleClose () {
      this.sorderStateDrawer = false
    },
    showOrderState (value) {
      this.sorderStateDrawer = true
    },
    // 导出
    async tableExport (list, name) {
      // console.log(list)
      var colums = []
      var data = []
      await list.forEach(async item => {
        var obj = this.$utils.clone(item)
        delete obj.fabric
        delete obj.number
        delete obj.select
        delete obj.url
        delete obj.id
        for (let index = 0; index < item.number.length; index++) {
          const element = item.number[index]
          this.$utils.set(obj, element.name, element.val)
        }
        for (let index = 0; index < item.select.length; index++) {
          const element = item.select[index]
          this.$utils.set(obj, element.name, element.val)
        }
        data.push(obj)
      })
      this.$utils.objectEach(data[0], (item, key) => {
        var columnsObj = {}
        this.$utils.set(columnsObj, 'label', key)
        this.$utils.set(columnsObj, 'prop', key)
        colums.push(columnsObj)
      })
      console.log(colums)
      this.$export.excel({
        columns: colums,
        data: data,
        title: name + 'CAD排料图'
        // merges: ['A1', 'BG']
      })
        .then(() => {
          this.$message(name + '导出表格成功')
        })
    },
    getSorderCadlayout () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能查看一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.selectRow = checks[0]
      this.cadLayoutShow = true
    },
    removeEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能删除一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.$XModal.confirm('您确定要删除该数据?').then(type => {
        if (type === 'confirm') {
          if (checks[0].id != null) {
            this.$api.ActionRequest(this.api.delete, checks).then(res => {
              this.$XModal.message({ message: '删除成功！', status: 'success' })
              this.loadData()
            })
          }
        }
      })
    },
    tableCellClick ({ column, row }) {
      if (column && column.type === 'checkbox') {
        return
      } else {
        this.$refs.master_table.clearCheckboxRow()
        this.$refs.master_table.toggleCheckboxRow(row)
      }
      this.selectRow = row
    },
    editEvent (name) {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能编辑一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.gosorder(checks[0].id, checks[0].code, name)
    },
    cellDblClick ({ row }) {
      this.gosorder(row.id, row.code, 'odm_sorderpromodify')
    },
    gosorder (id, orderid, name) {
      this.$router.push({
        name: name,
        params: {
          id: id, sorderid: orderid
        }
      })
    },
    printEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能打印一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var url = this.mtmpai.replace('/api/', '')
      window.open(`${url}/fs/print/sorderpro/pdf?num=${checks[0].id}`, '_blank')
    },
    rowClassName ({ row, rowIndex }) {
      var stateClass = ''
      switch (row.statusID) {
        case 0: // 待定 ClientUndetermined
          stateClass = 'sorderstate-client'
          break
        case 1:// 已确认Confirmed
          stateClass = 'sorderstate-confirmed' //  background-color: #0598e1;
          break
        case 20:// 客服锁定CLock
          stateClass = 'sorderstate-clock'
          break
        case 21:// 客服审核完成 //CChecked
          stateClass = 'sorderstate-cchecked'
          break
        case 22:// 客服驳回 CReject
          stateClass = 'sorderstate-customer'
          break
        case 30:// 技术锁定 //MLock
          stateClass = 'sorderstate-mlock'
          break
        case 31:// 技术审核完成 MChecked
          stateClass = 'sorderstate-MChecked'
          break
        case 32:// 技术驳回 MReject
          stateClass = 'sorderstate-technology'
          break
        case 40:// 计划下单Planed
          stateClass = 'sorderstate-planed'
          break
        case 41:// 计划驳回 PReject
          stateClass = 'sorderstate-preject'
          break
        case 50: // 完成 Finished
          stateClass = 'sorderstate-finished'
          break
        default:
          stateClass = ''
          break
      }
      return stateClass
    },
    // 订单复制
    deepCloneEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能编辑一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.deepClone, checks[0]).then(res => {
        this.loadData()
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { text: query }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (to.params.text) {
        vm.searchForm.text = to.params.text
        to.params.text = null
      }
      if (to.params.refresh) {
        vm.loadData()
      }
    })
  }
}
</script>

<style lang="scss" >
.sorderlist {
  // .mtmtoolbar {
  //   overflow-x: auto;
  //   overflow-y: hidden;
  // }
  // .el-drawer{
  //     overflow-y: auto !important;
  // }
  .expandclass {
    background-color: #e6f7ff;
  }
  .sorderstate-client {
    background-color: #909399;
    color: cornsilk;
  }
  .sorderstate-confirmed {
    background-color: #0598e1;
    color: cornsilk;
  }
  //客服驳回
  .sorderstate-customer {
    background-color: #0da468;
    color: cornsilk;
  }
  //客服锁定CLock
  .sorderstate-clock {
    background-color: #0d97a4;
    color: cornsilk;
  }
  //客服审核完成 //CChecked
  .sorderstate-cchecked {
    background-color: #0da410;
    color: cornsilk;
  }
  //技术 技术驳回
  .sorderstate-technology {
    background-color: #e6a23c;
    color: cornsilk;
  }
  //技术 技术锁定
  .sorderstate-mlock {
    background-color: #a68934;
    color: cornsilk;
  }
  //技术 技术审核完成
  .sorderstate-MChecked {
    background-color: #a47e0d;
    color: cornsilk;
  }
  //计划下单Planed
  .sorderstate-planed {
    background-color: #ea6157;
    color: cornsilk;
  }
  //计划驳回 PReject
  .sorderstate-preject {
    background-color: #de3327;
    color: cornsilk;
  }
  //完成 Finished
  .sorderstate-finished {
    background-color: #0d6aa4;
    color: cornsilk;
  }
}
</style>
