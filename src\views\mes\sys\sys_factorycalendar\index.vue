<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <!-- <vxe-button size="mini" content="生成本年生产日历" @click="createdate(0)">
            <template #dropdowns>
              <vxe-button content="生成本年生产日历(不含周日)" @click="createdate(1)"></vxe-button>
              <vxe-button content="生成本年生产日历(不含周六周日)" @click="createdate(2)"></vxe-button>
            </template>
          </vxe-button>
          <vxe-button size="mini" content="生成下一年生产日历" @click="createdate(3)">
            <template #dropdowns>
              <vxe-button content="生成下一年生产日历(不含周日)" @click="createdate(4)"></vxe-button>
              <vxe-button content="生成下一年生产日历(不含周六周日)" @click="createdate(5)"></vxe-button>
            </template>
          </vxe-button> -->

          <vxe-button size="mini" content="创建生产日历" @click="createCalendar=!createCalendar"></vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">

            <vxe-form-item field="month" :item-render="{}"> <template #default>
                <el-select v-model="searchForm.month" filterable placeholder="月" size="mini" clearable>
                  <el-option v-for="item in months" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="week" :item-render="{}"> <template #default>
                <el-select v-model="searchForm.week" filterable placeholder="星期" size="mini" clearable>
                  <el-option v-for="item in weeks" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="年份/4位数" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='SysFactorycalendarMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="year" title="年份" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="month" title="月份" sortable width="100"></vxe-table-column>
      <vxe-table-column field="day" title="日期" sortable width="100"></vxe-table-column>
      <vxe-table-column field="week" title="星期" sortable width="100" :formatter="val=>formatWeek(val)"></vxe-table-column>
      <vxe-table-column field="date" title="日期" sortable width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')"></vxe-table-column>
      <vxe-table-column field="startTime" title="开始时间" sortable width="100" :formatter="val=>formatDate(val,'HH:mm')"></vxe-table-column>
      <vxe-table-column field="endTime" title="结束时间" sortable width="100" :formatter="val=>formatDate(val,'HH:mm')"></vxe-table-column>
      <vxe-table-column field="workHour" title="工作时长" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="1200" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="日期选择" field="date" span="24" :item-render="{}"> <template #default>
            <el-date-picker v-model="selectRow.date" type="date" placeholder="选择日期" size="mini" :clearable="false">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="开始时间" field="startTime" span="12" :item-render="{}"> <template #default>
            <el-time-picker v-model="selectRow.startTime" placeholder="选择时间" size="mini" :picker-options="{selectableRange: '5:00:00 - 12:0:000'}" :clearable="false"></el-time-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="结束时间" field="endTime" span="12" :item-render="{}"> <template #default>
            <el-time-picker v-model="selectRow.endTime" placeholder="结束时间" size="mini" :picker-options="{selectableRange: '12:00:00 - 23:59:00'}" :clearable="false"></el-time-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="年" field="year" span="12" :item-render="{name: 'input', attrs: {type: 'number', disabled:true}}"></vxe-form-item>
        <vxe-form-item title="月" field="month" span="12" :item-render="{name: 'input', attrs: {type: 'number',disabled:true}}"></vxe-form-item>
        <vxe-form-item title="日" field="day" span="12" :item-render="{name: 'input', attrs: {type: 'number',disabled:true}}"></vxe-form-item>
        <vxe-form-item title="周" field="week" span="12" :item-render="{name: '$select', options:weeks, props: {disabled:true}}"></vxe-form-item>
        <vxe-form-item title="工作时长" field="workHour" span="12" :item-render="{name: 'input', attrs: {type: 'number',disabled:true}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="createCalendar" title="生成日历" width="800" resize destroy-on-close>
      <vxe-form title-align="right" title-width="100">
        <vxe-form-item title="创建" span="24" :item-render="{}"> <template #default>
            <el-radio-group v-model="number" size="medium">
              <el-radio :label="0"><span style="font-size:15px">本年的生产日历</span></el-radio>
              <el-radio :label="1"><span style="font-size:15px">本年的生产日历(不含周日)</span></el-radio>
              <el-radio :label="2"><span style="font-size:15px">本年的生产日历(不含周六周日)</span></el-radio>
              <el-radio :label="3"><span style="font-size:15px">下一年生产日历</span></el-radio>
              <el-radio :label="4"><span style="font-size:15px">下一年生产日历(不含周日)</span></el-radio>
              <el-radio :label="5"><span style="font-size:15px">下一年生产日历(不含周六周日)</span></el-radio>
            </el-radio-group>
          </template>
        </vxe-form-item>
        <vxe-form-item title="选择工作时间段" span="24" :item-render="{}"> <template #default>
            <el-time-picker is-range v-model="worktime" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围" size="mini">
            </el-time-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary" @click="add">确定</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'sys_factorycalendar',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        date: new Date(),
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
        week: '0123456'.charAt(new Date().getDay()),
        startTime: new Date(new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDay(), 8),
        endTime: new Date(new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDay(), 22),
        workHour: 14,
        isActive: true,
        remark: null
      },
      formRules: {
        date: [{ required: true, message: '请选择日期' }],
        startTime: [{ required: true, message: '请选择开始时间' }],
        endTime: [{ required: true, message: '请选择结束时间' }]
      },
      api: {
        get: '/mes/sys_factorycalendar/get',
        add: '/mes/sys_factorycalendar/adds',
        edit: '/mes/sys_factorycalendar/updates',
        delete: '/mes/sys_factorycalendar/deletes',
        createCalendar: '/mes/sys_factorycalendar/CreateCalendar'

      },
      months: [
        { label: '一月', value: '1' },
        { label: '二月', value: '2' },
        { label: '三月', value: '3' },
        { label: '四月', value: '4' },
        { label: '五月', value: '5' },
        { label: '六月', value: '6' },
        { label: '七月', value: '7' },
        { label: '八月', value: '8' },
        { label: '九月', value: '9' },
        { label: '十月', value: '10' },
        { label: '十一月', value: '11' },
        { label: '十二月', value: '12' }
      ],
      weeks: [
        { label: '星期日', value: '0' },
        { label: '星期一', value: '1' },
        { label: '星期二', value: '2' },
        { label: '星期三', value: '3' },
        { label: '星期四', value: '4' },
        { label: '星期五', value: '5' },
        { label: '星期六', value: '6' }
      ],

      footerCompanyInfo: false,
      createCalendar: false,
      number: 0,
      worktime: [new Date(2021, 4, 15, 8), new Date(2021, 4, 15, 22)]
    }
  },
  watch: {
    'selectRow.date': {
      deep: true,
      handler: function (newval, oldval) {
        if (newval !== null) {
          var date = newval
          if (!this.$utils.isDate(newval)) {
            date = this.$utils.toStringDate(newval, 'yyyy/MM/dd HH:mm:ss')
          }
          this.selectRow.year = date.getFullYear()
          this.selectRow.month = date.getMonth() + 1
          this.selectRow.day = date.getDate()
          this.selectRow.week = '0123456'.charAt(date.getDay())
        }
      }
    },
    'selectRow.startTime': {
      deep: true,
      handler: function (newval, oldval) {
        if (newval !== null && this.selectRow.endTime) {
          var date = newval
          if (!this.$utils.isDate(newval)) {
            date = this.$utils.toStringDate(newval, 'yyyy/MM/dd HH:mm:ss')
          }
          if (!this.$utils.isDate(this.selectRow.endTime)) {
            this.selectRow.workHour = this.$utils.toStringDate(this.selectRow.endTime, 'yyyy/MM/dd HH:mm:ss').getHours() - date.getHours()
          } else {
            this.selectRow.workHour = this.selectRow.endTime.getHours() - date.getHours()
          }
        }
      }
    },
    'selectRow.endTime': {
      deep: true,
      handler: function (newval, oldval) {
        if (newval !== null && this.selectRow.endTime) {
          var date = newval
          if (!this.$utils.isDate(newval)) {
            date = this.$utils.toStringDate(newval, 'yyyy/MM/dd HH:mm:ss')
          }
          if (!this.$utils.isDate(this.selectRow.startTime)) {
            this.selectRow.workHour = date.getHours() - this.$utils.toStringDate(this.selectRow.startTime, 'yyyy/MM/dd HH:mm:ss').getHours()
          } else {
            this.selectRow.workHour = date.getHours() - this.selectRow.startTime.getHours()
          }
        }
      }
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    },
    mGetDate () {
      var date = new Date()
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var d = new Date(year, month, 0)
      return d.getDate()
    },
    async createdate (val) {
      this.$XModal.confirm('如果系统中已存在,会重置,确定码？').then(async type => {
        if (type === 'confirm') {
          this.number = val
        } else {
          this.$XModal.message({ message: '取消操作' })
        }
      })
    },
    async add () {
      await this.$api.ActionRequest(this.api.createCalendar, { number: this.number, startTime: this.worktime[0], endTime: this.worktime[1] }).then(result => {
        this.createCalendar = false
        this.loadData()
      })
    },
    formatWeek ({ cellValue }) {
      var str = '星期一'
      switch (cellValue) {
        case 0:
          str = '星期日'
          break
        case 1:
          str = '星期一'
          break
        case 2:
          str = '星期二'
          break
        case 3:
          str = '星期三'
          break
        case 4:
          str = '星期四'
          break
        case 5:
          str = '星期五'
          break
        case 6:
          str = '星期六'
          break
        default:
          break
      }
      return str
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
