<template>
  <el-tooltip effect="dark" :content="tooltipContent" placement="bottom">
    <el-button class="d2-ml-0 d2-mr btn-text can-hover" type="text" @click="handleClick">
      <el-badge v-if="readMessage > 0" :max="99" :value="readMessage" :is-dot="readMessage === 0">
        <d2-icon :name="readMessage === 0 ? 'envelope-o' : 'envelope'" style="font-size: 20px" />
      </el-badge>
      <d2-icon v-else name="envelope" style="font-size: 20px" />
    </el-button>
  </el-tooltip>
</template>

<script>
import { mapState } from 'vuex'
export default {
  naem: 'workbench',
  data () {
    return {
      timer: '',
      tooltipContent: '新任务!',
      readMessage: 1,
      api: {
        get: '/mtm/sym_workbenchdetail/GetRead'
      },
      number: 30
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  created () {
    this.getData()
  },
  mounted () {
    this.timer = setInterval(this.getData, 1000 * this.number)// 毫秒
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  methods: {
    async getData () {
      await this.$api.ActionRequest(this.api.get, { userID: this.info.userid }).then(result => {
        this.readMessage = result
      }).catch(() => {
        clearInterval(this.timer)
        console.log(this.number)
        this.number += this.number
        this.timer = setInterval(this.getData, 1000 * this.number)
      })
    },
    handleClick () {
      this.$router.push({
        name: 'personWorkBench'
      })
    }
  }
}
</script>
