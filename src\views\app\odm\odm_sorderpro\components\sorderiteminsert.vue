<template>
  <vxe-modal v-model="itemMaster.show" title="添加面料" width="600" resize destroy-on-close>
    <vxe-form :data="itemMaster.itemForm" :rules="itemMaster.formRules" title-align="right" title-width="100" @submit="itemSubmitEvent">
      <vxe-form-item title="物料分类" field="itemGroupID" span="12" :item-render="{ name: '$select', options: ItemMLGroupComboStore}"></vxe-form-item>
      <vxe-form-item title="业务归属" field="businessGroup" span="12" :item-render="{ name: '$select', options: BusinessGroupComboStore}"></vxe-form-item>
      <vxe-form-item title="原始货号" field="originalItemNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
      <vxe-form-item title="年份" field="yearNo" span="12" :item-render="{name: 'input',attrs: {type: 'number',}}"></vxe-form-item>
      <vxe-form-item title="纹理(CM)" field="textureGroupID" span="12" :item-render="{ name: '$select', options: ItemTextureMLGroupComboStore}"></vxe-form-item>
      <vxe-form-item title="门幅" field="width" span="12" :item-render="{}"> <vxe-input v-model="itemMaster.itemForm.width" type="float"></vxe-input>
      </vxe-form-item>
      <vxe-form-item title="工艺属性" field="technologyGroupID" span="12" :item-render="{ name: '$select', options: TechnologyGroupComboStore}"></vxe-form-item>
      <vxe-form-item title="面料成分" field="itemComp" span="12" :item-render="{name: 'input'}"></vxe-form-item>
      <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
          <vxe-button type="submit" status="primary">提交</vxe-button>
          <vxe-button type="reset">重置</vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </vxe-modal>
</template>

<script>
export default {
  name: 'soprderiteminsert',
  props: {
    sorderStore: {
      type: Object
    }
  },
  data () {
    return {
      itemMaster: {
        show: false,
        itemForm: {
          id: null,
          code: null,
          itemGroupID: null,
          businessGroup: null,
          originalItemNo: null,
          yearNo: new Date().getFullYear().toString().substring(2, 4),
          technologyGroupID: null,
          textureGroupID: null,
          width: null,
          itemComp: null,
          remark: null,
          isActive: true,
          createByClientID: this.sorderStore.clientID
        },
        formRules: {
          itemGroupID: [{ required: true, message: '请选择物料分类' }],
          businessGroup: [{ required: true, message: '请选择业务归属' }],
          originalItemNo: [{ required: true, message: '请输入原始货号' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
          colorNo: [{ required: true, message: '请输入色号' }, { min: 1, max: 20, message: '长度在 1 到 20 个字符' }],
          yearNo: [{ required: true, message: '年份必填' }],
          textureGroupID: [{ required: true, message: '纹理必须选择' }],
          width: [{ required: true, message: '幅宽必须输入' }]
        }

      },
      api: {
        TechnologyGroupComboStore: '/mtm/combo/TechnologyGroupComboStore',
        ItemMLGroupComboStore: '/mtm/combo/ItemMLGroupComboStore',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore',
        ItemTextureMLGroupComboStore: '/mtm/combo/ItemTextureMLGroupComboStore'
      },
      ItemTextureMLGroupComboStore: [],
      ItemMLGroupComboStore: [],
      TechnologyGroupComboStore: [],
      BusinessGroupComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemTextureMLGroupComboStore).then(result => {
        this.ItemTextureMLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.TechnologyGroupComboStore).then(result => {
        this.TechnologyGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemMLGroupComboStore).then(result => {
        this.ItemMLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
    },
    itemMasterEvent () {
      this.itemMaster.itemForm.originalItemNo = this.sorderForm.itemText
      this.itemMaster.show = !this.itemMaster.show
    },
    async itemSubmitEvent () {
      await this.$api.ActionRequest(this.api.addItem, this.itemMaster.itemForm).then(async result => {
        this.$notify({
          message: '添加成功',
          type: 'success'
        })
        await this.getItembyQuery(this.itemMaster.itemForm.originalItemNo)
        this.ItemtableData.forEach(item => {
          if (item.originalItemNo.indexOf(this.itemMaster.itemForm.originalItemNo) >= 0) {
            this.itemCellClickEvent({ row: item })
          }
        })
        // var first = this.ItemtableData.GetFirstElement("originalItemNo", this.itemMaster.itemForm.originalItemNo)
        // if (first && first !== null) {
        //   console.log(first)
        //   this.itemCellClickEvent({ row: first })
        // }
        this.itemMaster.show = false
        this.$utils.clear(this.itemMaster.itemForm, null)
        this.itemMaster.itemForm.isActive = true
        this.itemMaster.itemForm.yearNo = new Date().getFullYear().toString().substring(2, 4)
      })
    }
  }
}
</script>

<style>
</style>
