
<template>
  <d2-container class="modelelemshowpage">
    <template slot="header" v-if="info.userType===0">
      <!-- <vxe-button type="text" icon="fa fa-cog" size='medium' v-if="info.userType===0" @click="componentsname='productionstationset'"></vxe-button> -->
      <!-- <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button> -->
      <!-- <span>{{ip}}</span> -->
    </template>
    <audio :src="successsrc" controls="controls" ref="sucessaudio" style="display: none;"></audio>
    <audio :src="errorsrc" controls="controls" ref="erroraudio" style="display: none;"></audio>
    <component :is="componentsname" :userinfo='info' :form="form" :ip="ip" :productstationconfigData="productstationconfigData">
      <template slot="scanqty">
        <h2 style="float: left;" @click="detailShow=!detailShow"> 扫描数量: </h2>
        <vxe-button type="text" icon="vxe-icon--question" class="tool-btn" @click="detailShow=!detailShow"></vxe-button>
        <h1>{{scanQty}}</h1>
      </template>
      <template slot="numinput">
        <el-input maxlength="10" v-model="number" :disabled="isDisable" show-word-limit ref="numinputref" class="numinput" placeholder="扫描条形码" @blur="inputblur" @input="inputchange" @keyup.enter.native="submit" type="number">
        </el-input>
      </template>
    </component>
    <vxe-modal v-model="detailShow" :title="'扫描记录'" width="42%" height="50%" resize destroy-on-close mask-closable>
      <production-plan-detail v-if="detailShow" :form="productstationconfigData" />
    </vxe-modal>
    <div class="repairbtn" v-if="form&&form.sorderType===0">
      <el-button type="info" circle @click="repairbtnEvent">原单</el-button>
    </div>
    <el-drawer title="原始订单数据" :visible.sync="drawer" size="80%" destroy-on-close>
      <div style="width:100%;height:100%" v-loading="originalloading" element-loading-background="rgba(0, 0, 0, 0.8)" element-loading-text="数据加载中请稍后！">
        <component :is="componentsname" :userinfo='info' :form="OriginalSorder" :ip="ip" :productstationconfigData="productstationconfigData">
        </component>
      </div>

    </el-drawer>
  </d2-container>
</template>

<script>
import sucessaudio from '@/assets/voice/success.mp3'
import erroraudio from '@/assets/voice/error.mp3'
import notset from './components/notset'
import modelelemshow from './showpage/modelelemshow'
import qualityshow from './showpage/qualityshow'
// import washinglable from './showpage/washinglable'
import productionstationset from './components/productionstationset'
import ProductionPlanDetail from './components/ProductionPlanDetail.vue'
import { mapState } from 'vuex'
export default {
  name: 'productiontechnology', // 生产工艺
  components: {
    notset,
    modelelemshow,
    qualityshow,
    // washinglable, // washinglable
    productionstationset,
    ProductionPlanDetail

  },
  computed: {
    ...mapState('mes/ptm/productiontechnology/index', [
      'productstationconfig'
    ]),
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  watch: {
    productstationconfig: {
      deep: true,
      handler (newval, oldval) {
        this.componentschange()
      }
    }
  },
  data () {
    return {
      scanQty: 0,
      detailShow: false,
      successsrc: sucessaudio,
      errorsrc: erroraudio,
      componentsname: 'notset',
      number: '', // 221100850316
      isDisable: false,
      ip: null,
      productstationconfigData: null,
      api: {
        get: '/mes/pTM_ProductionTechnology/getModelElem',
        getOriginalSorder: '/mes/pTM_ProductionTechnology/GetOriginalSorder',
        getScan: '/mes/pTM_ProductionTechnology/GetScan',
        getip: '/mes/PTM_ProductionTechnologyIPSet/getOrCreate'
      },
      drawer: false,
      form: null,
      originalloading: false,
      OriginalSorder: null
    }
  },
  mounted () {
    this.timer = setInterval(() => {
      var numinput = this.$refs.numinputref
      if (numinput) {
        numinput.focus()
      }
    }, 1000)
    this.ipSet = setInterval(async () => {
      await this.getIP()
    }, 10 * 1000)
  },
  beforeDestroy () {
    clearInterval(this.timer)
    clearInterval(this.ipSet)
  },
  async created () {
    // this.componentschange()
    // this.getUserIP((ip) => {
    //   this.ip = ip
    //   this.getIP(ip)
    // });

    await this.getIP()
  },
  methods: {
    // 1:在chrome 浏览器地址栏中输入：chrome://flags/
    // 2:搜索 #enable-webrtc-hide-local-ips-with-mdns 该配置 并将属性改为 disabled
    // 以上不生效 安装插件 WebRTC Network Limiter 后选择第二项就可以了

    getUserIP (onNewIP) {
      const MyPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection
      const pc = new MyPeerConnection({
        iceServers: []
      })
      const noop = () => {
      }
      const localIPs = {}
      const ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g
      const iterateIP = (ip) => {
        if (!localIPs[ip]) onNewIP(ip)
        localIPs[ip] = true
      }
      pc.createDataChannel('')
      pc.createOffer().then((sdp) => {
        sdp.sdp.split('\n').forEach(function (line) {
          if (line.indexOf('candidate') < 0) return
          line.match(ipRegex).forEach(iterateIP)
        })
        pc.setLocalDescription(sdp, noop, noop)
      }).catch((reason) => {
      })
      pc.onicecandidate = (ice) => {
        if (!ice || !ice.candidate || !ice.candidate.candidate || !ice.candidate.candidate.match(ipRegex)) return
        ice.candidate.candidate.match(ipRegex).forEach(iterateIP)
      }
    },
    async getIP () {
      await this.getUserIP(async (ip) => {
        this.ip = ip
        console.log(`当前电脑IP:${ip}`)
        await this.$api.ActionRequest(this.api.getip, { ip: ip }).then(result => {
          console.log(result)
          if (result) {
            this.productstationconfigData = result
            this.componentsname = result.viewTypeStr
            this.getScanQty()
          } else {
            this.componentsname = 'notset'
          }
        })
      })
    },
    componentschange () {
      if (this.productstationconfig.viewTypeStr === null) {
        this.componentsname = 'notset'
      } else {

      }
    },
    // 回车
    async submit () {
      if (this.input === null) {
        return
      }
      await this.getData()
      this.number = null
    },
    inputchange (val) {
      if (val.length >= 20) {
        this.number = val.substr(0, 20)
      }
    },
    inputblur () {
      this.getinputfocus()
      // console.log('失去焦点')
    },
    getinputfocus () {
      this.$nextTick(() => {
        var numinput = this.$refs.numinputref
        numinput.focus()
      })
    },
    repairbtnEvent () {
      this.drawer = true
      this.getOriginalSorder().then(res => {

      })
    },
    getOriginalSorder () {
      return new Promise((resolve) => {
        this.originalloading = true
        this.OriginalSorder = null
        this.$api.ActionRequest(this.api.getOriginalSorder, { number: this.number, productionStationID: this.productstationconfigData.id, originalSorderID: this.form.sorderID, groupID: this.form.groupID }).then(result => {
          this.OriginalSorder = result
          this.originalloading = false
          return resolve()
        })
      })
    },
    async getData () {
      this.isDisable = true
      await this.$api.ActionRequest(this.api.get, { number: this.number, productionStationID: this.productstationconfigData.id }).then(result => {
        this.voiceplay(true)
        this.form = result
        this.getinputfocus()
        this.isDisable = false
        this.getScanQty()
      }).catch(res => {
        this.voiceplay(false)
        this.isDisable = false
      })
    },
    voiceplay (b) {
      if (b) {
        this.$refs.sucessaudio.play()
      } else {
        this.$refs.erroraudio.play()
      }
    },
    // 获取扫描数量
    async getScanQty () {
      await this.$api.ActionRequest(this.api.getScan, { isToday: true, productionStationID: this.productstationconfigData.id }).then(result => {
        // console.log(result)
        this.scanQty = result.totalCount
      })
    }
  }
}
</script>

<style lang='scss' scope>
html {
  // overflow: hidden;
}
.modelelemshowpage {
  // height: 100%;
  .numinput input::-webkit-outer-spin-button,
  .numinput input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  .repairbtn {
    position: absolute;
    right: 50px;
    top: 20%;
  }
  .sordernum {
    line-height: 50px;
  }
  .d2-container-full__body {
    height: 100%;
  }
  .d2-container-full {
    height: 100%;
  }
  // .d2-container-full {
  //   .d2-container-full__body {
  //     height: calc(100vh);
  //   }
  // }
  .numinput {
    font-size: 20px;
    font-weight: 700;
    .vxe-input {
      height: 61px !important;
      width: 280px !important;

      input: {
        padding: 4px 0px;
        font-size: 30px !important;
        height: 60px !important;
        line-height: 60px !important;
        -ms-line-height: 40px;
        width: 260px !important;
      }
      input[type="number"] {
        -moz-appearance: textfield;
      }
      .el-input {
        width: 300px;
      }
      .el-input__inner {
        font-size: 30px !important;
        font-weight: 500 !important;
        border: 3px solid #dcdfe6;
        width: 260px !important;
      }
      .vxe-input:not(.is--disabled).is--active .vxe-input--inner {
        border: 3px solid #409eff !important;
      }
      .el-input.is-active .el-input__inner,
      .el-input__inner:focus {
        border: 3px solid #409eff;
      }
    }
  }
  .numinput input[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
