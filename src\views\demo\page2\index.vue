<template>

    <transition>
      <keep-alive v-if="keepalive">
        <component :is="activepage" :form="masterfrom" @nextpage="nextpage" />
      </keep-alive>
      <template v-else>
        <component :is="activepage" :form="masterfrom" @nextpage="nextpage" />
      </template>
    </transition>

</template>

<script>
import master from './master.vue'
import detail from './detail.vue'
export default {
  name: 'page2',
  components: {
    master,
    detail
  },
  data () {
    return {
      activepage: 'master',
      keepalive: true,
      masterfrom: null,
      api: {
        combox: 'Factory/getComboxStore'
      }
    }
  },
  methods: {
    nextpage (item) {
      this.activepage = item.pagename
      this.masterfrom = item.data
      if (item.keepalive) {
        this.keepalive = item.keepalive
      } else {
        this.keepalive = false
      }
    }
  }
}
</script>
