export default {
  name: 'tableSetHeightMixins',
  data () {
    return {
      tableHeight: 0// 表格高度
    }
  },
  mounted () {
    window.onresize = () => {
      return (() => {
        this.setTableHeight()
      })()
    }
    this.setTableHeight()
  },
  methods: {
    setTableHeight: function () {
      this.$nextTick(() => {
        var contenttop = document.querySelector('.d2-container-full__body')
        var contenttopclient = contenttop.getBoundingClientRect()
        this.tableHeight = contenttopclient.height - 50
        console.log('表格高度:' + this.tableHeight)
      })
    }

  },
  // 初始化
  created () {
    this.setTableHeight()
  }
}
