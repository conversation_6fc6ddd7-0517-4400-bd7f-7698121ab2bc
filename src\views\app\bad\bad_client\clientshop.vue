<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" @click="insertEvent()" v-if="menuAction.allowEdit">新增</vxe-button>
          <vxe-button status="perfect" @click="saveEvent" v-if="menuAction.allowEdit">保存</vxe-button>
        </template>
        <template v-slot:tools>
          <!-- <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button> -->
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="BadClientShopDetailTable" keep-source ref="clientShopxTable" :edit-rules="validRules" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" :edit-config="{trigger: 'click', selected: true, mode: 'cell',showStatus: true, icon: 'fa fa-pencil'}" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="code" sortable title="店铺编码" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" sortable title="店铺名称" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="contact" sortable title="联系人" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="tel" title="电话" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="countryCN" title="中文国家名称" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="country" title="国家" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="countryCode" title="国家代码" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>

      <vxe-table-column field="stateProvince" title="州省区" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="stateProvinceCode" title="州省区代码" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="city" title="城市" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="stateCode" title="城市代码" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="locationName" title="地区名称" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="shopArea" title="区域" sortable width="100" :edit-render="{name: '$select', options: ShopAreaTypeComboStore}"> </vxe-table-column>
      <vxe-table-column field="zipCode" title="邮编" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="email" title="Email" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="mobile" title="座机" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="address" title="地址" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="address1" title="地址1" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>

      <vxe-table-column title="操作" width="100px" show-overflow v-if="menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
import { unionWith } from 'lodash'
export default {
  name: 'BadClientShop',
  mixins: [detailMixins],
  props: {

  },
  data () {
    return {
      tableRef: 'clientShopxTable',
      api: {
        get: '/mtm/bAD_ClientShop/get',
        add: '/mtm/bAD_ClientShop/adds',
        edit: '/mtm/bAD_ClientShop/updates',
        delete: '/mtm/bAD_ClientShop/deletes',
        ShopAreaTypeComboStore: '/mtm/combo/ShopAreaTypeComboStore'
      },
      ShopAreaTypeComboStore: [],
      validRules: {
        code: [
          { required: true, message: '顾客编码必须填写' }
        ],
        codeName: [
          { required: true, message: '店铺名称必须填写' }
        ],
        contact: [
          { required: true, message: '联系人必须填写' }
        ],
        tel: [
          { required: true, message: '联系电话必须填写' }
        ]
      }

    }
  },
  created () {
    this.loadData({ id: this.form.id })
    this.tableData = []
    this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ShopAreaTypeComboStore).then(result => {
        this.ShopAreaTypeComboStore = result
      })
    },
    async fullValidEvent () {
      var xtable = this.$refs[this.tableRef]
      const errMap = await xtable.fullValidate().catch(errMap => errMap)
      if (errMap) {
        const msgList = []
        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            const { rowIndex, column, rules } = params
            rules.forEach(rule => {
              msgList.push(`第 ${rowIndex} 行 ${column.title} 校验错误：${rule.message}`)
            })
          })
        })
        this.$XModal.message({
          status: 'error',
          message: () => {
            return [
              <div class="red" style="max-height: 400px;overflow: auto;">
                {
                  msgList.map(msg => <div>{msg}</div>)
                }
              </div>
            ]
          }
        })
        return false
      } else {
        // this.$XModal.message({ status: 'success', message: '校验成功！' })
        return true
      }
    },
    async insertEvent (row) {
      const record = { clientID: this.form.id }
      var xtable = this.$refs[this.tableRef]
      const { row: newRow } = await xtable.insertAt(record, row)
      await xtable.setActiveCell(newRow, 'code')
    },
    getTableData () {
      var xtable = this.$refs[this.tableRef]
      var insertData = xtable.getInsertRecords()
      var updateData = xtable.getUpdateRecords()
      var data = unionWith(insertData, updateData)
      return data
    },
    async saveEvent () {
      var b = this.fullValidEvent()
      if (b) {
        var xtable = this.$refs[this.tableRef]
        var insertData = xtable.getInsertRecords()
        var updateData = xtable.getUpdateRecords()
        if (insertData.length > 0) {
          await this.$api.ActionRequest(this.api.add, insertData).then(res => {
            this.loadData({ id: this.form.id })
          })
        }
        if (updateData.length > 0) {
          await this.$api.ActionRequest(this.api.edit, updateData).then(res => {
            this.loadData({ id: this.form.id })
          })
        }
      }
    },
    async removeEvent (row) {
      var xtable = this.$refs[this.tableRef]
      xtable.remove(row)
      if (row.id !== undefined) {
        await this.$api.ActionRequest(this.api.delete, [row]).then(res => {
          this.loadData({ id: this.form.id })
        })
      }
    }

  }
}
</script>

<style>
</style>
