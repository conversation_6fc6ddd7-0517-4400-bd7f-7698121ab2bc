<template>
  <d2-container class="model">
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">

            <vxe-form-item field="businessSubType">
              <template #default="{ data }">
                <el-select v-model="data.businessSubType" filterable placeholder="版型分类" size="mini" clearable class="businessSubType">
                  <el-option v-for="item in ModelBusinessSubTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelGroupID">
              <template #default="{ data }">
                <el-select v-model="data.modelGroupID" filterable placeholder="版型系列" size="mini" clearable>
                  <el-option v-for="item in ModelGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelBaseID">
              <template #default="{ data }">
                <el-select v-model="data.modelBaseID" filterable placeholder="基本版型图片" size="mini" clearable>
                  <el-option v-for="item in ModelBaseComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" placeholder="类别" clearable size="mini" style="width:110px">
                  <el-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="genderID">
              <template #default="{ data }">
                <el-select v-model="data.genderID" filterable placeholder="性别" clearable size="mini" style="width:70px">
                  <el-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sizeListID">
              <template #default="{ data }">
                <el-select v-model="data.sizeListID" filterable placeholder="规格单" clearable size="mini" class="sizeListID">
                  <el-option v-for="num in SizeListComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sewBaseID">
              <template #default="{ data }">
                <el-select v-model="data.sewBaseID" filterable placeholder="缝份类别" clearable size="mini" class="sewBaseID">
                  <el-option v-for="num in SewBaseComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="designNo">
              <vxe-input v-model.trim="searchForm.designNo" placeholder="设计号" suffix-icon="fa fa-search" clearable class="designNo"></vxe-input>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModel_master_table' ref='master_table' :loading="tableLoading" @cell-dblclick="cellDblClick" :height="TableHeight" :data="tableData" :row-class-name="rowClassName" highlight-current-column :custom-config="{storage: true}" :checkbox-config="{checkRowKeys: defaultSelecteRows, highlight: true}">
      <!-- <vxe-table-column type="seq" width="60"></vxe-table-column> -->
      <vxe-table-column field="id" title="ID" :visible="false"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="businessSubTypeText" title="业务类型" sortable width="100px">
        <template v-slot="{ row }">
          <template v-if="row.businessSubType===0">
            <el-tag type="success">{{row.businessSubTypeText}}</el-tag>
          </template>
          <template v-else>
            <el-tag type="warning">{{row.businessSubTypeText}}</el-tag>
          </template>
        </template>
      </vxe-table-column>
      <vxe-table-column field="modelGroupText" title="版型系列" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelBaseText" title="基础版型图片" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="genderText" title="性别" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="issueDate" title="日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="code" title="版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="designNo" title="设计号" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="shortName" title="版型简称" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="codeName" title="款式描述" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="modelName" title="款式描述" sortable ></vxe-table-column> -->
      <!-- <vxe-table-column field="codeName" title="负责人" sortable ></vxe-table-column> -->
      <vxe-table-column field="sizeListText" title="规格单" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sewBaseText" title="缝份列表" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isShopDefault" title="商城默认版" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isModelDesignByElemListDetail" title="款式上下级" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isClientShow" title="客户端显示？" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="maxEase" title="最大加放量" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="minEase" title="最小加放量" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="prefix" title="排料前缀" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isRuleSize" title="支持算法" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="originalModelCode" title="来源版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="originalModelName" title="来源版型名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemName" title="物料名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemOriginalItemNo" title="物料原始货号" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="originalModelName" title="来源版型" sortable ></vxe-table-column> -->
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <el-button type="success" size="mini" @click="selectEvent()">确定</el-button>
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep, isEmpty } from 'lodash'
export default {
  name: 'MomModelMaster',
  mixins: [masterTableMixins],
  components: {
  },
  props: {
  },
  data () {
    return {
      isAutoLoding: false,
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        businessSubType: null,
        modelBaseID: null,
        groupID: null,
        genderID: true,
        // issueDate: null,
        sizeListID: null,
        sewBaseID: null,
        isClientShow: false,
        maxEase: null,
        minEase: null,
        prefix: null,
        isRuleSize: false,
        itemID: null,
        originalModelID: null,
        soderDetailModelSource: null,
        designNo: null

      },

      api: {
        get: '/mtm/mom_model/get',
        add: '/mtm/mom_model/adds',
        edit: '/mtm/mom_model/updates',
        delete: '/mtm/mom_model/deletes',
        deepclone: '/mtm/mom_model/deepClone',
        modelBaseComboStore: '/mtm/combo/modelBaseComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        SizeListComboStore: '/mtm/combo/sizeListComboStore',
        SewBaseComboStore: '/mtm/combo/sewBaseComboStore',
        ModelGroupComboStore: '/mtm/combo/ModelGroupComboStore',
        ModelBusinessSubTypeComboStore: '/mtm/combo/ModelBusinessSubTypeComboStore',
        ItemComboStoreByQuery: '/mtm/comboQuery/ItemComboStoreByQuery'
      },
      ModelBaseComboStore: [],
      ModelGroupComboStore: [],
      GroupComboStore: [],
      SizeListComboStore: [],
      SewBaseComboStore: [],
      ItemComboStoreByQuery: [],
      ModelBusinessSubTypeComboStore: '',
      sorderModelData: [],
      searchSorderModelForm: {
        text: ''
      }
    }
  },
  async created () {
    await this.getCombStore()
    if (this.masterSeach != null) {
      this.searchForm = Object.assign(this.searchForm, this.masterSeach)
    }
    this.loadData()
  },
  methods: {
    // 数据加载
    async loadData () {
      return new Promise((resolve, reject) => {
        this.tableLoading = true
        this.$api.ActionRequest(this.api.get, this.searchForm).then(result => {
          this.tableData = result.items
          this.searchForm.totalCount = result.totalCount
          this.tableLoading = false
          resolve({ data: result.items })
        })
        // this.tableLoading = false
      })
    },

    async getCombStore () {
      await this.$api.ActionRequest(this.api.modelBaseComboStore).then(result => {
        this.ModelBaseComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SizeListComboStore).then(result => {
        this.SizeListComboStore = result
      })
      await this.$api.ActionRequest(this.api.SewBaseComboStore).then(result => {
        this.SewBaseComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelBusinessSubTypeComboStore).then(result => {
        this.ModelBusinessSubTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelGroupComboStore).then(result => {
        this.ModelGroupComboStore = result
      })
    },
    selectEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length === 0) {
        this.$XModal.message({ message: '请勾选要生成的版型', status: 'error' })
        return
      }
      if (rows.length > 50) {
        this.$XModal.message({ message: '最大只能选择50条数据', status: 'error' })
        return
      }
      this.$emit('getSelelctModel', rows)
    }
  }
}
</script>

<style lang="scss" >
</style>
