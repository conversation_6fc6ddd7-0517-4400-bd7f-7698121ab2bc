
export default ({ service, request, faker, tools }) => ({
  /**
     * @description sym_user
     * @param {Object} data 登录携带的信息
     */
  SYM_USER_GET (data) {
    return request({
      url: '/mtm/sym_user/get',
      method: 'post',
      data
    })
  },
  SYM_USER_ADDS (data) {
    return request({
      url: '/mtm/sym_user/adds',
      method: 'post',
      data
    })
  },
  SYM_USER_UPDATES (data) {
    return request({
      url: '/mtm/sym_user/updates',
      method: 'post',
      data
    })
  },
  SYM_USER_DELETES (data) {
    return request({
      url: '/mtm/sym_user/deletes',
      method: 'post',
      data
    })
  }
})
