<template>
  <d2-container class="PrdProductionplanDetail">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增 </vxe-button> -->
        </template>
        <template v-slot:tools>
          <!-- <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button> -->
          <!-- <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="searchEvent()"></vxe-button> -->
        </template>
      </vxe-toolbar>

    </template>
    <template>
      <d2-container>
        <split-pane :min-percent='20' :default-percent='35' split="vertical">
          <template slot="paneL">
            <d2-container>
              <template slot="header">
                <vxe-toolbar perfect custom>
                  <template v-slot:tools>
                    <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="loadDataEvent()"></vxe-button>
                    <!-- <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
                      <vxe-form-item field="text" :item-render="{}">                        <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                        </vxe-input>
                      </vxe-form-item>
                      <vxe-form-item :item-render="{}">  <template #default>
<vxe-button type="submit" status="success">查询</vxe-button>
                        <vxe-button type="reset">重置</vxe-button>
  </template>
</vxe-form-item>
                    </vxe-form> -->
                  </template>
                </vxe-toolbar>
              </template>
              <vxe-table id='PrdProductionplandetailDetailTable' ref='master_table' height="auto" @cell-click='tableCellClick' :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}">
                <vxe-table-column type="radio" width="60"></vxe-table-column>
                <vxe-table-column field="lineNum" title="行号" width="100"> </vxe-table-column>
                <vxe-table-column field="serialNumber" title="序列号" width="100"> </vxe-table-column>
                <vxe-table-column field="groupName" title="类别" width="100"> </vxe-table-column>
                <vxe-table-column field="productionPlanDetailStateText" title="状态" width="100"> </vxe-table-column>
                <vxe-table-column field="customerNumber" title="客户订单号" width="100"> </vxe-table-column>
                <vxe-table-column field="clientPersonName" title="顾客" width="100"> </vxe-table-column>
                <vxe-table-column field="gender" title="性别" :formatter="formatSex" width="100"> </vxe-table-column>
                <vxe-table-column field="customerSize" title="顾客尺码" width="100"> </vxe-table-column>
                <vxe-table-column field="finalTextureText" title="纹理" width="100"> </vxe-table-column>
                <vxe-table-column field="finalWidth" title="幅宽" width="100"> </vxe-table-column>
                <vxe-table-column field="itemName" title="面料号" width="100"> </vxe-table-column>
                <vxe-table-column field="modelCode" title="版型" width="100"> </vxe-table-column>
                <vxe-table-column field="modelName" title="版型" width="100"> </vxe-table-column>
                <vxe-table-column field="halfFitting" title="半成品试衣" :formatter="formatBool" width="100"> </vxe-table-column>
                <vxe-table-column field="sizeCode" title="规格" width="100"> </vxe-table-column>
                <vxe-table-column field="qty" title="数量" width="100"> </vxe-table-column>
                <vxe-table-column field="startNumber" title="起始数" width="100"> </vxe-table-column>
                <vxe-table-column field="endNumber" title="结束数" width="100"> </vxe-table-column>
                <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
                <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column> -->
                <!-- <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"> </vxe-table-column> -->
                <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
                <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
                <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
                <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
                </vxe-table-column>
                <vxe-table-column title="操作" width="50" v-if="menuAction.allowEdit||menuAction.allowDelete">
                  <template v-slot="{ row }">
                    <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button> -->
                    <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
                    </vxe-button>
                  </template>
                </vxe-table-column>
              </vxe-table>
              <template slot="footer">
                <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'Total']">
                  <!-- <template v-slot:right>
                  <mtm-footer-companyinfo v-if="footerCompanyInfo" />
                </template> -->
                </vxe-pager>
              </template>
              <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
                <!-- <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}">
  <template #default>
 </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}">
  </template>
</vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form> -->
              </vxe-modal>
            </d2-container>
          </template>
          <template slot="paneR">
            <el-tabs v-model="activeName" type="border-card" style="height:99%">
              <el-tab-pane label="排产信息" name="productPlanSchedule" style="height:99%">
                <product-plan-schedule :productPlanDetail="selectRow" />
              </el-tab-pane>
              <el-tab-pane label="工艺信息" name="productPlanElem" style="height:99%">
                <product-plan-elem :productPlanDetail="selectRow" />
              </el-tab-pane>
              <el-tab-pane label="规格数据" name="productPlanSize" style="height:99%">
                <product-plan-size :productPlanDetail="selectRow" />
              </el-tab-pane>
            </el-tabs>
          </template>
        </split-pane>
      </d2-container>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import ProductPlanSchedule from './productplanschedule'
import ProductPlanElem from './productplanelem'
import ProductPlanSize from './productplansize'
import { cloneDeep } from 'lodash'
export default {
  name: 'PrdProductionplanDetail',
  mixins: [detailTableMixins],
  components: {
    ProductPlanSchedule, ProductPlanElem, ProductPlanSize
  },
  data () {
    return {
      formData: {
        // code: '',
        // codeName: '',
        remark: '',
        isActive: true

      },
      activeName: 'productPlanSchedule',
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mes/prd_productionplandetail/get',
        // add: '/mes/prd_productionplandetail/adds',
        // edit: '/mes/prd_productionplandetail/updates',
        delete: '/mes/prd_productionplandetail/deletes'

      }
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ productionPlanID: this.form.id }).then(({ data }) => {
      var row = this.tableData[0]
      this.$refs.master_table.setRadioRow(row)
      this.selectRow = cloneDeep(row)
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    loadDataEvent () {
      this.loadData({ productionPlanID: this.form.id }).then(({ data }) => {
        this.$refs.master_table.setRadioRow(this.tableData[0])
      })
    },
    tableCellClick ({ row, rowIndex, column, columnIndex }) {
      if (column.title === '操作') { return }
      this.drawer = true
      this.selectRow = cloneDeep(row)
      this.$refs.master_table.setRadioRow(row)
    },
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    }
  }
}
</script>

<style lang="scss" >
.PrdProductionplanDetail {
  .el-tabs__content {
    height: calc(100vh - 212px);
  }
}
</style>
