
import { mapState } from 'vuex'
export default {
  name: 'DetailElemMixins',
  props: {
    elemItem: {
      type: Object,
      requited: true
    },
    editAction: {
      type: Boolean,
      requited: true
    },
    sorderStore: {
      type: Object
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },

  methods: {
    defaultStyle (modelelemid, elems) {
      elems.forEach(item => {
        if (modelelemid === item.modelElemID && item.default) {
          return ' background:#9acd32;'
        }
      })
    },
    ModelElemChange (modelelemid, elems, elemlist) {
      this.$emit('ModelElemChange', { modelelemid: modelelemid, elemlist: elemlist, modelelem: modelelemid !== null && modelelemid !== '' ? elems.GetFirstElement('modelElemID', modelelemid) : null })
    },
    QtyPrase (qty) {
      return this.fomatFloat(qty, 2)
    },
    fomatFloat (src, pos) {
      return Math.round(src * Math.pow(10, pos)) / Math.pow(10, pos)
    },
    elemIsReuired (item, name) {
      if (name === 'elem' && item.modelElemRequired) {
        if (item.modelElemID === null || item.modelElemID === '') {
          return 'elemIsReuired'
        }
      }
      if (name === 'item' && item.itemRequired && item.isInputItem && item.modelElemID !== null) {
        if (item.itemID === null || item.itemID === '') {
          return 'elemIsReuired'
        }
      }
      if (name === 'input' && item.inputRequired) {
        if (item.input === null || item.input === '') {
          return 'elemIsReuired'
        }
      }
    }
  }
}
