<template>
        <table class="table table-striped table-bordered table-responsive table-elem" style="border-collapse: collapse;table-layout: fixed;word-wrap: break-word; word-break: break-all;margin:0px">
                      <tbody class="text-center">
                        <tr>
                          <td style="width:10%;"><span class="font-transform-normal" style="width:100%;">部位</span></td>
                          <td style="width:35%;"><span class="font-transform-normal" style="width:100%;">款式/工艺</span></td>
                          <td style="width:50%;"><span class="font-transform-normal" style="width:100%;">辅料</span></td>
                          <td><span class="font-transform-normal" style="width:100%;">数量</span></td>
                        </tr>
                        <tr v-for="(elem) in filtr_sorderDetailElmes(item.SorderDetailElems.filter(it=>{return it.ModelElemBase.indexOf('缝份')<0}))" v-bind:key="elem.ModelElemBase">
                          <td class="text-bold" style="-webkit-transform: scale(1.0);vertical-align: middle;text-align: center;">
                            <div style="width:100%">
                              <p class="fontspan font-transform-normal" style="margin-top: 1px;  font-size: 12px;">
                                {{elem.ModelElemBase}}
                              </p>
                            </div>
                          </td>
                          <td>
                            <table class="model-elem-border" style="width:100%;text-align: left;">
                              <tr v-for="item in elem.List1" :key="item.ModelElemList">
                                <td style="width:30%;" class="text-bold">
                                  <div>
                                    <p class="fontspan font-transform-normal" style="font-size: 12px;">
                                      {{item.ModelElemList}}
                                    </p>
                                  </div>
                                </td>
                                <td style="width:70%;">
                                  <div>
                                    <p class="fontspan font-transform-normal">
                                      {{item.ModelElem}}
                                    </p>
                                  </div>
                                </td>
                              </tr>
                            </table>
                          </td>
                          <td colspan="2">
                            <table class="model-elem-border" style="width:100%;text-align: left;">
                              <tr v-for="item in elem.List2" :key="item.ModelElemList">
                                <td style="width:120px;" class="text-bold">
                                  <div>
                                    <p class="fontspan font-transform-normal" style="font-size: 12px;">
                                      {{item.ModelElemList}}
                                    </p>
                                  </div>
                                </td>
                                <td>
                                  <div>
                                    <p class="fontspan font-transform-normal">
                                      {{item.ModelElem}}
                                    </p>
                                  </div>
                                </td>
                                <td style="text-align: right;width:9%">
                                  <div style="width:100%;">
                                    <p class="fontspan">
                                      {{item.Qty==0?'&nbsp;':item.Qty}}
                                    </p>
                                  </div>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
</template>

<script>
export default {

}
</script>

<style>

</style>
