<template>
    <table class="table table-bordered table-striped table-elem">
                        <tr>
                          <td class="text-bold" style="vertical-align: middle;width:52px;">
                            <p class="font-transform-normal">成衣部位</p>
                          </td>

                          <template v-if="item.GroupID!=6">
                            <td class="text-bold">
                              <p class="font-transform-normal">前衣长</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">后中长</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">胸围</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">中腰</p>
                            </td>
                            <template v-if="item.GroupID!=5">
                              <td class="text-bold">
                                <p class="font-transform-normal">臀围</p>
                              </td>
                              <td class="text-bold">
                                <p class="font-transform-normal">肩宽</p>
                              </td>
                              <td class="text-bold">
                                <p class="font-transform-normal">左袖长</p>
                              </td>
                              <td class="text-bold">
                                <p class="font-transform-normal">右袖长</p>
                              </td>
                              <td class="text-bold">
                                <p class="font-transform-normal">袖肥</p>
                              </td>
                              <td class="text-bold">
                                <p class="font-transform-normal">袖口</p>
                              </td>
                            </template>
                          </template>
                          <template v-else>
                            <td class="text-bold">
                              <p class="font-transform-normal">裤腰围</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">臀围</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">左裤长</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">右裤长</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">横档</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">膝围</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">脚口</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">通档</p>
                            </td>
                            <td class="text-bold">
                              <p class="font-transform-normal">立档</p>
                            </td>
                          </template>
                        </tr>
                        <tr>
                          <td class="text-bold">
                            <p class="font-transform-normal">标准规格</p>
                          </td>
                          <template v-if="item.GroupID!=6">
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.FrontLength}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.BackMiddleLength}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.Bust}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.MiddleWaist}}</p>
                            </td>
                            <template v-if="item.GroupID!=5">
                              <td>
                                <p class="font-transform-normal">{{SorderDetailSize.JacketHipline}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.ShoulderWidth}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.LeftSleeveLength}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.RightSleeveLength}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.SleeveWidth}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.Cuff}}</p>
                              </td>
                            </template>
                          </template>
                          <template v-else>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.Waist}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.Hipline}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.LeftTrouserLong}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.RightTrouserLong}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.CrossCrotch}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.KneeGirth}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.TrouserBottom}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.ThroughCrotch}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.StandCrotch}}</p>
                            </td>
                          </template>
                        </tr>
                        <tr>
                          <td class="text-bold">
                            <p class="font-transform-normal">修正值</p>
                          </td>
                          <template v-if="item.GroupID!=6">
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.FrontLength}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.BackMiddleLength}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.Bust}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.MiddleWaist}}</p>
                            </td>
                            <template v-if="item.GroupID!=5">
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.JacketHipline}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.ShoulderWidth}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.LeftSleeveLength}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.RightSleeveLength}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.SleeveWidth}}</p>
                              </td>
                              <td>
                                <p class="font-transform-normal">{{item.SorderDetailSize.Cuff}}</p>
                              </td>
                            </template>
                          </template>
                          <template v-else>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.Waist}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.Hipline}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.LeftTrouserLong}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.RightTrouserLong}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.CrossCrotch}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.KneeGirth}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.TrouserBottom}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.ThroughCrotch}}</p>
                            </td>
                            <td>
                              <p class="font-transform-normal">{{item.SorderDetailSize.StandCrotch}}</p>
                            </td>
                          </template>
                        </tr>
                        <tr class="text-bold size-finish">
                          <td class="text-bold" style="font-size:12px;">
                            <p class="font-transform-normal">成衣规格</p>
                          </td>
                          <template v-if="item.GroupID!=6">
                            <td>{{item.SorderDetailSize.FrontLength}}</td>
                            <td>{{item.SorderDetailSize.BackMiddleLength}}</td>
                            <td>{{item.SorderDetailSize.Bust}}</td>
                            <td>{{item.SorderDetailSize.MiddleWaist}}</td>
                            <template v-if="item.GroupID!=5">
                              <td>{{item.SorderDetailSize.JacketHipline}}</td>
                              <td>{{item.SorderDetailSize.ShoulderWidth}}</td>
                              <td>{{item.SorderDetailSize.LeftSleeveLength}}</td>
                              <td>{{item.SorderDetailSize.RightSleeveLength}}</td>
                              <td>{{item.SorderDetailSize.SleeveWidth}}</td>
                              <td>{{item.SorderDetailSize.Cuff}}</td>
                            </template>
                          </template>
                          <template v-else>
                            <td>{{item.SorderDetailSize.Waist}}</td>
                            <td>{{item.SorderDetailSize.Hipline}}</td>
                            <td>{{item.SorderDetailSize.LeftTrouserLong}}</td>
                            <td>{{item.SorderDetailSize.RightTrouserLong}}</td>
                            <td>{{item.SorderDetailSize.CrossCrotch}}</td>
                            <td>{{item.SorderDetailSize.KneeGirth}}</td>
                            <td>{{item.SorderDetailSize.TrouserBottom}}</td>
                            <td>{{item.SorderDetailSize.ThroughCrotch}}</td>
                            <td>{{item.SorderDetailSize.StandCrotch}}</td>
                          </template>
                        </tr>
                      </table>
</template>

<script>
export default {

}
</script>

<style>

</style>
