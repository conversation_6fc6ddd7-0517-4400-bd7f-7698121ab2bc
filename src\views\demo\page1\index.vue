<template>
  <d2-container>
    <div class="page1" flex-box="1">
      <vxe-grid
        border
        resizable
        keep-source
        ref="xGrid"
        id="toolbar_demo_2"
        height="auto"
        :pager-config="tablePage"
        :loading="loading"
        :custom-config="tableCustom"
        :data="tableData"
        :columns="tableColumn"
        :toolbar="tableToolbar"
        :form-config="tableForm"
        :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
      >
        <template v-slot:toolbar_buttons>
          <vxe-input v-model="searchName" placeholder="搜索"></vxe-input>
          <vxe-button status="primary" @click="loadData">搜索</vxe-button>
          <vxe-button @click="loadData">刷新</vxe-button>
          <vxe-button @click="insertEvent">新增</vxe-button>
          <vxe-button @click="saveEvent">保存</vxe-button>
          <vxe-button @click="$refs.xGrid.exportData()">导出.csv</vxe-button>
        </template>
      </vxe-grid>
    </div>
  </d2-container>
</template>

<script>
import tableMixinxs from '@/components/table/tablemixins/table-mixins.js'
export default {
  name: 'page1',
  mixins: [tableMixinxs],
  data () {
    return {
      tablePage: {
        perfect: true,
        pageSize: 15
      },
      searchName: '',
      loading: false,
      tableData: [],
      tableForm: {
        data: {
          name: '',
          sex: ''
        },
        items: [
          {
            field: 'name',
            title: '名称',
            itemRender: { name: '$input', props: { placeholder: '请输入名称' } }
          },
          {
            field: 'sex',
            title: '性别',
            titlePrefix: {
              message: '帮助信息！！！',
              icon: 'fa fa-info-circle'
            },
            itemRender: { name: '$select', options: [] }
          },
          {
            itemRender: {
              name: '$buttons',
              children: [
                {
                  props: { type: 'submit', content: '查询', status: 'primary' }
                },
                { props: { type: 'reset', content: '重置' } }
              ]
            }
          }
        ]
      },
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        },
        perfect: true,
        refresh: {
          icon: 'fa fa-refresh',
          iconLoading: 'fa fa-spinner fa-spin'
        },
        import: {
          icon: 'fa fa-upload'
        },
        export: {
          icon: 'fa fa-download'
        },
        print: {
          icon: 'fa fa-print'
        },
        zoom: {
          iconIn: 'fa fa-arrows-alt',
          iconOut: 'fa fa-expand'
        }
        // custom: {
        //   icon: 'fa fa-cog'
        // }
      },
      tableColumn: [
        { type: 'checkbox', width: 50 },
        { type: 'seq', width: 60 },
        { field: 'name', title: 'Name', editRender: { name: 'input' } },
        {
          title: '分类',
          children: [
            {
              field: 'nickname',
              title: 'Nickname',
              editRender: { name: 'input' }
            },
            {
              title: '子类',
              children: [
                { field: 'role', title: 'Role', editRender: { name: 'input' } }
              ]
            }
          ]
        },
        {
          field: 'describe',
          title: 'Describe',
          showOverflow: true,
          editRender: { name: 'input' }
        }
      ]
    }
  },
  created () {
    this.loadData()
    this.findSexList()
  },
  methods: {
    loadData () {
      this.loading = true
      setTimeout(() => {
        this.tableData = []
        this.loading = false
      }, 100)
    },
    insertEvent () {
      this.$refs.xGrid.insert({
        name: 'xxx'
      })
    },
    saveEvent () {
      setTimeout(() => {
        const {
          insertRecords,
          removeRecords,
          updateRecords
        } = this.$refs.xGrid.getRecordset()
        this.$XModal.message({
          message: `新增 ${insertRecords.length} 条，删除 ${removeRecords.length} 条，更新 ${updateRecords.length} 条`,
          status: 'success'
        })
        this.loadData()
      }, 100)
    },
    async findSexList () {
      const sexList = [
        { label: '男', vuele: true },
        { label: '女', value: false }
      ]
      // 异步更新下拉选项
      this.tableForm.items[1].itemRender.options = sexList
    }
  }
}
</script>
<style>
.page1 {
  /* padding-top: 50px ; */
  height: 100%;
  display: flex;
}
</style>
