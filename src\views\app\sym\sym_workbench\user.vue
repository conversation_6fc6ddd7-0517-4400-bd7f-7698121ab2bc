<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table ref='master_table' :loading="tableLoading" id="SymUserMasterTable" class="sortable-column-demo" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <!-- <vxe-table-column field="username" title="账号" sortable show-overflow width="100"> </vxe-table-column> -->
      <vxe-table-column field="name" title="姓名" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="department" title="部门" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="gender" title="性别" :formatter="formatSex" show-overflow width="100"></vxe-table-column>
      <!-- <vxe-table-column field="birthday" title="生日" show-overflow :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="email" title="邮箱" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="tel" title="手机号" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="mobile" title="座机" show-overflow width="100"> </vxe-table-column>
      <vxe-table-column field="fax" title="传真" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="userType" title="用户类型" show-overflow :formatter="val=>formatSelect(val,userTypeList)" width="100"> </vxe-table-column>
      <vxe-table-column field="position" title="职位" show-overflow width="100"> </vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" show-overflow width="150"></vxe-table-column>
      <!-- <vxe-table-column field="viewTypeID" title="仅查看自己数据" :formatter="val=>formatBool(val)" width="100" show-overflow></vxe-table-column>
      <vxe-table-column field="lastLoggingDate" title="最后登录时间" :formatter="val=>formatDate(val)" show-overflow width="100"></vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <vxe-button status="success" @click="addEvent()">确定</vxe-button>
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'sym_user',
  mixins: [masterTableMixins],
  components: {
  },
  props: {
    form: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      sexList: [
        { label: '女', value: false },
        { label: '男', value: true }
      ],
      userTypeList: [
        { label: '系统账号', value: 0 },
        { label: '内部账户', value: 1 },
        { label: '外部账户', value: 2 }
      ],

      api: {
        get: '/mtm/sym_user/get',
        add: '/mtm/SYM_WorkbenchReceiverDetail/adds'
      }

    }
  },

  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {

    },
    async addEvent () {
      var data = this.$refs.master_table.getCheckboxRecords()
      if (data.length <= 0) {
        this.$notify.error({
          title: '错误',
          message: '请勾选要添加的信息'
        })
        return
      }
      var rows = data.map(item => { return { userID: item.id, workbenchID: this.form.id, isActive: true } })
      await this.$api.ActionRequest(this.api.add, rows).then(result => {
        this.$notify.success({
          title: '成功',
          message: '添加成功'
        })
        this.loadData()
        this.$emit('reload')
      })
    }

  }
}
</script>

<style>
</style>
