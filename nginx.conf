worker_processes auto;
events {
    worker_connections  1024;
    accept_mutex on;
}
http {
  include mime.types;
  default_type application/octet-stream;
  sendfile        on;
  client_header_timeout 600s;
  client_body_timeout 600s;
  keepalive_timeout 75s;
  gzip on;
  gzip_min_length 4k;
  gzip_comp_level 4;
  client_max_body_size 1024m;
  client_header_buffer_size 512k;
  client_body_buffer_size 8m;
  server_names_hash_bucket_size 512;
  proxy_headers_hash_max_size 51200;
  proxy_headers_hash_bucket_size 6400;
  gzip_types application/javascript application/x-javascript text/javascript text/css application/json application/xml;
  proxy_connect_timeout 600s;
  proxy_send_timeout 600s;
  proxy_read_timeout 600s;
  proxy_buffer_size 16k;
  proxy_buffers 4 64k;
  proxy_busy_buffers_size 128k;
  proxy_temp_file_write_size 128k;
  large_client_header_buffers 4 512k;

  server {
    listen       80;
    server_name  localhost;
    gzip on;
    gzip_static on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    proxy_connect_timeout 5m;
    proxy_send_timeout 5m;
    proxy_read_timeout 5m;

    location / {
      root   /usr/share/nginx/html;
      index  index.html index.htm;
      try_files $uri $uri/ /index.html;
      proxy_send_timeout 300; 
      proxy_read_timeout 300; 
      proxy_connect_timeout 300;
            
	  proxy_set_header           Host $host;
	  proxy_set_header           Cookie $http_cookie;
	  proxy_set_header           X-Real-IP $remote_addr;
	  proxy_set_header           X-Forwarded-For $proxy_add_x_forwarded_for;
	  proxy_set_header           HTTP_X_FORWARDED_FOR $remote_addr;
	  proxy_set_header           X-Forwarded-Server $host;

      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Credentials' 'true';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
      add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
      root   /usr/share/nginx/html;
    }
  }
}
