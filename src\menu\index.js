import { uniqueId } from 'lodash'
// import { getMenus } from './menus.js'

/**
 * @description 给菜单数据补充上 path 字段
 * @description https://github.com/d2-projects/d2-admin/issues/209
 * @param {Array} menu 原始的菜单数据
 */
export function supplementPath (menu) {
  return menu.map(e => ({
    ...e,
    path: e.path || uniqueId('d2-menu-empty-'),
    ...e.children ? {
      children: supplementPath(e.children)
    } : {}
  }))
}

export const menuHeader = menus()
export const menuAside = menus()

export function menus () {
  return supplementPath([
    { path: '/index', title: '首页', icon: 'home' }
    // {
    //   title: '系统管理中心',
    //   icon: 'users',
    //   children: [
    //     { title: '用户', icon: 'user-plus', path: '/app/sym/sym_user' },
    //     { title: '角色', icon: 'user', path: '/app/sym/sym_role' },
    //     { title: '权限', icon: 'user', path: '/app/sym/sym_action' },
    //     { title: '权限类别', icon: 'user', path: '/app/sym/sym_actiontype' },
    //     { title: '权限分组', icon: 'user', path: '/app/sym/sym_actiongroup' },
    //     { title: '用户送达方', icon: 'user', path: '/app/sym/sym_userclient' },
    //     { title: '部门', icon: 'user', path: '/app/sym/sym_department' }

    //   ]
    // },
    // {
    //   title: '系统设置中心',
    //   icon: 'cogs',
    //   children: [
    //     {
    //       title: '订单',
    //       icon: 'cog',
    //       children: [
    //         { title: '长短款', icon: 'cog', path: '/app/sys/sys_designstyle' },
    //         { title: '订单量体类别', icon: 'cog', path: '/app/sys/sys_sordersizetype' },
    //         { title: '订单类别', icon: 'cog', path: '/app/sys/sys_sordertype' },
    //         { title: '销售渠道', icon: 'cog', path: '/app/sys/sys_sorderchannel' }
    //       ]
    //     },
    //     {
    //       title: '规格单',
    //       icon: 'cog',
    //       children: [
    //         { title: '规格元素', icon: 'cog', path: '/app/sys/sys_sizeelemlist' },
    //         { title: '身高', icon: 'cog', path: '/app/sys/sys_sizeelema' },
    //         { title: '胸围', icon: 'cog', path: '/app/sys/sys_sizeelemb' },
    //         { title: '体型', icon: 'cog', path: '/app/sys/sys_sizeelemc' },
    //         { title: '臀围 ', icon: 'cog', path: '/app/sys/sys_sizeelemd' },
    //         { title: '算法 ', icon: 'cog', path: '/app/sys/sys_formula' },
    //         { title: '规格字段 ', icon: 'cog', path: '/app/sys/sys_sizecolumn' },
    //         { title: '规格字段->类别 ', icon: 'cog', path: '/app/sys/sys_sizecolumngroup' }
    //       ]
    //     },
    //     { title: '银行', icon: 'cog', path: '/app/sys/sys_bank' },
    //     { title: '品牌', icon: 'cog', path: '/app/sys/sys_brand' },
    //     { title: '客户线', icon: 'cog', path: '/app/sys/sys_brandline' },
    //     { title: '系统分类', icon: 'cog', path: '/app/sys/sys_class' },
    //     { title: '货币', icon: 'cog', path: '/app/sys/sys_currency' },
    //     { title: '类别上下装', icon: 'cog', path: '/app/sys/sys_grouptype' },
    //     { title: '类别', icon: 'cog', path: '/app/sys/sys_group' },
    //     { title: '子类别', icon: 'cog', path: '/app/sys/sys_subgroup' },
    //     { title: '转移类型', icon: 'cog', path: '/app/sys/sys_movetype' },
    //     { title: '付款方式', icon: 'cog', path: '/app/sys/sys_paymentmethod' },
    //     { title: '付款条款', icon: 'cog', path: '/app/sys/sys_paymentterms' },
    //     { title: '季度', icon: 'cog', path: '/app/sys/sys_season' },
    //     { title: '状态', icon: 'cog', path: '/app/sys/sys_status' },
    //     { title: '纹理', icon: 'cog', path: '/app/sys/sys_texture' },
    //     { title: '单位', icon: 'cog', path: '/app/sys/sys_unit' },
    //     { title: '税率', icon: 'cog', path: '/app/sys/sys_vat' },
    //     { title: '版型款式类别', icon: 'cog', path: '/app/sys/sys_modelelemtype' },
    //     { title: '版型类别', icon: 'cog', path: '/app/sys/sys_modeltype' },
    //     { title: '位置', icon: 'cog', path: '/app/sys/sys_position' },
    //     { title: 'CAD排料图', icon: 'cog', path: '/app/sys/sys_cadlayout' },
    //     { title: 'CAD衣片组', icon: 'cog', path: '/app/sys/sys_cadrule' },
    //     { title: 'CAD服务器配置', icon: 'cog', path: '/app/sys/sys_server' },
    //     { title: '单号', icon: 'cog', path: '/app/sys/sys_num' },
    //     { title: '版型图片类型', icon: 'cog', path: '/app/sys/sys_modelimagetype' }
    //   ]
    // },
    // {
    //   title: '基础数据中心',
    //   icon: 'database',
    //   children: [
    //     { title: '客户管理中心', icon: 'user', path: '/app/bad/bad_client' },
    //     { title: '页面 1' },
    //     { title: '页面 2' },
    //     { title: '页面 3' }
    //   ]
    // },
    // {
    //   title: '版型管理中心',
    //   icon: 'tachometer',
    //   children: [
    //     { title: '页面 1' },
    //     { title: '页面 2' },
    //     { title: '页面 3' }
    //   ]
    // },
    // {
    //   title: '订单管理中心',
    //   icon: 'briefcase',
    //   children: [
    //     { title: '页面 1' },
    //     { title: '页面 2' },
    //     { title: '页面 3' }
    //   ]
    // },
    // {
    //   title: '仓库管理中心',
    //   icon: 'briefcase',
    //   children: [
    //     {
    //       title: '采购管理',
    //       icon: 'briefcase',
    //       children: [
    //         { title: '页面 1' },
    //         { title: '页面 2' },
    //         { title: '页面 3' }
    //       ]
    //     },
    //     { title: '页面 1' },
    //     { title: '页面 2' },
    //     { title: '页面 3' }
    //   ]
    // },
    // {
    //   title: '生产管理中心',
    //   icon: 'briefcase',
    //   children: [
    //     { title: '页面 1' },
    //     { title: '页面 2' },
    //     { title: '页面 3' }
    //   ]
    // }

  ])
}
