import * as echarts from 'echarts'
export default {
  data () {
    return {
      timer: '',
      myChart: null,
      height: '400px',
      width: '450px',
      option: {
        title: {
          text: '订单状态分布'
          // subtext: 'Living Expenses in Shenzhen'
        },
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        tooltip: {},
        legend: {
          data: ['数量']
        },
        xAxis: {
          data: []
        },
        yAxis: {},
        series: [
          {
            name: '数量',
            type: 'bar',
            label: {
              show: true,
              position: 'inside'
            },
            data: []
          }
        ]
      }
    }
  },
  beforeDestroy () {
    if (!this.myChart) {
      return
    }
    this.myChart.dispose()
    this.myChart = null
    clearInterval(this.timer)
  },
  mounted () {
    this.$nextTick(() => {
      this.timer = setInterval(this.get, 1000 * 60)// 毫秒
      this.initChart()
    })
  },
  async created () {
    await this.get()
  },
  methods: {
    async get () {
      await this.$api.ActionRequest(this.api.get, { day: this.day }).then(async result => {
        this.setOptions(result)
      })
    },
    initChart () {
      var chartDom = this.$refs.main
      this.myChart = echarts.init(chartDom)
      this.myChart.setOption(this.option)
      this.myChart.resize()
    },
    setOptions (data) {
      var xAxis = []
      var series = []
      data.forEach(a => {
        xAxis.push(a.statusText)
        series.push(a.count)
      })
      // 填入数据
      this.myChart.setOption({
        xAxis: {
          data: xAxis
        },
        series: [
          {
            type: 'bar',
            data: series
          }
        ]
      })
    },
    async change (val) {
      await this.get()
    }
  }
}
