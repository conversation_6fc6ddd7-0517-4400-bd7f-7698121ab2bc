<template>
  <d2-container class="odm_suitsupplyorder">
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->
          <!-- <vxe-button @click="auto()" status="success" v-if="menuAction.allowAdd">批量生成订单</vxe-button> -->
          <vxe-button @click="changeSelected()" status="success" v-if="menuAction.allowEdit&&info.userType===0">批量生成订单</vxe-button>
          <vxe-button @click="deletesEvent()" status="danger" v-if="menuAction.allowDelete">批量删除</vxe-button>
          <vxe-button @click="importJsonShow=!importJsonShow" status="warning" v-if="menuAction.allowAdd">导入JSON订单</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="dates" :item-render="{}">
              <template #default>
                <el-date-picker size="mini" v-model="searchForm.dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="suitSupply_OrderState" :item-render="{name: '$select', options: SuitSupply_OrderStateComboStore,props:{placeholder:'状态',clearable:true}}" />
            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='OdmSuitsupplyMasterTable' ref='master_table' height="auto" @cell-click='tableCellClick' @cell-dblclick="tableCellClickEvent" :enterable="true" :row-class-name="rowClassName" :loading="tableLoading" :data="tableData" :custom-config="{storage: true}" :tooltip-config="tooltipConfig">
      <vxe-table-column type="seq" width="60" />
      <vxe-table-column type="checkbox" width="60" />
      <vxe-table-column field="sorderNumber" title="MTM订单号" sortable width="100" />
      <vxe-table-column field="suitSupply_OrderStateText" title="状态" sortable width="100">
        <template v-slot="{ row }">
          <!-- {{ row. suitSupply_OrderStateText}} -->
          <!-- 转换失败 -->
          <vxe-button v-if="row.suitSupply_OrderState==10" status="primary" icon="el-icon-message-solid" :content="row. suitSupply_OrderStateText" round></vxe-button>
          <!-- 取消订单 -->
          <vxe-button v-if="row.suitSupply_OrderState==0" status="danger" icon="el-icon-close" :content="row. suitSupply_OrderStateText" round></vxe-button>
          <!-- 等待转换 -->
          <vxe-button v-if="row.suitSupply_OrderState==30" status="info" icon="el-icon-refresh-right" :content="row. suitSupply_OrderStateText" round></vxe-button>
          <!-- 转换中 -->
          <vxe-button v-if="row.suitSupply_OrderState==40" status="warning" icon="el-icon-refresh" :content="row. suitSupply_OrderStateText" round></vxe-button>
          <!-- 转换成功 -->
          <vxe-button v-if="row.suitSupply_OrderState==50" status="success" icon="vxe-icon--check" :content="row. suitSupply_OrderStateText" round></vxe-button>
        </template>
      </vxe-table-column>
      <vxe-table-column field="details" title="明细" sortable width="100" />
      <vxe-table-column field="receipt_Number" title="发票号" sortable width="100" />
      <!-- <vxe-table-column field="message" :show-overflow="'ellipsis'" title="消息" sortable width="200"> -->
      <vxe-table-column field="message" title="消息" sortable width="200"> </vxe-table-column>
      <vxe-table-column field="waring" title="警告" sortable width="200"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100" />
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100" />
      <vxe-table-column field="createBy" title="创建人" sortable width="100" />
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100" />
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column title="编辑" width="80" align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
        </template>
      </vxe-table-column>
      <vxe-table-column title="操作" width="180" align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button icon="vxe-icon-refresh" @click="ChangOrder(row)" v-if="conversioBtn(row)">生成</vxe-button>
          <vxe-button type="text" icon="vxe-icon--download" @click="createJson(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="'详细信息'" width="80%" height="80%" resize destroy-on-close>
      <detail-table :form="selectRow" v-if="showEdit" :readOnly="readOnly" :successEvent="successEvent" />
    </vxe-modal>
    <vxe-modal v-model="importJsonShow" :title="'上传JSON订单'" width="50%" height="30%" resize destroy-on-close>
      <up-load-josn v-if="importJsonShow" :upLoadSuccess="upLoadSuccess" />
    </vxe-modal>
  </d2-container>
</template>

<script>

import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
import UpLoadJosn from './uploadjson.vue'
import DetailTable from './detail'
import { mapState } from 'vuex'
export default {
  name: 'odm_suitsupplyorder',
  mixins: [masterTableMixins],
  components: {
    DetailTable,
    UpLoadJosn
  },

  data () {
    return {
      tooltipConfig: {
        contentMethod: ({ type, column, row, items, _columnIndex }) => {
          // 重写默认的提示内容
          if (column.field === 'message') {
            if (type === 'header') {
              return column.title
            } else if (type === 'footer') {
              return items[_columnIndex]
            }
            return row[column.field]
            // return '不想换行不想换行不想换\n行不想换行不想换<br>行不想换行不想换行不想换行'
          }
        },
        enterable: true
      },
      searchForm: {
        suitSupply_OrderState: null
      },
      formData: {
        sorderId: null,
        SuitSupply_OrderState: 0,
        // order_number: null,
        receipt_number: null,
        combinationID: null,
        dealer_id: null,
        order_booking_date: null,
        message: null,
        product: {},
        customerDetails: {},
        jacket: {},
        jacketSize: {},
        trouser: {},
        trouserSize: {},
        waistcoat: {},
        waistcoatSize: {},
        remark: '',
        isActive: true
      },
      importJsonShow: false,
      readOnly: false,
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/oDM_SorderSuitSupplyOrder/get', /// api/mtm/oDM_SorderSuitSupplyOrder/get
        add: '/mtm/oDM_SorderSuitSupplyOrder/adds',
        edit: '/mtm/oDM_SorderSuitSupplyOrder/updates',
        delete: '/mtm/oDM_SorderSuitSupplyOrder/deletes',
        auto: '/mtm/oDM_SorderSuitSupplyOrder/auto',
        conversionOrder: '/mtm/oDM_SorderSuitSupplyOrder/ConversionFactoryOrder',
        SuitSupply_OrderStateComboStore: '/mtm/combo/SuitSupply_OrderStateComboStore'
      },
      SuitSupply_OrderStateComboStore: []
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SuitSupply_OrderStateComboStore).then(result => {
        this.SuitSupply_OrderStateComboStore = result
      })
    },
    conversioBtn (row) {
      if (!this.menuAction.allowEdit) {
        return false
      }
      if (row.suitSupply_OrderState === 10) {
        return true
      } else {
        return false
      }
    },
    async auto () {
      await this.$api.ActionRequest(this.api.auto).then(result => {
        this.loadData()
      })
    },
    editEvent (row) {
      var data = cloneDeep(row)
      this.selectRow = data
      this.showEdit = true
      this.readOnly = false
    },
    successEvent () {
      this.showEdit = false
      this.readOnly = true
      this.loadData()
    },
    upLoadSuccess () {
      // console.log('导入成功')
      this.importJsonShow = false
      this.loadData()
    },
    changeSelected () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length === 0) {
        this.$XModal.message({ message: '请勾选要转换的数据', status: 'error' })
        return
      }
      if (rows.length > 50) {
        this.$XModal.message({ message: '一次最多只能生成10条订单', status: 'error' })
        return
      }
      this.changeOrders(rows)
    },
    async changeOrders (data) {
      await this.$api.ActionRequest(this.api.conversionOrder, data).then(result => {
        this.$message({ type: 'success', message: '订单状态变更:生成中,订单已经在后台自动转换,请等待三分钟后再来查看！', duration: 3000 })
        this.loadData()
      }).catch(() => {
      })
    },
    async ChangOrder (data) {
      await this.changeOrders([data])
    },
    tableCellClickEvent ({ row, rowIndex, column }) {
      var data = cloneDeep(row)
      this.selectRow = data
      this.readOnly = true
      if (column.title === '操作') {
        this.readOnly = false
        this.showEdit = false
      } else if (column.title === '编辑') {
        this.readOnly = false
        this.showEdit = true
      } else {
        this.showEdit = true
      }
    },
    createJson (res) {
      // var data = res
      var data = JSON.stringify(res)
      // encodeURIComponent解决中文乱码
      const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(data)
      // 通过创建a标签实现
      const link = document.createElement('a')
      link.href = uri
      var date = this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss')
      // 对下载的文件命名
      link.download = res.sorderNumber + '_' + res.receipt_Number + '_' + date + '.json'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

  }
}
</script>

<style lang="scss" >
.odm_suitsupplyorder {
  .vxe-body--expanded-cell {
    background-color: #909399;
  }
}
</style>
