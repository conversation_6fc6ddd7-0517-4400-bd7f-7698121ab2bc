<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <!-- <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button> -->
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增
          </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template :item-render="{}">
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>

    </template>

    <vxe-table id='SymImportsordermodelsizeDetailTable' ref='master_table' height="auto" :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="groupCode" title="版型系列编码" width="100"> </vxe-table-column>
      <vxe-table-column field="groupName" title="版型系列名称" width="100"> </vxe-table-column>
      <!-- <vxe-table-column field="sizeColumnCode" title="规格编码" width="100"> </vxe-table-column>
      <vxe-table-column field="sizeColumnName" title="规格名称" width="100"> </vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column title="操作" width="100" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="版型系列" field="modelGroupID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.modelGroupID" filterable placeholder="规格字段" size="mini">
              <el-option v-for="item in ModelGroupComboStore" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'SymImportSorderModelSize',
  mixins: [detailTableMixins],

  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        importSorderBaseID: this.form.id,

        sort: 1,
        modelGroupID: null
      },
      formRules: {
        // code: [{ required: true, message: '请输入编码' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        modelGroupID: [{ required: true, message: '请绑定版型系列' }]
      },
      api: {
        get: '/mtm/sym_importsordermodelgroup/get',
        add: '/mtm/sym_importsordermodelgroup/adds',
        edit: '/mtm/sym_importsordermodelgroup/updates',
        delete: '/mtm/sym_importsordermodelgroup/deletes',
        ModelGroupComboStore: '/mtm/combo/ModelGroupComboStore'
      },
      ModelGroupComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelGroupComboStore).then(result => {
        this.ModelGroupComboStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
