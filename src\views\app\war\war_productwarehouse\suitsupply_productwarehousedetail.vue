<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="despatchFinished" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.despatchFinished" placeholder="是否提货" clearable>
                  <vxe-option v-for="item in boolList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="detailType" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.productWarehouseDetailType" placeholder="类别" clearable>
                  <vxe-option v-for="item in ProductWarehouseDetailTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarProductwarehousedetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientPersonName" title="顾客" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientShopCode" title="店铺" sortable width="100"></vxe-table-column>
      <vxe-table-column field="productWarehouseDetailTypeText" title="状态" sortable width="100"></vxe-table-column>
      <vxe-table-column field="despatchFinished" title="是否提货" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="logisticsCompany" title="快递公司" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipmentsNumber" title="发货单号" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <!-- <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <vxe-button status="success" @click="pickUpEvent(0)" content="普通提货">
          </vxe-button>
          <vxe-button status="warning" @click="pickUpEvent(5)" content="UPS提货"></vxe-button>
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'war_productwarehousedetail',
  mixins: [detailTableMixins],
  props: {
    selectRowIds: {
      required: true
    },
    selectType: {
      required: true,
      default: 1
    },
    success: {
      type: Function
    }
  },
  components: {
  },
  data () {
    return {
      searchForm: {
        iDs: this.selectRowIds,
        productWarehouseDetailType: 1,
        despatchFinished: false
      },
      pickUpType: 0, /// /0其他 5 UPS
      api: {
        get: '/mtm/war_productwarehousedetail/SuitSupplyStopGet',
        SuitSupplyStopOrderPickUp: '/mtm/war_productwarehousedetail/SuitSupplyStopOrderPickUp',
        ProductWarehouseDetailTypeComboStore: '/mtm/combo/ProductWarehouseDetailTypeComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'

      },
      clientComboStoreByQuery: [],
      ProductWarehouseDetailTypeComboStore: []
      //   ProductWarehouseDetailTypeComboStore: []
    }
  },

  async created () {
    this.loadData()
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ProductWarehouseDetailTypeComboStore).then(result => {
        this.ProductWarehouseDetailTypeComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    pickUpEvent (pickUpType) {
      const xTable = this.$refs[this.tableRef]
      var iputlist = xTable.getCheckboxRecords()
      if (iputlist.length <= 0) {
        this.$XModal.message({ message: '请选择要发货的订单', status: 'error' })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '提货中，请稍后！！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.$api.ActionRequest(this.api.SuitSupplyStopOrderPickUp, { type: pickUpType, list: iputlist }).then(result => {
        this.$XModal.message({ message: '提货成功', status: 'success' })
        this.loadData()
        this.success()
        loading.close()
      }).catch(() => {
        loading.close()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
