<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <!-- <vxe-form-item field="productionTeamID" :item-render="{}"><template #default>
              <el-select v-model="searchForm.productionTeamID" filterable placeholder="生产小组" size="mini" remote reserve-keyword plremoteaceholder="生产小组" :remote-method="remoteMethod1">
                <el-option v-for="item in productionTeamComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template></vxe-form-item> -->
            <vxe-form-item field="viewType" :item-render="{}"><template #default>
                <el-select v-model="searchForm.viewType" filterable placeholder="请选择类型" size="mini" clearable>
                  <el-option v-for="item in viewTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"><template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template></vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadProductionstationMasterTable' ref='master_table' :row-class-name="rowClassName" height="auto" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <!-- <vxe-table-column field="productionTeamCode" title="生产小组编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="productionTeamCodeName" title="生产小组名称" sortable width="100"> </vxe-table-column> -->
      <vxe-table-column field="code" title="编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column>

      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="viewTypeText" title="类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isShowSorderSize" title="显示规格？" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="mustScan" title="必须扫描？" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="imagePositionsText" title="工艺图片位置" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <!-- <vxe-form-item title="生产小组" field="productionTeamID" span="12"><template #default>
          <el-select v-model="selectRow.productionTeamID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="生产小组" :remote-method="remoteMethod1">
            <el-option v-for="item in productionTeamComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template></vxe-form-item> -->
        <!-- <vxe-form-item title="类型" field="itemClassID" span="12"><template #default>
          <el-select v-model="selectRow.itemClassID" filterable placeholder="请选择类型" size="mini">
            <el-option v-for="item in ItemClassComboStore" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template></vxe-form-item> -->
        <vxe-form-item title="页面类型" field="viewType" span="12"><template #default>
            <el-select v-model="selectRow.viewType" filterable placeholder="请选择类型" size="mini" clearable>
              <el-option v-for="item in viewTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="是否显示规格" field="isShowSorderSize" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="工艺图片位置" field="imagePositions" :item-render="{}" span="24">
          <template #default>
            <el-select v-model.trim="selectRow.imagePositions" placeholder="位置" multiple clearable size="mini" style="width:90%">
              <el-option v-for="item in ImagePositionComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="必须扫描?" field="mustScan" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="8" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='50%'>
      <detail-table :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import { cloneDeep } from 'lodash'
import DetailTable from './detail'
export default {
  name: 'bad_productionstation',
  mixins: [masterTableMixins],
  components: {
    DetailTable
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        sort: 999,
        isActive: true,
        viewType: null,
        isShowSorderSize: true,
        mustScan: true,
        imagePositions: []
        // productionTeamID: null
      },
      formRules: {
        productionTeamID: [{ required: true, message: '请选择生产小组' }],
        isShowSorderSize: [{ required: true, message: '规格显示不能不能为空' }],
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      api: {
        get: '/mes/bad_productionstation/get',
        add: '/mes/bad_productionstation/adds',
        edit: '/mes/bad_productionstation/updates',
        delete: '/mes/bad_productionstation/deletes',
        viewTypeComboStore: '/mes/combo/viewTypeComboStore',
        ImagePositionComboStore: '/mes/combo/ImagePositionComboStore'
        // productionTeamComboStoreByQuery: '/mes/comboQuery/productionTeamComboStoreByQuery'
      },
      viewTypeComboStore: [],
      ImagePositionComboStore: [],
      // productionTeamComboStoreByQuery: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.viewTypeComboStore).then(result => {
        this.viewTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ImagePositionComboStore).then(result => {
        this.ImagePositionComboStore = result
      })
    }
    // remoteMethod1 (query) {
    //   this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery, { text: query }).then(result => {
    //     this.productionTeamComboStoreByQuery = result
    //   })
    // },
    // 编辑
    // async editEvent (row) {
    //   await this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery, { gid: row.productionSeriesID }).then(result => {
    //     this.productionTeamComboStoreByQuery = result
    //     this.selectRow = cloneDeep(row)
    //     this.showEdit = true
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
