<template>
  <d2-container>
    <div slot="header" class="clearfix">
      <span>基础信息</span>&nbsp;&nbsp;
      <span style="font-size: 16px;font-weight: 800;">{{activeModel.modelText}}</span>
      <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
    </div>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">添加一行</vxe-button>
          <vxe-button status="warning" @click="SorderDetailSizeChecksEvent">批量检验</vxe-button>
          <vxe-button status="danger" @click="showUpLoad">导入数据</vxe-button>
          <vxe-button status="success" content="导出模板(包含数据)" @click="exportClick(true)"></vxe-button>
          <vxe-button status="warning" @click="saveAllData">保存 </vxe-button>
          <vxe-button status="success" @click="clientPersonShow=!clientPersonShow">新增顾客</vxe-button>
          <vxe-button status="danger" @click="deleteAllEvent">删除当前页</vxe-button>
        </template>
        <template v-slot:tools>

          <el-input placeholder="请输入顾客/尺码/客户订单号" v-model.trim="searchForm.text" class="input-with-select" size="mini" clearable>
            <el-button slot="append" icon="el-icon-search " @click="load"></el-button>
          </el-input>
          <!-- <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="load">
              </vxe-button> -->
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table style="width: 100%;" :loading="tableLoading" border resizable keep-source show-overflow height="auto" ref="master_table" @cell-click='tableCellClick' :row-class-name="rowClassName" :data="tableData" :edit-config="{ trigger: 'click', mode: 'cell', autoClear: false,showStatus: true, }">>
      <vxe-table-column type="radio" width="60"></vxe-table-column>
      <vxe-table-column field="lineNum" title="行号" width="60"></vxe-table-column>
      <vxe-table-column field="clientPersonID" title="顾客" :edit-render="{autofocus: '.custom-input'}">
        <template #edit="{ row }">
          <client-person-select :sorderStore="sorderStore" :detailrow="row" @setClientPerson="setClientPerson" />
        </template>
        <template #default="{ row }">{{ row.clientPersonText }}</template>
      </vxe-table-column>
      <vxe-table-column field="height" title="身高" :edit-render="{name: '$input', props: {type: 'float',clearable:true}}"></vxe-table-column>
      <vxe-table-column field="sizeID1" title="尺码" :edit-render="{autofocus: '.custom-input'}">
        <template #edit="{ row }">
          <size-select :sorderStore="sorderStore" :detailrow="row" @setSize="setSize" />
        </template>
        <template #default="{ row }">{{ row.sizeIDText }}</template>
      </vxe-table-column>
      <vxe-table-column field="qty" title="数量" :edit-render="{name: '$input', props: {type: 'number',min:'1'}}"></vxe-table-column>
      <vxe-table-column field="customerNumber" title="客户订单号" :edit-render="{name: '$input', props: {clearable:true}}"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" :edit-render="{name: '$input', props: {clearable:true}}"></vxe-table-column>
      <!-- <vxe-table-column field="date13" title="Date" :edit-render="{name: '$input', props: {type: 'date'}}"></vxe-table-column> -->
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed'>
        <template #default="{ row }">
          <template>
            <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)"> </vxe-button>
            <vxe-button type="text" icon="fa fa-files-o" @click="deepCopyEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
          </template>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
    <el-dialog title="导入表格" :visible.sync="showUpLoadShow" width="30%" :before-close="handleClose" :append-to-body="true" :destroy-on-close="true">
      <up-load-excel v-if="showUpLoadShow" :upLoadResult="upLoadResult" :SorderID="sorderStore.id" :SorderProModelID="activeModel.id" :ClientID="sorderStore.clientID" :UserId="info.userid" />
    </el-dialog>
    <vxe-modal v-model="clientPersonShow" title="顾客" width="800" resize destroy-on-close transfer>
      <vxe-form :data="clientPsersonSelectRow" :rules="personValidRules" title-align="right" title-width="100" @submit="ClientPersonSubmitEvent">
        <vxe-form-item title="顾客编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顾客名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>

        <vxe-form-item title="身高" field="height" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="体重" field="weight" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>

        <vxe-form-item title="手机号" field="tel" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="性别" field="gender" span="12" :item-render="{name: '$switch',props:{openLabel:'男',closeLabel:'女'}}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template></vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import ClientPersonSelect from './clientpersonselect'
import SizeSelect from './sizeselect'
import UpLoadExcel from './uploadexcel.vue'
import { mapState } from 'vuex'
export default {
  name: 'TablePersonDataEdit',
  mixins: [masterTableMixins],
  components: {
    ClientPersonSelect,
    SizeSelect,
    UpLoadExcel
  },
  props: {
    activeModel: {
      type: Object,
      required: true
    },
    sorderStore: {
      type: Object
    },
    psersonheight: {
      type: String
    }
  },
  data () {
    return {
      mtmpai: process.env.VUE_APP_API,
      showUpLoadShow: false,
      tableData: [

      ],
      sexList: [
        { label: '女', value: '0' },
        { label: '男', value: '1' }
      ],
      searchForm: {
        sorderId: this.sorderStore.id,
        modelId: this.activeModel.modelId
      },
      api: {
        clientPsersonAdd: '/mtm/bAD_ClientPerson/adds',
        get: '/mtm/oDM_SorderDetail/getPro',
        edit: '/mtm/oDM_SorderDetail/modifyPro',
        edits: '/mtm/oDM_SorderDetail/modifysPro',
        delete: '/mtm/oDM_SorderDetail/deletePro',
        deepclone: '/mtm/oDM_SorderDetail/DeepClone',
        sorderProExportAsync: '/fs/export/sorderpro/excel',
        SorderSizeTypeComboStore: '/mtm/combo/SorderSizeTypeComboStore',
        checkSizes: '/mtm/oDM_SorderDetail/CheckAllDetailSizes'
      },
      SorderSizeTypeComboStore: [],
      clientPersonShow: false,
      clientPsersonSelectRow: {
        id: null,
        code: null,
        tel: null,
        codeName: null,
        height: null,
        weight: null,
        gender: true,
        clientID: this.sorderStore.clientID
      },
      personValidRules: {
        code: [
          { required: true, message: '顾客编码必须填写' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }
        ],
        codeName: [
          { required: true, message: '顾客名称必须填写' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }
        ],
        height: [
          { required: true, message: '身高必须填写' }
        ]
        // weight: [
        //   { required: true, message: '体重必须填写' }
        // ]
        // tel: [
        //   { required: true, message: '手机号必须填写' },
        //   { message: '请填写正确11位手机号', type: 'string', pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/ }
        // ]
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  async created () {
    await this.getCombStore()
    this.loadData().then((res) => {
      this.$nextTick(() => {
        var row = this.tableData[0]
        this.$refs.master_table.setRadioRow(row)
        this.selectRow = row
        this.setRadioRow()
      })
    })
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SorderSizeTypeComboStore).then(result => {
        this.SorderSizeTypeComboStore = result
      })
    },
    load () {
      this.loadData().then((res) => {
        this.$nextTick(() => {
          var row = this.tableData[0]
          this.setCheck(row)
        })
      })
    },
    deepCopyEvent (row) {
      this.tableLoading = true
      this.$api.ActionRequest(this.api.deepclone, row).then(result => {
        this.$XModal.message({ message: '复制成功', status: 'success' })
        this.tableLoading = false
        this.loadData()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    saveRowEvent (row) {
      const $table = this.$refs.master_table
      $table.clearActived().then(() => {
        this.modify(row).then(() => {
          this.selectRow = row
          this.setCheck(row)
        })
      })
    },
    editRowEvent (row) {
      const $table = this.$refs.master_table
      $table.setActiveRow(row)
    },
    cancelRowEvent (row) {
      const $table = this.$refs.master_table
      $table.clearActived().then(() => $table.revertData(row))
    },
    insertEvent () {
      var dto = { sorderId: this.activeModel.sorderId, modelId: this.activeModel.modelId, height: 0, qty: 1 }
      this.saveAllData().then(() => {
        this.modify(dto).then(res => {

        })
      })
    },
    modify (data) {
      // this.tableLoading = true
      return new Promise((resolve, reject) => {
        this.$api.ActionRequest(this.api.edit, data).then(result => {
          // this.tableData = result.items
          // this.searchForm.totalCount = result.totalCount
          // this.tableLoading = false
          this.loadData()
          resolve()
        }).catch(() => {
          // this.tableLoading = false
        })

        // this.tableLoading = false
      })
    },
    saveAllData () {
      return new Promise((resolve, reject) => {
        const $table = this.$refs.master_table
        const updateRecords = $table.getUpdateRecords()
        if (updateRecords.length === 0) {
          return resolve()
        }
        this.tableLoading = true
        this.$api.ActionRequest(this.api.edits, updateRecords).then(res => {
          this.loadData()
          this.tableLoading = false
          resolve()
        }).catch(error => {
          this.tableLoading = false
          reject(error)
        })
      })
    },
    setClientPerson ({ detailrow, person }) {
      detailrow.clientPersonID = person.id
      detailrow.clientPersonText = person.label
      detailrow.height = person.height
    },
    setRadioRow () {
      this.$emit('selectDetail', { data: this.selectRow })
    },
    setCheck (row) {
      // setTimeout(() => {
      // this.$nextTick(() => {
      if (!this.$refs.master_table.isCheckedByRadioRow(row)) {
        // console.log('没有选中')
        // this.$refs.master_table.setRadioRow(row)
      } else {
        // console.log('选中')
      }
      this.$refs.master_table.setRadioRow(row)
      this.$refs.master_table.setCurrentRow(row)
      this.$refs.master_table.setRadioRow(row).then(() => {
        this.selectRow = row
        this.setRadioRow()
      })
      // })
      // }, 200)
    },
    tableCellClick ({ column, row }) {
      if (column.title !== '操作') {
        this.setCheck(row)
      }
    },
    setSize ({ detailrow, sizeRow }) {
      console.log(sizeRow)
      if (sizeRow == null) {
        detailrow.sizeID1 = null
        detailrow.sizeText = null
      } else {
        detailrow.sizeID1 = sizeRow.id
        detailrow.sizeText = sizeRow.code
        detailrow.sizeIDText = sizeRow.code
      }
    },
    deleteAllEvent () {
      this.$XModal.confirm({ content: '您确定要删除该数据?', transfer: true, zIndex: 9999 }).then(type => {
        if (type === 'confirm') {
          this.tableLoading = true
          this.$api.ActionRequest(this.api.delete, this.tableData).then(result => {
            this.tableLoading = false
            this.$XModal.message({ message: '删除成功', status: 'success' })
            this.loadData()
          }).catch(() => {
            this.tableLoading = false
          })
        }
      })
    },
    rowClassName ({ row, rowIndex }) {
      // var sizeCheckClass = ''
      // if (!row.isChecked) {
      //   sizeCheckClass = 'sizeCheckError'
      // }
      // return sizeCheckClass
    },
    showUpLoad () {
      this.showUpLoadShow = true
    },
    handleClose (done) {
      this.showUpLoadShow = false
      done()
    },
    upLoadResult ({ success, info, error }) {
      console.log(success, info, error)
      var message = '导入成功'
      var su = 'success'
      if (success) {
        su = 'success'
        if (info !== null && info !== '') {
          message = info
        }
        this.load()
        this.showUpLoadShow = false
      } else {
        su = 'error'
        message = error
      }
      this.$message({
        dangerouslyUseHTMLString: true,
        showClose: true,
        type: su,
        message: message,
        center: true,
        duration: 1000 * 5,
        customClass: 'importrResult'
      })
    },
    exportClick () {
      console.log(1)
      const loading = this.$loading({
        lock: true,
        text: '导出中请稍等',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      var api = this.mtmpai.replace('/api/', '')
      var url = api + this.api.sorderProExportAsync
      // console.log(url)
      this.$api.ActionExcelRequest(url, { sorderID: this.sorderStore.id, hasData: true }).then(res => {
        const url = window.URL.createObjectURL(res) // 创建一个新的 URL 对象
        // console.log(url)
        // 以下代码一句话解释，在页面上生成一个a标签并指定href为上面的url,然后模拟点击，以实现自动下载
        var a = document.createElement('a')
        document.body.appendChild(a)
        a.href = url
        a.download = `订单[${this.sorderStore.code}]-大批量.xlsx`
        a.click()
        window.URL.revokeObjectURL(url)
        loading.close()
      }).catch(() => {
        loading.close()
      })
    },
    ClientPersonSubmitEvent () {
      this.$api.ActionRequest(this.api.clientPsersonAdd, [this.clientPsersonSelectRow]).then(result => {
        this.clientPersonShow = false
        this.$XModal.message({ message: '新增成功', status: 'success' })
        this.$utils.clear(this.clientPsersonSelectRow, null)
        this.clientPsersonSelectRow.clientID = this.sorderStore.clientID
        this.clientPsersonSelectRow.gender = true
        if (result[0]) {
          this.modify({ sorderId: this.activeModel.sorderId, modelId: this.activeModel.modelId, clientPersonID: result[0].id, height: result[0].height, qty: 1 })
        }
      })
    },
    async SorderDetailSizeChecksEvent () {
      // const xTable = this.$refs.master_table
      // console.log(this.tableData)
      const loading = this.$loading({
        lock: true,
        text: '根据顾客数量的多少,计算花费时间也不同,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      await this.$api.ActionRequest(this.api.checkSizes, this.tableData).then(res => {
        this.$message({ message: '批量检验完成', type: 'success' })
        this.loadData()
        loading.close()
      }).catch(() => {
        loading.close()
      })
    }
  }
}
</script>

<style lang="scss" >
.sorderprodetail {
  .sizeCheckError {
    background-color: #a47e0d;
    // color: cornsilk;
  }
}
</style>
