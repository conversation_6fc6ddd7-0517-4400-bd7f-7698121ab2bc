<template>
  <d2-container class="portrait1">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button status="warning" @click="print" icon="vxe-icon--print">打印</vxe-button>
          <vxe-button :content="tablename" status="warning"  disabled></vxe-button>
        </template>
      </vxe-toolbar>

    </template>
    <div id="reportfrom" class="reportfrom">
      <div class="root">
        <pdf ref="pdf" :src="pdfurl"></pdf>
      </div>
    </div>
  </d2-container>
</template>

<script>
import reportfrommix from './reportfrom'
export default {
  mixins: [reportfrommix],
  data () {
    return {
      apiurl: '/fs/print/reportfrom2/pdf',
      apiurl2: '/fs/print/reportfrom2_1/pdf',
      apiurl3: '/fs/print/reportfrom2_2/pdf'
    }
  },
  computed: {
    pdfurl () {
      var api = this.mtmpai.replace('/api/', '')
      var _url = this.apiurl
      if (this.type === 2) {
        _url = this.apiurl2
      }
      if (this.type === 3) {
        _url = this.apiurl3
      }
      var url = api + _url + '?id=' + this.form.id + '&.pdf'
      return url
    }
  }
}
</script>

<style  lang="scss" >
.portrait1 {
  @page {
    /* size: 21cm 29.7cm; */
    font-size: 14pt;
    font-family: SimSun, 宋体, serif;
    color: black;
    line-height: 1.4;
    text-align: justify;
    margin: 0;
    padding: 0;
  }
  .root {
    /*需要将预览显示的界面限定在A4大小*/
    width: 340mm;
    /*这个高度为什么不是A4的大小，是经过N次验证的方式得到的，唯一的目的就是为了保证预览和打印预览一致*/
    /*可能是我写的有一点问题，但是如果设置为297，那么显示就会出现问题*/
    height: 241mm;
    /*上下不要设置padding,否则打印预览下面的footer就会往上走*/
    padding: 0 0 0 0;
    /* margin-bottom: 24mm; */
    background-color: white;
  }
  /*下面两个样式是为了保证屏幕上预览和打印预览一致*/
  @media screen {
    .content {
      width: 340mm;
      height: 241mm;
      /* padding-top:12mm; */
      display: flex;
      flex-direction: column;
    }
  }
  @media print {
    .content {
      width: 340mm;
      height: 241mm;
      padding-top: 18mm;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
