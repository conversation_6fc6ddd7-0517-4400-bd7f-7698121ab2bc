
// ---------------String begin------------------

/// /两种调用方式
// var template1 = "我是{0}，今年{1}了";
// var template2 = "我是{name}，今年{age}了";
// var result1 = template1.format("loogn", 22);
// var result2 = template2.format({ name: "loogn", age: 22 });
/// /两个结果都是"我是loogn，今年22了"
String.prototype.format = function (args) {
  var result = this
  if (arguments.length > 0) {
    if (arguments.length == 1 && typeof (args) === 'object') {
      for (var key in args) {
        var reg = new RegExp('({' + key + '})', 'g')
        // console.log(key + "---" + reg + "=---" + args[key]);
        result = result.replace(reg, args[key] == undefined ? '' : args[key])
      }
    } else {
      for (var i = 0; i < arguments.length; i++) {
        if (arguments[i] != undefined) {
          // var reg = new RegExp("({[" + i + "]})", "g");//这个在索引大于9时会有问题，谢谢何以笙箫的指出
          var reg = new RegExp('({)' + i + '(})', 'g')
          result = result.replace(reg, arguments[i])
        }
      }
    }
  }
  return result
}

// 以一特定规律组成的字符串里是否包含val,splitLim默认为,或;或，或；或、
String.prototype.isExistInSplit = function (val, splitLim) {
  if (splitLim == undefined) {
    splitLim = /,|;|，|；|、/gi
  }
  var arr = this.split(splitLim)
  var ind = arr.indexOf(val)
  return ind > -1
}

// ---------------String end------------------------------------------

// ---------------Array begin（Array中包含对Map的定义）-------------------

/* ie8下不支持indexof、forEach、filter,自己扩展。 */
if (!Array.prototype.indexOf) {
  Array.prototype.indexOf = function (elt /*, from */) {
    var len = this.length >>> 0

    var from = Number(arguments[1]) || 0
    from = (from < 0) ? Math.ceil(from) : Math.floor(from)
    if (from < 0) { from += len }

    for (; from < len; from++) {
      if (from in this &&
                this[from] === elt) { return from }
    }
    return -1
  }
}
if (!Array.prototype.forEach) {
  Array.prototype.forEach = function (fn, thisObj) {
    var scope = thisObj || window
    for (var i = 0, j = this.length; i < j; ++i) {
      fn.call(scope, this[i], i, this)
    }
  }
}
if (!Array.prototype.filter) {
  Array.prototype.filter = function (fn, thisObj) {
    var scope = thisObj || window
    var a = []
    for (var i = 0, j = this.length; i < j; ++i) {
      if (!fn.call(scope, this[i], i, this)) {
        continue
      }
      a.push(this[i])
    }
    return a
  }
}

if (!Array.prototype.map) {
  Array.prototype.map = function (fn, thisObj) {
    var scope = thisObj || window
    var a = []
    for (var i = 0, j = this.length; i < j; ++i) {
      var r = fn.call(scope, this[i], i, this)
      if (!r) {
        continue
      }
      a.push(r)
    }
    return a
  }
}
if (!Array.prototype.distinct) {
  Array.prototype.distinct = function () {
    return this.reduce(function (new_array, old_array_value) {
      if (new_array.indexOf(old_array_value) == -1) new_array.push(old_array_value)
      return new_array // 最终返回的是 prev value 也就是recorder
    }, [])
  }
}

// 返回数组中所有某个属性（propertyName）的属性值在min和max之间的元素。
Array.prototype.PropertyValueBetween = function (propertyName, min, max) {
  // 参数正确性。
  if (propertyName == null || propertyName.length == 0) {
    throw ('属性名称不能为空！')
  }
  if (parseFloat(min) > parseFloat(max)) {
    throw ('最大值不能小于等于最小值！')
  }

  // 返回结果
  var resultArr = new Array()

  var self = this
  for (var i = 0; i < self.length; i++) {
    var obj = self[i]
    if (typeof (obj[propertyName]) !== 'function') {
      if (min == max) {
        if (parseFloat(obj[propertyName]) == min) {
          resultArr.push(obj)
        }
      } else if (parseFloat(obj[propertyName]) >= min && parseFloat(obj[propertyName]) <= max) {
        resultArr.push(obj)
      }
    }
  }

  return resultArr
}

function ValidProperty (propertyName, value) {
  // 参数正确性。
  if (propertyName == null || propertyName.length == 0) {
    console.log(propertyName)
    console.log(value)
    throw ('属性名称不能为空！')
  }
  if (value == null || value == undefined || value.length == 0) {
    console.log(propertyName)
    console.log(value)
    throw ('属性值不能为空！')
  }
}
// 数组中元素的名为propertyName的属性的包含value则返回。
Array.prototype.PropertyValueLike = function (propertyName, value) {
  ValidProperty(propertyName, value)
  // 返回结果
  var resultArr = new Array()

  var self = this
  for (var i = 0; i < self.length; i++) {
    var obj = self[i]
    if (typeof (obj[propertyName]) !== 'string') {
      break
    }
    if (obj[propertyName].indexOf(value) > -1) {
      resultArr.push(obj)
    }
  }
  return resultArr
}

// 数组中元素的名为propertyName的属性的包含value则返回。
Array.prototype.PropertyValueEqual = function (propertyName, value) {
  ValidProperty(propertyName, value)
  // 返回结果
  var resultArr = new Array()

  var self = this
  for (var i = 0; i < self.length; i++) {
    var obj = self[i]
    // ie8下最后一个元素为undefined
    if (obj == undefined) {
      continue
    }
    if (typeof (obj[propertyName]) === 'function') {
      break
    }
    if (obj[propertyName] == value) {
      resultArr.push(obj)
    }
  }
  return resultArr
}

// 数组中是否存在属性名为propertyName属性值为value的元素。
Array.prototype.ElementExist = function (propertyName, value) {
  var self = this
  var arr = self.PropertyValueEqual(propertyName, value)
  if (arr.length > 0) {
    return true
  } else {
    return false
  }
}
// 按属性在数组查找唯一元素，如果没找到或找到多个都返回undefined。
Array.prototype.GetOnlyElement = function (propertyName, value) {
  var self = this
  var arr = self.PropertyValueEqual(propertyName, value)
  if (arr.length == 1) {
    return arr[0]
  } else {

  }
}

// 按属性在数组查找符合条件的元素，并返回第一个，如果没找到或找到多个都返回undefined。
Array.prototype.GetFirstElement = function (propertyName, value) {
  var self = this
  var arr = self.PropertyValueEqual(propertyName, value)
  if (arr.length > 0) {
    return arr[0]
  } else {

  }
}

// 数组中元素的名为propertyName的属性的包含value则返回。
Array.prototype.ElementReplace = function (propertyName, value, newobj) {
  ValidProperty(propertyName, value)
  var self = this
  for (var i = 0; i < self.length; i++) {
    var obj = self[i]
    // ie8下最后一个元素为undefined
    if (obj == undefined) {
      continue
    }
    if (typeof (obj[propertyName]) === 'function') {
      break
    }
    if (obj[propertyName] == value) {
      self[i] = newobj
      // 此处可用$.extend合并 self[i] 和 newobj，不过要依赖于juqery.
    }
  }
}

// 按属性找出某实体在数组中位置。
Array.prototype.myIndexOf = function (propertyName, value) {
  ValidProperty(propertyName, value)
  var self = this
  var ind = -1
  for (var i = 0; i < self.length; i++) {
    var obj = self[i]
    // ie8下最后一个元素为undefined
    if (obj == undefined) {
      continue
    }
    if (typeof (obj[propertyName]) === 'function') {
      break
    }
    if (obj[propertyName] == value) {
      ind = i
      break
    }
  }
  return ind
}
// 按某个属性删除元素。
Array.prototype.RemoveElement = function (propertyName, value) {
  ValidProperty(propertyName, value)
  var self = this
  var ind = self.myIndexOf(propertyName, value)
  if (ind > -1) {
    return self.splice(ind, 1)
  }
  return self
}

Array.prototype.contains = function (item) {
  for (var i = 0; i < this.length; i++) {
    if (this[i] == item) {
      return true
    }
  }
  return false
}
Array.prototype.equals = function (array) {
  return this.sort().toString() == array.sort().toString()
}
Array.prototype.del = function (item) {
  for (var i = 0; i < this.length; i++) {
    if (this[i] == item) {
      this.splice(i, 1)
      break
    }
  }
}
Array.prototype.dels = function (array) {
  var tempData = {}

  for (var i = 0; i < (array || []).length; i++) {
    tempData[array[i]] = true
  }

  var tempArray = []

  for (var i = 0; i < this.length; i++) {
    if (!tempData[this[i]]) {
      tempArray.push(this[i])
    }
  }

  return tempArray
}
Array.prototype.unique = function () {
  var array = []
  var tempdata = {}
  for (var i = 0, item; (item = this[i]) != null; i++) {
    if (!tempdata[item]) {
      array.push(item)
      tempdata[item] = true
    }
  }
  return array
}
// 去除数组重复数据
Array.prototype.uniquetwo = function () {
  var res = [this[0]]
  for (var i = 1; i < this.length; i++) {
    var repeat = false
    for (var j = 0; j < res.length; j++) {
      if (this[i] == res[j]) {
        repeat = true
        break
      }
    }
    if (!repeat) {
      res.push(this[i])
    }
  }
  return res
}

/*
* MAP对象，实现MAP功能
*
* 接口：
* size()     获取MAP元素个数
* isEmpty()    判断MAP是否为空
* clear()     删除MAP所有元素
* put(key, value)   向MAP中增加元素（key, value)
* remove(key)    删除指定KEY的元素，成功返回True，失败返回False
* get(key)    获取指定KEY的元素值VALUE，失败返回NULL
* element(index)   获取指定索引的元素（使用element.key，element.value获取KEY和VALUE），失败返回NULL
* containsKey(key)  判断MAP中是否含有指定KEY的元素
* containsValue(value) 判断MAP中是否含有指定VALUE的元素
* values()    获取MAP中所有VALUE的数组（ARRAY）
* keys()     获取MAP中所有KEY的数组（ARRAY）
*
* 例子：
* var map = new Map();
*
* map.put("key", "value");
* var val = map.get("key")
* ……
*
*/
function Map () {
  this.elements = new Array()

  // 获取MAP元素个数
  this.size = function () {
    return this.elements.length
  }

  // 判断MAP是否为空
  this.isEmpty = function () {
    return (this.elements.length < 1)
  }

  // 删除MAP所有元素
  this.clear = function () {
    this.elements = new Array()
  }

  // 向MAP中增加元素（key, value)
  this.put = function (_key, _value) {
    this.remove(_key)
    this.elements.push({
      key: _key,
      value: _value
    })
  }

  // 删除指定KEY的元素，成功返回True，失败返回False
  this.remove = function (_key) {
    var bln = false
    try {
      for (i = 0; i < this.elements.length; i++) {
        if (this.elements[i].key == _key) {
          this.elements.splice(i, 1)
          return true
        }
      }
    } catch (e) {
      bln = false
    }
    return bln
  }

  // 获取指定KEY的元素值VALUE，失败返回NULL
  this.get = function (_key) {
    try {
      for (i = 0; i < this.elements.length; i++) {
        if (this.elements[i].key == _key) {
          return this.elements[i].value
        }
      }
    } catch (e) {
      return null
    }
  }

  // 获取指定索引的元素（使用element.key，element.value获取KEY和VALUE），失败返回NULL
  this.element = function (_index) {
    if (_index < 0 || _index >= this.elements.length) {
      return null
    }
    return this.elements[_index]
  }

  // 判断MAP中是否含有指定KEY的元素
  this.containsKey = function (_key) {
    var bln = false
    try {
      for (i = 0; i < this.elements.length; i++) {
        if (this.elements[i].key == _key) {
          bln = true
        }
      }
    } catch (e) {
      bln = false
    }
    return bln
  }

  // 判断MAP中是否含有指定VALUE的元素
  this.containsValue = function (_value) {
    var bln = false
    try {
      for (i = 0; i < this.elements.length; i++) {
        if (this.elements[i].value == _value) {
          bln = true
        }
      }
    } catch (e) {
      bln = false
    }
    return bln
  }

  // 获取MAP中所有VALUE的数组（ARRAY）
  this.values = function () {
    var arr = new Array()
    for (i = 0; i < this.elements.length; i++) {
      arr.push(this.elements[i].value)
    }
    return arr
  }

  // 获取MAP中所有KEY的数组（ARRAY）
  this.keys = function () {
    var arr = new Array()
    for (i = 0; i < this.elements.length; i++) {
      arr.push(this.elements[i].key)
    }
    return arr
  }
}

// ---------------Array end-------------------

// ---------------Date begin-------------------

// -----------------------------------
// 时间字符串转为为时间格式
// -----------------------------------
function getDate (strDate) {
  if (strDate instanceof Date) {
    return strDate
  }
  if (strDate.indexOf('/Date(') > -1) {
    // 毫秒数转时间。
    strDate = parseInt(strDate.replace('/Date(', '').replace(')/', ''))
  } else {
    // 时间格式转换。
    strDate = strDate.replace(/-/g, '/')
  }
  var date = new Date(strDate)
  return date
}

// ---------------------------------------------------
// 求两个时间的天数差.参数：dateOne, dateTwo可以日期格式，也可以是有效日期的字符串。
// ---------------------------------------------------
function millisecondsBetween (dateOne, dateTwo) {
  var toS = Object.prototype.toString
  // 如果不是日期格式，转为日期格式。
  if (toS.call(dateOne) != '[object Date]') {
    dateOne = dateOne.replace(/-/g, '/')
    dateOne = Date.parse(dateOne)
  }
  if (toS.call(dateTwo) != '[object Date]') {
    dateTwo = dateTwo.replace(/-/g, '/')
    dateTwo = Date.parse(dateTwo)
  }
  var cha = dateTwo - dateOne
  return cha
}
// 两个时间相差的秒数
function secondsBetween (dateOne, dateTwo) {
  return millisecondsBetween(dateOne, dateTwo) / 1000
}
// 两个时间相差的分钟数
function minutesBetween (dateOne, dateTwo) {
  return millisecondsBetween(dateOne, dateTwo) / 60000
}
// 两个时间相差的小时数
function hoursBetween (dateOne, dateTwo) {
  return millisecondsBetween(dateOne, dateTwo) / 3600000
}
// 两个时间相差的天数
function daysBetween (dateOne, dateTwo) {
  // 86400000 一天的毫秒数
  return millisecondsBetween(dateTwo - dateOne) / 86400000
}
// 两日期之间的月份差。
function monthsBetween (dateOne, dateTwo) {
  // 按30天计算
  return millisecondsBetween(dateOne, dateTwo) / 2592000000
}

// 两日期之间的年份差。
function yearsBetween (dateOne, dateTwo) {
  // 按365天计算。
  return millisecondsBetween(dateOne, dateTwo) / 31536000000
}

// ---------------------------------------------------
// 日期格式化
// 格式 YYYY/yyyy/YY/yy 表示年份
// MM/M 月份
// W/w 星期
// dd/DD/d/D 日期
// hh/HH/h/H 时间
// mm/m 分钟
// ss/SS/s/S 秒
// 可配合 Date.toLocaleString() Date.toLocaleTimeString() 使用。
// ---------------------------------------------------
Date.prototype.format = function (formatStr) {
  var str = formatStr
  var Week = ['日', '一', '二', '三', '四', '五', '六']

  str = str.replace(/yyyy|YYYY/, this.getFullYear())
  str = str.replace(/yy|YY/, (this.getYear() % 100) > 9 ? (this.getYear() % 100).toString() : '0' + (this.getYear() % 100))

  var month = this.getMonth() + 1
  str = str.replace(/MM/, month > 9 ? month.toString() : '0' + month)
  str = str.replace(/M/g, month)

  str = str.replace(/w|W/g, Week[this.getDay()])

  str = str.replace(/dd|DD/, this.getDate() > 9 ? this.getDate().toString() : '0' + this.getDate())
  str = str.replace(/d|D/g, this.getDate())

  str = str.replace(/hh|HH/, this.getHours() > 9 ? this.getHours().toString() : '0' + this.getHours())
  str = str.replace(/h|H/g, this.getHours())
  str = str.replace(/mm/, this.getMinutes() > 9 ? this.getMinutes().toString() : '0' + this.getMinutes())
  str = str.replace(/m/g, this.getMinutes())

  str = str.replace(/ss|SS/, this.getSeconds() > 9 ? this.getSeconds().toString() : '0' + this.getSeconds())
  str = str.replace(/s|S/g, this.getSeconds())

  return str
}

// ---------------------------------------------------
// --日期计算
// ---------------------------------------------------
// var date = new Date();
// date.setMonth(date.getMonth() - 1);

// ---------------------------------------------------
// 取得日期数据信息
// 参数 interval 表示数据类型
// y 年 m月 d日 w星期 ww周 h时 n分 s秒
// ---------------------------------------------------
Date.prototype.DatePart = function (interval) {
  var myDate = this
  var partStr = ''
  var Week = ['日', '一', '二', '三', '四', '五', '六']
  switch (interval) {
    case 'y': partStr = myDate.getFullYear(); break
    case 'm': partStr = myDate.getMonth() + 1; break
    case 'd': partStr = myDate.getDate(); break
    case 'w': partStr = Week[myDate.getDay()]; break
    case 'ww': partStr = myDate.WeekNumOfYear(); break
    case 'h': partStr = myDate.getHours(); break
    case 'n': partStr = myDate.getMinutes(); break
    case 's': partStr = myDate.getSeconds(); break
  }
  return partStr
}

// ---------------------------------------------------
// 取得当前日期所在月的最大天数
// ---------------------------------------------------
Date.prototype.MaxDayOfDate = function () {
  var myDate = this
  var ary = myDate.toArray()
  var date1 = (new Date(ary[0], ary[1] + 1, 1))
  var date2 = date1.dateAdd(1, 'm', 1)
  var result = dateDiff(date1.Format('yyyy-MM-dd'), date2.Format('yyyy-MM-dd'))
  return result
}
// ---------------------------------------------------
// 判断闰年
// ---------------------------------------------------
Date.prototype.isLeapYear = function () {
  return (this.getYear() % 4 == 0 && ((this.getYear() % 100 != 0) || (this.getYear() % 400 == 0)))
}

function getDays () {
  // 构造当前日期对象
  var date = new Date()

  // 获取年份
  var year = date.getFullYear()

  // 获取当前月份
  var mouth = date.getMonth() + 1

  // 定义当月的天数；
  var days

  // 当月份为二月时，根据闰年还是非闰年判断天数
  if (mouth == 2) {
    days = year % 4 == 0 ? 29 : 28
  } else if (mouth == 1 || mouth == 3 || mouth == 5 || mouth == 7 || mouth == 8 || mouth == 10 || mouth == 12) {
    // 月份为：1,3,5,7,8,10,12 时，为大月.则天数为31；
    days = 31
  } else {
    // 其他月份，天数为：30.
    days = 30
  }
  return days
}

// ---------------Date end-------------------

// ---------------HttpUtility begin-------------------

var HttpUtility = (function () {
  // 对象转换为url参数
  function toQueryString (obj) {
    var ret = []
    var values
    for (var key in obj) {
      key = encodeURIComponent(key)
      values = obj[key]
      if (values) {
        // 数组
        if (values.constructor == Array) {
          ret.push(key + '=' + encodeURIComponent(values.join(',')))
        }
        // 字符串
        else {
          ret.push(key + '=' + encodeURIComponent(values))
        }
      }
    }
    return ret.join('&')
  }
  // 按名称获取url中的参数
  function getUrlParamByName (name) {
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
    var txt = decodeURI(window.location.search)
    var r = txt.substr(1).match(reg)
    if (r != null) return unescape(r[2]); return null
    //
    //        //忽略参数大小写
    //        var reg = new RegExp("(^|&)" + name.toLowerCase() + "=([^&]*)(&|$)", "i");
    //        var txt = decodeURI(window.location.search);
    //        var r = txt.toLowerCase().substr(1).match(reg);
    //        if (r != null) return unescape(r[2]); return null;
  }
  // 获取文件扩展名，filePath文件网络地址。
  function getFileExtension (filePath) {
    var index = filePath.indexOf('?')
    if (index != -1) {
      filePath = filePath.substring(0, index)
    }
    var ext = RegExp('\.[^\.]+$').exec(filePath) || {}
    return ext
  }

  var httpUtil = new Object()
  httpUtil.toQueryString = toQueryString
  httpUtil.getUrlParamByName = getUrlParamByName
  httpUtil.getFileExtension = getFileExtension

  return httpUtil
})()

// ---------------HttpUtility end-------------------

// ---------------ObjectTools begin-------------------
// 获取对象的类型的字符串。
function getType (o) {
  var _toS = Object.prototype.toString
  var _types = {
    undefined: 'undefined',
    number: 'number',
    boolean: 'boolean',
    string: 'string',
    '[object Function]': 'function',
    '[object RegExp]': 'regexp',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object Error]': 'error'
  }
  return _types[typeof o] || _types[_toS.call(o)] || (o ? 'object' : 'null')
}

// #region clone：拷贝一份数据对象
function clone (oglObject) {
  var bucket
  // if (oglObject instanceof Array)
  if (getType(oglObject) == 'array') {
    bucket = []
    var i = oglObject.length
    while (i--) {
      bucket[i] = clone(oglObject[i])
    }
    return bucket
  }
  // else if (oglObject instanceof Function)
  else if (getType(oglObject) == 'function') {
    return oglObject
  }
  // else if (oglObject instanceof Object)
  else if (getType(oglObject) == 'object') {
    bucket = {}
    for (var k in oglObject) {
      bucket[k] = clone(oglObject[k])
    }
    return bucket
  } else {
    return oglObject
  }
}
// JS验证空值
function IsNotNullOrEmpty (obj) {
  if (obj != undefined && obj != '' &&
    obj != 'null' && obj != null &&
    obj != 'undefined' && obj) {
    return false
  } else {
    return true
  }
}
function isNullOrEmpty (obj) {
  if (obj == null || obj == undefined || typeof obj === 'null' || typeof obj === 'undefined' || obj == '') {
    return true
  } else {
    return false
  }
}
// js验证数字正则表达式

function istrue (a) {
  var reg = /^(([a-z]+[0-9]+)|([0-9]+[a-z]+))[a-z0-9]*$/i
  return reg.test(a)
}

// 检查是否为正数
function isUnsignedNumeric (a) {
  var reg = /^d+(.d+)?$/
  return reg.test(a)
}

// 检查是否为正整数
function isUnsignedInteger (a) {
  var reg = /^d+$/
  return reg.test(a)
}

// 检查是否数字
function isNum (a) {
  var reg = /^d+(.d+)?$/
  reg.test(a)
}

// 检查是否整数
function isInt (a) {
  var reg = /^-?d+$/
  return reg.test(a)
}

function trimStart (val, c) {
  if (c == null || c == '') {
    var str = val.replace(/^s*/, '')
    return str
  } else {
    var rg = new RegExp('^' + c + '*')
    var str = val.replace(rg, '')
    return str
  }
}

// 相等，比较各类对象是否相等。
var ObjectEquals = {

  // Object
  obj: function (obj_1, obj_2) {
    if ((obj_1 === obj_2)) { return true }
    if (!(obj_2 instanceof Object) || (obj_2 === null)) { return false } // null is not instanceof Object.
    var i = 0 // object property counter.
    for (var k in obj_1) {
      i++
      var o1 = obj_1[k]
      var o2 = obj_2[k]
      // o1.equals(o2)
      if ((o1 != null) && !(this.json(o1, o2))) { return false } // inner object.
    }
    for (var k in obj_2) { // compare object property counter.
      i--
    }
    return i === 0
  },

  // Array
  arr: function (arr_1, arr_2) {
    if (arr_1 == null && arr_2 == null) {
      return true
    } else if (arr_1 != null && arr_2 == null) {
      return false
    } else if (arr_1 == null && arr_2 != null) {
      return false
    }
    if (!(arr_2 instanceof Array)) { return false }
    if (arr_1 === arr_2) { return true }
    var l = arr_1.length
    if (l != arr_2.length) { return false }
    for (var i = 0; i < l; i++) {
      var o1 = arr_1[i]
      var o2 = arr_2[i]
      // o1.equals(o2)
      if (!(o1 === null ? o2 === null : this.json(o1, o2))) { return false } // inner array.
    }
    return true
  },

  // String
  str: function (str_1, str_2) {
    return ((str_2 instanceof String) || (typeof (str_2) === 'string')) && (str_1.valueOf() === str_2.valueOf())
  },

  // Number
  num: function (num_1, num_2) {
    return ((num_2 instanceof Number) || (typeof (num_2) === 'number')) && (num_1.valueOf() === num_2.valueOf())
  },

  // Boolean
  bool: function (bool_1, bool_2) {
    return ((bool_2 instanceof Boolean) || (typeof (bool_2) === 'boolean')) && (bool_1.valueOf() === bool_2.valueOf())
  },

  // Date
  date: function (date_1, date_2) {
    return (date_2 instanceof Date) && (date_1.valueOf() === date_2.valueOf())
  },

  // Function
  fun: function (fun_1, fun_2) {
    return (fun_2 instanceof Function) && (fun_1.valueOf() === fun_2.valueOf())
  },

  // RegExp
  reg: function (reg_1, reg_2) {
    return (reg_2 instanceof RegExp) &&
            (reg_1.source === reg_2.source) &&
            (reg_1.global === reg_2.global) &&
            (reg_1.ignoreCase === reg_2.ignoreCase) &&
            (reg_1.multiline === reg_2.multiline)
  },

  // Json
  json: function (json_1, json_2) {
    var cons = json_1.constructor.toString()

    var type = (cons || '').replace(/function|native code|\(|\)|\{|\}|\[|\]|\n| /g, '')

    switch (type) {
      case 'Object':
        return this.obj(json_1, json_2)
        break

      case 'Array':
        return this.arr(json_1, json_2)
        break

      case 'String':
        return this.str(json_1, json_2)
        break

      case 'Number':
        return this.num(json_1, json_2)
        break

      case 'Boolean':
        return this.bool(json_1, json_2)
        break

      case 'Date':
        return this.date(json_1, json_2)
        break

      case 'Function':
        return this.fun(json_1, json_2)
        break

      case 'RegExp':
        return this.reg(json_1, json_2)
        break

      default:
        return this.obj(json_1, json_2)
        break
    }
  }
}

// ---------------ObjectTools end-------------------

// ---------------valid begin-------------------

// 表单验证。
var Valid = (function () {
  // 检查值是否是手机号码（11位数字）
  function checkPhoneNumber (value) {
    var reg = /^\d{11}$/
    if (reg.test(value)) {
      return true
    } else {
      return false
    }
  }

  var valid = new Object()
  valid.checkPhoneNumber = checkPhoneNumber
  return valid
})()

// ---------------valid end-------------------

// 对象转化为str
function ObjToString (o) {
  var r = []
  var otype = 'undefined'
  if (typeof o === 'string') {
    return '"' + o.replace(/(['\"\\])/g, '\\$1').replace(/(\n)/g, '\\n').replace(/(\r)/g, '\\r').replace(/(\t)/g, '\\t') + '"'
  }
  if (typeof o === 'undefined') {
    return otype
  }
  if (typeof o === 'object') {
    if (o === null) return 'null'
    else if (!o.sort) {
      for (var i in o) {
        r.push('\"' + i + '\":' + this.ObjToString(o[i]))
      }
      r = '{' + r.join() + '}'
    } else {
      for (var i = 0; i < o.length; i++) {
        r.push(this.ObjToString(o[i]))
      }
      r = '[' + r.join() + ']'
    }
    return r
  }
  return o.toString()
}
// 对象转化为str(特殊处理：给数字类型的值加上"").
function ObjToStringWithQuot (o) {
  var r = []
  var otype = 'undefined'
  if (typeof o === 'string') {
    return '"' + o.replace(/(['\"\\])/g, '\\$1').replace(/(\n)/g, '\\n').replace(/(\r)/g, '\\r').replace(/(\t)/g, '\\t') + '"'
  }
  if (typeof o === 'undefined') {
    return otype
  }
  if (typeof o === 'object') {
    if (o === null) return 'null'
    else if (!o.sort) {
      for (var i in o) {
        r.push('\"' + i + '\":' + this.ObjToStringWithQuot(o[i]))
      }
      r = '{' + r.join() + '}'
    } else {
      for (var i = 0; i < o.length; i++) {
        r.push(this.ObjToStringWithQuot(o[i]))
      }
      r = '[' + r.join() + ']'
    }
    return r
  }
  return '"' + o.toString() + '"'
}

function getQueryString (name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  var r = location.search.substr(1).match(reg)
  if (r != null) { return unescape(decodeURI(r[2])) }
  return null
}

function clearBackStorage () {
  var clearArray = new Array()
  for (var i = 0; i < sessionStorage.length; i++) {
    var name = sessionStorage.key(i)
    if (name.indexOf('historyUrl') !== -1) {
      clearArray.push(name)
    }
  }
  for (var j = 0; j < clearArray.length; j++) {
    sessionStorage.removeItem(clearArray[j])
  }
}

function defaultString (str, defaultStr) {
  if (isNullOrEmpty(str)) {
    return defaultStr
  }
  return str
}

// // 为每个页面增加后退功能
// function addGoBackEvent() {

//     var debug = true;

//     var currentLink = document.location.href;

//     //移除某些a自动加入的#；
//     if (currentLink.indexOf("#") == currentLink.length - 1) {
//         currentLink = currentLink.substring(0, currentLink.length - 1);
//     }
//     //    var currentLink = document.referrer;
//     if (currentLink == "" || currentLink == undefined) {
//         return;
//     }

//     var currentArrayName = "GoBackArray";
//     var currentArrayTemp = sessionStorage[currentArrayName];
//     var currentArray;

//     //后退栈堆
//     if (currentArrayTemp == undefined) {
//         currentArray = new Array();

//     } else {
//         currentArray = JSON.parse(currentArrayTemp);
//     }

//     //-----------------------------------
//     //判断当前页是否在栈堆中
//     //没有则加入栈堆
//     //如果存在栈堆中，则把后面push入的全部移除。
//     //-----------------------------------
//     var currentIndex = -1;  //得到插入重复值之前的最前页面的索引
//     currentArray.push(currentLink);

//     for (var i = 0; currentArray.length; i++) {
//         if (currentArray[i] == currentLink) {
//             currentIndex = i;
//             break;
//         }
//     }
//     //a b c d c d b a
//     if (debug) {
//         console.log("删除前: " + currentArray.length + "   " + JSON.stringify(currentArray));
//     }
//     for (var j = currentArray.length - 1; j > currentIndex; j--) {
//         var deleteLink = currentArray.pop();
//         //            if (BrowserSenser.isWeixin()) {
//         //                //微信替换路径
//         //                history.replaceState('', "", deleteLink);
//         //            }
//     }

//     sessionStorage.setItem(currentArrayName, JSON.stringify(currentArray));
//     if (debug) {
//         console.log("最终长度: " + currentArray.length + "   " + JSON.stringify(currentArray));
//     }

//     $('.icon-fanhuix').click(function () {
//         currentArray.pop(); //弹出当前页
//         var goLink = currentArray.pop();   //找到上一个页面
//         if (goLink == undefined) {
//             console.log("已经退到头了，无退可退 ");
//             return;
//         }
//         window.location.href = goLink;
//     });

// }

// var EventUtil = {
//   addHandler: function (element, type, handler) {
//     if (element.addEventListener) {
//       element.addEventListener(type, handler, false)
//     } else if (element.attachEvent) {
//       element.attachEvent('on' + type, handler)
//     } else {
//       element['on' + type] = handler
//     }
//   },
//   getEvent: function (event) {
//     return event || window.event
//   },
//   getClipboardText: function (event) {
//     var clipboardData = (event.clipboardData || window.clipboardData)
//     return clipboardData.getData('text')
//   },
//   setClipboardText: function (event, value) {
//     if (event.clipboardData) {
//       return event.clipboardData.setData('text/plain', value)
//     } else if (window.clipboardData) {
//       return window.clipboardData.setData('text', value)
//     }
//   },
//   preventDefault: function (event) {
//     if (event.preventDefault) {
//       event.preventDefault()
//     } else {
//       event.returnValue = false
//     }
//   }
// }

function realconsole () {
  alert('hello.thanks use me')
}

export {
  clone,
  realconsole,
  isNullOrEmpty,
  defaultString,
  trimStart
}
