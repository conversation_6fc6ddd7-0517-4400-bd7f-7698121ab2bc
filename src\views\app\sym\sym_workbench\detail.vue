<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="roleInsertEvent" v-if="menuAction.allowAdd&&form.receiverObject===1">新增角色</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="userInsertEvent" v-if="menuAction.allowAdd&&form.receiverObject===0">新增用户</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>

      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='SymWorkbenchMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="roleName" title="角色" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="userName" title="用户" sortable width="100"></vxe-table-column>

      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="50" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
    <vxe-modal v-model="userListShow" title="用户添加" width="65%" height="50%" resize destroy-on-close>
      <user-list :form="form" v-if="userListShow" @reload="reload" />
    </vxe-modal>
    <vxe-modal v-model="roleListShow" title="角色添加" width="65%" height="50%" resize destroy-on-close>
      <role-list :form="form" v-if="roleListShow" @reload="reload" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import RoleList from './role.vue'
import UserList from './user.vue'
export default {
  name: 'SYM_WorkbenchReceiverDetail',
  mixins: [detailTableMixins],
  components: {
    RoleList,
    UserList
  },
  data () {
    return {
      userListShow: false,
      roleListShow: false,
      api: {
        get: '/mtm/SYM_WorkbenchReceiverDetail/get',
        add: '/mtm/SYM_WorkbenchReceiverDetail/adds',
        delete: '/mtm/SYM_WorkbenchReceiverDetail/deletes'
      }
    }
  },
  async created () {
    this.loadData({ id: this.form.id })
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {

    },
    reload () {
      this.loadData()
      this.roleListShow = false
      this.userListShow = false
    },
    roleInsertEvent () {
      this.roleListShow = !this.roleListShow
    },
    userInsertEvent () {
      this.userListShow = !this.userListShow
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
