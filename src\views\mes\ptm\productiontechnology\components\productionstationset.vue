<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="page" @submit="load()">
            <vxe-form-item field="text" :item-render="{}"><template #default>
                <el-input v-model.trim="page.text" placeholder="编码/名称" suffix-icon="fa fa-search" size="mini" clearable> </el-input>
              </template></vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-pager @page-change='pageChange' background :current-page.sync="page.currentPage" :page-size.sync="page.pageSize" :total="page.totalResult" :layouts="['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', , 'FullJump', 'Total']">
    </vxe-pager>
    <div class="productionstationset">
      <template v-for="(item,index) in Data">
        <vxe-button :status="activebtncmp(item)" :key='index' @click="btnClick(item)">{{item.code}}:{{item.codeName}} </vxe-button>
      </template>
    </div>
    <template slot="footer">

    </template>
  </d2-container>
</template>

<script>
import { mapActions } from 'vuex'
import defaultset from './index'
export default {
  name: 'productionstationset',
  mixins: [defaultset],
  data () {
    return {
      Data: [],
      api: {
        get: '/mes/bad_productionstation/get'
      },
      btnsize: 'mini',
      activebtn: '',
      page: {
        currentPage: 1,
        pageSize: 50,
        totalResult: 0,
        hasViewType: true,
        skipCount: 0,
        maxResultCount: 50,
        text: null
      }
    }
  },
  computed: {
    // ...mapState('mes/ptm/productiontechnology/index', [
    //   'productstationconfig'
    // ]),

  },
  async created () {
    await this.load()
    this.activebtn = this.productstationconfig.id
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    ...mapActions('mes/ptm/productiontechnology/index', [
      'set'
    ]),
    activebtncmp (item) {
      if (this.activebtn === item.id) {
        return 'success'
      } else {
        return ''
      }
    },
    async load () {
      await this.$api.ActionRequest(this.api.get, this.page).then(result => {
        this.Data = result.items
        this.page.totalResult = result.totalCount
      })
    },
    // 分页发生改变时会触发该事件
    pageChange ({ currentPage, pageSize }) {
      this.page.maxResultCount = pageSize
      this.page.skipCount = (currentPage - 1) * pageSize
      this.page.currentPage = currentPage
      this.load()
    },
    btnClick (item) {
      this.$XModal.confirm('确定当前电脑设置为【' + item.codeName + '】工位吗？').then(async type => {
        if (type === 'confirm') {
          this.set({
            code: item.code,
            codeName: item.codeName,
            id: item.id,
            viewType: item.viewType,
            viewTypeStr: item.viewTypeStr,
            isShowSorderSize: item.isShowSorderSize
          })
        } else {
          this.$XModal.message({ message: '取消操作' })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.productionstationset {
  position: absolute;
  top: 35%;
  transform: translateY(-50%);
  width: 100%;
  text-align: center;
  color: #909399;
}
</style>
