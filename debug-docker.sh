#!/bin/bash

echo "=== Docker 配置调试脚本 ==="

# 检查必要文件是否存在
echo "1. 检查必要文件..."
files=("startup.sh" "Dockerfile" "public/index.html")
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
    fi
done

# 检查 startup.sh 权限
if [ -f "startup.sh" ]; then
    if [ -x "startup.sh" ]; then
        echo "✅ startup.sh 有执行权限"
    else
        echo "⚠️  startup.sh 没有执行权限，正在修复..."
        chmod +x startup.sh
    fi
fi

# 构建测试镜像
echo "2. 构建测试镜像..."
docker build -t frontend-debug:latest . 2>&1 | tee build.log

if [ ${PIPESTATUS[0]} -ne 0 ]; then
    echo "❌ 镜像构建失败，请检查 build.log"
    exit 1
fi

echo "✅ 镜像构建成功"

# 启动测试容器
echo "3. 启动测试容器..."
docker stop frontend-debug 2>/dev/null
docker rm frontend-debug 2>/dev/null

docker run -d --name frontend-debug -p 8080:80 \
  -e VUE_APP_USER_API="https://debug-sso-api.com/api/" \
  -e VUE_APP_API="https://debug-mtm-api.com/api/" \
  -e VUE_APP_MESAPI="https://debug-mes-api.com/api/" \
  -e VUE_APP_TITLE="调试标题" \
  frontend-debug:latest

sleep 3

# 检查容器状态
echo "4. 检查容器状态..."
if [ "$(docker ps -q -f name=frontend-debug)" ]; then
    echo "✅ 容器启动成功"
else
    echo "❌ 容器启动失败"
    echo "容器日志："
    docker logs frontend-debug
    exit 1
fi

# 检查启动日志
echo "5. 检查启动日志..."
docker logs frontend-debug

# 检查配置是否生效
echo "6. 检查配置是否生效..."
echo "检查容器内的 index.html 文件："
docker exec frontend-debug cat /usr/share/nginx/html/index.html | grep -A 10 "运行时配置"

# 测试HTTP响应
echo "7. 测试HTTP响应..."
sleep 2
if command -v curl >/dev/null 2>&1; then
    echo "获取首页内容..."
    curl -s http://localhost:8080 | grep -A 10 "window.VUE_APP" || echo "未找到配置信息"
else
    echo "curl 未安装，请手动访问 http://localhost:8080 检查"
fi

# 检查环境变量
echo "8. 检查容器内环境变量..."
docker exec frontend-debug env | grep VUE_APP

echo "=== 调试完成 ==="
echo "如果配置未生效，请检查以下几点："
echo "1. 确保 startup.sh 有执行权限"
echo "2. 确保 public/index.html 包含正确的占位符"
echo "3. 确保 Dockerfile 正确复制了 startup.sh"
echo "4. 检查容器日志中是否有错误信息"
echo ""
echo "清理测试容器："
echo "docker stop frontend-debug && docker rm frontend-debug"
