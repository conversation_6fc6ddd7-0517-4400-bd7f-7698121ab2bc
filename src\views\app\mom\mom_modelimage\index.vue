<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="positionID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.positionID" placeholder="位置" clearable>
                  <vxe-option v-for="num in PositionComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelImageTypeID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.modelImageTypeID" placeholder="图片类别" clearable>
                  <vxe-option v-for="num in ModelImageTypeComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelimageMasterTable' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="modelImageTypeText" title="版型类别图片" sortable width="100"></vxe-table-column>
      <vxe-table-column field="positionText" title="图片位置" sortable width="100"></vxe-table-column>
      <vxe-table-column field="code" title="编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="imagePath" title="图片位置" sortable width="100"></vxe-table-column>
      <vxe-table-column field="imageUrl" title="图片" sortable> <template v-slot="{ row }">
          <el-popover placement="right-end" width="800" trigger="hover">
            <el-image :src="row.imageUrl" fit="fill">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.imageUrl" fit="scale-down" slot="reference" style="height:36px;">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>
        </template>
      </vxe-table-column>
      <vxe-table-column field="modelText" title="版型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row,['imagePath','imageUrl','id','xid'])" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" ref="ruleForm" :rules="formRules" title-align="right" title-width="100" @submit="submitEventTest('ruleForm')">
        <vxe-form-item title="编码" field="code" span="24" :item-render="{}"> <vxe-input v-model="selectRow.code" placeholder="请输入编码" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="24" :item-render="{}"> <vxe-input v-model="selectRow.codeName" placeholder="请输入编码" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="图片类别" field="modelImageTypeID" span="12" :item-render="{}"> <!-- <vxe-input v-model="selectRow.modelImageTypeID" placeholder="请输入编码" clearable></vxe-input> -->
          <template #default="{ data }">
            <vxe-select v-model="data.modelImageTypeID" placeholder="图片类别" clearable>
              <vxe-option v-for="num in ModelImageTypeComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片位置" field="positionID" span="12" :item-render="{}"> <!-- <vxe-input v-model="selectRow.positionID" placeholder="请输入编码" clearable></vxe-input> -->
          <template #default="{ data }">
            <vxe-select v-model="data.positionID" placeholder="位置" clearable>
              <vxe-option v-for="num in PositionComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item field="modelID" title="绑定版型" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelID" filterable remote reserve-keyword placeholder="版型" :remote-method="modelRemoteMethod" size="mini" clearable>
              <el-option v-for="item in modelComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片" field="imageUrl" span="24" :item-render="{}"> <template #default>
            <el-upload ref="upload" :action="mtmapi+api.add" list-type="picture-card" :file-list="fileList" :auto-upload="false" :multiple="false" :data="selectRow" :on-change="onchange" :on-error="onError">
              <i class="el-icon-plus"></i>
            </el-upload>
            <!-- <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="selectRow.imageUrl" alt="">
          </el-dialog> -->
          </template>
        </vxe-form-item>
        <vxe-form-item span="24" align='center' :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
import { mapState } from 'vuex'
// import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modelimage',
  mixins: [masterTableMixins],
  components: {

  },
  watch: {
    selectRow (val, old) {
      if (val.imageUrl != null) {
        this.fileList = [{ name: val.imageUrl, url: val.imageUrl }]
      } else {
        this.fileList = []
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      dialogVisible: false,
      formData: {
        // xid: '00000000-0000-0000-0000-000000000000',
        code: '',
        codeName: '',
        // remark: '',
        // isActive: true,
        modelImageTypeID: null,
        positionID: null,
        imageUrl: null,
        createID: null,
        createBy: null
      },
      fileList: [],
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 100, message: '长度在 3 到 50 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 100, message: '长度在 2 到 50 个字符' }],
        positionID: [{ required: true, message: '请选择图片位置' }],
        modelImageTypeID: [{ required: true, message: '请选择图片类型' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modelimage/get',
        // add: 'mtm/mom_modelimage/adds',
        add: 'mtm/mom_modelimage/AddOrUpdate',
        edit: '/mtm/mom_modelimage/updates',
        delete: '/mtm/mom_modelimage/deletes',
        ModelImageTypeComboStore: '/mtm/combo/ModelImageTypeComboStore',
        PositionComboStore: '/mtm/combo/PositionComboStore',
        modelComboStore: '/mtm/comboQuery/modelComboStoreByQuery'

      },
      modelComboStore: [],
      PositionComboStore: [],
      ModelImageTypeComboStore: [],
      mtmapi: process.env.VUE_APP_API,
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelImageTypeComboStore).then(result => {
        this.ModelImageTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.PositionComboStore).then(result => {
        this.PositionComboStore = result
      })
    },
    modelRemoteMethod (query) {
      this.$api.ActionRequest(this.api.modelComboStore, { text: query }).then(result => {
        this.modelComboStore = result
      })
    },
    onchange (file, fileList) {
      if (fileList.length > 1) {
        fileList.shift()
      }
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.modelComboStore, { gid: row.modelID }).then(result => {
        this.modelComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    copyRowEvent (row, attributenames = [], code = false, codeName = false) {
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }

      // if (!code && XEUtils.has(this.selectRow, 'code')) {
      //   this.selectRow.code = null
      // }
      // if (!codeName && XEUtils.has(this.selectRow, 'codeName')) {
      //   this.selectRow.codeName = null
      // }
      if (attributenames.length > 1) {
        attributenames.forEach(name => {
          this.selectRow[name] = null
        })
      }
      delete this.selectRow.xid
      this.showEdit = true
    },
    submitEventTest (formName) {
      var upload = this.$refs.upload
      this.$refs[formName].validate().then(v => {
        var file = upload.uploadFiles[0]
        if (upload.uploadFiles.length === 0) {
          return
        }
        if (upload.uploadFiles.length > 1) {
          return
        }
        this.selectRow.createBy = this.info.name
        this.selectRow.createID = this.info.userid
        if (file.raw) {
          upload.submit()
          this.showEdit = false
          this.loadData()
        } else {
          this.$api.ActionRequest(this.api.edit, [this.selectRow]).then((res) => {
            this.showEdit = false
            this.loadData()
          })
        }
      })
    },
    onError (err, file, fileList) {
      var res = err.toString().replace('Error:', '')
      console.log(res)
      const { success, error } = JSON.parse(res)
      console.log(success)
      if (!success) {
        this.$XModal.message({ message: error.message, status: 'error' })
      }
    }
    // // 复制
    // async copyRowEvent(row, code = false, codeName = false) {
    //   this.selectRow = cloneDeep(row)
    //   if (this.$utils.has(this.selectRow, 'id')) {
    //     this.selectRow.id = null
    //   }
    //   if (this.$utils.has(this.selectRow, 'id')) {
    //     this.selectRow.id = null
    //   }
    //   if (this.$utils.has(this.selectRow, 'imagePath')) {
    //     this.selectRow.imagePath = null
    //   }
    //   if (this.$utils.has(this.selectRow, 'imageUrl')) {
    //     this.selectRow.imageUrl = null
    //   }
    //   this.showEdit = true
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
