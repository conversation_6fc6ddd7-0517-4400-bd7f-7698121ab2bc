<template>
  <d2-container class="productpackingdetaillsit">
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:buttons>
          <!-- <vxe-button status="warning" disabled>清单下载</vxe-button> -->
          <vxe-button status="success" @click="PrintEvent">装箱单打印</vxe-button>
          <vxe-button status="warning" @click="ShipmentByUpsEvent">UPS发货</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table border id='productpackingMasterTable' height="auto" ref='master_table' :data="tableData" :expand-config="{iconOpen: 'fa fa-minus-square', iconClose: 'fa fa-plus-square'}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column type="expand" width="35" class-name="expandclass">
        <template v-slot:content="{ row, rowIndex }">
          <product-packing-detail-item :dataRow='row' :rowIndex='rowIndex' />
        </template>
      </vxe-table-column>
      <vxe-table-column type="seq" width="50"></vxe-table-column>
      <vxe-table-column field="productBoxTypeText" title="箱子类型" sortable width="150"> </vxe-table-column>
      <!-- <vxe-table-column field="number" title="序号" sortable width="100"> </vxe-table-column> -->
      <vxe-table-column field="productBoxNumber" title="箱号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingShortNumber" title="短号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="logisticsCompany" title="物流公司" sortable width="100"></vxe-table-column>
      <vxe-table-column field="weight" title="箱子单重" sortable width="100"></vxe-table-column>
      <vxe-table-column field="countWeight" title="总重" sortable width="100"></vxe-table-column>
      <vxe-table-column field="size" title="箱子体积" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="qty" title="数量" width="100"></vxe-table-column>
      <vxe-table-column field="graphicImage" title="快递单" sortable width="200px">
        <template v-slot="{ row }">
          <el-popover placement="right-end" width="200" trigger="hover">
            <el-image class="graphicImageclass" :src="'data:image/png;base64,'+row.graphicImage" fit="contain">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image class="graphicImageclass" :src="'data:image/png;base64,'+row.graphicImage" fit="contain" slot="reference">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>

        </template>

      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column title="操作" width="200" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button status="warning" @click="print(row)" v-if="menuAction.allowEdit">打印快递</vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <vxe-modal v-model="showEdit" :title=" '编辑&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="箱子类型" field="productBoxType" span="12" :item-render="{name: '$select', options: productBoxTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="箱号" field="productBoxNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="快递单号" field="trackingNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="短号" field="trackingShortNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="物流公司" field="logisticsCompany" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="箱子单重" field="weight" span="12" :item-render="{name: '$input',   props: { type: 'number'}}"></vxe-form-item>
        <vxe-form-item title="总重" field="countWeight" span="12" :item-render="{name: '$input',   props: { type: 'number'}}"></vxe-form-item>
        <vxe-form-item title="箱子体积" field="size" span="12" :item-render="{name: '$input',   props: { type: 'number'}}"></vxe-form-item>
        <vxe-form-item title="数量" field="qty" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>

        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import ProductPackingDetailItem from './productpackingdetailitem.vue'
export default {
  name: 'ProductPackingDetailList', // 发货清单
  mixins: [detailTableMixins],
  components: { ProductPackingDetailItem },
  props: {
  },
  data () {
    return {
      api: {
        get: '/mtm/wAR_ProductPacking/get',
        edit: '/mtm/wAR_ProductPacking/updates',
        ShipmentByUps: '/mtm/wAR_Shipment/ShipmentByUps',
        UploadPaperlessDocument: '/mtm/wAR_Shipment/UploadPaperlessDocument',
        productBoxTypeComboStore: '/mtm/combo/productBoxTypeComboStore'
      },
      mtmpai: process.env.VUE_APP_API,
      tableData: [],
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      productBoxTypeComboStore: []
    }
  },

  async created () {
    await this.getCombStore()
    this.loadData({ shipmentID: this.form.id })
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.productBoxTypeComboStore).then(result => {
        this.productBoxTypeComboStore = result
      })
    },
    reload () {
      // console.log('装箱单重新生成')
      this.loadData({ shipmentID: this.form.id })
    },
    PrintEvent () {
      var data = this.$refs.master_table.getCheckboxRecords()
      if (data.length <= 0) {
        this.$message({ type: 'error', message: '请勾选要打印的装箱单' })
        return
      }
      var ids = data.map(item => { return { IDs: item.id } })
      var str = ''
      ids.forEach(item => {
        str += 'IDS=' + item.IDs + '&'
      })
      console.log(str)
      var url = this.mtmpai.replace('/api/', '')
      window.open(`${url}/fs/print/productpacking/pdf?${str}`, '_blank')
    },
    print (row) {
      // 打印样式
      const printStyle = `
              img{
                   margin: 0 auto;
                   width: 100%;
                   height:100%;
              }
              `
      // const printStyle = `
      //         img{
      //              margin: 0 auto;
      //              width: 100%;
      //              height:100%;
      //       -moz-transform:rotate(90deg);
      //       -webkit-transform:rotate(90deg);
      //       filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
      //         }
      //         `
      // 打印模板
      const printTmpl = `
                <img src="data:image/png;base64,${row.graphicImage}">
                `
      this.$XPrint({
        sheetName: '快递单打印',
        style: printStyle,
        content: printTmpl
      })
    },
    async ShipmentByUpsEvent1 () {
      const loading = this.$loading({
        lock: true,
        text: 'UPS发货中请稍后,请稍后.....',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      await this.$api.ActionRequest(this.api.ShipmentByUps, this.form).then(result => {
        this.$XModal.message({ message: '发货成功', status: 'success' })
        this.showEdit = false
        this.loadData({ shipmentID: this.form.id })
        loading.close()
      }).catch(() => {
        loading.close()
      })
    },
    ShipmentByUpsEvent () {
      const loading = this.$loading({
        lock: true,
        text: '正在推送商业发票到UPS,请稍后.....',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.$api.ActionRequest(this.api.UploadPaperlessDocument, this.form).then(result => {
        this.$XModal.message({ message: '发票推送成功', status: 'success' })
        loading.text = '正在UPS发货,请稍后.....'
        this.UpLoadShipment(this.form).then(() => {
          this.$XModal.message({ message: '发票推送成功', status: 'success' })
          this.loadData({ shipmentID: this.form.id })
          loading.close()
        })
      }).catch(() => {
        loading.close()
      })
    },
    // UPS发货
    async UpLoadShipment (row) {
      return new Promise((resolve, reject) => {
        this.$api.ActionRequest(this.api.ShipmentByUps, row).then(result => {
          this.$XModal.message({ message: '发货成功', status: 'success' })
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    // 推送发票
    async UploadPaperlessDocumentEvent (row) {
      return new Promise((resolve, reject) => {
        this.$api.ActionRequest(this.api.UploadPaperlessDocument, row).then(result => {
          this.$XModal.message({ message: '发票推送成功', status: 'success' })
          resolve()
        }).catch(() => {
          reject()
        })
      })
    }
  }
}

</script>

<style lang="scss" >
.productpackingdetaillsit {
  .graphicImageclass {
    img {
      -moz-transform: rotate(90deg);
      -webkit-transform: rotate(90deg);
      filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
    }
  }
}
</style>
