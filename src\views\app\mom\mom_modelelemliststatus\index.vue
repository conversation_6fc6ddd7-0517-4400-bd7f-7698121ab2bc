<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="item in GroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>

            <vxe-form-item field="statusID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.statusID" placeholder="节点" clearable>
                  <vxe-option v-for="item in SorderStatusComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelelemliststatusMasterTable' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="statusName" title="节点名称" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="groupName" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemTypeName" title="款式类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemListName" title="款式名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemRequired" title="款式明细必填" sortable width="100px" :formatter="val=>formatBool(val)"></vxe-table-column>
      <vxe-table-column field="itemRequired" title="物料必填" sortable width="100px" :formatter="val=>formatBool(val)"></vxe-table-column>
      <vxe-table-column field="qtyRequired" title="数量必填" sortable width="100px" :formatter="val=>formatBool(val)"></vxe-table-column>
      <vxe-table-column field="inputRequired" title="文本必填" sortable width="100px" :formatter="val=>formatBool(val)"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式" field="modelElemListID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="请输入款式" :remote-method="remoteMethod" clearable size="mini">
              <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="节点" field="statusID" span="12" :item-render="{}"> <template #default="{ data }">
            <vxe-select v-model="data.statusID" placeholder="节点" clearable>
              <vxe-option v-for="item in SorderStatusComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="物料必填" field="itemRequired" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.itemRequired" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="数量必填" field="qtyRequired" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.qtyRequired" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="内容必填" field="inputRequired" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.inputRequired" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式明细" field="modelElemRequired" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.modelElemRequired" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isActive" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{}"> <template #default>
            <vxe-textarea v-model="selectRow.remark" placeholder="备注" show-word-count></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modelelemliststatus',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        itemRequired: false,
        qtyRequired: false,
        inputRequired: false,
        statusID: null,
        modelElemListID: null,
        modelElemRequired: false,
        remark: '',
        isActive: true
      },
      formRules: {
        modelElemListID: [{ required: true, message: '款式必填' }],
        statusID: [{ required: true, message: '节点' }]
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modelelemliststatus/get',
        add: '/mtm/mom_modelelemliststatus/adds',
        edit: '/mtm/mom_modelelemliststatus/updates',
        delete: '/mtm/mom_modelelemliststatus/deletes',
        GroupComboStore: '/mtm/combo/groupComboStore',
        SorderStatusComboStore: '/mtm/combo/sorderStatusComboStore',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/modelElemListComboStoreByQuery'
      },
      footerCompanyInfo: false,
      GroupComboStore: [],
      SorderStatusComboStore: [],
      ModelElemListComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    this.remoteMethod()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SorderStatusComboStore).then(result => {
        this.SorderStatusComboStore = result
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    // 编辑
    editEvent (row) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row, code = false, codeName = false) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (!code && this.$utils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (!codeName && this.$utils.has(this.selectRow, 'codeName')) {
        this.selectRow.codeName = null
      }
      this.showEdit = true
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
