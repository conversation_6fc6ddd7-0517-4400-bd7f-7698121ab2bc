const { notEmpty } = require('../utils.js')

module.exports = {
  description: 'generate a view',
  prompts: [
    {
      type: 'input',
      name: 'production',
      message: 'view production please',
      default:'app',
      validate: notEmpty('production')
    },
    {
      type: 'input',
      name: 'namespace',
      message: 'view namespace please',
      validate: notEmpty('namespace')
    },
    {
      type: 'input',
      name: 'name',
      message: 'view name please',
      validate: notEmpty('name')
    },
    {
      type: 'checkbox',
      name: 'blocks',
      message: 'Blocks:',
      choices: [
        {
          name: 'includeDetail',
          value: 'needDetail',
          checked: false
        },
        {
          name: '<template>',
          value: 'template',
          checked: true
        },
        {
          name: '<script>',
          value: 'script',
          checked: true
        },
        {
          name: 'style',
          value: 'style',
          checked: true
        }

      ],
      validate(value) {
        if (value.indexOf('script') === -1 && value.indexOf('template') === -1) {
          return 'View require at least a <script> or <template> tag.'
        }
        return true
      }
    }
  ],
  actions: data => {
    const name = '{{name}}'
    const namespace = '{{namespace}}'
    const production = '{{production}}'
    const actions = [{
      type: 'add',
      path: `src/views/${production}/${namespace}/${name}/index.vue`,
      templateFile: 'plop-templates/master/index.hbs',
      data: {
        name: name,
        namespace: namespace,
        needDetail: data.blocks.includes('needDetail'),
        template: data.blocks.includes('template'),
        script: data.blocks.includes('script'),
        style: data.blocks.includes('style')
      }
    }]

    return actions
  }
}
