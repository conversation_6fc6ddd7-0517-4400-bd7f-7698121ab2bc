<template>
  <div id="data-view">
    <dv-full-screen-container>

      <top-header />

      <div class="main-content">

        <cards />
        <div class="block-left-right-content">
          <!-- <ranking-board /> -->
          <bottom-right-table1 />
          <div class="block-top-bottom-content">
            <div class="block-top-content">
              <rose-chart />

              <!-- <water-level-chart /> -->
              <top-left-cmp />

              <scroll-board />
            </div>

            <digital-flop />
            <!-- <top-middle-cmp /> -->
            <top-right-cmp />
          </div>
        </div>
      </div>
    </dv-full-screen-container>
  </div>
</template>

<script>
import topHeader from './topHeader'
import digitalFlop from './digitalFlop'
// import rankingBoard from './rankingBoard'
import roseChart from './roseChart'
// import waterLevelChart from './waterLevelChart'
import scrollBoard from './scrollBoard'
import cards from './cards'
// import topMiddleCmp from './TopMiddleCmp.vue'
import BottomRightTable1 from './BottomRightTable1.vue'
import TopLeftCmp from './TopLeftCmp.vue'
import TopRightCmp from './TopRightCmp.vue'
export default {
  name: 'DataView',
  components: {
    topHeader,
    digitalFlop,
    // rankingBoard,
    roseChart,
    // waterLevelChart,
    scrollBoard,
    cards,
    TopLeftCmp,
    TopRightCmp,
    // topMiddleCmp,
    BottomRightTable1
  },
  data () {
    return {}
  },
  methods: {}
}
</script>

<style lang="scss">
#data-view {
  width: 100%;
  height: 100%;
  background-color: #030409;
  color: #fff;

  #dv-full-screen-container {
    background-image: url("./img/bg.png");
    background-size: 100% 100%;
    box-shadow: 0 0 3px blue;
    display: flex;
    flex-direction: column;
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .block-left-right-content {
    flex: 1;
    display: flex;
    margin-top: 20px;
  }

  .block-top-bottom-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-left: 20px;
  }

  .block-top-content {
    height: 45%;
    display: flex;
    flex-grow: 0;
    box-sizing: border-box;
    padding-bottom: 20px;
  }
}
</style>
