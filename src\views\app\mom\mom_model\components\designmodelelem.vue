<template>
  <d2-container class="designmodelelem">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <el-button type="success" size="mini" @click="sorderDetailElemSave()">保存</el-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="get(form.id, form.itemID)"></vxe-button>
        </template>
      </vxe-toolbar>
    </template>
    <div element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)">
      <div v-loading="SorderDetailElemDataLoading" element-loading-text="款式明细加载中" element-loading-spinner="el-icon-loading">
        <!-- <el-card class="box-card">
          <sorder-detail-elem-image id="id_sorderdetailelemimage" :form="form" :SorderDetailElemData="SorderDetailElemData" />
        </el-card> -->
        <el-row class="elemswitch">
          <vxe-switch v-model="elemBasics" open-label="基础信息" close-label="基础信息" class="elemBasicsactive"></vxe-switch>
          <template v-if="info.userType!==2">
            <vxe-switch v-model="isPlanActive" open-label="配色" close-label="配色" class="isPlanActive"></vxe-switch>
          </template>
          <vxe-switch v-model="isPlus" open-label="深定制" close-label="轻定制" class="isPlusactive"></vxe-switch>
          <!-- <vxe-switch v-model="kuanshiactive" open-label="款式" close-label="款式" class="kuanshiactive"></vxe-switch> -->
          <!-- <vxe-switch v-model="gongyiactive" open-label="工艺" close-label="工艺" class="gongyiactive"></vxe-switch> -->
          <!-- <vxe-switch v-model="fuliaoactive" open-label="辅料" close-label="辅料" class="fuliaoactive"></vxe-switch> -->
        </el-row>
        <!-- <el-row class="isCheckedElem">
          <el-col :span="24">
            <el-button :type="SorderDetailModel.isCheckedElem?'success':'danger'" size="mini" @click="sorderDetailElemCheck">款式明细检验</el-button>
          </el-col>
          <el-col :span="24" v-if="SorderDetailModel.messageElem!==null&&SorderDetailModel.messageElem!==''">
            <el-alert :title="SorderDetailModel.messageElem" type="error">
            </el-alert>
          </el-col>
        </el-row> -->
        <template v-for="(baseitem,index) in DetailElemViewData">
          <div id='id_sorderdetailelem' :key="baseitem.modelElemBaseCode+index" class="modelElemBase">{{clientModelElemBaseCode(baseitem)}}</div>
          <template v-for="(item1,rowindex1) in SorderDetailElemData.filter(item=>{return item.modelElemBaseID===baseitem.modelElemBaseID})">
            <template v-if="elemBasicsShow(item1)">
              <!-- <template v-if="clientShow(item1)"> -->
              <template v-if="isPlanShow(item1)">
                <template v-if="isPlusShow(item1)">
                  <kuan-shi v-if="item1.modelElemTypeID==1" v-show="kuanshiactive" :key="index+''+rowindex1" :elemItem="item1" :editAction="editProdModel" @ModelElemChange="ModelElemChange" />
                  <gong-yi v-if="item1.modelElemTypeID==2" v-show="gongyiactive" :key="index+''+rowindex1" :elemItem="item1" :editAction="editProdModel" @ModelElemChange="ModelElemChange" />
                  <fu-liao v-if="item1.modelElemTypeID==3||item1.modelElemTypeID==4" v-show="fuliaoactive" :key="index+''+rowindex1" :elemItem="item1" :editAction="editProdModel" @ModelElemChange="ModelElemChange" @itemDataChange="itemDataChange" />
                </template>
              </template>
              <!-- </template> -->
            </template>
          </template>
        </template>
      </div>
    </div>
    <!-- <el-row :gutter="20">
      <el-col :span="4" :offset="20">
        <el-button type="success" size="mini" @click="sorderDetailElemSave()">保存</el-button>
      </el-col>
    </el-row> -->
  </d2-container>
</template>
<script>
// import SorderDetailElemImage from './sorderdetailelemimage'
import KuanShi from './kuanshi'
import GongYi from './gongyi'
import FuLiao from './fuliao'
import { cloneDeep } from 'lodash'
import { mapState } from 'vuex'

export default {
  name: 'DesignModelElem',
  components: {
    // SorderDetailElemImage,
    KuanShi,
    GongYi,
    FuLiao
  },
  props: {
    form: {
      type: Object,
      required: true
    }
  },
  watch: {
    SorderDetailElemData: {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== oldVal && newVal !== null) {
          // console.log(`款式明细---面料${newVal}`)
        }
      }
    }
  },
  data () {
    return {
      api: {
        get: '/mtm/mOM_ModelModelElem/GetDesignModelElem',
        rule: '/mtm/mOM_ModelElemRule/calculateElemRule',
        modelElemEdit: '/mtm/mOM_ModelModelElem/ModifyDesignModelElem',
        // sorderDetailElemCheck: '/mtm/oDM_SorderDetailElem/CheckSorderDetailElem',
        itemElemItem: '/mtm/oDM_SorderDetailElem/ElemItemGet'
      },
      activeNames: ['1', '2', '3'],
      ElemList: [
        { name: '款式1' }
      ],
      kuanshiactive: true,
      gongyiactive: true,
      fuliaoactive: true,
      elemBasics: false, // 基础信息显示和隐藏,
      isPlanActive: false, // 配色
      isPlus: false, // 深定制简定制

      SorderDetailElemDataLoading: false,
      editProdModel: true,
      DetailElemViewData: [],
      SorderDetailElemData: []
    }
  },

  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
    // DetailElemViewData: {
    //   get() {
    //     if (this.SorderDetailElemData.length > 0) {
    //       var datalist = this.filterElems(this.SorderDetailElemData)
    //       return datalist
    //     }

    //   },
    //   set(val) {
    //     // this.filterElems(val)
    //   }
    // }
  },
  // watch: {
  //   SorderDetailElemData: {
  //     deep: true,
  //     handler: (newVal, oldVal) => {
  //       this.DetailElemViewData = this.filterElems(newVal)
  //     }
  //   }
  // },
  created () {
    if (this.form.id) {
      this.get(this.form.id, this.form.itemID)
    }
  },
  methods: {
    get (modelid, itemID = null) {
      //  const loading = this.$loading({
      //     lock: true,
      //     text: '款式明细加载中...请稍后...',
      //     spinner: 'el-icon-loading',
      //     // background: 'rgba(0, 0, 0, 0.7)'
      //   });
      if (modelid === null) {
        return
      }
      this.$api.ActionRequest(this.api.get, { modelId: modelid, itemID: itemID }).then(result => {
        this.SorderDetailElemDataLoading = true
        // console.log('款式明细加载')
        this.SorderDetailElemData = result
        this.DetailElemViewData = this.filterElems(this.SorderDetailElemData)
        result.forEach(element => {
          if (this.SorderDetailModel && this.SorderDetailModel.id) {
            element.sorderDetailModelID = this.SorderDetailModel.id
          }
          this.activeNames.push(element.modelElemBaseCode)
        })
        this.SorderDetailElemDataLoading = false
        // loading.close()
      })
    },
    // // 内外账户
    // clientShow (item) {
    //   if (this.info.userType === 1 || this.info.userType === 0) {
    //     return true // 管理员账号以及内部账户
    //   } else {
    //     // 外部账户
    //     return item.isClientShow
    //   }
    // },
    // 深定制简定制
    isPlusShow (item) {
      if (!this.isPlus) {
        if (item.isPlus) {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },
    // 基础款式明细显示隐藏
    elemBasicsShow (item) {
      if (!item.isCustomerShow && !this.elemBasics) {
        return false
      } else {
        return true
      }
    },
    // 配色选项
    isPlanShow (item) {
      if (!this.isPlanActive) {
        return true
      } else {
        return item.isPlanShow
      }
    },
    // 部位
    clientModelElemBaseCode (baseitem) {
      return baseitem.modelElemBaseCode + ':' + baseitem.modelElemBaseName + '[' + baseitem.modelElemList.length + ']'
    },
    filterElems (list) {
      var data = cloneDeep(list)
      var bases = data.reduce((newarry, oldarry) => {
        let index = -1
        newarry.some((item, i) => {
          if (item.modelElemBaseID === oldarry.modelElemBaseID) {
            index = i
            return true
          }
        })
        if (index > -1) {
          newarry[index].modelElemList.push(oldarry)
        } else {
          newarry.push({
            modelElemBaseID: oldarry.modelElemBaseID,
            modelElemBaseCode: oldarry.modelElemBaseCode,
            modelElemBaseName: oldarry.modelElemBaseName,
            modelElemList: [oldarry]
          })
        }
        return newarry
      }, [])
      return bases
      // this.DetailElemViewData=bases;
    },
    filterModelElems (item, typearr) {
      // 过滤款式类型：款式、工艺、辅料
      if (item.modelElemTypeID !== 4) {
        if (this.activeNames.indexOf(item.modelElemTypeID + '') < 0) {
          return false
        }
      }
      if (typearr !== undefined && typearr && typearr.length > 0) {
        var iselemtype = typearr.indexOf(item.modelElemTypeID)
        if (iselemtype < 0) {
          return false
        }
      }
      // 过滤客户端显示
      if (!item.isClientShow) {
        return false
      }
      return true
    },
    async ModelElemChange ({ modelelemid, elemlist, modelelem }) {
      var elem = this.SorderDetailElemData.GetFirstElement('modelElemListID', elemlist.modelElemListID)
      if (modelelemid !== null && modelelemid !== '') {
        elem = Object.assign(elem, modelelem)
        elem.itemImageUrl = modelelem.itemImageUrl
      } else {
        elem.modelElemID = null
        elem.qty = null
        elem.input = null
        elem.itemID = null
        elem.itemImageUrl = null
      }

      await this.getElemRule(elem)
    },

    itemDataChange (data) {
      switch (data.Type) {
        case 'Input':
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemText = data.Item.label
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemID = data.Item.value
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).input = data.Input
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).qty = data.Qty
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).IsInput = false
          break
        case 'Item':
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemID = data.Item.value
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemText = data.Item.label
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).unit = data.Item.unit
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemWidth = data.Item.itemWidth
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).qty = data.Qty
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).input = ''
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemImageUrl = data.Item.imageUrl
          this.itemElemItem(data.ModelElemID, data.Item.value)
          break
        case 'ClearItem':
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).input = ''
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemID = null
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemText = null
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemWidth = null
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).unit = ''
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemImageUrl = null
          // this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).qty = ''
          break
        default:
          break
      }
    },
    // 辅料配色方案
    itemElemItem (modelElemID, itemID) {
      this.$api.ActionRequest(this.api.itemElemItem, { ModelElemID: modelElemID, itemID: itemID }).then(res => {
        if (res.length > 0) {
          res.forEach(item => {
            if (this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID)) {
              this.SorderDetailElemData.GetFirstElement('modelElemID', item.modelElemID).itemID = item.itemID
            }
          })
        }
      })
    },
    async getElemRule (elem) {
      if (elem.modelElemRules.length === 0) {
        return
      }
      var _this = this
      // console.log('款式明细变化,积累公式')
      // console.log(elem.modelElemID)
      // console.log(elem.modelElemRules)
      var rule = []
      //   var detailSizeData = this.GetDetailSizeData()
      this.$utils.arrayEach(elem.modelElemRules, (item, key) => {
        var a = { ModelElemRuleID: item.modelElemRuleID, BomRuleType: item.bomRuleType, ModelElems: [], SizeColumns: [], otherElemRules: [] }
        this.$utils.arrayEach(item.modelElemListIDs, (elem, key1) => {
          var _elem = this.SorderDetailElemData.GetFirstElement('modelElemListID', elem)
          if (_elem) {
            if (item.bomRuleType === 5) {
              a.otherElemRules.push({ itemID: _this.sorderStore.itemID, SorderType: _this.sorderStore.sorderTypeID })
              a.ModelElems.push({ ModelElemListID: elem, ModelElemID: _elem.modelElemID, ItemID: _elem.itemID, ItemWidth: _elem.itemWidth, Qty: _elem.qty })
            } else {
              a.ModelElems.push({ ModelElemListID: elem, ModelElemID: _elem.modelElemID, ItemID: _elem.itemID, ItemWidth: _elem.itemWidth, Qty: _elem.qty })
            }
          }
        })
        // this.$utils.arrayEach(item.sizeElemListIDs, (elem, key1) => {
        //   var _detialSize = detailSizeData
        //   var size = _detialSize.GetFirstElement('sizeColumnID', elem)
        //   if (size) {
        //     a.SizeColumns.push({ SizeColumnID: elem, Finish: size.finish })
        //   }
        // })
        rule.push(a)
      })
      await this.calculateElemRule(rule)
    },
    async calculateElemRule (list) {
      if (list.length === 0) {
        return
      }
      await this.$api.ActionRequest(this.api.rule, list).then(result => {
        this.$utils.arrayEach(result, async (elem, key1) => {
          await this.setElem(elem)
        })
      })
    },
    async setElem (elemList) {
      var elem = this.SorderDetailElemData.GetFirstElement('modelElemListID', elemList.modelElemListID)
      if (elem) {
        if (elemList.type === 1 && elemList.modelElemID === null) {
          elem.modelElemID = null
          elem.qty = null
          elem.itemID = null
          await this.getElemRule(elem)
        } else {
          if (elemList.type === 1) {
            if (elem.modelElem.ElementExist('modelElemID', elemList.modelElemID)) {
              elem.modelElemID = elemList.modelElemID
              var selectElem = elem.modelElem.GetFirstElement('modelElemID', elemList.modelElemID)
              if (selectElem !== null) {
                elem.isInput = selectElem.isInput
                elem.itemID = selectElem.itemID
                elem.isInputItem = selectElem.isInputItem
                elem.isItemAdd = selectElem.isItemAdd
                elem.isItemImageAdd = selectElem.isItemImageAdd
              }
            }
            await this.getElemRule(elem)
          }
          if (elemList.type === 2) {
            elem.qty = elemList.qty
          }
          if (elemList.type === 4 || elemList.type === 5) {
            elem.itemID = elemList.itemID
          }
        }
      }
    },
    // 保存款式明细
    async sorderDetailElemSave () {
      //   if (this.EditState) {
      //     return this.EditState
      //   }
      this.SorderDetailElemDataLoading = true
      var list = cloneDeep(this.SorderDetailElemData.filter(item => { return item.modelElemID !== null }))
      list.forEach(item => {
        delete item.isInput
        delete item.isInputItem
        delete item.modelElemRules
        delete item.positionIDs
        delete item.modelElem
        delete item.inputRequired
        delete item.itemRequired
        delete item.modelElemRequired
        delete item.qtyRequired
      })
      return await this.$api.ActionRequest(this.api.modelElemEdit, list).then(async res => {
        this.SorderDetailElemDataLoading = false
        return true
      }).catch(() => {
        return false
      })
    },
    async sorderDetailElemCheck () {
      var b = await this.sorderDetailElemSave()
      if (b) {
        this.$api.ActionRequest(this.api.sorderDetailElemCheck, [this.SorderDetailModel.id]).then(res => {
          var model = res.detailModels.GetFirstElement('modelID', this.SorderDetailModel.modelID)
          if (model) {
            this.SorderDetailModel.isCheckedElem = model.isChecked
            this.SorderDetailModel.messageElem = model.message
            this.$notify({
              title: model.isChecked ? '成功' : '失败',
              dangerouslyUseHTMLString: true,
              message: model.isChecked ? '检验通过' : model.message,
              type: model.isChecked ? 'success' : 'error'
            })
          }
        })
      }
    }
  }

}
</script>

<style lang="scss">
.designmodelelem {
  padding: 10px;
  .elemIsReuired {
    border: 1px solid #f56c6c;
  }
  .kuanshiactive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #5bae11;
    }
  }
  .gongyiactive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #bf971f;
    }
  }
  .fuliaoactive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #0598e1;
    }
  }
  .elemBasicsactive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #6e42b1;
    }
  }
  .isPlanActive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #b940ff;
    }
  }
  .modeleleminput input {
    width: 120px !important;
  }
  .modelElemBase {
    font-size: 13px;
    font-weight: 600;
  }
  // min-height: 700px;
  .kuanshispan {
    display: inline-block;
    width: 150px;
    color: #5bae11;
    font-size: 13px;
  }
  // .gongyispan .gongyispan.is-checked + .el-checkbox__label{
  //   color: #e6a23c;
  // }
  .kuanshigongyi {
    padding: 2px;
    min-width: 380px;
    display: inline-block;
    margin-left: 20px;
    // font-size: 10px;
    input {
      width: 250px;
    }
  }
  .fuliao {
    padding: 2px;
    display: inline-block;
    margin-left: 20px;
    // font-size: 10px;
    input {
      width: 250px;
    }
  }
  .gongyispan {
    width: 150px;
    display: inline-block;
    color: #bf971f;
    font-size: 13px;
  }
  .fuliaospan {
    width: 150px;
    display: inline-block;
    font-size: 13px;
    color: #0598e1;
  }

  .el-popover {
    opacity: 0.98;
  }

  .item {
    margin-top: 0px;
  }
  .elemlistcode {
    display: inline-block;
    font-weight: bold;
    width: 150px;
  }

  .el-collapse-item__content {
    padding-bottom: 5px;
  }

  /**divtable**/
  .div-table {
    width: 100%;
    margin: 0.5rem auto 0;
  }
  .table-tr {
    overflow: hidden;
    /* border: 1px solid #bfbfbf; */
    border-top: 0;
    width: 700px;
  }
  .table-title {
    width: calc(100% - 17px);
    border-top: 1px solid #bfbfbf;
  }
  .v-table-title-cell > .table-title {
    border-top: 0;
  }
  .table-body {
    width: 100%;
    /* max-height: 5rem; */
    min-height: 1.5rem;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  .table-th,
  .table-td {
    /* text-align: center; */
    float: left;
    /* border-right: 1px solid #bfbfbf; */
    margin-right: -1px; /* 抵消右边框宽度 */
    min-height: 0.3rem; /* 防止内容为空时缺失 */
    line-height: 0.3rem;
    padding-bottom: 999px;
    margin-bottom: -999px;
    overflow: hidden;
  }
  .table-th span,
  .table-td span {
    /* display: block; */
    padding: 0 0.05rem;
    font-size: 13px;
  }
}
</style>
