<template>
  <d2-container>
    <vxe-table align="center" :data="log" height="auto">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="time" title="时间"></vxe-table-column>
      <vxe-table-column field="message" title="错误消息"></vxe-table-column>
      <vxe-table-column field="url" title="地址">
        <template v-slot="{ row }"> {{get(row, 'meta.url')}}</template>
      </vxe-table-column>
      <vxe-table-column title="信息">
        <template v-slot="{ row }">
          <el-tag v-if="get(row, 'meta.instance.$vnode.componentOptions.tag')" type="info" size="mini">
            &#60;{{get(row, 'meta.instance.$vnode.componentOptions.tag')}}&gt;
          </el-tag>
          <el-tag v-else type="info" size="mini">
            &#60;{{get(row, 'meta.trace')}}&gt;
          </el-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column title="查看详情">
        <template v-slot="{ row }">
          <el-button type="primary" size="mini" @click="handleShowMore(row)">
            <d2-icon name="eye" />
          </el-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <el-button slot="footer" type="primary" size="mini" :loading="uploading" @click="handleUpload">
      <d2-icon name="cloud-upload" />
      上传 {{log.length}} 日志数据
    </el-button>
  </d2-container>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { get } from 'lodash'
export default {
  name: 'log',
  data () {
    return {
      uploading: false
    }
  },
  computed: {
    ...mapState('d2admin/log', [
      'log'
    ])
  },
  methods: {
    ...mapMutations('d2admin/log', [
      'clean'
    ]),
    get,
    handleShowMore (log) {
      // 打印一条日志的所有信息到控制台
      this.$notify({
        type: 'info',
        title: '日志详情',
        message: '完整的日志内容已经打印到控制台'
      })
      this.$log.capsule('D2Admin', 'handleShowMore', 'primary')
      console.group(log.message)
      console.log('time: ', log.time)
      console.log('type: ', log.type)
      console.log(log.meta)
      console.groupEnd()
    },
    // 日志上传
    handleUpload () {
      this.uploading = true
      this.$notify({
        type: 'info',
        title: '日志上传',
        message: `开始上传${this.log.length}条日志`
      })
      setTimeout(() => {
        this.clean()
        this.uploading = false
        this.$notify({
          type: 'success',
          title: '日志上传',
          message: '上传成功'
        })
      }, 3000)
    }
  }
}
</script>
