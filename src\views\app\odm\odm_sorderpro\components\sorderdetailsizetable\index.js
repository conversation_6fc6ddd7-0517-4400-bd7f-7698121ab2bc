export default {
  name: 'CheckSize',
  props: {
    DetailSizeData: {
      type: Array,
      required: true
    },
    SorderDetailModel: {
      type: Object,
      required: true
    },
    sizeTableLoding: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      tableHeight: '500'
    }
  },
  methods: {
    sizeRequitedClass ({ row }) {
      // if (row.isRequired) {
      //   return 'isRequiredRow'
      // }
    },
    cellClassName ({ row, column }) {
      var cellclass = ''
      switch (this.SorderDetailModel.sorderSizeTypeID) {
        case '1':
          cellclass = this.measurecellclassName(row, column)
          break
        case '2':
          cellclass = this.finishcellclassName(row, column)
          break
        case '3':
          break
        case '4':
        case '5':
          cellclass = this.standrdNoclassName(row, column)
          break
      }
      // cellclass = this.requireClass(row, column)
      return cellclass
    },
    requireClass (row, column) {
      var str = ''
      if (column.type === 'seq') {
        if (row.isRequired) {
          str = 'CellRequitedClass'
        }
      }
      return str
    },
    finishcellclassName (row, column) {
      var str = ''
      if (column.title === '成衣') {
        str = 'CellRequitedClass'
      } else {
        str = this.requireClass(row, column)
      }
      return str
    },
    standrdNoclassName (row, column) {
      var str = ''
      if (column.title === '标准修正') {
        str = 'CellRequitedClass'
      } else {
        str = this.requireClass(row, column)
      }

      return str
    },
    measurecellclassName (row, column) {
      var str = ''
      if (column.title === '量体' || column.title === '量体加放量') {
        str = 'CellRequitedClass'
      } else {
        str = this.requireClass(row, column)
      }
      return str
    },
    activeCellMethod ({ column, columnIndex }) {
      if (column.property === 'isManual' || column.property === 'pure') {
        return true
      }
      if (this.SorderDetailModel.sorderSizeTypeID === '3' || this.SorderDetailModel.sorderSizeTypeID === '4' || this.SorderDetailModel.sorderSizeTypeID === '5') {
        if (column.property === 'fix1') {
          return true
        }
      }
      if (this.SorderDetailModel.sorderSizeTypeID === '2') { // 成衣
        if (column.property === 'finish') {
          return true
        }
      }
      if (this.SorderDetailModel.sorderSizeTypeID === '1') { // 量体
        if (column.property === 'measure' || column.property === 'ease') {
          return true
        }
      }
      return false
    },
    inputFocus (name, row) {
      var num = this.$utils.toNumber(row[name])
      if (num === 0) {
        row[name] = null
      }
    },
    fix1Blur (row) {
      if (row.fix1 === '' || row.fix1 === null) {
        row.finish = row.standard1
      } else {
        if (parseFloat(row.fix1) < row.fix1Min) {
          row.fix1 = row.fix1Min
        }
        if (parseFloat(row.fix1) > row.fix1Max) {
          row.fix1 = row.fix1Max
        }
        var count = row.standard1 == null ? 0 : parseFloat(row.standard1)
        row.finish = count + parseFloat(row.fix1)
      }
    }
  }
}
