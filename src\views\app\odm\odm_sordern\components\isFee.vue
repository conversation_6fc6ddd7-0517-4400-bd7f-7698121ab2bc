<template>
  <el-tooltip class="item" effect="dark" :content="'此项收费:'+price" placement="top-start">
    <!-- <i class="fa fa-money"></i> -->

    <span style="color:red">    <i class="el-icon-s-goods"></i>{{price}}</span>
  </el-tooltip>

</template>

<script>
export default {
  props: {
    price: {
      type: Number,
      default: 0
    }
  },
  // 收费显示
  name: 'isFee'
}
</script>

<style>
</style>
