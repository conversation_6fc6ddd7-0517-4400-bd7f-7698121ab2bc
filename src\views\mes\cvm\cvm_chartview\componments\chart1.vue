<template>
  <board-chart :data="data" :settings="settings " :type='type' class="chart1">
    <template slot="chartheader">
      <el-row>
        <el-col :span="8">
          <h2 style="margin-left: 20px;">工段延期数</h2>
        </el-col>
        <el-col :span="16">

        </el-col>
      </el-row>
    </template>
  </board-chart>
</template>

<script>
import boardChart from '@/components/charts/chart'
export default {
  name: 'worksecationpostpone', // 工段延迟数
  components: {
    boardChart
  },
  data () {
    return {
      data: {
        columns: ['label', 'extensionNumber'],
        rows: [

        ]
      },
      settings: {
        labelMap: {
          label: '工段',
          extensionNumber: '延期数'
        },
        label: {
          normal: {
            show: true,
            position: 'top'
          }
        }
      },
      type: 'pie',
      api: {
        get: '/mes/boardChart/WorkSecationPostpone',
        GroupComboStore: '/mtm/combo/groupComboStore'
      },
      groupIDs: [],
      GroupComboStore: []
    }
  },
  mounted () {
    this.timer = setInterval(this.get, 1000 * 60)// 毫秒
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  async created () {
    await this.getCombStore()
    this.get()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
        this.GroupComboStore.forEach(item => {
          this.groupIDs.push(item.value)
        })
      })
    },
    async get () {
      await this.$api.ActionRequest(this.api.get, { groupIDs: this.groupIDs }).then(result => {
        this.data.rows = result
      })
    },
    async change () {
      await this.get()
    }
  }
}
</script>

<style lang="scss" scope>
.chart1 {
  height: 100%;
}
</style>
