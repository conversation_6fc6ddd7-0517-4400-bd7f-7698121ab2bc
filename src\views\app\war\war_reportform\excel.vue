<template>
  <d2-container class='stockinvoicesdetail'>
    <vxe-table id='WarItemstickinvoicesdetailDetailTable' keep-source ref='master_table' height="auto" :export-config="exportConfig" :row-class-name="rowClassName" :data="tableData" :loading="tableLoading" :footer-method="footerMethod" show-footer>
      <vxe-colgroup field="group0" title="序号" align="center">
        <vxe-table-column type="seq" title="No." width="60"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group1" title="交货单号">
        <vxe-table-column field="deliveryNote" title="Delivery Note" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group2" title="销售单号">
        <vxe-table-column field="sorderNumber" title="JYY Ref." width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group3" title="发货日期">
        <vxe-table-column field="deliveryDate" title="Delivery Date" width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')">
        </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group4" title="品名">
        <vxe-table-column field="groupText" title="Descriptions" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group5" title="面料号">
        <vxe-table-column field="fabricCode" title="Fabric Code" cell-type="string" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group6" title="店铺代码">
        <vxe-table-column field="shopCode" title="Shope Code" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group7" title="小票号">
        <vxe-table-column field="ticketNo" title="Ticket No." width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group8" title="顾客姓名">
        <vxe-table-column field="customerName" title="Customer name" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group9" title="订单号">
        <vxe-table-column field="jacketNo" title="Order No.(Jacket)" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group10" title="订单号">
        <vxe-table-column field="trouserNo" title="Order No.(Trousers)" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group11" title="订单号">
        <vxe-table-column field="waistcoatNo" title="Order No.(Waistcoat)" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group12" title="数量明细">
        <vxe-table-column field="quantityDetails" title="Quantity details" cell-type="string" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group13" title="数量">
        <vxe-table-column field="qty" title="Quantity" type="string" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group14" title="材料费">
        <vxe-table-column field="fabricCost" title="Fabric Cost" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group15" title="加工费">
        <vxe-table-column field="cmt" title="CMT" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group16" title="单价">
        <vxe-table-column field="unitPrice" title="Unit Price" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group17" title="金额">
        <vxe-table-column field="amount" title="Full Product Price" width="100"> </vxe-table-column>
      </vxe-colgroup>
      <vxe-colgroup field="group18" title="美元汇率">
        <vxe-table-column field="exchangeRate" title="Change Rate" width="100"> </vxe-table-column>
      </vxe-colgroup>
    </vxe-table>
    <template slot="footer">
      <vxe-button status="primary" @click="tableExport1" size="mini">
        <d2-icon name="download" />
        导出 Excel
      </vxe-button>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'DetailExcel',
  mixins: [detailTableMixins],
  props: {
    Ids: {
      type: Array
    }
  },
  components: {

  },
  data () {
    return {
      exportConfig: {
        // 默认选中类型
        type: 'xlsx',
        // 自定义类型
        types: ['xlsx', 'csv', 'html', 'xml', 'txt']
      },
      tableLoading: false,
      api: {
        get: '/mtm/wAR_ReportForm/createReportForm5'
      },
      data: null
    }
  },
  async created () {
    if (this.Ids.length > 0) {
      this.$api.ActionRequest(this.api.get, { iDs: this.Ids }).then(res => {
        this.data = res
        this.tableData = res.reportFormDetails
      })
    }
  },
  methods: {
    tableExport () {
      // const xTable = this.$refs[this.tableRef]
      // xTable.exportData({
      //   filename: '收款发票-' + new Date().getTime(),
      //   sheetName: 'Sheet1'
      // })
      const xTable = this.$refs[this.tableRef]
      const exportConfig = {
        type: 'csv',
        dataFilterMethod ({ row }) {
          // if (row.fabricCode) {
          //   row.fabricCode = ' ' + row.fabricCode
          // }
          if (row.quantityDetails) {
            row.quantityDetails = ' ' + row.quantityDetails
          }
          return row
        },
        filename: '收款发票-' + this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss'),
        data: xTable.data,
        mode: 'all',
        isHeader: true,
        original: true // 是否为源数据
      }
      xTable.exportData(exportConfig)
    },
    tableExport1 () {
      // const xTable = this.$refs[this.tableRef]
      this.$refs[this.tableRef].exportData({
        filename: '收款发票-' + this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss'),
        sheetName: 'Sheet1',
        isColgroup: false,
        isFooter: false,
        type: 'xlsx'
      })
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count += Number(item[field])
      })
      count = this.$utils.toFixed(count, 2)
      return count
    },
    footerMethod ({ columns, data }) {
      const footerData = [
        columns.map((column, _columnIndex) => {
          if (_columnIndex === 16) {
            return 'TOTAL:'
          }
          if (['amount'].includes(column.property)) {
            return 'US$' + this.sumNum(data, 'amount')
          }
          return null
        })
      ]
      return footerData
    }

  }
}
</script>

<style lang="scss" >
.stockinvoicesdetail {
  .itemselect {
    width: 100%;
  }
}
</style>
