<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="SysClientPersonTable" ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="code" title="顾客编码" width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="顾客姓名" width="100px"></vxe-table-column>
      <vxe-table-column field="gender" title="性别" width="100px" :formatter="val=>formatSex(val)"> </vxe-table-column>
      <vxe-table-column field="height" title="身高/cm" width="100px"></vxe-table-column>
      <vxe-table-column field="weight" title="体重/kg" width="100px"></vxe-table-column>
      <vxe-table-column field="tel" title="手机号"></vxe-table-column>
      <vxe-table-column field="company" title="公司" width="100px"></vxe-table-column>
      <vxe-table-column field="department" title="部门" width="100px"></vxe-table-column>
      <vxe-table-column field="position" title="职位" width="100px"></vxe-table-column>
      <vxe-table-column field="birthDate" title="出生日期" width="100px" :formatter="val=>formatDate(val)"></vxe-table-column>
      <vxe-table-column field="mobile" title="座机" width="100px"></vxe-table-column>
      <vxe-table-column field="address" title="地址" width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="100px" show-overflow :fixed='tableOptFixed'>
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="顾客编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顾客姓名" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="性别" field="gender" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.gender" filterable placeholder="性别" size="mini">
              <el-option v-for="item in sexList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="身高/cm" field="height" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="体重/kg" field="weight" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="手机号" field="tel" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="公司" field="company" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="部门" field="department" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="职位" field="position" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="出生日期" field="birthDate" span="12" :item-render="{}"> <template #default>
            <el-date-picker v-model="selectRow.birthDate" type="date" placeholder="选择日期">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="座机" field="mobile" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="地址" field="address" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <client-person-image :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import { mapState } from 'vuex'
import detailMixins from '@/mixins/detail_table_mixins/index'
import ClientPersonImage from './clientpersonimage'
export default {
  name: 'clientperson',
  computed: {
    ...mapState('d2admin/user', ['info'])
  },
  mixins: [detailMixins],
  components: {
    ClientPersonImage
  },
  data () {
    return {
      formData: {
        id: null,
        code: null,
        codeName: null,
        remark: null,
        gender: true,
        height: null,
        weight: null,
        tel: null,
        company: null,
        department: null,
        position: null,
        birthDate: null,
        mobile: null,
        isActive: true,
        clientID: null,
        address: null
      },
      api: {
        get: '/mtm/bad_clientperson/get',
        add: '/mtm/bad_clientperson/adds',
        edit: '/mtm/bad_clientperson/updates',
        delete: '/mtm/bad_clientperson/deletes'
      },
      formRules: {
        code: [
          { required: true, message: '顾客编码必须填写' }
        ],
        codeName: [
          { required: true, message: '顾客姓名必须填写' }
        ],
        gender: [
          { required: true, message: '性别必须选择' }
        ],
        height: [
          { required: true, message: '身高必须填写' }
        ],
        weight: [
          { required: true, message: '体重必须填写' }
        ]
        // tel: [
        //   { required: true, message: '手机号必须填写' },
        //   //   {message: '请填写正确手机号' , type:'string',pattern: /^(([0+]d{2,3}-)?(0d{2,3})-)(d{7,8})(-(d{3,}))?$/},
        //   { message: '请填写正确11位手机号', type: 'string', pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/ }
        // ]
      }
    }
  },
  created () {
    this.formData.clientID = this.info.clientID
    this.loadData({ id: this.info.clientID })
  },
  methods: {

  }
}
</script>

<style>
</style>
