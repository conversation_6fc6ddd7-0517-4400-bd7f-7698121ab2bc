# Docker 运行时配置

## 📁 简化的测试工具

本文件夹包含最基本的 Docker 配置测试脚本。

### 🚀 测试脚本

| 文件名                   | 用途     | 说明                       |
| ------------------------ | -------- | -------------------------- |
| `windows-build-test.bat` | 构建测试 | Windows 兼容的完整构建测试 |
| `verify-config.bat`      | 配置验证 | 验证运行时配置是否生效     |
| `quick-test.bat`         | 快速测试 | 一键验证 Docker 配置       |
| `cleanup.bat`            | 清理工具 | 清理所有测试容器和镜像     |

### 📖 使用方法

#### 1. 快速测试

```cmd
cd docker-test
quick-test.bat
```

#### 2. 验证配置

```cmd
verify-config.bat
```

#### 3. 清理资源

```cmd
cleanup.bat
```

### ✅ 验证方法

运行测试脚本后，在浏览器控制台运行：

```javascript
console.log("配置检查:", {
  VUE_APP_API: window.VUE_APP_API,
  VUE_APP_USER_API: window.VUE_APP_USER_API,
  VUE_APP_MESAPI: window.VUE_APP_MESAPI,
  VUE_APP_TITLE: window.VUE_APP_TITLE,
});
```

### 🎯 生产使用

```cmd
docker run -d --name my-app -p 8080:80 \
  -e "VUE_APP_USER_API=https://your-sso.com/api/" \
  -e "VUE_APP_API=https://your-api.com/api/" \
  -e "VUE_APP_MESAPI=https://your-mes.com/api/" \
  -e "VUE_APP_TITLE=Your App Title" \
  your-image:tag
```
