<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->
          <vxe-button @click="exportSelectEvent" v-if="menuAction.allowPrint" content="导出选中">
            <template #dropdowns>
              <vxe-button @click="exportDBSelectEvent" v-if="menuAction.allowPrint" type="text" content="(大宝)导出选中"></vxe-button>
            </template>
          </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemClassID">
              <template #default="{ data }">
                <vxe-select v-model="data.itemClassID" placeholder="类别" clearable>
                  <vxe-option v-for="item in ItemClassComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="businessGroupID">
              <template #default="{ data }">
                <el-select v-model="data.businessGroupID" placeholder="业务类型" filterable clearable size="mini">
                  <el-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="technologyGroupID">
              <template #default="{ data }">
                <el-select v-model="data.technologyGroupID" placeholder="工艺属性" filterable clearable size="mini">
                  <el-option v-for="item in TechnologyGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="textureGroupID">
              <template #default="{ data }">
                <el-select v-model="data.textureGroupID" placeholder="面料纹理" filterable clearable size="mini">
                  <el-option v-for="item in ItemTextureGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemGroupID">
              <template #default="{ data }">
                <el-select v-model="data.itemGroupID" placeholder="分类" filterable clearable size="mini">
                  <el-option v-for="item in ItemGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItemstockMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox"></vxe-table-column>
      <vxe-table-column field="ownerClientName" title="归属客户" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="code" title="面料编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="supplierItemCode" title="供应商编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="supplierItemName" title="供应商名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="number" title="序号" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="itemClassText" title="物料类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemGroupText" title="物料分类" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="businessGroupText" title="业务归属" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="inventoryQty" title="库存数" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemSize" title="规格型号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="safetyInventoryQty" title="安全库存" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="maxInventoryQty" title="最大库存数" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="unitSellingPrice" title="销售单据" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="clientCode" title="客户编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="yearNo" title="年份" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="transparency" title="透胶/光性" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="technologyText" title="工艺属性" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="unitGroupText" title="单位分类" sortable width="100px"></vxe-table-column>

      <vxe-table-column field="textureGroupText" title=" 纹理(CM)" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="width" title="门幅单位" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemComp" title="面料成分" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="yarn" title="纱织" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="weight" title="克重" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="shrink" title="缩率" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemBrand" title="物料品牌" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sorderBomShow" title="Bom清单中不显示" :formatter='formatBool' sortable width="100px"></vxe-table-column>

      <!-- <vxe-table-column field="retailPrice" title="零售价格" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="wholesalePrice" title="采购价格" sortable width="100px"></vxe-table-column>

      <vxe-table-column field="itemStockPositionText" title="库位" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sorderOccupyQty" title="订单占用数" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="isElemShow" title="工艺单图片展示？" :formatter='formatBool' sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180px" show-overflow :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit" :disabled="!row.isActive"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete" :disabled="!row.isActive"></vxe-button>
          <vxe-button type="text" icon="fa fa-copy" v-if="menuAction.allowAdd" @click="copyRowEvent(row)"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="物料名称" field="itemID" span="12">
          <template #default="{ data }">
            <el-select v-model.trim="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod" disabled>
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户" field="clientID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword clearable :remote-method="remoteMethod4">
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="供货商" field="supplierItemID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.supplierItemID" filterable placeholder="供货商" size="mini" remote reserve-keyword clearable :remote-method="remoteMethod5">
              <el-option v-for="item in clientComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="零售价格" field="retailPrice" span="12" :item-render="{name: '$input',  props: { type: 'float'}}"></vxe-form-item> -->
        <vxe-form-item title="采购价格" field="wholesalePrice" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="有效库存数" field="inventoryQty" span="12" :item-render="{name: '$input',  props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="安全库存" field="safetyInventoryQty" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="最大库存" field="maxInventoryQty" span="12" :item-render="{name: '$input', props: { type: 'float'}  }"></vxe-form-item>
        <vxe-form-item title="库位" field="itemStockPositionID" span="12" :item-render="{name: '$select', options: ItemStockPositionComboStore,props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="订单占用数" field="sorderOccupyQty" span="12" :item-render="{name: 'input',   attrs:{disabled:true},}"></vxe-form-item>
        <vxe-form-item title="Bom清单不显示" field="sorderBomShow" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="DBExportTableShow" :title="'大宝数据导出'" width="70%" height="60%" resize destroy-on-close :show="ExportTableShow">
      <db-export-table v-if="DBExportTableShow" :selectTable="DBSelectDataTable" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import DbExportTable from './compontents/DBExportTable.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_itemstock',
  mixins: [masterTableMixins],
  components: {
    DbExportTable
  },
  data () {
    return {
      formData: {
        itemID: null,
        retailPrice: null,
        wholesalePrice: null,
        inventoryQty: null,
        safetyInventoryQty: null,
        maxInventoryQty: null,
        itemStockPositionID: null,
        sorderOccupyQty: null,
        remark: null,
        isActive: true,
        sorderBomShow: false,
        supplierItemID: null
      },
      DBExportTableShow: false,
      DBSelectDataTable: [],
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/war_itemstock/get',
        add: '/mtm/war_itemstock/adds',
        edit: '/mtm/war_itemstock/updates',
        delete: '/mtm/war_itemstock/deletes',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore',
        ItemTextureGroupComboStore: '/mtm/combo/ItemTextureGroupComboStore',
        ItemGroupComboStore: '/mtm/combo/ItemGroupComboStore',
        TechnologyGroupComboStore: '/mtm/combo/TechnologyGroupComboStore',
        ItemClassComboStore: '/mtm/combo/ItemClassComboStore',
        ItemStockPositionComboStore: '/mtm/combo/ItemStockPositionComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      clientComboStoreByQuery1: [],
      ItemClassComboStore: [],
      BusinessGroupComboStore: [],
      ItemGroupComboStore: [],
      ItemTextureGroupComboStore: [],
      TechnologyGroupComboStore: [],
      ItemStockPositionComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemClassComboStore).then(result => {
        this.ItemClassComboStore = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemTextureGroupComboStore).then(result => {
        this.ItemTextureGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemGroupComboStore).then(result => {
        this.ItemGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.TechnologyGroupComboStore).then(result => {
        this.TechnologyGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemStockPositionComboStore).then(result => {
        this.ItemStockPositionComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
        this.clientComboStoreByQuery1 = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.supplierItemID }).then(result => {
        this.clientComboStoreByQuery1 = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 编辑
    async copyRowEvent (row) {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.selectRow.id = null
        this.showEdit = true
      })
    },
    ExportTableShow () {
      this.DBSelectDataTable = []
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    exportDBSelectEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要导出的数据', status: 'error' })
        return
      }
      this.DBSelectDataTable = list
      this.DBExportTableShow = true
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
