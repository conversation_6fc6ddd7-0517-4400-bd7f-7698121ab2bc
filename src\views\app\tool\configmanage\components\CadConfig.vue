<template>
  <vxe-form :data="CadConfig" :rules="formRules" title-align="right" title-width="200">
    <vxe-form-item title="CadJson配置Gap间隔值" field="gap" span="24" :item-render="{name: '$input', attrs: {type: 'float'},props: { type: 'float'}}"></vxe-form-item>
    <vxe-form-item align="center" span="24" :item-render="{}">
      <template #default>
        <vxe-button status="primary" @click="submitEvent">保存</vxe-button>
      </template>
    </vxe-form-item>
  </vxe-form>
</template>
<script>
// import util from '@/libs/util.js'
export default {
  name: 'CadConfig',
  props: {
    success: {
      type: Function
    }
  },
  data () {
    return {
      CadConfig: {
        gap: null
      },
      formRules: {
        gap: [{ required: true, message: '请输入间隔值' }]
      },
      api: {
        get: '/mtm/configService/getCadConfig',
        edit: '/mtm/configService/UpdateCadConfig'
      }
    }
  },
  created () {
    this.get()
  },
  methods: {
    get () {
      this.$api.ActionRequest(this.api.get).then(result => {
        this.CadConfig = result
      })
    },
    submitEvent () {
      console.log(1)
      this.$api.ActionRequest(this.api.edit, this.CadConfig).then(result => {
        this.$XModal.message({ message: '修改成功', status: 'success' })
        this.success()
      })
    }
  }
}
</script>

  <style>
</style>
