<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-save" status="perfect" @click="insertEvent" v-if="menuAction.allowEdit">保存</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm">
            <vxe-form-item field="text">

              <template #default="{ data }">
                <vxe-select v-model="data.actionTypeID" placeholder="选择权限类别" clearable>
                  <vxe-option v-for="num in actionTypeCombStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item> <vxe-form-item>
              <template #default>
                <vxe-button type="submit" @click="loadData" status="success">查询</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table ref='master_table' id='SymRoleDetailTable' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :loading="submitLoading" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <!-- <vxe-table-column field="actionTypeCode" title="权限类别编码" sortable show-overflow></vxe-table-column> -->
      <vxe-table-column field="actionTypeName" title="权限类别名称" sortable show-overflow width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="code" title="角色编码" sortable show-overflow> </vxe-table-column> -->
      <vxe-table-column field="codeName" title="角色名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="查看" show-overflow width="50px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.isActive"></vxe-checkbox>
        </template>
      </vxe-table-column>
      <vxe-table-column field="allowAdd" title="添加" show-overflow width="50px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.allowAdd"></vxe-checkbox>
        </template>
      </vxe-table-column>
      <vxe-table-column field="allowEdit" title="编辑" show-overflow width="50px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.allowEdit"></vxe-checkbox>
        </template>
      </vxe-table-column>
      <vxe-table-column field="allowDelete" title="删除" show-overflow width="50px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.allowDelete"></vxe-checkbox>
        </template>
      </vxe-table-column>
      <vxe-table-column field="allowPrint" title="打印/导出" show-overflow width="50px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.allowPrint"></vxe-checkbox>
        </template>
      </vxe-table-column>

    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>

  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'SymRoleDetail',
  mixins: [detailTableMixins],
  // props: {
  //   footerCompanyInfo: {
  //     type: Boolean,
  //     default: true
  //   }
  // },
  data () {
    return {
      formData: {},
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }]
      },
      api: {
        get: '/mtm/sym_roleaction/get',
        edit: '/mtm/sym_roleaction/updates',
        actionTypeCombStore: '/mtm/combo/actionTypeComboStore'
      },
      actionTypeCombStore: []
    }
  },
  async created () {
    this.loadData({ id: this.form.id })
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.actionTypeCombStore).then(result => {
        this.actionTypeCombStore = result
      })
    },
    async insertEvent () {
      // const loading = this.$loading({
      //   lock: true,
      //   text: '保存中...',
      //   spinner: 'el-icon-loading',
      // });
      await this.$api.ActionRequest(this.api.edit, this.tableData).then(result => {
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.loadData({ id: this.form.id })
        // loading.close();
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
