<template>
  <d2-container class="page" type="full">
    <d2-page-cover />
    <template slot="footer">
      <div class="indexfooter">
        <div>
          <d2-icon-svg class="logo" name="xiaoma" />
        </div>
        <div class="btn-group">
          <span class="btn-group__btn">Vue3.0</span> |
          <span class="btn-group__btn">D2Admin</span> |
          <!-- <span class="btn-group__btn" @click="$open('')">简化版</span> |
          <span class="btn-group__btn" @click="$open('')">掘金</span> | -->
          <el-popover :width="172" trigger="hover">
            <p class="d2-mt-0 d2-mb-10">{{company}} </p>
            <img src="./image/<EMAIL>" style="width: 172px;">
            <span slot="reference" class="btn-group__btn btn-group__btn--link">
              <d2-icon name="weixin" />
              {{company}} {{versions}}
            </span>
            <p style="font-size: 12px; margin-top: 0px; margin-bottom: 0px;">
              12345678901
            </p>
          </el-popover>
        </div>
      </div>
    </template>
  </d2-container>
</template>

<script>
// import D2Badge from './components/d2-badge'
// import D2Help from './components/d2-help'
import D2PageCover from './components/d2-page-cover'
export default {
  components: {
    // D2Badge,
    // D2Help,
    D2PageCover
  },
  data () {
    return {
      company: process.env.VUE_APP_Development_Company,
      versions: process.env.VUE_APP_Development_Versions
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  .logo {
    width: 300px;
    height: 70px;
  }
  .btn-group {
    color: $color-text-placehoder;
    font-size: 12px;
    line-height: 12px;
    margin-top: 0px;
    margin-bottom: 20px;
    .btn-group__btn {
      color: $color-text-sub;
      &:hover {
        color: $color-text-main;
      }
      &.btn-group__btn--link {
        color: $color-primary;
      }
    }
  }
  .indexfooter {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    flex-direction: column;
    -webkit-align-content: center;
    align-content: center;
    justify-content: center;
  }
}
</style>
