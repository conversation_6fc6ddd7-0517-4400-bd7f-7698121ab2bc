<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" size="mini" clearable>
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="genderID">
              <template #default="{ data }">
                <el-select v-model="data.genderID" filterable placeholder="性别" size="mini" clearable>
                  <el-option v-for="item in sexList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable placeholder="款式" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod2" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery2" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="bomRuleType">
              <template #default="{ data }">
                <vxe-select v-model="data.bomRuleType" placeholder="分类" clearable>
                  <vxe-option v-for="item in RuleTypeEnumComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelRuleType">
              <template #default="{ data }">
                <vxe-select v-model="data.modelRuleType" placeholder="类型" clearable>
                  <vxe-option v-for="item in ModelRuleTypeEnumsComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelelemruleMasterTable' ref='master_table' :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}" :height="TableHeight">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="bomRuleTypeText" title="分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelRuleTypeText" title="计算分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemListText" title="关联款式" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="genderID" title="性别" sortable width="100" :formatter="val=>formatSex(val)"> </vxe-table-column>
      <vxe-table-column field="conditionStr" title="条件" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="operationString" title="公式" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="externalSort" title="外部顺序" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-copy" v-if="menuAction.allowAdd" @click="copyRowEvent(row)"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">

      <el-row>
        <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
          <vxe-form-item title="关联款式对象" field="modelElemListID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemListID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod">
                <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="计算分类" field="bomRuleType" span="12">
            <template #default="{ data }">
              <el-select v-model="data.bomRuleType" filterable placeholder="请选择" size="mini">
                <el-option v-for="item in RuleTypeEnumComboStore" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="支持类型" field="modelRuleType" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelRuleType" filterable placeholder="请选择" size="mini">
                <el-option v-for="item in ModelRuleTypeEnumsComboStore" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="条件" field="conditionStr" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
          <vxe-form-item title="公式" field="operationString" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
          <vxe-form-item title="内部顺序" field="sort" span="8" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
          <vxe-form-item title="外部顺序" field="externalSort" span="8" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
          <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
          <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
          <vxe-form-item align="center" span="24">
            <template v-slot>
              <vxe-button type="submit" status="primary">保存</vxe-button>
              <vxe-button type="reset">重置</vxe-button>
            </template>
          </vxe-form-item>
        </vxe-form>
      </el-row>
      <el-divider content-position="left"></el-divider>
      <el-row>
        <vxe-form :data="dome" title-align="right" title-width="100">
          <vxe-form-item title="款式查询" field="modelElemListID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemListID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" clearable :remote-method="remoteMethod1">
                <el-option v-for="item in ModelElemListComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="款式明细查询" field="modelElemID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" clearable :remote-method="remoteMethod3">
                <el-option v-for="item in ModelElemComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="客户查询" field="clientID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.clientID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" clearable :remote-method="remoteMethod4">
                <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="物料查询" field="itemID" span="12">
            <template #default="{ data }">
  <el-select v-model="data.itemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="物料" clearable :remote-method="remoteMethod5">
    <el-option v-for="item in ItemComboStoreByQuery" :key="item.value" :label="'['+item.code+']'+item.label" :value="item.value">
    </el-option>
  </el-select>
</template>
          </vxe-form-item>
        </vxe-form>

      </el-row>
      <el-divider content-position="left"></el-divider>
      <el-row>
        <el-alert title="款式:Mxxx.C" type="success" :closable="false">
        </el-alert>
        <el-alert title="款式明细:Exxx.C/Exxx.ID" type="info" :closable="false">
        </el-alert>
        <!-- <el-alert title="客户:Client.C" type="warning" :closable="false"></el-alert> -->
        <el-alert title="物料:辅料Ixxxx.ID,面料LItem.C" type="success" :closable="false">
        </el-alert>
        <el-alert title="业务类型:TSorderType.C,目前业务类型只有MTMGD(MTM高定)/TP(贴牌)/MTMTZ(团装)" type="info" :closable="false">
        </el-alert>
        <el-alert title="规格:Sxxxx.F" type="warning" :closable="false">
        </el-alert>
      </el-row>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modelelemrule',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      dome: {
        modelElemListID: null,
        modelElemID: null,
        clientID: null
      },
      formData: {
        modelElemListID: null,
        bomRuleType: null,
        modelRuleType: null,
        // otherElemID: null,
        conditionStr: null,
        operationString: null,
        sort: 1,
        externalSort: 1,
        remark: '',
        isActive: true
      },
      formRules: {
        modelElemListID: [{ required: true, message: '请选择款式' }],
        bomRuleType: [{ required: true, message: '请选择分类' }],
        modelRuleType: [{ required: true, message: '请选择类型' }],
        conditionStr: [{ required: true, message: '请输入条件' }],
        operationString: [{ required: true, message: '请输入结果' }],
        sort: [{ required: true, message: '请输入排序' }],
        externalSort: [{ required: true, message: '请输入外部排序' }]
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
      },

      api: {
        get: '/mtm/mom_modelelemrule/get',
        add: '/mtm/mom_modelelemrule/adds',
        edit: '/mtm/mom_modelelemrule/updates',
        delete: '/mtm/mom_modelelemrule/deletes',
        ModelRuleTypeEnumsComboStore: '/mtm/combo/ModelRuleTypeEnumsComboStore',
        RuleTypeEnumComboStore: '/mtm/combo/RuleTypeEnumComboStore',
        SorderTypeComboStore: '/mtm/combo/SorderTypeComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ItemComboStoreByQuery: '/mtm/comboQuery/ItemComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        SizeColumnComboStoreByQuery: '/mtm/comboQuery/SizeColumnComboStoreByQuery'

      },
      GroupComboStore: [],
      ModelRuleTypeEnumsComboStore: [],
      RuleTypeEnumComboStore: [],
      clientComboStoreByQuery: [],
      ItemComboStoreByQuery: [],
      ModelElemListComboStoreByQuery: [],
      ModelElemListComboStoreByQuery1: [],
      ModelElemListComboStoreByQuery2: [],
      ModelElemComboStoreByQuery: [],
      ModelElemComboStoreByQuery1: [],
      SizeColumnComboStoreByQuery: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelRuleTypeEnumsComboStore).then(result => {
        this.ModelRuleTypeEnumsComboStore = result
      })
      await this.$api.ActionRequest(this.api.RuleTypeEnumComboStore).then(result => {
        this.RuleTypeEnumComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStoreByQuery).then(result => {
        this.ItemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.ModelElemListComboStoreByQuery1 = result
        this.ModelElemListComboStoreByQuery2 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
        this.ModelElemComboStoreByQuery1 = result
      })
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery1 = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery2 = result
      })
    },
    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { text: query }).then(result => {
        this.ItemComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row) {
      if (this.$utils.has(row, 'id')) {
        row.id = null
      }
      if (this.$utils.has(row, 'code')) {
        row.code = null
      }
      if (this.$utils.has(row, 'codeName')) {
        row.codeName = null
      }
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
