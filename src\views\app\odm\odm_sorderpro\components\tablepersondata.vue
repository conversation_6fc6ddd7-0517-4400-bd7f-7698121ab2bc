<template>
  <el-card class="box-card sorderprodetail">
    <div slot="header" class="clearfix">
      <span>基础信息</span>&nbsp;&nbsp;
      <span style="font-size: 16px;font-weight: 800;">{{activeModel.modelText}}</span>
      <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
    </div>
    <div class="text item">
      <el-row>
        <el-col :span="24">
          <vxe-toolbar>
            <template v-slot:buttons>
              <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
              <vxe-button status="warning">批量检验</vxe-button>
              <vxe-button status="danger" @click="showUpLoad">导入数据</vxe-button>
              <vxe-button status="success" content="导出模板(包含数据)" @click="exportClick(true)"></vxe-button>
            </template>
            <template v-slot:tools>
              <el-input placeholder="请输入顾客/尺码/客户订单号" v-model.trim="searchForm.text" class="input-with-select" size="mini" clearable>
                <el-button slot="append" icon="el-icon-search " @click="load"></el-button>
              </el-input>
              <!-- <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="load">
              </vxe-button> -->
            </template>
          </vxe-toolbar>
          <vxe-table style="width: 100%;" border resizable keep-source show-overflow height="200" ref="master_table" @cell-click='tableCellClick' :row-class-name="rowClassName" :data="tableData" :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false,showStatus: true, }">>
            <vxe-table-column type="radio" width="60"></vxe-table-column>
            <vxe-table-column field="lineNum" title="行号" width="60"></vxe-table-column>
            <vxe-table-column field="clientPersonID" title="顾客" :edit-render="{autofocus: '.custom-input'}">
              <template #edit="{ row }">
                <client-person-select :sorderStore="sorderStore" :detailrow="row" @setClientPerson="setClientPerson" />
              </template>
              <template #default="{ row }">{{ row.clientPersonText }}</template>
            </vxe-table-column>
            <vxe-table-column field="height" title="身高" :edit-render="{name: '$input', props: {type: 'float'}}"></vxe-table-column>
            <vxe-table-column field="sizeID1" title="尺码" :edit-render="{autofocus: '.custom-input'}">
              <template #edit="{ row }">
                <size-select :sorderStore="sorderStore" :detailrow="row" @setSize="setSize" />
              </template>
              <template #default="{ row }">{{ row.sizeText }}</template>
            </vxe-table-column>
            <vxe-table-column field="qty" title="数量" :edit-render="{name: '$input', props: {type: 'number'}}"></vxe-table-column>
            <vxe-table-column field="customerNumber" title="客户订单号" :edit-render="{name: 'input'}"></vxe-table-column>
            <vxe-table-column field="remark" title="备注" :edit-render="{name: 'input'}"></vxe-table-column>
            <!-- <vxe-table-column field="date13" title="Date" :edit-render="{name: '$input', props: {type: 'date'}}"></vxe-table-column> -->
            <vxe-table-column title="操作" width="150" :fixed='tableOptFixed'>
              <template #default="{ row }">
                <template v-if="$refs.master_table.isActiveByRow(row)">
                  <vxe-button @click="saveRowEvent(row)">保存</vxe-button>
                  <vxe-button @click="cancelRowEvent(row)">取消</vxe-button>
                </template>
                <template v-else>
                  <vxe-button type="text" icon="fa fa-edit" @click="editRowEvent(row)">编辑</vxe-button>
                  <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)"> </vxe-button>
                  <vxe-button type="text" icon="fa fa-files-o" @click="deepCopyEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
                </template>
              </template>
            </vxe-table-column>
          </vxe-table>
        </el-col>
        <el-col :span="24">
          <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
          </vxe-pager>
        </el-col>

      </el-row>
    </div>
    <el-dialog title="导入表格" :visible.sync="showUpLoadShow" width="30%" :before-close="handleClose" :append-to-body="true" :destroy-on-close="true">
      <up-load-excel v-if="showUpLoadShow" :upLoadResult="upLoadResult" :SorderID="sorderStore.id" :SorderProModelID="activeModel.id" />
    </el-dialog>
  </el-card>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import ClientPersonSelect from './clientpersonselect'
import SizeSelect from './sizeselect'
import UpLoadExcel from './uploadexcel.vue'
export default {
  name: 'TablePersonData',
  mixins: [masterTableMixins],
  components: {
    ClientPersonSelect,
    SizeSelect,
    UpLoadExcel
  },
  props: {
    activeModel: {
      type: Object,
      required: true
    },
    sorderStore: {
      type: Object
    },
    psersonheight: {
      type: String
    }
  },
  data () {
    return {
      mtmpai: process.env.VUE_APP_API,
      showUpLoadShow: false,
      tableData: [

      ],
      sexList: [
        { label: '女', value: '0' },
        { label: '男', value: '1' }
      ],
      searchForm: {
        sorderId: this.sorderStore.id,
        modelId: this.activeModel.modelId
      },
      api: {
        get: '/mtm/oDM_SorderDetail/getPro',
        edit: '/mtm/oDM_SorderDetail/modifyPro',
        delete: '/mtm/oDM_SorderDetail/deletePro',
        deepclone: '/mtm/oDM_SorderDetail/DeepClone',
        SorderProExportAsync: '/fs/export/sorderpro/excel'
      }

    }
  },
  created () {
    this.loadData().then((res) => {
      this.$nextTick(() => {
        var row = this.tableData[0]
        this.$refs.master_table.setRadioRow(row)
        this.selectRow = row
        this.setRadioRow()
      })
    })
  },
  methods: {
    load () {
      this.loadData().then((res) => {
        this.$nextTick(() => {
          var row = this.tableData[0]
          this.setCheck(row)
        })
      })
    },
    deepCopyEvent (row) {
      this.tableLoading = true
      this.$api.ActionRequest(this.api.deepclone, row).then(result => {
        this.$XModal.message({ message: '复制成功', status: 'success' })
        this.tableLoading = false
        this.loadData()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    saveRowEvent (row) {
      const $table = this.$refs.master_table
      $table.clearActived().then(() => {
        this.modify(row).then(() => {
          this.selectRow = row
          this.setCheck(row)
        })
      })
    },
    editRowEvent (row) {
      const $table = this.$refs.master_table
      $table.setActiveRow(row)
    },
    cancelRowEvent (row) {
      const $table = this.$refs.master_table
      $table.clearActived().then(() => $table.revertData(row))
    },
    insertEvent () {
      var dto = { sorderId: this.activeModel.sorderId, modelId: this.activeModel.modelId, height: 0, qty: 1 }
      this.modify(dto)
    },
    modify (data) {
      this.tableLoading = true
      return new Promise((resolve, reject) => {
        this.$api.ActionRequest(this.api.edit, data).then(result => {
          this.tableData = result.items
          this.searchForm.totalCount = result.totalCount
          this.tableLoading = false
          this.loadData()
          resolve({ data: result.items })
        }).catch(() => {
          this.tableLoading = false
        })
        // this.tableLoading = false
      })
    },
    setClientPerson ({ detailrow, person }) {
      detailrow.clientPersonID = person.id
      detailrow.clientPersonText = person.label
      detailrow.height = person.height
    },
    setRadioRow () {
      this.$emit('selectDetail', { data: this.selectRow })
    },
    // 编辑事件
    editClosedEvent ({ row, column }) {
      this.selectRow = row
      // this.$emit('saveAll')
      const $table = this.$refs.master_table
      const field = column.property
      // const cellValue = row[field]
      // 判断单元格值是否被修改
      if ($table.isUpdateByRow(row, field)) {
        setTimeout(() => {
          // 局部更新单元格为已保存状态
          this.modify(row).then(() => {
            $table.reloadRow(row, null, field)
            this.setCheck(row)
          })
        }, 100)
      }
    },
    setCheck (row) {
      // setTimeout(() => {
      // this.$nextTick(() => {
      if (!this.$refs.master_table.isCheckedByRadioRow(row)) {
        // console.log('没有选中')
        // this.$refs.master_table.setRadioRow(row)
      } else {
        // console.log('选中')
      }
      this.$refs.master_table.setRadioRow(row)
      this.$refs.master_table.setCurrentRow(row)
      this.$refs.master_table.setRadioRow(row).then(() => {
        this.selectRow = row
        this.setRadioRow()
      })
      // })
      // }, 200)
    },
    tableCellClick ({ column, row }) {
      if (column.title !== '操作') {
        this.setCheck(row)
      }
    },
    setSize ({ detailrow, sizeRow }) {
      if (sizeRow == null) {
        detailrow.sizeID1 = null
        detailrow.sizeText = null
      } else {
        detailrow.sizeID1 = sizeRow.id
        detailrow.sizeText = sizeRow.code
      }
    },
    rowClassName ({ row, rowIndex }) {
      var sizeCheckClass = ''
      if (!row.isChecked) {
        sizeCheckClass = 'sizeCheckError'
      }
      return sizeCheckClass
    },
    showUpLoad () {
      this.showUpLoadShow = true
    },
    handleClose (done) {
      this.showUpLoadShow = false
      done()
    },
    upLoadResult ({ success, info, error }) {
      // console.log(success, info, error)
      var message = '导入成功'
      var su = 'success'
      if (success) {
        su = 'success'
        if (info !== null && info !== '') {
          message = info
        }
        this.load()
        this.showUpLoadShow = false
      } else {
        su = 'error'
        message = error
      }
      this.$message({
        dangerouslyUseHTMLString: true,
        showClose: true,
        type: su,
        message: message,
        center: true,
        duration: 1000 * 5,
        customClass: 'importrResult'
      })
    },
    exportClick (hasData = false) {
      var api = this.mtmpai.replace('/api/', '')
      var url = api + this.api.SorderProExportAsync
      this.$api.ActionExcelRequest(url, { sorderID: this.sorderStore.id, hasData: hasData, sorderProModelID: this.activeModel.id }).then(res => {
        const url = window.URL.createObjectURL(res) // 创建一个新的 URL 对象
        console.log(url)
        // 以下代码一句话解释，在页面上生成一个a标签并指定href为上面的url,然后模拟点击，以实现自动下载
        var a = document.createElement('a')
        document.body.appendChild(a)
        a.href = url
        a.download = '订单模板.xlsx'
        a.click()
        window.URL.revokeObjectURL(url)
      })
    }
  }
}
</script>

<style lang="scss" >
.sorderprodetail {
  .sizeCheckError {
    background-color: #a47e0d;
    // color: cornsilk;
  }
}
</style>
