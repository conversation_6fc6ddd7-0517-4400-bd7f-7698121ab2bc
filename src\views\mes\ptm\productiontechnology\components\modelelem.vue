<template>
  <d2-container class="modelelem">
    <h3>工艺信息</h3>
    <vxe-toolbar perfect custom>
      <template v-slot:tools>
      </template>
    </vxe-toolbar>
    <vxe-table align="center" v-if="elemshow" :show-overflow="false" id="productiontechnology_modelelem" height="1000"  row-class-name="modelelemrowclass" :scroll-y="{scrollToTopOnChange:true}" ref="productiontechnology_modelelem" :data="ModelElems" :custom-config="{storage: true}">
      <!-- <vxe-table-column type="seq" width="60"></vxe-table-column> -->
      <vxe-table-column field="modelElemTypeName" title="款式" width="70" class-name='cellclass'></vxe-table-column>
      <!-- <vxe-table-column field="modelElemBaseSort" title="部位" width="100"></vxe-table-column> -->
      <!-- <vxe-table-column field="modelElemBaseName" title="部位" width="100"></vxe-table-column> -->
      <vxe-table-column field="modelElemListName" title="款式" width="100"></vxe-table-column>
      <vxe-table-column field="modelElemCode" title="编码" width="100"></vxe-table-column>
      <vxe-table-column field="modelElemName" title="工艺" width="100"></vxe-table-column>
      <vxe-table-column field="itemName" title="物料数据" width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="数量" width="48"></vxe-table-column>
      <vxe-table-column field="itemComp" title="成分" width="100"></vxe-table-column>
      <vxe-table-column field="input" title="备注" width="100"></vxe-table-column>
    </vxe-table>
  </d2-container>
</template>

<script>
export default {
  name: 'modelElem',
  props: {
    form: {
      type: Object
    }
  },
  watch: {
    'form.modelElems': {
      deep: true,
      handler (newval, oldval) {
        if (newval && newval !== null) {
          this.ModelElems = []
          this.elemshow = false
          console.log(this.$refs.productiontechnology_modelelem.getScroll())
          this.$refs.productiontechnology_modelelem.refreshScroll()
          this.$refs.productiontechnology_modelelem.clearScroll()
          this.$refs.productiontechnology_modelelem.refreshColumn()

          this.$refs.productiontechnology_modelelem.scrollTo(0, 0)
          this.ModelElems = newval.map(item => { if (item.modelElemTypeID === 3) { return item } })// 只显示辅料
          this.elemshow = true
        }
      }
    }
  },
  data () {
    return {
      ModelElems: [],
      elemshow: true,
      tableHeight: 800
    }
  },
  mounted () {
    window.onresize = () => {
      return (() => {
        this.setTableHeight()
      })()
    }
    this.setTableHeight()
  },
  // 初始化
  created () {
    if (this.ModelElems.length === 0 && this.form) {
      this.$refs.productiontechnology_modelelem.clearScroll()
      this.ModelElems = this.form.modelElems.map(item => { if (item.modelElemTypeID === 3) { return item } })// 只显示辅料
    }
    this.setTableHeight()
  },
  methods: {
    setTableHeight: function () {
      this.$nextTick(() => {
        var contenttop = document.querySelector('#app')
        var contenttopclient = contenttop.getBoundingClientRect()
        this.tableHeight = contenttopclient.height - 600
        // console.log('表格高度:' + this.tableHeight)
      })
    }
  }

}
</script>

<style lang='scss'>
.modelelem {
  //  height:calc(100%-210px);
  // max-height: 800px;
  // max-height: calc(100%-210px);
  overflow: auto;
  // max-height: 800px;
  .vxe-table.size--mini .vxe-body--column.col--ellipsis,
  .vxe-table.size--mini .vxe-footer--column.col--ellipsis,
  .vxe-table.size--mini .vxe-header--column.col--ellipsis,
  .vxe-table.vxe-editable.size--mini .vxe-body--column {
    height: 25px !important;
  }
  .vxe-table--body-wrapper {
    overflow-x: auto !important;
  }
  .d2-container-full__body {
    // overflow-x: auto !important;
    overflow-y: hidden !important;
  }
  .modelelemrowclass {
    font-size: 16px;
    font-weight: 700;
  }
  // .scroll-top {
  //   overflow: auto;
  //   overflow-x: hidden;
  //   overflow-y: scroll;
  // }

  // .scroll-top::-webkit-scrollbar {
  //   width: 100%;
  //   height: 10px;
  //   background: transparent;
  // }

  // .scroll-top::-webkit-scrollbar-thumb {
  //   background: gray;
  //   border-radius: 10px;
  // }
}
</style>
