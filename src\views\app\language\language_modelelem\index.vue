<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="languageType">
              <template #default="{ data }">
                <el-select v-model="data.languageType" filterable placeholder="语言类型" size="mini" style="width:110px">
                  <el-option v-for="item in LanguageComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="gender">
              <template #default="{ data }">
                <vxe-select v-model="data.gender" placeholder="性别" clearable style="width:110px">
                  <vxe-option :key="true" value="true" label="男"></vxe-option>
                  <vxe-option :key="false" value="false" label="女"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelGroupID">
              <template #default="{ data }">
                <el-select v-model="data.modelGroupID" placeholder="类别" clearable size="mini" style="width:110px">
                  <el-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemTypeID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemTypeID" placeholder="款式类别" size="mini" style="width:110px" clearable>
                  <el-option v-for="item in ModelElemTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemBaseID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelElemBaseID" filterable remote reserve-keyword placeholder="款式部位" size="mini" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in ModelElemBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='LanguageModelelemMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60" />
      <vxe-table-column type="checkbox" width="60" />
      <vxe-table-column field="languageTypeText" title="语言类型" sortable width="100" />
      <vxe-table-column field="modelGroupText" title="品类" sortable width="100" />
      <vxe-table-column field="modelElemBaseText" title="部位" sortable width="100" />
      <vxe-table-column field="genderText" title="性别" sortable width="100" />
      <vxe-table-column field="modelElemTypeText" title="类型" sortable width="100" />
      <vxe-table-column field="modelElemListText" title="款式" sortable width="100" />
      <vxe-table-column field="modelElemText" title="款式明细" sortable width="100" />
      <vxe-table-column field="code" title="编码" sortable width="100" />
      <vxe-table-column field="codeName" title="名称" sortable width="100" />
      <vxe-table-column field="description" title="描述" sortable width="100" />
      <vxe-table-column field="remark" title="备注" sortable width="100" />
      <vxe-table-column field="sort" title="顺序" sortable width="100" />
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100" />
      <vxe-table-column field="createBy" title="创建人" sortable width="100px" />
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100" />
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" show-zoom resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}" />
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}" />
        <vxe-form-item title="语言类型" field="languageType" span="12" :item-render="{name: '$select', options: LanguageComboStore}"></vxe-form-item>
        <!-- <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}" /> -->

        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}" />
        <vxe-form-item title="描述" field="description" span="24" :item-render="{name: '$textarea'}" />
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}" />
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'language_modelelem',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        languageType: 10
      },
      formData: {
        code: '',
        codeName: '',
        remark: '',
        languageType: 10,
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 100, message: '长度在 2 到 100 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 100, message: '长度在 2 到 100 个字符' }]
      },
      api: {
        get: '/mtm/language_modelelem/get',
        add: '/mtm/language_modelelem/adds',
        edit: '/mtm/language_modelelem/updates',
        delete: '/mtm/language_modelelem/deletes',
        LanguageComboStore: '/mtm/combo/LanguageComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        ModelElemTypeComboStore: '/mtm/combo/modelElemTypeComboStore',
        ModelElemBaseComboStoreByQuery: '/mtm/comboQuery/ModelElemBaseComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      LanguageComboStore: [],
      GroupComboStore: [],
      ModelElemTypeComboStore: [],
      ModelElemBaseComboStoreByQuery: [],
      ModelElemListComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.LanguageComboStore).then(result => {
        this.LanguageComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemTypeComboStore).then(result => {
        this.ModelElemTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    }
    // remoteMethod(query,gid) {
    // return new Promise(resolve => {
    // this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query, gid: gid }).then(result => {
    // this.ModelElemListComboStoreByQuery = result
    // return resolve(true)
    // })
    // })
    // })

    // editEventThen(row) {
    // this.remoteMethod(null, row.id).then(res => { this.showEdit = true })
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
