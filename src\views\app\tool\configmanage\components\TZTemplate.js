export default {
  name: 'TzExcelTemplate',
  data () {
    return {
      api: {
        init: '/mtm/sym_importsorderbase/initSorderProExcelImportTemlate'
      }
    }
  },
  methods: {
    async InitTZTemplateEvent () {
      this.$confirm('如果团装通用模板数据已存在,此操作会重置已存在的数据,是否继续操作', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.ActionRequest(this.api.init, 2).then(result => {
          this.$message({
            message: '生成成功',
            type: 'success'
          })
        })
      }).catch(() => {

      })
    }
  }
}
