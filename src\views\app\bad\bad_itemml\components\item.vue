<template>
  <div>
    <el-select v-model="modelElem.itemID1" :remote-method="query=>remoteMethod(query,modelElem)" @change="modelElemChange" size="mini" remote filterable placeholder="货号" clearable>
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'modelElemItem',
  data () {
    return {
      options: [],
      itemID: null
    }
  },
  props: {
    api: {
      type: Object,
      requited: true
    },
    modelElem: {
      type: Object,
      requited: true
    },
    itemSet: {
      type: Function,
      requited: true
    }
  },
  watch: {
    'modelElem.itemID1': {
      handler: function (newval, oldval) {
        if (newval === null) {
          this.itemID = null
        }
      }
    }
  },
  async created () {
    // console.log(1)
    await this.get('', this.modelElem.itemID1, this.modelElem.modelElemID)
    this.itemID = this.modelElem.itemID1
  },
  methods: {
    async get (query, id = null, modelElemID = null) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query, gid: id, modelElemID: modelElemID }).then(result => {
        this.options = result
      })
    },
    async remoteMethod (query, modelElem) {
      await this.get(query, null, modelElem.modelElemID)
    },
    modelElemChange (val) {
      this.itemSet(this.modelElem, val)
    }

  }
}
</script>

<style>
</style>
