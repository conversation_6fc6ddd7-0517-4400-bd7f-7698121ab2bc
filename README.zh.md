[D2Admin](https://github.com/d2-projects/d2-admin) 是一个完全 **开源免费** 的企业中后台产品前端集成方案，使用最新的前端技术栈，小于 60kb 的本地首屏 js 加载，已经做好大部分项目前期准备工作，并且带有大量示例代码，助力管理系统敏捷开发。

**中文** | [English](https://github.com/d2-projects/d2-admin-start-kit)

##

docker build -t xiaoma-front-end:t1 .
docker rm xiaoma-front-end
docker rmi xiaoma-front-end:t1
docker stop xiaoma-front-end
docker run -d --name xiaoma-front-end -p 3000:80 xiaoma-front-end:t1

docker build -t xiaoma-front-end:t1 . | docker run -d --name xiaoma-front-end -p 3000:80 xiaoma-front-end:t1
docker stop xiaoma-front-end | docker rm xiaoma-front-end | docker rmi xiaoma-front-end:t1

## 预览

![Deploy preview](https://github.com/d2-projects/d2-admin-start-kit/workflows/Deploy%20preview/badge.svg)
[![Netlify Status](https://api.netlify.com/api/v1/badges/08ff8c93-f0a8-497a-a081-440b31fb3aa4/deploy-status)](https://app.netlify.com/sites/d2-admin-start-kit/deploys)

下列访问地址均由最新的 master 分支代码同时构建部署，访问效果完全一致，请根据自身网络情况选择合适的访问链接。

| 位置       | 链接                                                        | 部署位置     |
| ---------- | ----------------------------------------------------------- | ------------ |
| d2.pub     | [preview](https://d2.pub/d2-admin-start-kit/preview)        | 中国服务器   |
| cdn.d2.pub | [preview](https://cdn.d2.pub/d2-admin-start-kit/preview)    | 七牛云 CDN   |
| github     | [preview](https://d2-projects.github.io/d2-admin-start-kit) | GitHub pages |
| netlify    | [preview](https://d2-admin-start-kit.netlify.com)           | Netlify CDN  |

## 其它同步仓库

| 位置   | 链接                                                                                                                                           |
| ------ | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| 码云   | [https://gitee.com/d2-projects/d2-admin-start-kit](https://gitee.com/d2-projects/d2-admin-start-kit)                                           |
| coding | [https://d2-projects.coding.net/p/d2-projects/d/d2-admin-start-kit/git](https://d2-projects.coding.net/p/d2-projects/d/d2-admin-start-kit/git) |

> 如果您在 github 仓库下载很慢，可以尝试使用我们的码云仓库克隆代码
