<template>
  <d2-container class="clientPersonDetail">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="editEvent" v-if="menuAction.allowEdit">保存</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-form :data="form" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </template>
    <template>
      <d2-container>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" style="height:99%">
          <el-tab-pane label="顾客" name="clientperson">
            <client-person :form="form" ref='ClientPerson' />
          </el-tab-pane>
          <el-tab-pane label="店铺/分公司" name="clientshop">
            <client-shop :form="form" ref='ClientShop' />
          </el-tab-pane>
          <el-tab-pane label="地址" name="clientaddress">
            <client-address :form="form" ref="ClientAddress" />
          </el-tab-pane>
          <el-tab-pane label="关联版型" name="clientmodel">
            <client-model :form="form" ref="ClientModel" />
          </el-tab-pane>
        </el-tabs>
      </d2-container>
    </template>
  </d2-container>
</template>

<script>
import actionMixins from '@/mixins/action_mixins/index'
import ClientPerson from './clientperson'
import ClientShop from './clientshop'
import ClientAddress from './clientaddress'
import ClientModel from './clientmodel'
import { mapActions } from 'vuex'
// import Qs from 'qs'
export default {
  name: 'BadClientDetail',
  mixins: [actionMixins],
  components: {
    ClientPerson, ClientShop, ClientAddress, ClientModel
  },
  props: {
    form: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      activeName: 'clientperson',
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 3, max: 12, message: '长度在 3 到 20 个字符' }],
        ClassID: [{ required: true, message: '请选择系统分类' }]
      },
      formItems: [
        { field: 'code', title: '客户编码', span: 6, itemRender: { name: '$input' } },
        { field: 'codeName', title: '客户名称', span: 6, itemRender: { name: '$input' } },
        { field: 'shortName', title: '简称', span: 6, itemRender: { name: '$input' } },
        { field: 'contact', title: '联系人', span: 6, itemRender: { name: '$input' } },
        { field: 'tel', title: '手机号', span: 6, itemRender: { name: '$input' } },
        { field: 'fax', title: '传真', span: 6, itemRender: { name: '$input' } },
        { field: 'mobile', title: '电话号码', span: 6, itemRender: { name: '$input' } },
        { field: 'email', title: '邮箱', span: 6, itemRender: { name: '$input' } },
        { field: 'address', title: '地址', span: 6, itemRender: { name: '$input' } },
        { field: 'source', title: '来源', span: 6, itemRender: { name: '$input' } },
        { field: 'customerCode', title: '自编码', span: 6, itemRender: { name: '$input' } },
        { field: 'supplierCode', title: '供应商编码', span: 6, itemRender: { name: '$input' } },
        { field: 'classID', title: '系统分类', span: 6, itemRender: { name: '$select', options: [] } },
        // { field: 'BankID', title: '银行', span: 12, itemRender: { name: '$input' } },
        // { field: 'CurrencyID', title: '货币', span: 12, itemRender: { name: '$input' } },
        // { field: 'VatID', title: '税率', span: 12, itemRender: { name: '$input' } },
        // { field: 'PaymentTermsID', title: '付款条款', span: 12, itemRender: { name: '$input' } },
        // { field: 'PaymentMethodID', title: '付款方式', span: 12, itemRender: { name: '$input' } },
        { field: 'clientCode1', title: '客户编码', span: 6, itemRender: { name: '$input' } },
        { field: 'clientTypeCode', title: '客户类型编码', span: 6, itemRender: { name: '$input' } },
        { field: 'channelCode', title: '子编码', span: 6, itemRender: { name: '$input' } },
        { field: 'salerCode', title: '销售编码', span: 6, itemRender: { name: '$input' } },
        { field: 'salerName', title: '销售名称', span: 6, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 6, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 6, itemRender: { name: '$switch' } }
        // { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: { edit: '/mtm/bad_client/updates' }

    }
  },
  methods: {
    ...mapActions('app/bad/bad_client', ['loadBadClient']),
    goback () {
      this.$emit('nextpage', { pagename: 'master', data: {}, keepalive: false })
    },
    submitEvent () { },
    handleClick () { },

    async editEvent () {
      var personData = this.$refs.ClientPerson.getTableData()
      var a1 = await this.$refs.ClientPerson.fullValidEvent()
      var shopData = this.$refs.ClientShop.getTableData()
      var a2 = await this.$refs.ClientShop.fullValidEvent()
      var addressData = this.$refs.ClientAddress.getTableData()
      var a3 = await this.$refs.ClientAddress.fullValidEvent()
      if (!a1 || !a2 || !a3) {
        return
      }
      this.form.clientPerson = personData
      this.form.clientShop = shopData
      this.form.clientAddress = addressData
      await this.$api.ActionRequest(this.api.edit, [this.form]).then(result => {
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.$refs.ClientPerson.loadData({ id: this.form.id })
        this.$refs.ClientShop.loadData({ id: this.form.id })
      })
    }
  }
}
</script>

<style  lang="scss">
.clientPersonDetail {
  .el-tabs__content {
    height: 90%;
  }
}
</style>
