
<template>
  <div :class="{html2svg:html2svg}">
    <div style="margin:0;padding:0;background-color:#fff;" id="pdfDom" class="split">
      <div class="bodyclass" ref="bodyclass">
        <div class="container-body" id="searchresults" v-for="(item,index2) in SorderList" v-bind:key="index2" style="page-break-before:always">
          <!-- <div v-if="getHeightUnfold(index2==(sorder.SorderInfo.length-1))" style="page-break-after:always;"></div> -->
          <table class="table main-table" id="table_data">
            <tr>
              <th>
                <div class="text-bold table-title table-thead">
                  <div style="position: absolute;  white-space:nowrap; font-size: 12px;">
                    <div>
                      <div style="display: inline-block;float: left">生产<br>单号 </div>
                      <div style="font-size: 23px;padding-left: 10px;  margin-left: 25px;">{{item.PlanNum}}</div>
                      <div style="clear: both"></div>
                    </div>
                    <div style="text-align: left;"> <span>车间：{{item.WorkShop}}</span>&nbsp;</div>
                  </div>
                  <div class="main-title">
                    <div class="title-content">
                      <span>温州金鸳鸯服饰有限公司MTM订单</span><br>
                      <!-- <span>SHARMOON.EZ Garment CO.,LTD MTM ORDERS</span> -->
                    </div>
                    <div v-if="item.HalfFitting" style="position: absolute;top: 10px;right: 150px;font-size: 21px;font-weight: 600;text-align: right; font-family: LiSu;">
                      <div>
                        <!-- <span>试衣</span> -->
                      </div>
                    </div>
                    <div style="position: absolute;top: 10px;right: 10px;font-size: 15px;font-weight: 600;text-align: right;">
                      <div>
                        <!-- <span>{{sorder.GroupTexts.join(' ')}} </span> -->
                      </div>
                      <div>
                        <span>{{item.PlanSerialNum}}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </th>
            </tr>
            <tfoot style="z-index:9999 !important">
              <tr>
                <td>
                  <!-- <span id="pageFooter"></span> -->
                  <div class="table-footer clearfix" style="z-index: 9999 !important;background: white;width: 100%;text-align: left;">
                    <div class="font-transform-normal" style="width:25%;">时间：{{dateToStr(new Date(),true)}}</div>
                    <div class="font-transform-normal" style="width:25%;">客服：{{item.Owner||""}}</div>
                    <!-- <div class="font-transform-normal" style="width:25%;">配色：<span v-if="item.PFilledOwner!=null && item.PFilledOwner!=''">{{item.PFilledOwner}}<span v-if="item.PFilledOwnerMobile!=null && item.PFilledOwnerMobile!=''">({{item.PFilledOwnerMobile}})</span></span></div> -->
                    <div class="font-transform-normal">技术审核：{{item.CheckBy||""}}</div>
                  </div>
                </td>
              </tr>
            </tfoot>
            <tbody style="width:100%;height:100%" class="sorder-body">
              <tr>
                <td>
                  <div class="main-container ">
                    <div class="main-content table-responsive">
                      <table class="table table-bordered table-striped table-elem" style="border-left:1px solid #000;border-right:1px solid #000;">
                        <tr>
                          <td class="text-bold text-align-left" style="border: none;width:60px;">订单号</td>
                          <td class="text-align-left text-bold" style="border: none;">{{item.ID}}</td>
                          <td class="text-bold text-align-left" style="border: none;width:60px;">下单日期</td>
                          <td class="text-align-left" style="border: none;">{{dateToStr(new Date(item.IssueDate),false)}}</td>
                          <td class="text-bold text-align-left" style="border: none;width:60px;">交货日期</td>
                          <td class="text-align-left" style="border: none;">{{dateToStr(new Date(item.ProdPlanDate),false)}}</td>
                          <td rowspan="2" class="text-bold sorder_head_label" style="border: none;">{{item.GroupName}}</td>
                        </tr>
                        <tr>
                          <td class="text-bold text-align-left" style="border: none;">客户编号</td>
                          <td colspan="3" class="text-align-left" style="border: none;">{{item.Client}}</td>
                          <td class="text-bold text-align-left" style="border: none;">姓名</td>
                          <td class="text-bold text-align-left" style="border: none;">{{item.ClientPerson}}</td>
                        </tr>
                      </table>
                      <table class="table table-bordered table-striped table-elem" style="border:1px solid #000000">
                        <tr>
                          <td class="text-bold text-align-left text-italic text-vertical-align" style="border: none;width:8%;">
                            <p>子订单号</p>
                          </td>
                          <td class="text-bold text-align-left text-italic" style="border: none;width:13%;font-size:15px;font-weight:1000;">{{item.SubOrderNum}}</td>
                          <td class="text-bold text-align-left text-italic text-vertical-align" style="border: none;width:8%;">
                            <p>销售单号</p>
                          </td>
                          <td class="text-bold text-align-left text-italic" style="border: none;width:16%;font-size:15px;font-weight:1000;">{{item.Num}}</td>
                          <td class="text-bold text-align-left text-italic text-vertical-align" style="border: none;width:13%;">
                            <p>流水号</p>
                          </td>
                          <td class="text-align-left text-italic" style="border: none;width:13%;font-size:15px;font-weight:1000;">{{item.PlanSerialNum}}</td>
                          <td style="border: none;"></td>
                          <td class="text-bold text-align-right text-vertical-align" style="border: none;">
                            <p class="font-transform-normal">数量</p>
                          </td>
                          <td style="border: none;width:8%;font-size:15px;font-weight:1000;">
                            <p class="font-transform-normal">{{item.Qty}}</p>
                          </td>
                        </tr>
                      </table>
                      <table class="table table-bordered table-striped table-elem">
                        <tr>
                          <td class="text-bold text-align-left sorder_head_label" style="width:10%">
                            <p>半成品试衣</p>
                          </td>
                          <td class="text-align-left text-vertical-align" style="width:13%;font-size:15px;font-weight:1000;">{{item.HalfFitting ? "是" : "否"}}</td>
                          <td class="text-bold text-align-left sorder_head_label" style="width:10%">
                            <p class="font-transform-normal">洗水唛号型</p>
                          </td>
                          <td class="text-align-left text-vertical-align" style="width:27%">
                            <p class="font-transform-normal">{{item.CustomerSize}}</p>
                          </td>
                          <td class="text-bold text-align-left sorder_head_label">
                            <p class="font-transform-normal">成分</p>
                          </td>
                          <td class="text-align-left text-vertical-align">
                            <p class="font-transform-normal">{{item.FinalComposition}}</p>
                          </td>
                          <!-- <td v-if="getBusinessSubType(item.BussinessSubType) != ''" class="text-bold" style="vertical-align:middle;width:42px;font-size:22px;" rowspan="3">{{getBusinessSubType(item.BussinessSubType) }}</td> -->
                        </tr>
                        <tr>
                          <td class="text-bold text-align-left sorder_head_label">
                            <p class="font-transform-normal">面料编码</p>
                          </td>
                          <td class="text-align-left text-vertical-align">
                            <p class="font-transform-normal">{{item.ItemCode}}</p>
                          </td>
                          <td class="text-bold text-align-left sorder_head_label">
                            <p class="font-transform-normal">客供面料号</p>
                          </td>
                          <td class="text-align-left text-vertical-align">
                            <p class="font-transform-normal">{{item.FinalFabric}}</p>
                          </td>
                          <td class="text-bold text-align-left sorder_head_label">
                            <p class="font-transform-normal">面料纹理</p>
                          </td>
                          <td colspan="1" class="text-align-left text-vertical-align">
                            <p class="font-transform-normal">{{item.TextureName}}</p>
                          </td>
                        </tr>
                        <tr>
                          <td class="text-bold text-align-left sorder_head_label">
                            <p class="font-transform-normal">号型</p>
                          </td>
                          <td class="text-align-left text-vertical-align">
                            <p class="font-transform-normal">{{item.SizeCode==null?"":item.SizeCode}}</p>
                          </td>
                          <td class="text-bold text-align-left sorder_head_label">
                            <p class="font-transform-normal">版型编码</p>
                          </td>
                          <td class="text-align-left text-vertical-align">
                            <p class="font-transform-normal">{{item.ModelCode}}</p>
                          </td>
                          <td class="text-bold text-align-left sorder_head_label">
                            <p class="font-transform-normal">款式描述</p>
                          </td>
                          <td colspan="1" class="text-align-left text-vertical-align">
                            <p class="font-transform-normal">{{item.ModelName}}</p>
                          </td>

                        </tr>

                      </table>
                      <!-- 规格单 -->
                      <sorder-detail-size />
                      <!-- 特体 -->
                      <template v-if="item.BodyRemark!=null">
                        <table class="table table-bordered table-striped table-elem" style="  page-break-after: avoid;">
                          <tr>
                            <td class="text-bold text-align-center" style="vertical-align: middle;width:52px;">
                              <p class="font-transform-normal">特体</p>
                            </td>
                            <td class="text-align-left" style="vertical-align: middle;">
                              <p class="font-transform-normal">{{item.BodyRemark.RemarkStr}}</p>
                            </td>
                          </tr>
                        </table>
                      </template>
                      <!-- 样板排料和净版 -->
                      <table class="table table-striped table-bordered table-responsive table-elem" style="page-break-after: avoid;">
                        <tr>
                          <td class="text-bold text-align-center" style="vertical-align: middle;width:52px;">
                            <p class="font-transform-normal">样板排料</p>
                          </td>
                          <td style="text-align: left;white-space: pre;">
                            <p class="font-transform-normal">{{item.CadLayout}}</p>
                          </td>
                          <td class="text-bold text-align-center" style="vertical-align: middle;width:52px;">
                            <p class="font-transform-normal">净版</p>
                          </td>
                          <td style="text-align: left;white-space: pre;width:38.55%;">
                            <p class="font-transform-normal">{{item.OtherModel_m}}</p>
                          </td>
                        </tr>
                      </table>
                      <!-- 图片 -->
                      <sorder-detail-image />

                    </div>
                  </div>
                  <div class="main-content ">
                    <!-- 款式工艺  -->
                    <sorder-detail-elem />
                    <!-- 缝份 -->
                    <!-- <table class="table table-striped table-bordered table-responsive table-elem" style="  page-break-after: avoid;">
                      <tr class="pdfAvoidPageItem" v-for="(elem) in filterfengfen(item.SorderDetailElems.filter(it=>{return it.ModelElemBase.indexOf('缝份')>=0}))" v-bind:key="elem.ModelElemBase">
                        <td class="text-bold" style="vertical-align: middle;width:10%;">
                          <p class="font-transform-normal">缝份</p>
                        </td>
                        <td style="width:30%">
                          <table class="model-elem-ff-border" style="width:100%;text-align: left;">
                            <tr v-for="item in elem.List1" :key="item.ModelElemList">
                              <td style="width:40%;" class="text-bold">
                                <div>
                                  <p class="fontspan font-transform-normal" style="font-size: 12px;">
                                    {{item.ModelElemList}}
                                  </p>
                                </div>
                              </td>
                              <td style="width:70%;">
                                <div>
                                  <p class="fontspan font-transform-normal">
                                    {{item.ModelElem}}
                                  </p>
                                </div>
                              </td>
                            </tr>
                          </table>
                        </td>
                        <td style="width:30%">
                          <table class="model-elem-ff-border" style="width:100%;text-align: left;">
                            <tr v-for="item in elem.List2" :key="item.ModelElemList">
                              <td style="width:120px;" class="text-bold">
                                <div>
                                  <p class="fontspan font-transform-normal" style="font-size: 12px;">
                                    {{item.ModelElemList}}
                                  </p>
                                </div>
                              </td>
                              <td>
                                <div>
                                  <p class="fontspan font-transform-normal">
                                    {{item.ModelElem}}
                                  </p>
                                </div>
                              </td>
                            </tr>
                          </table>
                        </td>
                        <td>
                          <table class="model-elem-ff-border" style="width:100%;text-align: left;">
                            <tr v-for="item in elem.List3" :key="item.ModelElemList">
                              <td style="width:120px;" class="text-bold">
                                <div style="width:110%;">
                                  <p class="fontspan font-transform-normal" style="font-size: 12px;">
                                    {{item.ModelElemList}}
                                  </p>
                                </div>
                              </td>
                              <td>
                                <div>
                                  <p class="fontspan font-transform-normal">
                                    {{item.ModelElem}}
                                  </p>
                                </div>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table> -->
                    <!-- CTCL信息 -->
                    <table class="table table-striped table-bordered table-responsive table-elem" style="page-break-after: avoid;" v-if="item.CtClDesc&&(item.CtClDesc.CtCode || item.CtClDesc.ClCode)">
                      <tr>
                        <td style="text-align: left;">
                          <template v-if="item.CtClDesc.CtCode">
                            <span class="font-transform-normal"><b>CT代码：</b>{{item.CtClDesc.CtCode}}；{{ctclCodeDesc_method(item.CtClDesc.CtCodeDesc)}}</span>
                            <p class="font-transform-normal"><b>CT裁剪方式：</b>{{item.CtClDesc.CtModeDesc}}</p>
                          </template>
                          <template v-if="item.CtClDesc.ClCode">
                            <span class="font-transform-normal"><b>CL代码：</b>{{item.CtClDesc.ClCode}}；{{ctclCodeDesc_method(item.CtClDesc.ClCodeDesc)}}</span>
                            <p class="font-transform-normal"><b>CL缝制方式：</b>{{item.CtClDesc.ClModeDesc}}</p>
                          </template>
                        </td>
                      </tr>
                    </table>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="pagebreak_flag">&nbsp;</div>
        </div>
      </div>
    </div>
    <div class="printbtn" v-if="!hideBtns&&!html2svg">
      <el-button type="info" @click="preview">打印</el-button>
    </div>

  </div>
</template>

<script>
// import api from "@/api/sal/sal_sorderproduct/index.js";
import { isNullOrEmpty } from '@/plugin/UtilityTools'
import sorderDetailSize from './sorderdetailsize'
import sorderDetailImage from './sorderdetailimage'
import sorderDetailElem from './sorderdetailelem'
export default {
  name: 'sorderprint',
  components: {
    sorderDetailSize,
    sorderDetailImage,
    sorderDetailElem
  },
  data () {
    return {
      SorderList: [],
      res: '',
      arrIntImgRule1: [[1, 5, 6], [2, 4, 8], [10, 11, -1], [3, 9, 7]],
      arrIntImgRule2: [[1, 2, -1, -1], [3, 4, 5, 6], [7, 8, 9, 10]],
      html2svg: false, // 是否html2svg中调用：如为是，要隐藏按钮且放大页面，以便生成的svg更清晰
      hideBtns: false, // 是否隐藏按钮，在组合单查询里显示时隐藏按钮
      api: {
        get: '/mtm/oDM_Sorder/print',
        getpdf: '/mtm/oDM_Sorder/sorderPrint'
      }
    }
  },

  watch: {},
  methods: {
    pdf () {
      this.$api.ActionRequest(this.api.getpdf).then(result => {
        debugger
        var pdf = result.filePdf
        console.log(pdf)
        var blob = new Blob([pdf], { type: result.mimeType })
        var fileURL = URL.createObjectURL(blob)
        window.open(fileURL)
      })
    },
    get () {
      var nums = this.$route.query.num
      this.$api.ActionRequest(this.api.get, { nums: nums }).then(result => {
        this.SorderList = result
      })
    },
    dateToStr (d, islong) {
      if (isNullOrEmpty(d)) {
        return ''
      }
      const ultZeroize = (v, l) => {
        var z = ''
        l = l || 2
        v = String(v)
        for (var i = 0; i < l - v.length; i++) {
          z += '0'
        }
        return z + v
      }
      if (typeof d === 'string') d = new Date(d * 1000)
      if (islong) {
        return (
          d.getFullYear() +
          '-' +
          ultZeroize(d.getMonth() + 1) +
          '-' +
          ultZeroize(d.getDate()) +
          ' ' +
          ultZeroize(d.getHours()) +
          ':' +
          ultZeroize(d.getMinutes()) +
          ':' +
          ultZeroize(d.getSeconds())
        )
      } else {
        return (
          d.getFullYear() +
          '-' +
          ultZeroize(d.getMonth() + 1) +
          '-' +
          ultZeroize(d.getDate())

        )
      }
    },
    preview () {
      // // 控制分页，每高度超过1320
      // var pringFun = () => {
      //   var doc = document.getElementsByClassName('sorder-body')
      //   var pagebreak_flag = document.getElementsByClassName('pagebreak_flag')
      //   // debugger;
      //   for (var i = 0; i < doc.length; i++) {
      //     var height = doc[i].scrollHeight
      //     var a = Math.ceil(height / 1010)
      //     if (a % 2 == 1) {
      //       pagebreak_flag[i].style = 'page-break-before:always'
      //     }
      //   }
      //   window.print()// scrollHeight
      // }
      // pringFun()
    },
    htmlToPdf () { }
  },
  created () {
    // this.get()
    this.pdf()
  }
}
</script>

<style scoped>
@import "./index.css";
@import "./print_index.css";
.printbtn {
  position: fixed;
  bottom: 5%;
  left: 95%;
  z-index: 999;
}
body {
  margin: 0;
  background-image: none;
  font-size: 8px;
  width: 190mm; /* needed for Chrome */
}
/* #pageFooter {
    display: table-footer-group;
} */
.bodyclass {
  counter-increment: page;
}
#pageFooter:after {
  /* counter-increment: page;     */
  /* content:"page" counter(page) ; */
  left: 0;
  white-space: nowrap;
  z-index: 20px;
  /* -moz-border-radius: 5px;
  -moz-box-shadow: 0px 0px 4px #222; */
  background-image: -moz-linear-gradient(top, #eeeeee, #cccccc);
  /* background-image: -moz-linear-gradient(top, #eeeeee, #cccccc); */
}

.sorder_head_label {
  vertical-align: middle !important;
  text-align: center;
  width: 60px;
}

.sorder_head_text {
  text-align: left;
  width: 133px;
}
/*html2svg*/
.html2svg .split {
  zoom: 2;
  float: left;
  margin-left: 15px !important;
  /* font-family: 宋体 !important; */
  /*left:50%;
   transform: translateX(-50%);
    position:relative;  */
}
.html2svg:after {
  content: "";
  display: block;
  clear: both;
  visibility: hidden;
}
</style>
