<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增 </vxe-button>
          <vxe-button status="warning" @click="insertPlusShowEvent" v-if="menuAction.allowAdd">批量增加 </vxe-button>
          <vxe-button status="danger" @click="deletesEvent" v-if="menuAction.allowDelete">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword plremoteaceholder="客户" clearable :remote-method="remoteMethod4">
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemID">
              <template #default="{ data }">
                <el-select v-model="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword plremoteaceholder="面料" clearable :remote-method="remoteMethod2">
                  <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label+'['+item.code+']'" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadItemmodelgroupDetailTable' ref='master_table' height="auto" :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}" :loading="tableLoading">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="groupCode" title="品类编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="groupCodeName" title="品类名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="itemCode" title="面料编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="itemCodeName" title="面料名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="clientCode" title="客户编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="clientCodeName" title="客户名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column title="操作" width="100" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" show-zoom resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="面料" field="itemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="面料" :remote-method="remoteMethod2" clearable>
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label+'['+item.code+']'" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户" field="clientID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}" />
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="insertPlusShow" :title="'批量增加'" width="50%" height="50%" show-zoom resize destroy-on-close>
      <MLItem :add="false" :opt="false" :companyinfoShow="false" ref="mlitem" v-if="insertPlusShow">
        <template v-slot:otherbtn>
          <vxe-button type="submit" status="primary" @click="insertPlusEvent">保存</vxe-button>
        </template>
      </MLItem>
    </vxe-modal>
  </d2-container>
</template>


<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import MLItem from '@/views/app/bad/bad_itemml/master.vue'//E:\workgit\Shop\XiaoMa.Front.End\src\views\app\bad\bad_itemml\master.vue
import { cloneDeep } from 'lodash'
export default {
  name: 'SysGroupDetail',
  mixins: [detailTableMixins],
  components: {
    MLItem,
  },
  data() {
    return {
      searchForm: {
        clientID: null,
        itemID: null,
      },
      insertPlusShow: false,
      formData: {
        itemID: '',
        groupID: this.form.id,
        clientID: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
      },
      api: {
        get: '/mtm/bad_itemmodelgroup/get',
        add: '/mtm/bad_itemmodelgroup/adds',
        edit: '/mtm/bad_itemmodelgroup/updates',
        delete: '/mtm/bad_itemmodelgroup/deletes',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        //ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      //ModelElemListComboStoreByQuery
    }
  },
  async created() {
    await this.getCombStore()
    this.loadData({ groupID: this.form.id }).then(({ data }) => {
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore() {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { itemClassID: 1 }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod2(query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query, itemClassID: 1 }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod4(query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent(row) {
      this.selectRow = cloneDeep(row)
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
        this.showEdit = true
      })
      // this.selectRow = cloneDeep(row)

    },
    insertPlusShowEvent() {
      this.insertPlusShow = true
    },
    insertPlusEvent() {
      const mlitem = this.$refs['mlitem']
      if (!mlitem) {
        return
      }
      var list = mlitem.$refs['master_table'].getCheckboxRecords().map(item => { return { itemID: item.id, groupID: this.form.id, isActive: true, skipCheck: true } })
      this.$api.ActionRequest(this.api.add, list).then(result => {
        this.loadData({ groupID: this.form.id })
        this.insertPlusShow = false
      })
    }
    //remoteMethod(query) {
    //this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
    //this.ModelElemListComboStoreByQuery = result
    //})
  }
}
</script>

<style lang="scss" scoped>
</style>
