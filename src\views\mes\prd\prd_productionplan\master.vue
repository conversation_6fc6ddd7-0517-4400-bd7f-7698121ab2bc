<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="success" icon="fa fa-play-circle" v-if="menuAction.allowEdit" @click="createSchedule()">排产</vxe-button>
          <vxe-button size="mini" status="warning" icon="vxe-icon--refresh" content="更新数据" @click="updateData()">更新数据
            <template #dropdowns>
              <vxe-button type="text" status="primary" content="更新指定订单" @click="showSorderNumber=!showSorderNumber"></vxe-button>
            </template>
          </vxe-button>

          <!-- <vxe-button status="warning" icon="vxe-icon--refresh" v-if="menuAction.allowEdit" @click="updateData()">更新数据</vxe-button> -->
          <vxe-button status="warning" icon="vxe-icon--refresh" v-if="menuAction.allowEdit" @click="updateStatePlus()">批量开始</vxe-button>
          <vxe-button status="success" icon="vxe-icon--refresh" v-if="menuAction.allowEdit" @click="deliveryDateShow=!deliveryDateShow">修改订单信息</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
          <!-- <vxe-button status="warning" content="警告颜色"></vxe-button> -->
          <!-- <vxe-button status="danger" content="危险颜色"></vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="IsPlaning">
              <template #default="{ data }">
                <el-select v-model="data.IsPlaning" filterable placeholder="是否排产" size="mini" clearable>
                  <el-option v-for="item in isPlanings" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sorderType" :item-render="{}"><template #default>
                <el-select v-model="searchForm.sorderType" filterable placeholder="订单类型" size="mini" clearable>
                  <el-option v-for="item in SorderTypeStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="state">
              <template #default="{ data }">
                <el-select v-model="data.state" filterable placeholder="状态" size="mini" clearable>
                  <el-option v-for="item in productionPlanStateComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>

            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PrdProductionplanMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="订单类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderNum" title="订单编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户名称" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="clientName" title="客户名称" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="deliveryDate" title="交货日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100"></vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isUrgent" title="是否加急" :formatter="formatBool" sortable width="100">
        <template v-slot="{ row }">
          <template v-if="row.isUrgent">
            <span style="color:red">是</span>
          </template>
          <template v-else>
            否
          </template>
        </template>
      </vxe-table-column>
      <vxe-table-column title="是否排产" :formatter='formatBool' sortable width="100">
        <template v-slot="{ row }">
          <template v-if="!row.isPlanning">
            <span style="color:red">否</span>
          </template>
          <template v-else>
            是
          </template>
        </template>
      </vxe-table-column>
      <vxe-table-column field="stateText" title="状态" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180" :fixed='tableOptFixed' align="center" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">

          <template v-if="row.isPlanning">
            <template v-if="row.state===0||row.state===2">
              <vxe-button type="text" status="success" icon="fa fa-play-circle" v-if="menuAction.allowEdit" @click="updateState(row,'play')"></vxe-button>
              <vxe-button type="text" status="danger" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
            </template>
            <template v-else-if="row.state===20">
              <!-- <vxe-button type="text" icon="fa fa-stop-circle" status="danger" v-if="menuAction.allowEdit&&row.state!==2" @click="updateState(row,'stop')"></vxe-button> -->
            </template>
            <template v-else>
              <vxe-button type="text" icon="fa fa-stop-circle" status="danger" v-if="menuAction.allowEdit&&row.state!==2" @click="updateState(row,'stop')"></vxe-button>
            </template>
          </template>
          <template v-else>
            <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
            <vxe-button type="text" status="danger" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>

          </template>

          <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="交货日期" field="deliveryDate" span="12">
          <template #default>
            <!-- <el-time-picker v-model="selectRow.deliveryDate" placeholder="选择时间" size="mini"></el-time-picker> -->
            <el-date-picker v-model="selectRow.deliveryDate" type="date" placeholder="选择日期" size="mini">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <vxe-form-item title="是否加急" field="isUrgent" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="showSorderNumber" title="输入MTM中订单号" width="600" resize destroy-on-close>
      <vxe-form :data="SorderNumberForm" :rules="SorderNumberformRules" title-align="right" title-width="100" @submit="sorderNumbersubmitEvent">
        <vxe-form-item title="编码名称" field="sorderNumber" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="注意" span="24">
          <template #default>
            <el-tag type="warning">只能同步MTM中已有的订单，并且状态在【技术审核完成】或【计划下单】才嫩同步</el-tag>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">确定</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="deliveryDateShow" title="批量修改订单信息" width="600" resize destroy-on-close>
      <vxe-form v-if="deliveryDateShow" :data="deliveryDateForm" :rules="deliveryDateformRules" title-align="right" title-width="100" @submit="sorderDeliveryDateSubmitEvent">
        <vxe-form-item title="交货日期" field="deliveryDate" span="24" :item-render="{name: '$input', props: {type: 'datetime',}}"></vxe-form-item>
        <!-- <vxe-form-item title="注意" span="24" :item-render="{}"><template #default>
            <el-tag type="warning">只能同步MTM中已有的订单，并且状态在【技术审核完成】或【计划下单】才嫩同步</el-tag>
          </template>
        </vxe-form-item> -->
        <vxe-form-item title="加急" field="isUrgent" span="12" :item-render="{name: '$switch'}"> </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">确定</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'prd_productionplanMaster',
  mixins: [masterTableMixins],

  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        state: null
      },
      isPlanings: [
        { value: true, label: '已排产' },
        { value: false, label: '未排产' }
      ],
      showSorderNumber: false,
      deliveryDateShow: false,
      deliveryDateForm: {
        deliveryDate: null,
        isUrgent: false
      },
      deliveryDateformRules: {
        // deliveryDate: [{ required: true, message: '请选择交货时间' }]
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      SorderNumberForm: {
        sorderNumber: null
      },
      SorderNumberformRules: {
        sorderNumber: [{ required: true, message: '请输入MTM订单编码' }, { min: 8, max: 20, message: '长度在 8 到 20 个字符' }]
      },
      api: {
        get: '/mes/prd_productionplan/get',
        add: '/mes/prd_productionplan/adds',
        edit: '/mes/prd_productionplan/updates',
        delete: '/mes/prd_productionplan/deletes',
        updateData: '/mes/pRD_ProductionPlan/getSorderFromMTMBySorderID',
        updateState: '/mes/pRD_ProductionPlan/updateState',
        CreatePlanSchedules: '/mes/pRD_ProductPlanSchedule/createPlanSchedules',
        productionPlanStateComboStore: '/mes/combo/productionPlanStateComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        SorderTypeStore: '/mes/combo/SorderTypeStore'

      },
      clientComboStoreByQuery: [],
      SorderTypeStore: [],
      productionPlanStateComboStore: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.productionPlanStateComboStore).then(result => {
        this.productionPlanStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.SorderTypeStore).then(result => {
        this.SorderTypeStore = result
      })
    },
    createSchedule (factoryID) {
      this.tableLoading = true
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length <= 0) {
        this.tableLoading = false
        this.$XModal.message({ message: '请选择要排产的订单', status: 'error' })
        return
      }
      var count = checks.length
      if (count > 10) {
        this.tableLoading = false
        this.$XModal.message({ message: `最多勾选${count}条数据`, status: 'error' })
        return
      }
      var sorderIDs = checks.map(a => { return { sorderID: a.sorderID, sorderRepairID: a.sorderRepairID } })
      this.$XModal.confirm('更新数据会清空已有数据,您确定要更新吗？').then(async type => {
        if (type === 'confirm') {
          await this.$api.ActionRequest(this.api.CreatePlanSchedules, { sorderIDs: sorderIDs, factoryID: factoryID }).then(result => {
            this.loadData()
            this.tableLoading = false
            this.$XModal.message({ message: '排产成功', status: 'success' })
          }).catch(() => {
            this.tableLoading = false
          })
        } else {
          this.tableLoading = false
          this.$XModal.message({ message: '取消操作' })
        }
      })
    },
    async updateState (row, click) {
      switch (click) {
        case 'play':
          row.state = 0
          break
        case 'pause':
          row.state = 1
          break
        case 'stop':
          row.state = 2
          break
        default:
          break
      }
      await this.$api.ActionRequest(this.api.updateState, [row]).then(result => {
        this.loadData()
      })
    },
    async sorderNumbersubmitEvent () {
      if (this.SorderNumberForm.sorderNumber === null) {
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '拉取数据中！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      await this.$api.ActionRequest(this.api.updateData, { sorderNumber: this.SorderNumberForm.sorderNumber }).then(async result => {
        this.showSorderNumber = false
        this.SorderNumberForm.sorderNumber = null
        this.loadData()
        this.$XModal.message({ message: '更新成功', status: 'success' })
        this.tableLoading = false
        loading.close()
      }).catch(() => {
        this.tableLoading = false
        loading.close()
      })
    },
    async sorderDeliveryDateSubmitEvent () {
      this.tableLoading = true
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length <= 0) {
        this.tableLoading = false
        this.$XModal.message({ message: '请选择要更改交期的订单', status: 'error' })
        return
      }
      var rows = cloneDeep(checks)
      rows.forEach(item => {
        if (this.deliveryDateForm.deliveryDate != null) {
          item.deliveryDate = this.deliveryDateForm.deliveryDate
        }
        item.isUrgent = this.deliveryDateForm.isUrgent
      })
      await this.$api.ActionRequest(this.api.edit, rows).then(result => {
        this.tableLoading = false
        this.deliveryDateShow = false
        this.loadData()
      }).catch(() => {
        this.tableLoading = false
        this.deliveryDateShow = false
      })
    },
    async updateStatePlus () {
      this.tableLoading = true
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length <= 0) {
        this.tableLoading = false
        this.$XModal.message({ message: '请勾选订单', status: 'error' })
        return
      }
      if (checks.length > 10) {
        this.tableLoading = false
        this.$XModal.message({ message: '最多勾选10条数据', status: 'error' })
        return
      }
      checks.forEach(item => {
        item.state = 0
      })
      await this.$api.ActionRequest(this.api.updateState, checks).then(result => {
        this.tableLoading = false
        this.loadData()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    async updateData () {
      this.tableLoading = true
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length <= 0) {
        await this.$api.ActionRequest(this.api.updateData, { SorderIDs: [] }).then(async result => {
          this.loadData()
          this.$XModal.message({ message: '更新成功', status: 'success' })
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      } else {
        var sorderIDs = checks.map(a => { return a.sorderID })
        if (sorderIDs.length <= 0) {
          this.$XModal.message({ message: '已排产的订单无法获取信息', status: 'error' })
          this.tableLoading = false
          return
        }
        this.$XModal.confirm('更新数据会清空已有数据,您确定要更新吗？').then(async type => {
          if (type === 'confirm') {
            await this.$api.ActionRequest(this.api.updateData, { sorderIDs }).then(result => {
              this.loadData()
              this.$XModal.message({ message: '更新成功', status: 'success' })
              this.tableLoading = false
            }).catch(() => {
              this.tableLoading = false
            })
          } else {
            this.$XModal.message({ message: '取消操作' })
            this.tableLoading = false
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
