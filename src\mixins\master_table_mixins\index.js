import baseTableMixins from '../base_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  mixins: [baseTableMixins],
  data () {
    return {
      footerCompanyInfo: this.showfooterCompanyInfo,
      isAutoLoding: true

    }
  },
  watch: {

  },

  created () {
    if (this.masterSeach != null) {
      this.searchForm = Object.assign(this.searchForm, this.masterSeach)
    }
    if (this.isAutoLoding) {
      this.loadData()
    }
  },
  methods: {
    // 数据加载
    async loadData (form = null) {
      return new Promise((resolve, reject) => {
        this.tableLoading = true
        if (form != null) {
          this.searchForm = Object.assign(this.searchForm, form)
        }
        this.$api.ActionRequest(this.api.get, this.searchForm).then(result => {
          this.tableData = result.items
          this.searchForm.totalCount = result.totalCount
          this.tableLoading = false
          resolve({ data: result.items })
        }).catch(() => {
          this.tableLoading = false
        })
        // this.tableLoading = false
      })
    }

  }
}
