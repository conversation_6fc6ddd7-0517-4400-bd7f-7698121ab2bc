<template>
  <div id="cards">
    <div class="card-item" v-for="(card, i) in cards" :key="card.title">
      <div class="card-footer">
        <div class="card-footer-item">
          <div class="footer-title">计划完成数</div>
          <div class="footer-detail">
            <dv-digital-flop :config="card.planCount" style="width:70%;height:35px;" />
          </div>
        </div>
        <div class="card-footer-item">
          <div class="footer-title">昨日未完成数</div>
          <div class="footer-detail">
            <dv-digital-flop :config="card.yesterdayUnFinishedCount" style="width:70%;height:35px;" />
          </div>
        </div>
      </div>
      <div class="card-header">
        <div class="card-header-left">{{ card.title }}</div>
        <div class="card-header-right">{{ '0' + (i + 1) }}</div>
      </div>
      <dv-charts class="ring-charts" :option="card.ring" />
      <div class="card-footer">
        <div class="card-footer-item">
          <div class="footer-title">今日预计完成</div>
          <div class="footer-detail">
            <dv-digital-flop :config="card.planFinishCount" style="width:70%;height:35px;" />
          </div>
        </div>
        <div class="card-footer-item">
          <div class="footer-title">今日完成数</div>
          <div class="footer-detail">
            <dv-digital-flop :config="card.finishCount" style="width:70%;height:35px;" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Cards',
  data () {
    return {
      api: {
        get: '/mes/boardChart/ProductionPlanIndicator'
      },
      cards: [],
      data: [],
      colors: ['#4d99fc', '#f46827', '#40faee', '#4d99fc', '#f46827', '#40faee', '#4d99fc', '#f46827', '#40faee']
    }
  },
  methods: {
    async get () {
      await this.$api.ActionRequest(this.api.get).then(result => {
        // this.data = result;
        this.createData(result)
      })
    },
    createData (data) {
      const { randomExtend } = this
      this.cards = data.map((item, i) => ({
        title: item.label,
        planFinishCount: { // 计划完成数
          number: [item.planFinishCount],
          content: '{nt}',
          textAlign: 'right',
          style: {
            fill: '#ea6027',
            fontWeight: 'bold'
          }
        },
        planCount: { // 今日预计完成数
          number: [item.planCount],
          content: '{nt}',
          textAlign: 'right',
          style: {
            fill: '#67C23A',
            fontWeight: 'bold'
          }
        },
        yesterdayUnFinishedCount: { // 昨日未完成
          number: [item.yesterdayUnFinishedCount],
          content: '{nt}',
          textAlign: 'right',
          style: {
            fill: '#F56C6C',
            fontWeight: 'bold'
          }
        },
        finishCount: {
          number: [item.finishCount],
          content: '{nt}',
          textAlign: 'right',
          style: {
            fill: '#26fcd8',
            fontWeight: 'bold'
          }
        },
        ring: {
          series: [
            {
              type: 'gauge',
              startAngle: -Math.PI / 2,
              endAngle: Math.PI * 1.5,
              arcLineWidth: 13,
              radius: '80%',
              data: [
                { name: '完成率', value: item.prcentageComplete }
              ],
              axisLabel: {
                show: false
              },
              axisTick: {
                show: false
              },
              pointer: {
                show: false
              },
              backgroundArc: {
                style: {
                  stroke: '#224590'
                }
              },
              details: {
                show: true,
                formatter: '完成率{value}%',
                style: {
                  fill: this.colors[randomExtend(0, 8)],
                  fontSize: 20
                }
              }
            }
          ],
          color: ['#03d3ec']
        }
      }))
    },
    randomExtend (minNum, maxNum) {
      if (arguments.length === 1) {
        return parseInt(Math.random() * minNum + 1, 10)
      } else {
        return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10)
      }
    }
  },
  mounted () {
    const { get } = this

    get()

    setInterval(this.get, 1000 * 60)
  }
}
</script>

<style lang="scss">
#cards {
  display: flex;
  justify-content: space-between;
  height: 30%;

  .card-item {
    background-color: rgba(6, 30, 93, 0.5);
    border-top: 2px solid rgba(1, 153, 209, 0.5);
    width: 12%;
    display: flex;
    flex-direction: column;
  }

  .card-header {
    display: flex;
    height: 10%;
    align-items: center;
    justify-content: space-between;

    .card-header-left {
      font-size: 18px;
      font-weight: bold;
      padding-left: 20px;
    }

    .card-header-right {
      padding-right: 20px;
      font-size: 40px;
      color: #03d3ec;
    }
  }

  .ring-charts {
    height: 55%;
  }

  .card-footer {
    height: 25%;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  .card-footer-item {
    padding: 5px 10px 0px 10px;
    box-sizing: border-box;
    width: 40%;
    background-color: rgba(6, 30, 93, 0.7);
    border-radius: 3px;

    .footer-title {
      font-size: 15px;
      margin-bottom: 5px;
    }

    .footer-detail {
      font-size: 20px;
      color: #1294fb;
      display: flex;
      font-size: 18px;
      align-items: center;

      .dv-digital-flop {
        margin-right: 5px;
      }
    }
  }
}
</style>
