<template>
  <d2-container class='stockinvoicesdetail' :loading="updateloading">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd" :disabled="form.itemStockInvoicesState===1">新增</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent" :disabled="form.itemStockInvoicesState===1">批量删除</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="addplusEvent" v-if="menuAction.allowAdd">批量添加</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:buttons>
          <vxe-button status="success" @click="updateFormEvent" v-if="menuAction.allowAdd" :disabled="form.itemStockInvoicesState===1">确认</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItemstickinvoicesdetailDetailTable' keep-source ref='master_table' height="auto" :row-class-name="rowClassName" :data="tableData" :loading="tableLoading" :custom-config="{storage: true}" :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, icon: 'fa fa-pencil'}" @edit-closed="editClosedEvent" :edit-rules="formRules">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="invoicesNumber" title="单据号" width="100"> </vxe-table-column>

      <vxe-table-column field="clientName" title="客户" width="100"> </vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" width="100"> </vxe-table-column>
      <vxe-table-column field="itemName" title="物料名称" width="100"> </vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" width="100"> </vxe-table-column>
      <vxe-table-column v-if="form.itemStockInvoicesType==2" field="businessGroupText" title="业务属性" width="100"> </vxe-table-column>
      <template v-if="form.itemStockInvoicesState===1">
        <vxe-table-column field="batchNo" title="合同号" width="100"> </vxe-table-column>
        <vxe-table-column field="eoriNo" title="手册号" width="100"> </vxe-table-column>
        <vxe-table-column field="qty" title="数量" width="100"> </vxe-table-column>
        <vxe-table-column field="price" title="单价" width="100"> </vxe-table-column>
        <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      </template>
      <template v-else>
        <vxe-table-column field="batchNo" title="合同号" width="100" :edit-render="{name: 'input', attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="eoriNo" title="手册号" width="100" :edit-render="{name: 'input', attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="qty" title="数量" width="100" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}"> </vxe-table-column>
        <vxe-table-column field="price" title="单价" width="100" :edit-render="{name: '$input', props: {type: 'float', digits: 3}}"> </vxe-table-column>
        <vxe-table-column field="remark" title="备注" width="100" :edit-render="{name: 'input', attrs: {type: 'text'}}"> </vxe-table-column>
      </template>

      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column title="操作" width="100" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit" :disabled="form.itemStockInvoicesState===1"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete" :disabled="form.itemStockInvoicesState===1"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd" :disabled="form.itemStockInvoicesState===1"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading" show-zoom>
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="物料名称" field="itemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemID" filterable placeholder="物料名称" size="mini" remote reserve-keyword :remote-method="remoteMethod" class="itemselect">
              <el-option v-for="item in ItemComboStoreByQuery" :key="item.value" :label="'【'+item.label+'/'+item.name+'】'+item.code+'规格:'+item.itemSize" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="跳转添加面/辅料" span="12" :item-render="{}"> <template #default>
            &nbsp;
            <vxe-button type="text" status="success" content="面料" @click="goto('bad_itemml')"></vxe-button>
            <vxe-button type="text" status="warning" content="辅料" @click="goto('bad_itemfl')"></vxe-button>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户" field="clientID" span="12" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword clearable :remote-method="remoteMethod4">
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="业务属性" field="businessGroup" span="12" v-if="form.itemStockInvoicesType==2" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.businessGroup" filterable placeholder="业务属性" size="mini" reserve-keyword clearable>
              <el-option v-for="item in BusinessGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="价格" field="price" span="12" :item-render="{name: '$input', attrs: {type: 'float',}}"></vxe-form-item>
        <vxe-form-item title="数量" field="qty" span="24" :item-render="{name: '$input', attrs: {type: 'float',}}">
        </vxe-form-item>
        <vxe-form-item title="合同号" field="batchNo" span="12" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.batchNo" filterable placeholder="请选择" size="mini" remote reserve-keyword default-first-option allow-create plremoteaceholder="合同号" :remote-method="remoteMethod2" style="width:100%" @change="batchNoChange" clearable>
              <el-option v-for="item in ItemBatchComboStoreByQuery" :key="item.value" :label="item.label" :value="item.label">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.text }}</span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="手册号" field="eoriNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="showPlus" :title="'批量添加'" width="90%" height="60%" resize destroy-on-close>
      <item-stock-detail :form="form" v-if="showPlus" @addPlusThen='addPlusThen' />
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import ItemStockDetail from './compontents/index.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'WarItemstockinvoicesDetail',
  mixins: [detailTableMixins],
  components: {
    ItemStockDetail
  },
  data () {
    return {
      updateloading: false,
      formData: {
        itemStockInvoicesID: this.form.id,
        itemID: null,
        itemStockID: null,
        batchNo: null,
        price: null,
        qty: null,
        remark: '',
        isActive: true,
        businessGroup: null
      },
      showPlus: false,
      formRules: {
        itemID: [{ required: true, message: '请选择物料信息' }],
        price: [{ required: true, message: '请输入价格' }],
        businessGroup: [{ required: true, message: '请选择业务属性' }],
        qty: [{ required: true, message: '请输入数量' }]
      },
      api: {
        get: '/mtm/war_itemstockinvoicesdetail/get',
        add: '/mtm/war_itemstockinvoicesdetail/adds',
        updateForm: '/mtm/war_itemstockinvoices/updates',
        edit: '/mtm/war_itemstockinvoicesdetail/updates',
        delete: '/mtm/war_itemstockinvoicesdetail/deletes',
        ItemComboStoreByQuery: '/mtm/comboQuery/ItemComboStoreByQuery',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ItemBatchComboStoreByQuery: '/mtm/comboQuery/ItemBatchComboStoreByQuery',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore'
      },
      ItemBatchComboStoreByQuery: [],
      ItemComboStoreByQuery: [],
      clientComboStoreByQuery: [],
      BusinessGroupComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemComboStoreByQuery).then(result => {
        this.ItemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
    },
    goto (name) {
      this.$router.push({ name: name })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      if (this.selectRow.itemID === null) {
        this.$message({ message: '请先选择物料', type: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.ItemBatchComboStoreByQuery, { text: query, xid: this.selectRow.itemID }).then(result => {
        this.ItemBatchComboStoreByQuery = result
      })
    },
    batchNoChange (data) {
      var dto = this.ItemBatchComboStoreByQuery.GetFirstElement('label', data)
      if (dto) {
        this.selectRow.eoriNo = dto.text
      } else {
        this.selectRow.eoriNo = null
      }
    },
    async updateFormEvent () {
      if (this.tableData.length <= 0) {
        this.$message({ message: '明细数据不能为空', type: 'error' })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '操作中....',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      var data = cloneDeep(this.form)
      data.itemStockInvoicesState = 1
      await this.$api.ActionRequest(this.api.updateForm, [data]).then(result => {
        this.$message({ message: '操作成功', type: 'success' })
        loading.close()
        this.$emit('closeDetail')
      }).catch(errors => {
        loading.close()
        this.form.itemStockInvoicesState = 0
      })
    },
    // 编辑
    async editEvent (row) {
      this.selectRow = cloneDeep(row)
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemBatchComboStoreByQuery, { gid: row.itemBatchID }).then(result => {
        this.ItemBatchComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { gid: this.selectRow.itemID }).then(result => {
        this.ItemComboStoreByQuery = result
        this.showEdit = true
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.ItemComboStoreByQuery = result
      })
    },
    addplusEvent () {
      this.showPlus = true
    },
    addPlusThen () {
      this.loadData({ id: this.form.id }).then(({ data }) => {
      })
      this.showPlus = false
    },
    editClosedEvent ({ row, column }) {
      const $table = this.$refs.master_table
      const field = column.property
      // const cellValue = row[field]
      // 判断单元格值是否被修改
      var b = $table.isUpdateByRow(row, field)
      console.log(b)
      if (b) {
        this.$api.ActionRequest(this.api.edit, [row]).then(result => {
          $table.reloadRow(row, null, field)
          this.$XModal.message({ message: '保存成功', status: 'success' })
          this.loadData()
        })
      }
    }
  }
}
</script>

<style lang="scss" >
.stockinvoicesdetail {
  .itemselect {
    width: 100%;
  }
}
</style>
