<template>
  <div id="scroll-board">
    <dv-scroll-board :config="config" ref="scrollBoard" />
  </div>
</template>

<script>
export default {
  name: 'ScrollBoard',
  data () {
    return {
      api: {
        get: '/mes/boardChart/ProductionPlanSchedule'
      },
      Data: [],
      config: {
        header: ['订单号', '流水号', '类别', '工段', '工段截至时间', '状态', '加急'],
        data: [

        ],
        index: true,
        columnWidth: [100, 100, 150, 100, 120, 150, 100],
        align: ['center'],
        rowNum: 7,
        headerBGC: '#1981f6',
        headerHeight: 45,
        oddRowBGC: 'rgba(0, 44, 81, 0.8)',
        evenRowBGC: 'rgba(10, 29, 50, 0.8)'
      }
    }
  },
  mounted () {
    this.timer = setInterval(this.get, 1000 * 60)//
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  created () {
    this.get()
  },
  methods: {
    formatBool (cellValue) {
      var b = (cellValue === true ? '是' : '否')
      return b
    },
    formatDate (cellValue, format) {
      if (cellValue === null) {
        return null
      }
      var a = this.$utils.toDateString(this.formatLongDate(cellValue), format || 'yyyy-MM-dd HH:mm:ss')
      return a
    },
    formatLongDate (date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    doUpdate (rows) {
      this.$refs.scrollBoard.updateRows(rows)
    },
    async get () {
      await this.$api.ActionRequest(this.api.get).then(async result => {
        var arr = []
        await result.forEach(async element => {
          arr.push([
            element.sorderNum,
            element.serialNumber,
            element.groupName,
            element.workSecationName,
            this.formatDate(element.workSecationEndTime),
            element.stateText,
            this.formatBool(element.isUrgent)
          ])
        })
        this.doUpdate(arr)
      })
    }
  }
}
</script>

<style lang="scss">
#scroll-board {
  width: 50%;
  box-sizing: border-box;
  margin-left: 20px;
  height: 100%;
  overflow: hidden;
}
</style>
