<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="sizeColumnID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.sizeColumnID" filterable remote reserve-keyword placeholder="规格字段" :remote-method="remoteMethod1" size="mini" clearable>
                  <el-option v-for="item in SizeColumnComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="bodyID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.bodyID" filterable remote reserve-keyword placeholder="特体" :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in BodyComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MommodelsizecolumnbodyMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="sizeColumnCode" title="规格字段编码" sortable width="130"> </vxe-table-column>
      <vxe-table-column field="sizeColumnName" title="规格字段名称" sortable width="130"></vxe-table-column>
      <vxe-table-column field="bodyCode" title="特体编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="bodyName" title="特体名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="bodyValue" title="特体值" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value" title="调整系数" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="规格字段" field="sizeColumnID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.sizeColumnID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod1" clearable size="mini">
              <el-option v-for="item in SizeColumnComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="特体" field="bodyID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.bodyID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod2" clearable size="mini">
              <el-option v-for="item in BodyComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="调整系数" field="value" span="12" :item-render="{}"> <template #default>
            <el-input-number v-model="selectRow.value" :precision="2" :step="0.05" :max="2" :min="-1" size="mini"></el-input-number>
          </template>
        </vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modelsizecolumnbody',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        sizeColumnID: null,
        bodyID: null,
        sort: 999,
        remark: '',
        isActive: true,
        value: 1
      },
      formRules: {
        sizeColumnID: [{ required: true, message: '请选择规格字段' }],
        bodyID: [{ required: true, message: '请选择特体' }],
        value: [{ required: true, message: '请输入调整系数', type: 'number' }, { message: '长度在 0.01 到 1 之间' }],
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/mom_modelsizecolumnbody/get',
        add: '/mtm/mom_modelsizecolumnbody/adds',
        edit: '/mtm/mom_modelsizecolumnbody/updates',
        delete: '/mtm/mom_modelsizecolumnbody/deletes',
        BodyComboStoreByQuery: '/mtm/comboQuery/BodyComboStoreByQuery',
        SizeColumnComboStoreByQuery: '/mtm/comboQuery/SizeColumnComboStoreByQuery'
      },
      BodyComboStoreByQuery: [],
      SizeColumnComboStoreByQuery: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.BodyComboStoreByQuery).then(result => {
        this.BodyComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { text: query }).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { text: query }).then(result => {
        this.BodyComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { gid: row.bodyID }).then(result => {
        this.BodyComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row) {
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { gid: row.bodyID }).then(result => {
        this.BodyComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.selectRow.id = null
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
