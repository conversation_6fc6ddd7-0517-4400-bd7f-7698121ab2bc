# alpine镜像用的是Alpine Linux内核，比ubuntu内核要小很多。
FROM harbor.xmmtm.com/library/nginx:1.19-alpine
#FROM nginx:1.19.2-alpine

#设置工作目录 
WORKDIR /usr/share/nginx/html/

#复制编译好的项目到工作目录
COPY ./dist /usr/share/nginx/html
#复制启动脚本
COPY startup.sh /usr/share/nginx/html/startup.sh
#删除目录下的default.conf文件
# RUN rm /etc/nginx/conf.d/default.conf
#将default.conf复制到/etc/nginx/conf.d/下，用本地的default.conf配置来替换nginx镜像里的默认配置
# ADD default.conf /etc/nginx/conf.d/
RUN rm /etc/nginx/nginx.conf
COPY nginx.conf /etc/nginx/nginx.conf
#设置镜像端口为80
EXPOSE 80
EXPOSE 443
# 设置启动脚本权限
RUN chmod +x /usr/share/nginx/html/startup.sh
# 使用启动脚本启动
CMD ["/usr/share/nginx/html/startup.sh"]