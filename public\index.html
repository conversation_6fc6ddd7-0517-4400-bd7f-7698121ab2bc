<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>icon.ico">
    <!-- 使用 CDN 加速的 CSS 文件，配置在 vue.config.js 下 -->
    <% for (var i in htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.css) { %>
    <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="preload" as="style">
    <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="stylesheet">
    <% } %>
    <!-- 使用 CDN 加速的 JS 文件，配置在 vue.config.js 下 -->
    <% for (var i in htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.js) { %>
    <link href="<%= htmlWebpackPlugin.options.cdn.js[i] %>" rel="preload" as="script">
    <% } %>
    <title>{{VUE_APP_TITLE}}</title>
    <!-- 运行时配置 - 优先加载外部配置文件 -->
    <script src="./config.js"></script>
    <script>
      // 方案1: 内联配置（占位符会被startup.sh替换，作为备用）
      if (!window.CONFIG_LOADED) {
        window.VUE_APP_USER_API = '{{VUE_APP_USER_API}}';
        window.VUE_APP_API = '{{VUE_APP_API}}';
        window.VUE_APP_MESAPI = '{{VUE_APP_MESAPI}}';
        window.VUE_APP_TITLE = '{{VUE_APP_TITLE}}';
        console.log('🔧 使用内联配置作为备用');
      }

      // 调试信息
      console.log('🔍 配置检查:', {
        CONFIG_LOADED: window.CONFIG_LOADED,
        VUE_APP_API: window.VUE_APP_API,
        VUE_APP_USER_API: window.VUE_APP_USER_API,
        RUNTIME_CONFIG: window.RUNTIME_CONFIG
      });
    </script>
    <style>
      html, body, #app { height: 100%; margin: 0px; padding: 0px; width: 100%; }
      .d2-home { background-color: #303133; height: 100%; display: flex; flex-direction: column; }
      .d2-home__main { user-select: none; width: 100%; flex-grow: 1; display: flex; justify-content: center; align-items: center; flex-direction: column; }
      .d2-home__footer { width: 100%; flex-grow: 0; text-align: center; padding: 1em 0; }
      .d2-home__footer > a { font-size: 12px; color: #ABABAB; text-decoration: none; }
      .d2-home__loading { height: 32px; width: 32px; margin-bottom: 20px; }
    </style>
    <!-- <script>
      var _hmt = _hmt || [];
      var hmid = "bc38887aa5588add05a38704342ad7e8";
      (function() { var hm = document.createElement("script"); hm.src = "https://hm.baidu.com/hm.js?" + hmid; var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(hm, s);})();
    </script> -->
  </head>
  <body>
    <noscript>
      <strong>
        <!-- Sorry, D2Admin will not work properly without JavaScript support. Enable JavaScript for browsers and continue. -->
      </strong>
    </noscript>
    <div id="app">
      <div class="d2-home">
        <!-- <div class="d2-home__main">
          <img
            class="d2-home__loading"
            src="./image/loading/loading-spin.svg"
            alt="loading">
        </div> -->
        <div class="d2-home__footer">
          <!-- <a
            href="https://github.com/d2-projects/d2-admin"
            target="_blank">
            https://github.com/d2-projects/d2-admin
          </a> -->
        </div>
      </div>
    </div>
    <!-- 使用 CDN 加速的 JS 文件，配置在 vue.config.js 下 -->
    <% for (var i in htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.js) { %>
    <script src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
  </body>
</html>
