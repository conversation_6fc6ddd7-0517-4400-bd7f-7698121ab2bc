<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="danger" v-if="menuAction.allowDelete" @click="clearEvent" content="清除所有数据">
            <template #dropdowns>
              <vxe-button status="danger" v-if="menuAction.allowDelete" @click="deletesEvent" content="选择删除"></vxe-button>
            </template>
          </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" size="mini" clearable>
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PrdWashinglabelhistoryMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="receiptNumber" title="receiptNumber" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupName" title="品类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="position1" title="位置1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="position2A" title="位置2A" sortable width="100"></vxe-table-column>
      <vxe-table-column field="position2B" title="位置2B" sortable width="100"></vxe-table-column>
      <vxe-table-column field="model3" title="Model3" sortable width="100"></vxe-table-column>
      <vxe-table-column field="patternCode" title="PatternCode" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shopCode4" title="ShopCode4" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sorderDetailSize" title="订单规格" sortable width="100"></vxe-table-column>
      <vxe-table-column field="size5" title="Size5" sortable width="100"></vxe-table-column>
      <vxe-table-column field="ingredient6" title="面料成分6" sortable width="100"></vxe-table-column>
      <vxe-table-column field="ingredient7" title="身里成分7" sortable width="100"></vxe-table-column>
      <vxe-table-column field="ingredient8" title="袖里成分8" sortable width="100"></vxe-table-column>
      <vxe-table-column field="ingredient23" title="锻料成分23" sortable width="100"></vxe-table-column>
      <vxe-table-column field="talla9A" title="Talla9A" sortable width="100"></vxe-table-column>
      <vxe-table-column field="size9B" title="Size9B" sortable width="100"></vxe-table-column>
      <vxe-table-column field="position9C" title="位置9C" sortable width="100"></vxe-table-column>
      <vxe-table-column field="position9D" title="位置9D" sortable width="100"></vxe-table-column>
      <vxe-table-column field="spec10" title="Spec10" sortable width="100"></vxe-table-column>
      <vxe-table-column field="fabric11" title="Fabric11" sortable width="100"></vxe-table-column>
      <vxe-table-column field="stat12A" title="Stat12A" sortable width="100"></vxe-table-column>
      <vxe-table-column field="dror12B" title="Dror12B" sortable width="100"></vxe-table-column>
      <vxe-table-column field="position13" title="品类13" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isSuit" title="套装?" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="button14" title="Button14" sortable width="100"></vxe-table-column>
      <vxe-table-column field="thread15" title="Thread15" sortable width="100"></vxe-table-column>
      <vxe-table-column field="felt16" title="Felt16" sortable width="100"></vxe-table-column>
      <vxe-table-column field="lining17" title="Lining17" sortable width="100"></vxe-table-column>
      <vxe-table-column field="order18" title="Order18" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelCode19" title="ModelCode19" sortable width="100"></vxe-table-column>
      <vxe-table-column field="tarckingNo20" title="TarckingNo20" sortable width="100"></vxe-table-column>
      <vxe-table-column field="fc21" title="Fc21" sortable width="100"></vxe-table-column>
      <vxe-table-column field="date22" title="日期22" sortable width="100"></vxe-table-column>

      <vxe-table-column field="zipper24" title="Zipper24" sortable width="100"></vxe-table-column>
      <vxe-table-column field="position25" title="位置25" sortable width="100"></vxe-table-column>
      <vxe-table-column field="position26" title="位置26" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="message" title="消息" sortable width="100"></vxe-table-column>
      <vxe-table-column field="state" title="状态" :formatter='formatBool' sortable width="100"></vxe-table-column>

      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="80%" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="位置1" field="position1" span="8">
          <template #default>
            <vxe-textarea v-model="selectRow.position1" placeholder="请输入地址" :autosize="{minRows: 6}" clearable showWordCount></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="位置2A" field="position2A" span="8">
          <template #default>
            <vxe-textarea v-model="selectRow.position2A" placeholder="请输入地址" :autosize="{minRows: 6}" clearable showWordCount></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="位置2B" field="position2B" span="8">
          <template #default>
            <vxe-textarea v-model="selectRow.position2B" placeholder="请输入地址" :autosize="{minRows: 6}" clearable showWordCount></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="receiptNumber" field="receiptNumber" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Model3" field="model3" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="ShopCode4" field="shopCode4" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Size5" field="size5" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Talla9A" field="talla9A" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Size9B" field="size9B" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Position9C" field="position9C" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Position9D" field="position9D" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="面料成分6" field="ingredient6" span="6">
          <template #default>
            <vxe-textarea v-model="selectRow.ingredient6" placeholder="请输入地址" :autosize="{minRows: 6}" clearable showWordCount></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="身里成分7" field="ingredient7" span="6">
          <template #default>
            <vxe-textarea v-model="selectRow.ingredient7" placeholder="请输入地址" :autosize="{minRows: 6}" clearable showWordCount></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="袖里成分8" field="ingredient8" span="6">
          <template #default>
            <vxe-textarea v-model="selectRow.ingredient8" placeholder="请输入地址" :autosize="{minRows: 6}" clearable showWordCount></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="锻料成分23" field="ingredient23" span="6">
          <template #default>
            <vxe-textarea v-model="selectRow.ingredient23" placeholder="请输入地址" :autosize="{minRows: 6}" clearable showWordCount></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="Spec10" field="spec10" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Fabric11" field="fabric11" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Stat12A" field="stat12A" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Dror12B" field="dror12B" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Position13" field="position13" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Button14" field="button14" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Thread15" field="thread15" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Felt16" field="felt16" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Lining17" field="lining17" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Order18" field="order18" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="ModelCode19" field="modelCode19" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="TarckingNo20" field="tarckingNo20" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="FC21" field="fc21" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Date22" field="date22" span="6" :item-render="{name: '$input', props: { type: 'date',clearable:true}}"></vxe-form-item>

        <vxe-form-item title="Zipper24" field="zipper24" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="位置25" field="position25" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="位置26" field="position26" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="打印数量" field="qty" span="6" :item-render="{name: 'input', props: { type: 'number',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'prd_washinglabelhistory',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        clientID: null,
        groupID: null
      },
      formData: {
        id: null,
        position1: 'MADE IN CHINA\r\n' +
          'HECHO EN CHINA CON INSUMOS IMPORTADOS\r\n' +
          'FABRIQUÉ EN CHINE\r\n' +
          'FABRICADO EN CHINA\r\n' +
          'FATTO IN CINA\r\n' +
          'HERGESTELLT IN CHINA\r\n' +
          'LAVET I KINA\r\n' +
          'СДЕЛАНО В КИТАЕ\r\n' +
          '中国制造\r\n' +
          '中国製\r\n' +
          '제조국명   중국\r\n' +
          'صُنع في الصين\r\n',
        position2A: 'TRADEMARK\r\n' +
          'MARCA COMERCIAL\r\n' +
          'MARQUE DÉPOSÉE\r\n' +
          'MARCA REGISTRADA\r\n' +
          'MARCHIO DI FABRICA\r\n' +
          'HANDELSMARKE\r\n' +
          'VAREMÆRKE\r\n' +
          'ТОВАРНЫЙ ЗНАК\r\n',
        position2B: '商标\r\n' +
          '상표' +
          'トレードマーク\r\n' +
          'عَلامَة تِجَارِيَّة \r\n',
        model3: '',
        shopCode4: '',
        size5: '',
        ingredient6: '',
        ingredient7: '',
        ingredient8: '',
        talla9A: '',
        size9B: '',
        position9C: '',
        position9D: '',
        spec10: 'CM',
        fabric11: '',
        stat12A: '',
        dror12B: '',
        position13: '',
        button14: '',
        thread15: '',
        felt16: '',
        lining17: '',
        order18: '',
        modelCode19: '',
        tarckingNo20: '',
        fc21: 'CNJYY',
        date22: '',
        ingredient23: '',
        zipper24: '',
        remark: '',
        isActive: true,
        qty: '1'
      },
      formRules: {
        // code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
      },
      api: {
        get: '/mes/prd_washinglabelhistory/get',
        add: '/mes/prd_washinglabelhistory/adds',
        edit: '/mes/prd_washinglabelhistory/updates',
        delete: '/mes/prd_washinglabelhistory/deletes',
        GroupComboStore: '/mtm/combo/groupComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      GroupComboStore: [],
      clientComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (to.params.refresh) {
        vm.loadData().then(res => {
        })
      }
    })
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    clearEvent () {
      this.tableLoading = true
      this.$api.ActionRequest(this.api.delete, this.tableData).then(result => {
        this.$XModal.message({ message: '批量删除成功', status: 'success' })
        this.loadData()
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
