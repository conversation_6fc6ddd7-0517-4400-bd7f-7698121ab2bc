<template>
  <d2-container class="sorderlist">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <el-button type="danger" size="mini" v-if="menuAction.allowEdit" @click="editEvent">修改状态</el-button>
          <el-button type="danger" size="mini" v-if="menuAction.allowEdit" @click="editFinishEvent">修改完成</el-button>
          <el-button type="warning" size="mini" v-if="menuAction.allowEdit" @click="createNewNumberEvent">生成新的订单号</el-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom class="mtmtoolbar">
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="dates">
              <template #default>
                <el-date-picker size="mini" v-model="searchForm.dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="stateIDs">
              <template #default="{ data }">
                <el-select v-model.trim="data.stateIDs" placeholder="节点" multiple collapse-tags clearable size="mini">
                  <el-option v-for="item in SorderStatusComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <template v-if="!(info.userType==2)">
              <vxe-form-item field="clientID">
                <template #default="{ data }">
                  <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                    <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>
            </template>
            <vxe-form-item field="personID">
              <template #default="{ data }">
                <el-select v-model.trim="data.personID" filterable placeholder="顾客" size="mini" remote reserve-keyword :remote-method="remoteMethod5" clearable>
                  <el-option v-for="item in ClientPersonComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemID">
              <template #default="{ data }">
                <el-select v-model.trim="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
                  <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomSewbase_master_table' ref='master_table' @cell-click='tableCellClick' :checkbox-config="{highlight:false}" :stripe="false" :highlight-hover-row="false" :highlight-current-row="false" :highlight-current-column="false" :row-class-name="rowClassName" :loading="tableLoading" :height="TableHeight" :data="tableData" :expand-config="{accordion: true,iconOpen: 'fa fa-minus-square', iconClose: 'fa fa-plus-square'}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="statusText" title="订单状态" width="100"></vxe-table-column>
      <vxe-table-column field="code" title="订单号" width="100"></vxe-table-column>
      <vxe-table-column field="clientText" title="客户" width="100"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" width="100"> </vxe-table-column>
      <vxe-table-column field="tel" title="联系电话" width="100"></vxe-table-column>
      <vxe-table-column field="address" title="地址" width="100"></vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="业务类型" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="clientPerson" title="顾客" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="itemText" title="面料" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="finalTextureText" title="纹理" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="halfFitting" title="半成品试衣" :formatter="formatBool" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创单人" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="isUrgent" title="是否加急" width="100" sortable>
        <template v-slot="{ row }">
          <span v-if="row.isUrgent" style="color:red">加急</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="deliveryDate" title="期望交期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"></vxe-table-column>
      <vxe-table-column field="checkOn" title="客服审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="technicianCheckDate" title="技术审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="checkBy" title="审核人" width="100"></vxe-table-column>
      <vxe-table-column field="sorderFrom" title="订单来源" width="100"></vxe-table-column>
      <vxe-table-column field="mesProductionPlanStateText" title="MES状态" width="100"></vxe-table-column>
      <vxe-table-column field="mEsLastFinishedProductionSchedule" title="MES最新进度" width="100">
        <template v-slot="{ row }">
          <span v-if="row.mesSchedulesDto===null||row.mesSchedulesDto.length<=0">{{row.mEsLastFinishedProductionSchedule}}</span>
          <el-popover v-else placement="top-start" title="生产进度" width="700" trigger="hover">
            <vxe-table align="center" :data="row.mesSchedulesDto">
              <vxe-table-column field="groupName" title="类别" width="80px"></vxe-table-column>
              <vxe-table-column field="serialNumber" title="流水号" width="120px"></vxe-table-column>
              <vxe-table-column field="workSecationCodeName" title="工段" width="120px"></vxe-table-column>
              <vxe-table-column field="productionStationName" title="工位" width="120px"></vxe-table-column>
              <vxe-table-column field="stateText" title="状态" width="80px"></vxe-table-column>
              <vxe-table-column field="modifyOn" title="时间" :formatter="formatDate" width="135px"></vxe-table-column>
            </vxe-table>
            <span slot="reference">{{row.mEsLastFinishedProductionSchedule}}</span>
          </el-popover>

        </template>
      </vxe-table-column>

    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" title="订单流转" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="注意" span="24">
          <template #default>
            <el-alert title="请谨慎操作,可能会造成未知风险,操作不可逆。" type="error" effect="dark" :closable="false">
            </el-alert>
          </template>
        </vxe-form-item>
        <vxe-form-item title="订单号" field="code" span="12" :item-render="{name: 'input', attrs:{disabled:true}}"></vxe-form-item>
        <vxe-form-item title="目标状态" field="action" span="12">
          <template #default="{ data }">
            <el-select v-model="data.statusID" filterable placeholder="目标状态" size="mini">
              <el-option v-for="item in SorderStatusComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="修改原因" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
// import Vue from 'vue'
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import pluginExport from '@d2-projects/vue-table-export'
// import detailList from './listdetail'
import { mapState } from 'vuex'
import { cloneDeep } from 'lodash'
// Vue.use(pluginExport)
// import {groupBy } from "utils"
export default {
  name: 'odm_sorderlist',
  mixins: [masterTableMixins],
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        sequence: 9999
      },
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/odm_sorder/list',
        editState: '/mtm/odm_sorder/ChangeSorderState',
        updateSorderStateByFinished: '/mtm/odm_sorder/UpdateSorderStateByFinished',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ClientPersonComboStoreByQuery: '/mtm/comboQuery/ClientPersonComboStoreByQuery',
        SorderStatusComboStore: '/mtm/combo/sorderStatusComboStore',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        productionPlanState: '/mes/PRD_ProductionPlan/GetProductionPlanStateToMTM',
        UpdateSorderNumber: '/mtm/odm_sorder/UpdateSorderNumber'
      },
      detailForm: {
        sorderId: null

      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        remark: [{ required: true, message: '请输入操作原因' }, { min: 2, max: 200, message: '长度在 2 到 200 个字符' }]
      },
      SorderStatusComboStore: [],
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      ClientPersonComboStoreByQuery: [],
      searchForm: {
        stateIDs: [],
        sorderTypes: [0, 1, 2, 10, 30],
        totalCount: 0
      },
      isAutoLoding: false

    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
    //   ...mapState('d2admin/page', [
    //     'state'
    //   ])
  },
  async created () {
    await this.getCombStore()
    this.setSorderState()
    this.loadData()
  },
  methods: {
    setSorderState () {
      if (this.info.userType === 2) {

      }
      // var kefu = [0, 1, 20, 21, 22, 32]
      // var jishu = [21, 30, 31, 32]
      // if (this.info.userRoles.ElementExist('code', 'Customer')) {
      //   this.searchForm.stateIDs = this.$utils.union(this.searchForm.stateIDs, kefu)
      // }
      // if (this.info.userRoles.ElementExist('code', 'PatternMaker')) {
      //   this.searchForm.stateIDs = this.$utils.union(this.searchForm.stateIDs, jishu)
      // }
    },
    createNewNumberEvent () {
      this.$confirm('此操作会生成一个全新并且唯一的订单号, 是否继续?', '注意', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var checks = this.$refs.master_table.getCheckboxRecords()
        if (checks.length > 1) {
          this.$XModal.message({ message: '一次只能编辑一个订单', status: 'error' })
          return
        }
        if (checks.length < 1) {
          this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
          return
        }
        this.tableLoading = true
        this.$api.ActionRequest(this.api.UpdateSorderNumber, this.selectRow).then(result => {
          this.loadData()
          this.tableLoading = false
          this.$message({
            type: 'success',
            message: '生成成功!'
          })
        })
      }).catch(() => {
        this.tableLoading = false
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 数据加载
    async loadData () {
      this.tableLoading = true
      this.$api.ActionRequest(this.api.get, this.searchForm).then(async result => {
        this.tableData = result.items
        this.searchForm.totalCount = result.totalCount
        this.tableData = await this.getProductionPlanState(result.items)
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    async getProductionPlanState (data) {
      var sorderIDs = data.map((item) => {
        return item.id
      })
      if (sorderIDs.length <= 0) {
        return data
      }
      var list = await this.$api.ActionRequest(this.api.productionPlanState, { sorderId: sorderIDs }).then(async ({ totalCount, items }) => {
        return items
      })
      list.forEach(item => {
        var dto = this.$utils.find(data, it => it.id === item.sorderId)
        if (dto !== null) {
          dto.mEsLastFinishedProductionSchedule = item.lastFinishedProductionSchedule
          dto.mesProductionPlanStateText = item.productionPlanStateText
          dto.mesSchedulesDto = item.schedulesDto
        }
      })
      return data
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { isNotG: true }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SorderStatusComboStore).then(result => {
        this.SorderStatusComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
    },

    tableCellClick ({ column, row }) {
      // this.$refs.master_table.clearCheckboxRow()
      this.$refs.master_table.toggleCheckboxRow(row)
      this.selectRow = cloneDeep(row)
    },

    editEvent (name) {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能编辑一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.showEdit = true
    },
    editFinishEvent () {
      this.$confirm('此操作会把订单状态修改成已完成状态, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var checks = this.$refs.master_table.getCheckboxRecords()
        var sorderIDs = checks.map(item => { return item.id })
        this.$api.ActionRequest(this.api.updateSorderStateByFinished, sorderIDs).then(result => {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    submitEvent () {
      this.$api.ActionRequest(this.api.editState, this.selectRow).then(result => {
        this.$XModal.message({ message: '修改成功', status: 'success' })
        this.loadData()
        this.showEdit = false
      })
    },
    rowClassName ({ row, rowIndex }) {
      var stateClass = ''
      switch (row.statusID) {
        case 0: // 待定 ClientUndetermined
          stateClass = 'sorderstate-client'
          break
        case 1:// 已确认Confirmed
          stateClass = 'sorderstate-confirmed' //  background-color: #0598e1;
          break

        case 20:// 客服锁定CLock
          stateClass = 'sorderstate-clock'
          break
        case 21:// 客服审核完成 //CChecked
          stateClass = 'sorderstate-cchecked'
          break
        case 22:// 客服驳回 CReject
          stateClass = 'sorderstate-customer'
          break
        case 30:// 技术锁定 //MLock
          stateClass = 'sorderstate-mlock'
          break
        case 31:// 技术审核完成 MChecked
          stateClass = 'sorderstate-MChecked'
          break
        case 32:// 技术驳回 MReject
          stateClass = 'sorderstate-technology'
          break
        case 40:// 计划下单Planed
          stateClass = 'sorderstate-planed'
          break
        case 41:// 计划驳回 PReject
          stateClass = 'sorderstate-preject'
          break
        case 50: // 完成 Finished
          stateClass = 'sorderstate-finished'
          break
        default:
          stateClass = ''
          break
      }
      return stateClass
    },

    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { text: query }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (to.params.refresh) {
        vm.loadData()
      }
    })
  }
}
</script>

<style lang="scss" >
.sorderlist {
  // .mtmtoolbar {
  //   overflow-x: auto;
  //   overflow-y: hidden;
  // }
  .expandclass {
    background-color: #e6f7ff;
  }
  .sorderstate-client {
    background-color: #909399;
    color: cornsilk;
  }
  .sorderstate-confirmed {
    background-color: #0598e1;
    color: cornsilk;
  }
  //客服驳回
  .sorderstate-customer {
    background-color: #0da468;
    color: cornsilk;
  }
  //客服锁定CLock
  .sorderstate-clock {
    background-color: #0d97a4;
    color: cornsilk;
  }
  //客服审核完成 //CChecked
  .sorderstate-cchecked {
    background-color: #0da410;
    color: cornsilk;
  }
  //技术 技术驳回
  .sorderstate-technology {
    background-color: #e6a23c;
    color: cornsilk;
  }
  //技术 技术锁定
  .sorderstate-mlock {
    background-color: #a68934;
    color: cornsilk;
  }
  //技术 技术审核完成
  .sorderstate-MChecked {
    background-color: #a47e0d;
    color: cornsilk;
  }
  //计划下单Planed
  .sorderstate-planed {
    background-color: #ea6157;
    color: cornsilk;
  }
  //计划驳回 PReject
  .sorderstate-preject {
    background-color: #de3327;
    color: cornsilk;
  }
  //完成 Finished
  .sorderstate-finished {
    background-color: #0d6aa4;
    color: cornsilk;
  }
}
</style>
