<template>
  <d2-container class="modelelem">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
        </template>
      </vxe-toolbar>
    </template>

    <template>
      <d2-container>
        <split-pane :min-percent='15' :default-percent='20' split="vertical">
          <template slot="paneL" class="paneL">
            <vxe-form :data="form" :items="formItems" title-align="right" title-width="100"></vxe-form>
          </template>
          <template slot="paneR">
            <el-tabs type="border-card" v-model="activeName" style="height:99%">
              <el-tab-pane label="关联版型" name="modelmodelelem">
                <model-model-elem :form="form" />
              </el-tab-pane>
              <el-tab-pane label="关联CAD" name="modelelemcad">
                <model-elem-cad :form="form" />
              </el-tab-pane>
              <el-tab-pane label="关联客户版型" name="modelmodelelemclient">
                <model-model-elem-client :form="form" />
              </el-tab-pane>
            </el-tabs>
          </template>
        </split-pane>
      </d2-container>
    </template>

  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import ModelModelElem from './detail_modelmodelelem'
import ModelElemCad from './detail_modelelemcad'
import ModelModelElemClient from './detail_modelmodelelemclient'
export default {
  name: 'MomModelelemDetail',
  mixins: [detailTableMixins],
  components: {
    ModelModelElem,
    ModelElemCad,
    ModelModelElemClient
  },
  data () {
    return {
      activeName: 'modelmodelelem',
      formItems: [
        { field: 'code', title: '款式明细编码', span: 24, itemRender: { name: '$input', props: { disabled: true } } },
        { field: 'codeName', title: '款式明细名称', span: 24, itemRender: { name: '$input', props: { disabled: true } } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$textarea', props: { disabled: true } } },
        { field: 'isActive', title: '活动', span: 24, itemRender: { name: '$switch', props: { disabled: true } } }
        // { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ]
    }
  },
  created () {

  },

  methods: {
    // goback () {
    //   this.$emit('nextpage', { pagename: 'master', data: {}, masterSeach: this.masterSeach, keepalive: true })
    // }
  }
}
</script>

<style lang="scss" >
.modelelem {
  .el-tabs__content {
    height: 90%;
  }
  .d2-container-full__body {
    overflow-x: hidden !important;
  }
}
</style>
