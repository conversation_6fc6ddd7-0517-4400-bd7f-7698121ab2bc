<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="add">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">

            <vxe-form-item field="businessGroupID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.businessGroupID" placeholder="业务类型" filterable clearable size="mini">
                  <el-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="technologyGroupID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.technologyGroupID" placeholder="工艺属性" filterable clearable size="mini">
                  <el-option v-for="item in TechnologyGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="textureGroupID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.textureGroupID" placeholder="面料纹理" filterable clearable size="mini">
                  <el-option v-for="item in ItemTextureMLGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemGroupID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.itemGroupID" placeholder="面料分类" filterable clearable size="mini">
                  <el-option v-for="item in ItemMLGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default="{ data }">
                <vxe-input v-model.trim="data.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="BadItemMasterTable" ref='master_table' :row-class-name="rowClassName" @cell-dblclick="cellDblClick" :loading="tableLoading" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="code" title="面料编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="supplierItemCode" title="供应商编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="supplierItemName" title="供应商名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="number" title="序号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemClassText" title="物料类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemGroupText" title="物料分类" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="businessGroupText" title="业务归属" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="colorNo" title="色号" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="clientCode" title="客户编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="yearNo" title="年份" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="transparency" title="透胶/光性" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="technologyText" title="工艺属性" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="groupNames" title="品类" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isShopDefault" title="商城默认" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="unitGroupText" title="单位分类" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemSize" title="规格型号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="textureGroupText" title="纹理(CM)" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemFoldText" title="裁剪属性" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="width" title="门幅单位" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemComp" title="面料成分" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="markWashInfo" title="洗唛信息" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="placeOfOrigin" title="原产地" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="yarn" title="纱织" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="weight" title="克重" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="shrink" title="缩率" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="supplierItemCode" title="供应商编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="supplierItemName" title="供应商名称" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="itemBrand" title="物料品牌" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="unitSellingPrice" title="销售单价" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="retailPrice" title="零售价格" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="wholesalePrice" title="批发价格" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="inventoryQty" title="库存数" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isElemShow" title="工艺单图片展示？" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isClientItem" title="客户指定?" :formatter='formatBool' sortable width="100px"></vxe-table-column>

      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>

      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable></vxe-table-column>
      <vxe-table-column title="操作" width="180px" show-overflow :fixed='tableOptFixed' v-if="(menuAction.allowEdit||menuAction.allowDelete)&&opt">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa el-icon-picture" @click="imageCellClick(row)"></vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row,editEventCall)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <!-- <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
          <vxe-button type="text" icon="fa fa-copy" v-if="menuAction.allowAdd" @click="copyRowEvent(row,['originalItemNo','supplierItemID','markWashInfo','clientID'])"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="companyinfoShow" />
          <slot name="otherbtn"></slot>
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '面料-编辑&保存' : '面料-新增&保存'" width="1000" resize :destroy-on-close='true' :loading="submitLoading" remember>
      <vxe-form :data="selectRow" :rules="formRules1" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="面料编码" field="code" span="8">
          <template #default>
            <el-input placeholder="面料编码" v-model="selectRow.code" disabled size="mini">
            </el-input>
          </template>
        </vxe-form-item>

        <vxe-form-item title="编码名称" field="codeName" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="物料类别" field="itemClassID" span="6" :item-render="{ name: '$select', options: ItemClassComboStore, props: { disabled: true }}"></vxe-form-item>
        <vxe-form-item title="物料分类" field="itemGroupID" span="6">
          <template #default="{ data }">
            <el-select v-model="data.itemGroupID" filterable placeholder="物料分类" size="mini">
              <el-option v-for="item in ItemMLGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="业务归属" field="businessGroup" span="6" :item-render="{ name: '$select', options: BusinessGroupComboStore}"></vxe-form-item>
        <!-- <vxe-form-item title="原始货号" field="originalItemNo" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <vxe-form-item title="原始货号" field="originalItemNo" span="12">
          <template #default="{ data }">
            <el-select v-model="data.originalItemNo" filterable placeholder="请选择" size="mini" remote reserve-keyword default-first-option allow-create plremoteaceholder="款式" :remote-method="remoteMethod2" style="width:100%">
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.label">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="序号" field="number" span="6" :item-render="{name: 'input',attrs: {type: 'number',}}"></vxe-form-item> -->
        <vxe-form-item title="品类" field="groupIDs" span="24">
          <template #default="{ data }">
            <el-select v-model="data.groupIDs" multiple filterable placeholder="品类" size="mini" style="width: 100%;">
              <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="年份" field="yearNo" span="6" :item-render="{name: 'input',attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="客户" span="6" field="clientID">
          <template #default="{ data }">
            <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
              <el-option v-for="item in clientComboStoreByQuery" :key="item.label+item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="供应商" field="supplierItemID" span="6">
          <template #default="{ data }">
            <el-select v-model="data.supplierItemID" filterable placeholder="供应商" size="mini" clearable>
              <el-option v-for="item in SupplierItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="透胶/光性" field="transparency" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="工艺属性" field="technologyGroupID" span="6" :item-render="{ name: '$select', options: TechnologyGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="单位分类" field="unitGroupID" span="6" :item-render="{ name: '$select', options: ItemUnitGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="规格型号" field="itemSize" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="纹理(CM)" field="textureGroupID" span="6" :item-render="{ name: '$select', options: ItemTextureMLGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="裁剪属性" field="itemFold" span="6" :item-render="{ name: '$select', options: ItemFoldTypeComboStore,props:{clearable:true,placeholder:'不填默认对折'}}"></vxe-form-item>
        <vxe-form-item title="门幅" field="width" span="6">
          <vxe-input v-model="selectRow.width" type="float"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="面料成分" field="itemComp" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="纱织" field="yarn" span="6">
          <vxe-input v-model="selectRow.yarn" type="float"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="克重" field="weight" span="6">
          <vxe-input v-model="selectRow.weight" type="float"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="缩率" field="shrink" span="6">
          <vxe-input v-model="selectRow.shrink" type="float"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="洗唛信息" field="markWashInfo" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="物料品牌" field="itemBrand" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="原产地" field="placeOfOrigin" span="6" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="销售单价" field="unitSellingPrice" span="6">
          <vxe-input v-model="selectRow.unitSellingPrice" type="float"></vxe-input>
        </vxe-form-item>
        <!-- <vxe-form-item title="零售价格" field="retailPrice" span="6">
          <vxe-input v-model="selectRow.retailPrice" type="float"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="批发价格" field="wholesalePrice" span="6">
          <vxe-input v-model="selectRow.wholesalePrice" type="float"></vxe-input>
        </vxe-form-item> -->
        <!-- <vxe-form-item title="库存数" field="inventoryQty" span="6">
          <vxe-input v-model="selectRow.inventoryQty" type="float"></vxe-input>
        </vxe-form-item> -->
        <vxe-form-item title="工艺单图片展示？" title-width="150" field="isElemShow" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="是否客户指定?" field="isClientItem" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="商城默认" field="isShopDefault" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawerfl" :visible.sync="draweritemml" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <item-image :form="selectRow" v-if="draweritemml" />
    </el-drawer>
  </d2-container>
</template>

<script>
import { cloneDeep } from 'lodash'
import masterTableMixins from '@/mixins/master_table_mixins/index'
import itemImage from './components/itemimage'
export default {
  name: 'BadItemMaster', // 物料编码
  mixins: [masterTableMixins],
  props: {
    add: {
      type: Boolean,
      default: true
    },
    opt: {
      type: Boolean,
      default: true
    },
    companyinfoShow: {
      type: Boolean,
      default: true
    }
  },
  components: {
    itemImage
  },
  data() {
    return {
      searchForm: {
        itemClassID: 1
      },
      draweritemml: false,
      formData: {
        code: '待生成',
        codeName: '',
        remark: '',
        isActive: true,
        number: null,
        itemConfigBaseID: null,
        itemClassID: 1,
        itemGroupID: null,
        businessGroup: null,
        originalItemNo: null,
        colorNo: null,
        yearNo: new Date().getFullYear(),
        transparency: null,
        technologyGroupID: null,
        unitGroupID: null,
        itemSize: null,
        textureGroupID: null,
        supplierItemID: null,
        width: null,
        itemComp: null,
        yarn: null,
        weight: null,
        shrink: null,
        supplierItemCode: null,
        supplierItemName: null,
        itemBrand: null,
        tetailPrice: null,
        retailPrice: null,
        wholesalePrice: null,
        inventoryQty: null,
        clientID: null,
        isElemShow: false,
        unitSellingPrice: null,
        placeOfOrigin: null,
        isClientItem: false,
        itemFold: null,
        markWashInfo: null,
        isShopDefault: false,
        groupIDs: [],
      },
      formRules1: {
        itemGroupID: [{ required: true, message: '请选择物料分类' }],
        textureGroupID: [{ required: true, message: '请选择纹理' }],
        originalItemNo: [{ required: true, message: '请输入原始货号' }, { min: 2, max: 35, message: '长度在 2 到 35 个字符' }],
        // supplierItemCode: [{ required: true, message: '请输入供应商编码' }, { min: 6, max: 20, message: '长度在 6 到 20 个字符' }],
        technologyGroupID: [{ required: true, message: '请输入工艺属性' }],
        width: [{ required: true, message: '幅宽必填' }],
        // itemComp: [{ required: true, message: '面料成分必填' }],
        businessGroup: [{ required: true, message: '业务归属必填' }],
        yearNo: [{ required: true, message: '年份必填' }]
        // yearNo: [{ required: true, message: '请输入两位数年份' }, { type: 'number', min: 10, message: '最少两位数' }]
      },
      api: {
        get: '/mtm/bad_item/get',
        add: '/mtm/bad_item/adds',
        edit: '/mtm/bad_item/updates',
        delete: '/mtm/bad_item/deletes',

        ItemClassComboStore: '/mtm/combo/ItemClassComboStore',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore',
        TechnologyGroupComboStore: '/mtm/combo/TechnologyGroupComboStore',
        ItemUnitGroupComboStore: '/mtm/combo/ItemUnitGroupComboStore',
        ItemTextureMLGroupComboStore: '/mtm/combo/ItemTextureMLGroupComboStore',
        ItemMLGroupComboStore: '/mtm/combo/ItemMLGroupComboStore',
        SupplierItemComboStore: '/mtm/combo/SupplierItemComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        ItemFoldTypeComboStore: '/mtm/combo/ItemFoldTypeComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
      },
      clientComboStoreByQuery: [],
      footerCompanyInfo: false,
      BusinessGroupComboStore: [],
      TechnologyGroupComboStore: [],
      ItemUnitGroupComboStore: [],
      ItemClassComboStore: [],
      ItemTextureMLGroupComboStore: [],
      ItemTextureFLGroupComboStore: [],
      ItemMLGroupComboStore: [],
      ItemFLGroupComboStore: [],
      SupplierItemComboStore: [],
      ItemFoldTypeComboStore: [],
      GroupComboStore: [],
      ItemComboStore: []

    }
  },
  async created() {
    await this.getCombStore()
  },
  methods: {
    async getCombStore() {
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemFoldTypeComboStore).then(result => {
        this.ItemFoldTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemUnitGroupComboStore).then(result => {
        this.ItemUnitGroupComboStore = result
        this.formData.unitGroupID = result[0].value
      })
      await this.$api.ActionRequest(this.api.TechnologyGroupComboStore).then(result => {
        this.TechnologyGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemClassComboStore).then(result => {
        this.ItemClassComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemTextureMLGroupComboStore).then(result => {
        this.ItemTextureMLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemMLGroupComboStore).then(result => {
        this.ItemMLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SupplierItemComboStore).then(result => {
        this.SupplierItemComboStore = result
      })
    },
    remoteMethod(query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod2(query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    // 编辑
    async editEventCall(row) {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      // this.selectRow = cloneDeep(row)
      this.showEdit = true
    },
    imageCellClick(row) {
      this.selectRow = cloneDeep(row)
      this.draweritemml = true
    },
    handleClose() {
      this.draweritemml = false
    }
    // copyRowEvent (row) {
    //   row.id = null
    //   row.code = ''
    //   row.codeName = ''
    //   this.selectRow = cloneDeep(row)
    //   this.showEdit = true
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
