<template>
  <d2-container>
    <template v-slot:header> <el-card class="box-card">
        <el-alert title="只能修改[suitSupplyOrderDetails]下的数据,请勿修改任何id相关的数据,否则会造成数据丢失！" :closable="false" type="error" effect="dark">
        </el-alert>
      </el-card></template>
    <el-card class="box-card">
      <div>
        <JsonEditorVue v-model="formData" ref="jsoneditor" class="jse-theme-dark" v-bind="jsonprops" :readOnly="readOnly" :expanded-on-start="true" />
      </div>
    </el-card>
    <template v-slot:footer>
      <el-row style="float: right;">
        <vxe-button status="warning" size="mini" icon="vxe-icon--download" @click="createJson(form)">下载</vxe-button>
        <vxe-button status="warning" size="mini" v-if="!readOnly" :disabled="!hasJosnFlag">生成MTM订单</vxe-button>
        <vxe-button status="success" size="mini" @click="save()" v-if="!readOnly" :disabled="!hasJosnFlag">保存</vxe-button>
      </el-row>
    </template>
  </d2-container>
</template>

<script>
import JsonEditorVue from 'json-editor-vue'
// import 'vanilla-jsoneditor/themes/jse-theme-dark.css'
export default {
  name: 'SuitsupplyOrderJsonInfo',
  components: { JsonEditorVue },
  props: {
    form: { type: Object, default: null },
    successEvent: { type: Function },
    readOnly: { type: Boolean, default: true }
  },
  data () {
    return {
      api: {
        add: '/mtm/oDM_SorderSuitSupplyOrder/adds',
        edit: '/mtm/oDM_SorderSuitSupplyOrder/updates',
        delete: '/mtm/oDM_SorderSuitSupplyOrder/deletes'
      },
      formData: {},
      hasJosnFlag: true,
      jsonprops: {
        mode: 'text',
        mainMenuBar: false,
        navigationBar: true,
        // showBtns: true,
        stringified: false,
        indentation: 4,
        tabSize: 4,
        escapeControlCharacters: false,
        onChange: (content, previousContent, { contentErrors, patchResult }) => {
          console.log(content)
          if (contentErrors && !contentErrors.isRepairable) {
            this.hasJosnFlag = false
          } else {
            this.hasJosnFlag = true
          }
        }

      }
    }
  },
  created () {
    if (this.form != null) {
      this.formData = Object.assign(this.formData, this.form)
    }
  },
  methods: {
    async save () {
      if (!this.hasJosnFlag) {
        return
      }
      // console.log(this.formData)
      var url = this.api.add
      if (this.formData.id !== null) {
        url = this.api.edit
      }
      await this.$api.ActionRequest(url, [this.formData]).then(result => {
        this.successEvent()
        this.$XModal.message({ message: '保存成功', status: 'success' })
      })
    },
    createJson (res) {
      // var data = res
      var data = JSON.stringify(res)
      // encodeURIComponent解决中文乱码
      const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(data)
      // 通过创建a标签实现
      const link = document.createElement('a')
      link.href = uri
      var date = this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss')
      // 对下载的文件命名
      link.download = res.sorderNumber + '_' + res.receipt_Number + '_' + date + '.json'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

  <style>
</style>
