<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button status="success" v-if="menuAction.allowPrint" @click="exportSelectEvent">明细导出</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="state">
              <template #default="{ data }">
                <el-select v-model="data.state" placeholder="状态" filterable clearable size="mini">
                  <el-option v-for="item in ItemBomStateComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItembomMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="originalItemNo" title="订单面料" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="invoicesNumber" title="单据号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="订单类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="stateText" title="状态" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"></vxe-table-column>
      <vxe-table-column field="deliveryDate" title="交货日期" :formatter="val=>formatDate(val)" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column> -->
      <!-- <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row,column  }">
          <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button> -->
          <!-- <vxe-button status="success" @click="updateState(row,5)" v-if="menuAction.allowEdit&&(row.state===1||row.state===3)">备货</vxe-button> -->
          <vxe-button status="success" @click="updateState(row,9)" v-if="menuAction.allowEdit&&(row.state===5)">完成备货</vxe-button>
          <vxe-button status="warning" @click="detailShowEvent(row,column)" v-if="menuAction.allowEdit">清单</vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="detailShow" :title="selectRow.sorderNumber+'物料清单'" width="80%" height="60%" resize destroy-on-close :loading="submitLoading">
      <item-bom-detail :form="selectRow" @finish="finish" />
    </vxe-modal>
    <vxe-modal v-model="exportShow" :title="'物料清单导出'" width="80%" height="60%" resize destroy-on-close :loading="submitLoading">
      <export-table v-if="exportShow" :selectTable="selectRows" @finish="finish" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import ItemBomDetail from './detail.vue'
import ExportTable from './compontents/ExportTable.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_itembom',
  mixins: [masterTableMixins],
  components: {
    ItemBomDetail,
    ExportTable
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      exportShow: false,
      selectRows: [],
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      detailShow: false,
      api: {
        get: '/mtm/war_itembom/get',
        add: '/mtm/war_itembom/adds',
        edit: '/mtm/war_itembom/updates',

        delete: '/mtm/war_itembom/deletes',
        ItemBomStateComboStore: '/mtm/combo/ItemBomStateComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      ItemBomStateComboStore: [],
      clientComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemBomStateComboStore).then(result => {
        this.ItemBomStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    detailShowEvent (row, column) {
      this.selectRow = row
      this.detailShow = true
    },
    tableCellClick ({ row, column }) {
      if (column.title === '操作') {
        return
      }
      this.selectRow = row
      this.detailShow = true
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    exportSelectEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length === 0) {
        this.$XModal.message({ message: '请勾选要下载的数据', status: 'error' })
        return
      }
      if (rows.length > 30) {
        this.$XModal.message({ message: '一次性最大下载30订单', status: 'error' })
        return
      }
      this.selectRows = rows
      this.exportShow = true
    },
    finish () {
      this.detailShow = false
      this.exportShow = false
      this.selectRows = []
      this.loadData()
    },
    async updateState (row, state) {
      const loading = this.$loading({
        lock: true,
        text: '请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      var data = cloneDeep(row)
      data.state = state
      await this.$api.ActionRequest(this.api.edit, [data]).then(result => {
        this.$message({ message: '操作成功', type: 'success' })
        this.loadData()
        loading.close()
      }).catch(() => {
        loading.close()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
