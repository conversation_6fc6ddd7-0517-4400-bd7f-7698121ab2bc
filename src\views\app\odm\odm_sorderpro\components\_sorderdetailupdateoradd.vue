<template>
  <vxe-form :data="detailFormData" :rules="detailFormRules" title-align="right" title-width="100" @submit="submitDetailData">
    <vxe-form-item title="顾客" field="clientPersonID" span="12" :item-render="{}"><template #default>
        <vxe-pulldown ref="personDown" :size="size" :transfer="true">
          <template v-slot>
            <vxe-input v-model="detailFormData.clientPersonText" suffix-icon="fa fa-search" placeholder="点击搜索顾客" @keyup="personkeyupEvent" @focus="personFocusEvent" @suffix-click="personSuffixClick" clearable @clear="personClear"></vxe-input>
          </template>
          <template v-slot:dropdown>
            <div class="dropdownperson">
              <vxe-grid Id="sorderperson" keep-source highlight-hover-row auto-resize height="300" width="800" min-height="300px;" ref="personGrid" :loading="personLoading" :pager-config="personTablePage" :data="PsersontableData" :columns="PsersontableColumn" @cell-click="PsersonCellClickEvent" @page-change="PsersonPageChange" :edit-config="{trigger: 'manual', mode: 'row', showStatus: true, icon: 'fa fa-pencil'}" :toolbar="tableToolbar" :custom-config="{storage: false}">
                <template v-slot:toolbar_buttons>
                  <vxe-button @click="clientPersonShow=!clientPersonShow">新增</vxe-button>
                </template>
                <template v-slot:sex_default="{ row }">
                  <template>
                    {{row.gender?"男":"女"}}
                  </template>
                </template>
                <template v-slot:operate="{ row }">
                  <template v-if="$refs.personGrid.isActiveByRow(row)">
                    <vxe-button icon="fa fa-save" status="primary" title="保存" circle @click="savePsersonRowEvent(row)"></vxe-button>
                  </template>
                  <template v-else>
                    <vxe-button icon="fa fa-edit" title="编辑" circle @click="editPsersonRowEvent(row)"></vxe-button>
                  </template>
                  <vxe-button icon="fa fa-trash" title="删除" circle @click="removePsersonRowEvent(row)"></vxe-button>
                  <vxe-button icon="fa fa-copy" title="复制" circle @click="copyRowEvent(row)"></vxe-button>
                </template>
              </vxe-grid>
            </div>
          </template>
        </vxe-pulldown>
      </template>
    </vxe-form-item>
    <vxe-form-item title="身高" field="height" span="12" :item-render="{}"><template #default>
        <vxe-input v-model="detailFormData.height" placeholder="顾客身高" type="number" :size="size"></vxe-input>
      </template></vxe-form-item>
    <vxe-form-item title="面料编码" field="itemID" span="12" :item-render="{}"><template #default>
        <vxe-pulldown ref="itemDown" :size="size">
          <template v-slot>
            <vxe-input v-model="detailFormData.itemText" class="my-search" placeholder="点击搜索面料" @keyup="itemKeyupEvent" @focus="itemFocusEvent" @suffix-click="itemSuffixClick" clearable @clear="itemClear">
            </vxe-input>
            <el-button v-if="ItemtableData.length===0" type="success" icon="el-icon-plus" size="mini" @click="itemMasterEvent"></el-button>
          </template>
          <template v-slot:dropdown>
            <div class="dropdownitem">
              <vxe-grid id="sorderitemgrid" highlight-hover-row auto-resize height="300" :loading="itemLoading" :pager-config="itemTablePage" :data="ItemtableData" :columns="ItemtableColumn" @cell-click="itemCellClickEvent" @page-change="itemPageChange">
              </vxe-grid>
            </div>
          </template>
        </vxe-pulldown>
      </template>
    </vxe-form-item>
    <vxe-form-item title="业务归属" field="itemBusinessGroupID" span="12" :item-render="{}"><template #default>

        <vxe-select v-model="detailFormData.itemBusinessGroupID" placeholder="业务归属" clearable :size="size">
          <vxe-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
        </vxe-select>
      </template>
    </vxe-form-item>
    <vxe-form-item title="面料描述" field="finalFabric" span="12" :item-render="{}"><template #default>
        <vxe-input v-model="detailFormData.finalFabric" clearable :size="size"></vxe-input>
      </template></vxe-form-item>
    <vxe-form-item title="面料门幅" field="finalWidth" span="12" :item-render="{}"><template #default>
        <vxe-input v-model="detailFormData.finalWidth" type="number" clearable :size="size"></vxe-input>
      </template></vxe-form-item>
    <vxe-form-item title="面料纹理" field="finalTexture" span="12" :item-render="{}"><template #default>
        <vxe-select v-model="detailFormData.finalTexture" placeholder="面料纹理" clearable :size="size" class="finalTexture">
          <vxe-option v-for="item in ItemTextureMLGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
        </vxe-select>
      </template></vxe-form-item>
    <vxe-form-item title="面料成分" field="finalComposition" span="12" :item-render="{}"><template #default>
        <vxe-input v-model="detailFormData.finalComposition" clearable :size="size"></vxe-input>
      </template></vxe-form-item>
    <vxe-form-item title="面料长度" field="itemLength" span="12" :item-render="{}"><template #default>
        <vxe-input v-model="detailFormData.itemLength" type="number" clearable :size="size"></vxe-input>
      </template></vxe-form-item>
    <vxe-form-item title="半成品试衣" field="halfFitting" span="12" :item-render="{}"><template #default>
        <vxe-switch v-model="detailFormData.halfFitting" open-label="是" close-label="否" :size="size"></vxe-switch>
      </template></vxe-form-item>
    <vxe-form-item title="备注" field="remark" span="24" :item-render="{}"><template #default>
        <vxe-textarea v-model="detailFormData.remark" placeholder="订单备注" maxlength="100" show-word-count :size="size"></vxe-textarea>
      </template></vxe-form-item>
    <vxe-form-item align="center" span="24" :item-render="{}"><template #default>
        <vxe-button type="submit" status="primary">保存</vxe-button>
        <vxe-button type="reset">重置</vxe-button>
      </template></vxe-form-item>
  </vxe-form>
</template>

<script>
import { cloneDeep } from 'lodash'
export default {
  name: 'sorderDetailUpdateOrAdd',
  props: {
    sorderDetailShow: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'mini'
    }
  },
  data () {
    return {
      selectDetailRow: null,
      showSorderDetail: false,
      detailFormData: {
        sorderID: null,
        id: null,
        itemBusinessGroupID: null, // 面料业务归属
        itemID: null,
        itemLength: null,
        itemText: null,
        finalWidth: 0,
        finalTexture: null, // 纹理
        finalComposition: null, // 面料成分
        // finalFabricLabel: null, // 客供面料表
        finalFabric: null, // 客供面料号
        deliveryDate: null, // 期望交期
        clientPersonID: null,
        clientPersonText: null,
        height: null,
        HalfFitting: false,
        remark: null,
        detailModelData: []
      },
      ItemtableData: [],
      api: {
        ItemTextureMLGroupComboStore: '/mtm/combo/ItemTextureMLGroupComboStore',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore'
      },
      BusinessGroupComboStore: [],
      ItemTextureMLGroupComboStore: [],
      detailFormRules: {
        clientPersonID: [{ required: true, message: '请选择客户' }],
        itemID: [{ required: true, message: '请输入面料信息' }],
        finalTexture: [{ required: true, message: '请选择纹理' }],
        finalWidth: [{ required: true, message: '幅宽必填' }],
        height: [{ required: true, message: '请输入身高' }],
        finalFabric: [{ required: true, message: '面料描述不能为空' }],
        itemBusinessGroupID: [{ required: true, message: '面料业务属性必填' }]
      }
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemTextureMLGroupComboStore).then(result => {
        this.ItemTextureMLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
    },
    async personClear () {
      this.sorderForm.clientPersonID = null
      this.sorderForm.clientPersonText = null
      if (this.sorderForm.clientID === null || this.sorderForm.clientID === '') return
      await this.getPsersonbyQuery(null, this.sorderForm.clientPersonID)
    },
    personSuffixClick () {
      this.$refs.personDown.togglePanel()
    },
    itemSuffixClick () {
      this.$refs.itemDown.togglePanel()
    },
    itemMasterEvent () {
      this.itemMaster.itemForm.originalItemNo = this.sorderForm.itemText
      this.itemMaster.show = !this.itemMaster.show
    },
    async savePsersonRowEvent (row) {
      var b = await this.fullValidEvent()
      if (!b) {
        return
      }
      this.$refs.personGrid.clearActived().then(() => {
        this.loading = true
        var url = row.id == null ? this.api.clientPsersonAdd : this.api.clientPsersonEdit
        this.$api.ActionRequest(url, [row]).then(result => {
          this.loading = false
          this.getPsersonbyQuery()
          this.$XModal.message({ message: '保存成功！', status: 'success' })
        })
      })
    },
    editPsersonRowEvent (row) {
      this.clientPsersonSelectRow = Object.assign(this.clientPsersonSelectRow, cloneDeep(row))
      this.clientPersonShow = true
      // this.$refs.personGrid.setActiveRow(row)
    },
    itemFocusEvent () {
      this.$refs.itemDown.showPanel()
    },

    async removePsersonRowEvent (row) {
      await this.$api.ActionRequest(this.api.clientPsersonDelete, [row]).then(result => {
        this.$XModal.message({ message: '删除成功', status: 'success' })
        this.getPsersonbyQuery()
        this.$refs.personDown.showPanel()
      })
    },
    copyRowEvent (row) {
      this.clientPsersonSelectRow = Object.assign(this.clientPsersonSelectRow, cloneDeep(row))
      this.clientPsersonSelectRow.id = null
      this.clientPersonShow = true
    },
    async itemKeyupEvent (query) {
      if (!query) return
      const { value } = query || ''
      this.itemTablePage.currentPage = 1
      await this.getItembyQuery(value)
    },
    async getItembyQuery (query = null, itemId = null) {
      this.itemLoading = true
      await this.$api.ActionRequest(this.api.sorderItem, { Id: itemId, Text: query, MaxResultCount: this.itemTablePage.pageSize, SkipCount: (this.itemTablePage.currentPage - 1) * this.itemTablePage.pageSize }).then(result => {
        this.itemTablePage.total = result.totalCount
        this.ItemtableData = result.items
        this.itemLoading = false
      })
    },

    async itemClear () {
      this.sorderForm.itemText = null
      this.sorderForm.itemID = null
      this.sorderForm.finalWidth = null
      this.sorderForm.finalTexture = null
      this.sorderForm.finalComposition = null
      // this.sorderForm.finalFabricLabel = null
      this.sorderForm.finalFabric = null
      // this.ZB.KGML.id = null
      await this.getItembyQuery(null, this.sorderForm.itemID)
    },
    async personFocusEvent () {
      if (this.sorderForm.clientID === null) {
        this.$notify({
          message: '请先选择客户',
          type: 'error'
        })
        return
      }
      if (this.PsersontableData.length === 0) {
        await this.getPsersonbyQuery()
      }
      this.$refs.personDown.showPanel()
    },

    async fullValidEvent () {
      var xtable = this.$refs.personGrid
      const errMap = await xtable.fullValidate().catch(errMap => errMap)
      if (errMap) {
        const msgList = []
        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            const { rowIndex, column, rules } = params
            rules.forEach(rule => {
              msgList.push(`第 ${rowIndex} 行 ${column.title} 校验错误：${rule.message}`)
            })
          })
        })
        this.$XModal.message({
          status: 'error',
          message: () => {
            return [
              <div class="red" style="max-height: 400px;overflow: auto;">
                {
                  msgList.map(msg => <div>{msg}</div>)
                }
              </div>
            ]
          }
        })
        return false
      } else {
        return true
      }
    },
    async personkeyupEvent (query) {
      if (!query) return
      const { value } = query || ''
      this.personTablePage.currentPage = 1
      if (this.sorderForm.clientID === null || this.sorderForm.clientID === '') {
        this.$notify({
          message: '请先选择客户',
          type: 'error'
        })
        return
      };
      await this.getPsersonbyQuery(value)
    },
    async getPsersonbyQuery (query = null, personId = null) {
      this.personLoading = true
      await this.$api.ActionRequest(this.api.sorderPserson, { ClientID: this.sorderForm.clientID, isActive: true, Id: personId, Text: query, MaxResultCount: this.personTablePage.pageSize, SkipCount: (this.personTablePage.currentPage - 1) * this.personTablePage.pageSize }).then(result => {
        this.personTablePage.total = result.totalCount
        this.PsersontableData = result.items
        this.personLoading = false
      })
    },
    PsersonCellClickEvent ({ row, column }) {
      if (column.title !== '操作') {
        this.detailFormData.clientPersonID = row.id
        this.detailFormData.clientPersonText = row.label
        this.detailFormData.height = row.height + ''
        this.$refs.personDown.hidePanel()
      }
    },
    async PsersonPageChange ({ currentPage, pageSize }) {
      this.personTablePage.pageSize = pageSize
      this.personTablePage.currentPage = currentPage
      await this.getPsersonbyQuery()
    },
    async itemPageChange ({ currentPage, pageSize }) {
      this.itemTablePage.pageSize = pageSize
      this.itemTablePage.currentPage = currentPage
      await this.getItembyQuery()
    },
    async itemCellClickEvent ({ row }) {
      this.detailFormData.itemText = row.label
      this.detailFormData.itemID = row.itemID
      this.detailFormData.finalWidth = row.width
      this.detailFormData.finalTexture = row.finalTextureID
      this.detailFormData.finalComposition = row.finalComposition
      var id = row.itemID.toLocaleLowerCase()
      if (id === this.ZB.KGML.id) {
        this.detailFormData.finalFabric = null
      } else {
        this.detailFormData.finalFabric = row.label
      }
      this.$refs.itemDown.hidePanel()
    },
    submitDetailData () {
      this.selectDetailRow = null
      this.showDetailEdit = false
      this.detailTableData.push(this.detailFormData)
    }
  }
}
</script>

<style>
</style>
