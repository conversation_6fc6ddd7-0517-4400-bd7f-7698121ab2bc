<template>
  <d2-container class="odm_sorderprodetailmodel">
    <div style="width:100%;height:10px"></div>

    <el-card class="box-card" id='id_sorderdetailbodyandsize'>
      <template>
        <d2-container>
          <split-pane :min-percent='10' :default-percent='20' split="vertical">
            <template slot="paneL">
              <table-person-data-select :activeModel="activeModel" ref="tablePersonDataSelectRef" :sorderStore='sorderStore' @selectDetail="selectDetail" />
            </template>
            <template slot="paneR">
              <template>
                <el-row :gutter="10">
                  <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <span style="padding-left: 30px;color:red;    font-size: 20px;">订单号{{sorderStore.code}}</span>
                    <span style="font-size: 20px; font-weight: 700;margin-left: 50px;">规格特体</span>&nbsp;<span style="font-size: 14px; font-weight: 700;color: red;">[{{sorderDetailModel.lineNum}}]{{sorderDetailModel.clientPersonText}}</span>

                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <sorder-detail-size :SorderDetailModel="sorderDetailModel" :sorderStore="sorderStore" :height='sorderDetailModel.height+""' :sorderdetailbodySaveEven="sorderdetailbodysave" :getBodysData="getBodysData" @DetailModelReloadEvent="DetailModelReloadEvent" :SorderDetailSizeChecked="SorderDetailSizeChecked" ref="sorderDetailSizeRef">
                    </sorder-detail-size>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <sorder-detail-body :SorderDetailModel="sorderDetailModel" ref="sorderDetailBodyRef" :sorderStore="sorderStore">
                    </sorder-detail-body>
                  </el-col>
                </el-row>
              </template>
            </template>
          </split-pane>
        </d2-container>
      </template>

    </el-card>
    <div class="modelbtns">
      <el-row>
        <el-col :span="24">
          <el-button type="primary" size="mini" @click="prevMaster">返回上页</el-button>
        </el-col>
        <el-col :span="24">
          <el-button type="success" size="mini" @click="saveAll()">一键保存</el-button>
        </el-col>
      </el-row>
    </div>
  </d2-container>
</template>

<script>
// import SorderDetailElem from './soderdetailelem'
import SorderDetailSize from './sorderdetailsize'
import SorderDetailBody from './sorderdetailbody'
// import SorderDetailImage from './sorderdetailimage'
// import TablePersonDataEdit from './components/tablepersondataedit'
import TablePersonDataSelect from './components/tablepersondataselect.vue'
export default {
  name: 'DetailMaster',
  components: {
    // SorderDetailElem,
    // SorderDetailSizeAndBody,
    SorderDetailSize,
    SorderDetailBody,
    TablePersonDataSelect
    // SorderDetailImage,
    // TablePersonDataEdit
  },
  props: {
    activeModel: {
      type: Object,
      required: true
    },
    sorderStore: {
      type: Object
    },
    psersonheight: {
      type: String
    }
  },

  data () {
    return {
      timer: '',
      carousel: {
        autoplay: false,
        loop: false,
        indicatorPosition: 'none',
        arrow: 'never'
      },
      sorderDetailModel: {
        sizeID1: null,
        sizeIDText: null,
        modelId: null,
        sorderSizeTypeID: '5',
        lineNum: null,
        clientPersonText: null,
        isChecked: false,
        message: null

      },
      active: 'detailsizeandbody'

    }
  },
  // mounted() {
  //   this.timer = setTimeout(this.sizeAndBodySaveEvent, 1000);
  // },
  // beforeDestroy() {
  //   clearTimeout(this.timer);
  // },
  created () {

  },
  methods: {
    // 点击左侧顾客和好像
    async selectDetail ({ data }, callBack) {
      console.log('点选顾客')
      var b = true
      if (this.sorderDetailModel.id) {
        b = await this.saveAllEvent().then(res => {
          console.log(`点选顾客:${res}`)
          return res
        })
      }
      if (b) {
        this.sorderDetailModel = Object.assign(this.sorderDetailModel, data)
        this.$refs.sorderDetailSizeRef.clientPersonSizeChange()
        this.$refs.sorderDetailBodyRef.clientPersonSizeChange()
      }
      callBack(b)
    },
    SorderDetailSizeChecked (data) {
      // console.log('检验完成')
      // console.log(data)
    },
    // 重新加载顾客数据
    DetailModelReloadEvent ({ data }) {
      this.$refs.tablePersonDataSelectRef.reLoad(data)
    },
    async itemchange (val) {
      var b = false
      if (val === 'detailsizeandbody') {
        // b = await this.$refs.sorderDetailElemRef.sorderDetailElemSave()
      }
      if (val === 'detailelem') {
        b = await this.$refs.sorderDetailSizeRef.sorderDetailSizeSave()
        b = await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
      }
      if (val === 'detailimage') {
        // b = await this.$refs.sorderDetailElemRef.sorderDetailElemSave()
        b = await this.$refs.sorderDetailSizeRef.sorderDetailSizeSave()
        b = await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
      }
      if (!b) {
        return
      }
      switch (val) {
        case 'detailelem':
          this.$refs.carousel.setActiveItem('detailelem')
          break
        case 'detailsizeandbody':
          this.$refs.carousel.setActiveItem('detailsizeandbody')
          break
        case 'detailimage':
          this.$refs.carousel.setActiveItem('detailimage')
          break
        default:

          break
      }
    },
    // 点击导航菜单，页面滚动到指定位置
    handleScroll (index) {
      // this.navgatorIndex = index;
      this.$nextTick(() => {
        var selected = document.getElementById(index)
        if (selected !== null) {
          // smooth平滑过渡  // start上边框与视窗顶部平齐。默认值
          selected.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      })
    },
    async sorderdetailbodysave () {
      return await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
    },
    async sizeAndBodySaveEvent () {
      console.log('报错特体和规格')
      var b = false
      b = await this.$refs.sorderDetailSizeRef.sorderDetailSizeSave()
      b = await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
      if (b) {
        this.$XModal.message({ message: '保存成功！', status: 'success' })
      }
    },
    getBodysData () {
      return this.$refs.sorderDetailBodyRef.BodyListData.filter(item => { return item.bodyListID !== null })
    },

    // 全部保存
    async saveAll () {
      var b = await this.saveAllEvent()
      if (b) {
        this.$XModal.message({ message: '保存成功！', status: 'success' })
      }
    },
    async saveAllEvent () {
      // eslint-disable-next-line
      return new Promise(async (resolve, reject) => {
        var b1 = await this.$refs.sorderDetailSizeRef.sorderDetailSizeSave()
        var b2 = await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
        return Promise.all([b1, b2]).then((values) => {
          var b = true
          values.forEach(item => {
            if (!item && b) {
              b = true
            }
          })
          console.log(values)
          console.log('全部保存')
          if (b) {
            return resolve(true)
          } else {
            // eslint-disable-next-line
            return reject(false)
          }
        })
      })

      // if (b1 && b2) {
      //   return true
      // } else {
      //   // loading.close()
      //   return false
      // }
    },
    // 返回上一页
    async prevMaster () {
      // var b = true
      var b = await this.saveAllEvent()
      if (b) {
        this.$emit('prevMaster', { sorder: this.sorderStore })
      }
    }
  }
}
</script>

<style  lang="scss" >
.odm_sorderprodetailmodel {
  .modelbtns {
    position: fixed;
    bottom: 20%;
    right: 6%;
    z-index: 99999;
    width: 80px;
  }
  .el-carousel__container {
    height: 100% !important;
  }
  .carouselmain {
    // height: 90% !important;
    height: calc(100vh - 120px) !important;
  }
  .carousel {
    height: 100% !important;
    overflow-y: auto !important;
  }
}
</style>
