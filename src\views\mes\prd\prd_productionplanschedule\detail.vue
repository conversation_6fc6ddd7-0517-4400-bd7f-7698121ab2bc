<template>
  <div class="PrdProductionplanscheduleDetailTable">
    <vxe-table id='PrdProductionplanscheduleDetailTable' header-row-class-name="rowclass" ref='master_table' height="300" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="sorderNum" title="订单号" width="100"> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" width="120"> </vxe-table-column>
      <vxe-table-column field="groupName" title="类别" width="50"> </vxe-table-column>
      <vxe-table-column field="workSecationName" title="工段" width="150">
        <template v-slot="{ row }">
          <span :style="row.isWorckSectionPutOffed?'color: red;':''">{{row.workSecationName}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="workSecationEndTime" title="工段截至期" :formatter="formatDate" width="130">
        <template v-slot="{ row }">
          <span :style="row.isWorckSectionPutOffed?'color: red;':''">{{formatDate({cellValue:row.workSecationEndTime})}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="productionTeamName" title="小组" width="130">
        <template v-slot="{ row }">
          <span :style="row.isProductionTeamPutOffed?'color: red;':''">{{row.productionTeamName}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="productionTeamEndTime" title="小组截止期" width="130">
        <template v-slot="{ row }">
          <span :style="row.isProductionTeamPutOffed?'color: red;':''">{{formatDate({cellValue:row.productionTeamEndTime})}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="productionStationName" title="工位" width="150"> </vxe-table-column>
      <vxe-table-column field="productionProcessesCode" title="工序编码" width="80"> </vxe-table-column>
      <vxe-table-column field="productionProcessesName" title="工序名称" width="150"> </vxe-table-column>
      <vxe-table-column field="stateText" title="状态" width="80"> </vxe-table-column>
      <vxe-table-column field="scanTime" title="完成时间" width="130" :formatter="formatDate"> </vxe-table-column>
      <vxe-table-column field="message" title="消息" width="150"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
    </vxe-table>
    <!-- <template slot="footer"> -->
    <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      <template v-slot:right>
      </template>
    </vxe-pager>
    <!-- </template> -->
  </div>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'prd_productionplanschedule',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      api: {
        get: '/mes/pRD_ProductPlanSchedule/get'
      }
    }
  },
  async created () {
    this.loadData({ productionPlanDetailID: this.form.id })
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
    }

  }
}
</script>

<style lang="scss">
.PrdProductionplanscheduleDetailTable {
  border: 2px solid #e6a23c;
  .rowclass {
    color: red;
  }
}
</style>
