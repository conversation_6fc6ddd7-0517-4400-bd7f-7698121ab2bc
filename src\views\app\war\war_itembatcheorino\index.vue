<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">

            <vxe-form-item field="itemID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model.trim="data.itemID" filterable placeholder="物料名称" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
                  <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemBatchID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.itemBatchID" filterable placeholder="合同号" size="mini" remote reserve-keyword default-first-option plremoteaceholder="合同号" :remote-method="remoteMethod4" style="width:100%" clearable>
                  <el-option v-for="item in ItemBatchComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.text }}</span>
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="productGroupID" :item-render="{name: '$select', options: ReportFormConfigTypeProductGroupComboStore,props:{clearable:true,placeholder:'商品描述'}}"></vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default="{ data }">
                <vxe-input v-model.trim="data.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItembatcheorinoMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="itemBatchCode" title="海关合同号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="itemBatchEor" title="手册号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemCode" title="面料编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="eoriNo" title="备案料号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="projectNo" title="项号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="品类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="productGroupText" title="商品描述" sortable width="100"></vxe-table-column>
      <vxe-table-column field="ciqCode" title="CIQCode" sortable width="100"></vxe-table-column>
      <vxe-table-column field="weight" title="衣服总重" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="物料名称" field="itemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.itemID" filterable placeholder="物料" size="mini" remote reserve-keyword @change="itemChangeEvent" :remote-method="remoteMethod">
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="合同号" field="itemBatchID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemBatchID" filterable placeholder="请选择" size="mini" remote reserve-keyword default-first-option plremoteaceholder="合同号" :remote-method="remoteMethod4" style="width:100%">
              <el-option v-for="item in ItemBatchComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.text }}</span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="品类" field="groupText" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <vxe-form-item title="品类" field="groupText" span="6" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.groupText" filterable placeholder="物料名称" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
              <el-option v-for="item in groupTextList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="说明" span="18" :item-render="{}">
          <template #default>
            <el-alert title="品类：[1/0/0]表示上衣1/裤子0/马甲0,注意不要有多余的空格！" type="success" :closable="false"> </el-alert>
          </template>
        </vxe-form-item>

        <vxe-form-item title="项号" field="projectNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="备案料号" field="eoriNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="商品描述" field="productGroupID" span="12" :item-render="{name: '$select', options: ReportFormConfigTypeProductGroupComboStore,props:{clearable:true,placeholder:'商品描述'}}"></vxe-form-item>
        <vxe-form-item title="重量" field="weight" span="12" :item-render="{name: '$input', props: { type: 'float', min:0,max:5,digits:4}}"></vxe-form-item>
        <vxe-form-item title="CIQCode" field="ciqCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_itembatcheorino',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        productGroupID: null
      },
      formData: {
        itemID: null,
        itemBatchID: null,
        projectNo: null,
        eoriNo: null,
        groupText: '1/1/1',
        remark: '',
        productGroupID: null,
        isActive: true
      },
      groupTextList: [
        { label: '1/1/1', value: '1/1/1' },
        { label: '1/1/0', value: '1/1/0' },
        { label: '1/0/0', value: '1/0/0' },
        { label: '0/1/0', value: '0/1/0' },
        { label: '0/0/1', value: '0/0/1' },
        { label: '1/0/1', value: '1/0/1' },
        { label: '0/1/1', value: '0/1/1' }
      ],
      formRules: {
        itemID: [{ required: true, message: '请选择物料' }],
        itemBatchID: [{ required: true, message: '请选择手册号' }],
        groupText: [{ required: true, message: '请输入品类' }, { min: 4, max: 5, message: '长度在 4 到 5 个字符' }]
      },
      api: {
        get: '/mtm/war_itembatcheorino/get',
        add: '/mtm/war_itembatcheorino/adds',
        edit: '/mtm/war_itembatcheorino/updates',
        delete: '/mtm/war_itembatcheorino/deletes',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        ItemBatchComboStoreByQuery: '/mtm/comboQuery/ItemBatchComboStoreByQuery',
        ReportFormConfigTypeProductGroupComboStore: '/mtm/combo/ReportFormConfigTypeProductGroupComboStore'
      },
      ItemComboStore: [],
      ItemBatchComboStoreByQuery: [],
      ReportFormConfigTypeProductGroupComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      this.$api.ActionRequest(this.api.ItemBatchComboStoreByQuery).then(result => {
        this.ItemBatchComboStoreByQuery = result
      })
      this.$api.ActionRequest(this.api.ReportFormConfigTypeProductGroupComboStore).then(result => {
        this.ReportFormConfigTypeProductGroupComboStore = result
      })
    },
    itemChangeEvent () {
      this.selectRow.itemBatchID = null
      this.remoteMethod4()
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod4 (query) {
      if (this.selectRow.itemID === null) {
        this.$message({ message: '请先选择物料', type: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.ItemBatchComboStoreByQuery, { text: query, xid: this.selectRow.itemID }).then(result => {
        this.ItemBatchComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      this.$api.ActionRequest(this.api.ItemBatchComboStoreByQuery, { gid: row.itemBatchID }).then(result => {
        this.ItemBatchComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
