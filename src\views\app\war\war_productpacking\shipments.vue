<template>
  <div>
    <vxe-form :data="formData" title-align="right" title-width="100" @submit="submitEvent">
      <vxe-form-item title="联系人" field="contact" span="8" :item-render="{}"> <template v-slot>
          <el-input placeholder="请输入联系人" v-model="formData.contact" size="mini">
            <el-button slot="append" icon="el-icon-search" @click="personAddressShowEvent"></el-button>
          </el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="电话" field="tel" span="8" :item-render="{}"> <template v-slot>
          <vxe-input v-model="formData.tel" placeholder="请输入地址" :size="size">></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="收货地址" field="address" span="6" :item-render="{}"> <template v-slot>
          <vxe-input v-model="formData.address" placeholder="请输入地址" :size="size"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="物流公司" field="logisticsCompany" span="12" :item-render="{name: 'input'}"></vxe-form-item>
      <vxe-form-item title="快递单号" field="trackingNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
      <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}">
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
          <vxe-button type="submit" status="primary">发货</vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
    <vxe-table align="center" :data="tableData" height="200" ref="xTable">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" width="180"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" width="100"></vxe-table-column>
      <vxe-table-column field="clientPersonName" title="顾客" width="150"></vxe-table-column>
      <vxe-table-column field="productWarehouseDetailTypeText" title="状态" width="100"></vxe-table-column>
      <vxe-table-column field="logisticsCompany" title="快递公司" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipmentsNumber" title="发货单号" sortable width="100"></vxe-table-column>
    </vxe-table>
    <vxe-modal v-model="personAddress.Show" title="收货地址" width="1000" resize destroy-on-close>
      <vxe-grid border resizable height="530" :seq-config="{startIndex: (personAddress.tablePage.currentPage - 1) * personAddress.tablePage.pageSize}" :pager-config="personAddress.tablePage" :columns="personAddress.tableColumn" :data="personAddress.tableData" @page-change="handlePageChange" @cell-dblclick="personAddressTableCellClick"></vxe-grid>
    </vxe-modal>
  </div>
</template>

<script>

export default {
  name: 'war_productpacking',
  props: {
    selectRow: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      api: {
        get: '/mtm/war_productwarehousedetail/get',
        shipment: '/mtm/war_productwarehousedetail/Shipment'
      },
      formData: {
        productWarehouseID: this.selectRow.id,
        logisticsCompany: null,
        trackingNumber: null,
        remark: null,
        detailIds: []
      },
      tableData: [],
      personAddress: {
        Show: false,
        tableData: [],
        tableColumn: [
          { type: 'seq', width: 60 },
          { field: 'contact', title: '联系人' },
          { field: 'tel', title: '电话' },
          { field: 'address', title: '地址' },
          { field: 'contactDesc', title: '联系人描述' },
          { field: 'fax', title: '传真' },
          { field: 'mobile', title: '座机' },
          { field: 'email', title: '邮件' },
          { field: 'state', title: '国家' },
          { field: 'province', title: '省' },
          { field: 'city', title: '市' },
          { field: 'county', title: '县' },
          { field: 'street', title: '街道' },
          { field: 'port', title: '港口' },
          { field: 'transport', title: '运送方式' },
          { field: 'remark', title: '备注' }
        ],
        tablePage: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          align: 'left',
          pageSizes: [10, 20, 50, 100, 200, 500],
          layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
          perfect: true,
          id: null,
          maxResultCount: 10,
          skipCount: 0
        }
      }
    }
  },

  components: {},
  async created () {
    await this.loadData()
  },
  methods: {
    async loadData () {
      if (this.selectRow === null || this.selectRow.id === null) {
        return
      }
      await this.$api.ActionRequest(this.api.get, { id: this.selectRow.id, productWarehouseDetailType: 1 }).then(result => {
        this.tableData = result.items
      })
    },
    async submitEvent () {
      var data = this.$refs.xTable.getCheckboxRecords()
      if (data.length <= 0) {
        this.$notify.error({
          title: '错误',
          message: '请勾选要发货的明细订单'
        })
        return
      }
      this.formData.detailIds = data.map(item => { return item.id })
      await this.$api.ActionRequest(this.api.shipment, this.formData).then(result => {
        this.$notify.success({
          title: '成功',
          message: '发货成功'
        })
        this.loadData()
        this.$emit('reload')
      })
    },
    personAddressShowEvent () {
      if (this.sorderStore.clientID === null || this.sorderStore.clientID === '') {
        return
      }
      this.personAddress.Show = true
      this.personAddressGet()
    },
    personAddressGet () {
      this.$api.ActionRequest(this.api.PersonAddressGet, this.personAddress.tablePage).then(result => {
        this.personAddress.tableData = result.items
        this.personAddress.total = result.totalCount
      })
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.personAddress.tablePage.currentPage = currentPage
      this.personAddress.tablePage.pageSize = pageSize
      this.personAddress.tablePage.maxResultCount = pageSize
      this.personAddress.tablePage.skipCount = (currentPage - 1) * pageSize
      this.personAddressGet()
    },
    personAddressTableCellClick ({ row }) {
      this.sorderForm.contact = row.contact
      this.sorderForm.tel = row.tel
      this.sorderForm.address = row.address
      this.personAddress.Show = false
    }
  }

}
</script>

<style>
</style>
