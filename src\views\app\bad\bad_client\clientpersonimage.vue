<template>
  <d2-container class="clientpersonimage">
    <div>
      <el-upload :action="baseurl+api.add" list-type="picture-card" :on-success="onsuccess" :file-list="fileList" :auto-upload="true" :data="{PersonID:form.id}">
        <i slot="default" class="el-icon-plus"></i>
        <div slot="file" slot-scope="{file}">
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
              <i class="el-icon-zoom-in"></i>
            </span>
            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
              <i class="el-icon-download"></i>
            </span>
            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </div>
      </el-upload>
    </div>
    <el-dialog :visible.sync="dialogVisible" :modal="false" :append-to-body="true">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <template slot="footer">
      <el-pagination background layout="total,prev, pager, next" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
      </el-pagination>
    </template>
  </d2-container>
</template>

<script>
export default {
  name: 'ClientPersonImage',
  props: {
    form: {
      requited: true, type: Object
    }
  },
  data () {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      baseurl: process.env.VUE_APP_API,
      fileList: [],
      psersonImageLoading: false,
      disabled: false,
      api: {
        get: '/mtm/BAD_ClientPersonImage/get',
        add: 'mtm/BAD_ClientPersonImage/adds',
        delete: '/mtm/BAD_ClientPersonImage/deletes'
      },
      total: 0,
      searchForm: {
        personID: null,
        maxResultCount: 50,
        skipCount: 0
      }
    }
  },
  created () {
    this.searchForm.personID = this.form.id
    this.get()
  },
  methods: {
    get () {
      this.$api.ActionRequest(this.api.get, this.searchForm).then(result => {
        this.fileList = result.items
        this.total = result.totalCount
      })
    },
    handleRemove (file) {
      this.$api.ActionRequest(this.api.delete, [file]).then(result => {
        this.get()
      })
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleDownload (file) {
      console.log(file)
    },
    onsuccess (response, file, fileList) {
      this.get()
    },
    handleSizeChange (val) {

    },
    handleCurrentChange (val) {
      this.searchForm.skipCount = this.searchForm.maxResultCount * (val - 1)
      this.get()
    }
  }
}
</script>

<style lang="scss">
.clientpersonimage {
  height: 100%;
  width: 100%;
  padding: 10px;
}
</style>
