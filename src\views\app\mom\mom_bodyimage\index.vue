<template>
  <d2-container class="bodimage">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomBodyimageMasterTable' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-colgroup title="特体">
        <vxe-table-column field="bodyListCode" title="特体编码" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="bodyListName" title="特体名称" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="bodyCode" title="特体明细编码" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="bodyName" title="特体明细名称" sortable width="100px"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="特体1">
        <vxe-table-column field="bodyCode1" title="名称" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="bodyName1" title="名称" sortable width="100px"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-column field="imageSeq" title="顺序" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="imageUrl" title="图片" sortable width="100px">
        <template v-slot="{ row }">
          <el-popover placement="right-end" width="800" trigger="hover">
            <el-image :src="row.imageUrl" fit="scale-down">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.imageUrl" fit="scale-down" slot="reference" style="height:36px;">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>

        </template>
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="特体" field="bodyID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.bodyID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod2" clearable>
              <el-option v-for="item in BodyComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="特体1" field="bodyID1" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.bodyID1" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod3" clearable>
              <el-option v-for="item in BodyComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">

              </el-option>
            </el-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="图片" field="modelImageID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelImageID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod1" clearable @change="selectchange">
              <el-option v-for="item in ModelImageComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <el-tooltip class="item" effect="dark" :content=" item.label" placement="top-start">
                  <span style="float: left ;overflow:hidden;width:200px; text-overflow: ellipsis;    white-space: nowrap;   word-break:keep-all;" class="modelimageselect">{{ item.label }}</span>
                </el-tooltip>

                <span style="float: right; color: #8492a6; font-size: 13px">
                  <el-image :src="item.text" fit="fill" style="width: 100px; height: 100px">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片顺序" field="imageSeq" span="12" :item-render="{}"> <vxe-input v-model="selectRow.imageSeq" placeholder="图片顺序" type="number" clearable>
          </vxe-input>
        </vxe-form-item>
        <vxe-form-item title="图片" span="24" :item-render="{}"> <template #default>
            <el-image style="width: 200px; height: 200px" :src="imageurl" fit="fill">
            </el-image>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep, find } from 'lodash'
export default {
  name: 'mom_bodyimage',
  mixins: [masterTableMixins],
  components: {
  },
  watch: {
    'selectRow.modelImageID': function (val, old) {
      if (val === null || val === '') {
        this.imageurl = null
      }
    }
  },
  data () {
    return {
      formData: {
        imageSeq: null,
        bodyID: null,
        bodyID1: null,
        modelImageID: null
      },
      imageurl: null,
      formRules: {
        bodyID: [{ required: true, message: '请选择特体' }],
        modelImageID: [{ required: true, message: '请选择图片' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_bodyimage/get',
        add: '/mtm/mom_bodyimage/adds',
        edit: '/mtm/mom_bodyimage/updates',
        delete: '/mtm/mom_bodyimage/deletes',
        ModelImageComboStoreByQuery: '/mtm/comboQuery/ModelImageComboStoreByQuery',
        BodyComboStoreByQuery: '/mtm/comboQuery/BodyComboStoreByQuery'
      },
      footerCompanyInfo: false,
      ModelImageComboStoreByQuery: [],
      BodyComboStoreByQuery: [],
      BodyComboStoreByQuery1: []
    }
  },
  async created () {
    this.remoteMethod1()
    this.remoteMethod2()
    this.remoteMethod3()
    await this.getCombStore()

    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    selectchange (row) {
      var itme = find(this.ModelImageComboStoreByQuery, function (i) { return i.value === row })
      if (itme) {
        this.imageurl = itme.text
      } else { this.imageurl = null }
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { text: query }).then(result => {
        this.BodyComboStoreByQuery = result
      })
    },
    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { text: query }).then(result => {
        this.BodyComboStoreByQuery1 = result
      })
    },
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
        this.showEdit = true
      })
      await this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { gid: row.bodyID }).then(result => {
        this.BodyComboStoreByQuery = result
        this.showEdit = true
      })
      await this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { gid: row.bodyID1 }).then(result => {
        this.imageurl = row.imageUrl
        this.BodyComboStoreByQuery1 = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row, attributenames = [], code = false, codeName = false) {
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { gid: row.bodyID }).then(result => {
        this.BodyComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { gid: row.bodyID1 }).then(result => {
        this.imageurl = row.imageUrl
        this.BodyComboStoreByQuery1 = result
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (!code && this.$utils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (!codeName && this.$utils.has(this.selectRow, 'codeName')) {
        this.selectRow.codeName = null
      }
      if (attributenames.length > 1) {
        attributenames.forEach(name => {
          this.selectRow[name] = null
        })
      }
      this.showEdit = true
    }
  }
}
</script>

<style lang="scss" >
.bodimage {
  .modelimageselect {
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
