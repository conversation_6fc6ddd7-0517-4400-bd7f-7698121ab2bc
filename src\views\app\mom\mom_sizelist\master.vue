<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomSizelist_master_table' ref='master_table' :loading="tableLoading" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <!-- <vxe-table-column field="classText" title="系统归类" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sizeElemListText" title="规格元素" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="code" title="规格单编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="规格单名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="issueDate" title="日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="relax" title="放松量" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable></vxe-table-column>
      <vxe-table-column title="操作" width="120" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete||menuAction.allowAdd">
        <template v-slot="{ row }">
          <!-- <i class="fa fa-file" aria-hidden="true"></i> -->
          <vxe-button type="text" icon="fa fa-files-o" @click="deepCopyEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>

        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'MomSizelistMaster',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        issueDate: '',
        relax: '0',
        // classID: null,
        groupID: null,
        sizeElemListID: 1
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }],
        codeName: [{ required: true, message: '请输入编码名称' }],
        // classID: [{ required: true, message: '请选择分类' }],
        groupID: [{ required: true, message: '请选择类别' }],
        sizeElemListID: [{ required: true, message: '请选择规格元素' }]

      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        // { field: 'classID', title: '系统分类', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'groupID', title: '类别', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'sizeElemListID', title: '规格元素', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'relax', title: '加放量', span: 12, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$textarea' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_sizelist/get',
        add: '/mtm/mom_sizelist/adds',
        deepclone: '/mtm/mom_sizelist/deepClone',
        edit: '/mtm/mom_sizelist/updates',
        delete: '/mtm/mom_sizelist/deletes',
        GroupComboStore: '/mtm/combo/groupComboStore',
        SizeElemListComboStore: '/mtm/combo/sizeElemListComboStore'
      },
      footerCompanyInfo: false,
      GroupComboStore: [],
      SizeElemListComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.$utils.find(this.formItems, item => item.field === 'groupID').itemRender.options = this.GroupComboStore
    this.$utils.find(this.formItems, item => item.field === 'sizeElemListID').itemRender.options = this.SizeElemListComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })

      await this.$api.ActionRequest(this.api.SizeElemListComboStore).then(result => {
        this.SizeElemListComboStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
