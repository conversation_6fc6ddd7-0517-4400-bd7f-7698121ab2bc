<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="itemClassID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.itemClassID" placeholder="类型" clearable>
                  <vxe-option v-for="item in ItemClassComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadItemelemitemconfigMasterTable' ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="itemClassText" title="类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="groupText" title="分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemListName" title="款式名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemCode" title="款式明细编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemCodeName" title="款式明细名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="seq" title="顺序" sortable width="100"></vxe-table-column>

      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="100" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式明细" field="modelElemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod1">
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="类型" field="itemClassID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemClassID" filterable placeholder="请选择类型" size="mini">
              <el-option v-for="item in ItemClassComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="顺序" field="seq" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'bad_itemelemitemconfig',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        modelElemID: null,
        seq: 1,
        itemClassID: null,
        isActive: true
      },
      formRules: {
        modelElemID: [{ required: true, message: '请选择款式明细' }],
        itemClassID: [{ required: true, message: '请选在类型' }],
        seq: [{ required: true, message: '请输入顺序' }]
      },

      api: {
        get: '/mtm/bad_itemelemitemconfig/get',
        add: '/mtm/bad_itemelemitemconfig/adds',
        edit: '/mtm/bad_itemelemitemconfig/updates',
        delete: '/mtm/bad_itemelemitemconfig/deletes',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ItemClassComboStore: '/mtm/combo/ItemClassComboStore'
      },
      footerCompanyInfo: false,
      ModelElemComboStoreByQuery: [],
      ItemClassComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemClassComboStore).then(result => {
        this.ItemClassComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    // 编辑
    editEvent (row) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
