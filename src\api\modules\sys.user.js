// import { find, assign } from 'lodash'

export default ({ service, request, faker, tools }) => ({
  /**
   * @description 登录
   * @param {Object} data 登录携带的信息
   */
  SYS_USER_LOGIN(data = {}) {
    return request({
      url: 'sso/user/login',
      method: 'post',
      data,
      baseurl: window.VUE_APP_USER_API || process.env.VUE_APP_USER_API
    })
  },
  SYS_USER_TOKEN(data = {}) {
    return request({
      url: '/connect/token',
      method: 'post',
      data,
      baseurl: (window.VUE_APP_USER_API || process.env.VUE_APP_USER_API).replace('api/', ''),
      ContentType: 'application/x-www-form-urlencoded'
    })
  },
  SYS_USER_GET_BYID(data) {
    return request({
      url: '/sso/user/getuserbyid',
      method: 'get',
      params: data,
      baseurl: window.VUE_APP_USER_API || process.env.VUE_APP_USER_API
    })
  },
  SYS_USER_UPDATES(data) {
    return request({
      url: '/sso/user/updates',
      method: 'post',
      data,
      baseurl: window.VUE_APP_USER_API || process.env.VUE_APP_USER_API
    })
  },
  SYS_USER_MENUS(data) {
    return request({
      url: '/sso/menu/getMeun',
      method: 'GET',
      params: data,
      baseurl: window.VUE_APP_USER_API || process.env.VUE_APP_USER_API
    })
  }
})
