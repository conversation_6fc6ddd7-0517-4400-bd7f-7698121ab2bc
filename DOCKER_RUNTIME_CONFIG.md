# Docker 运行时配置解决方案

## 🎯 解决的问题

Vue.js 项目在构建时会将 `process.env.VUE_APP_*` 环境变量编译到代码中，导致 Docker 容器无法在运行时动态修改这些配置。

## ✅ 解决方案

通过修改源代码，使其优先使用 `window.VUE_APP_*` 变量，然后在 Docker 容器启动时通过脚本设置这些变量。

## 🔧 核心修改

### 1. 修改的文件

- `src/api/service.js` - 主要 API 配置
- `src/api/modules/sys.user.js` - 用户 API 配置  
- `src/api/modules/base/index.js` - 基础 API 配置
- `src/libs/util.js` - 应用标题配置
- `src/views/mes/prd/prd_printlabel/index.vue` - MES API 配置

### 2. 修改模式

将所有的：
```javascript
process.env.VUE_APP_API
```

替换为：
```javascript
window.VUE_APP_API || process.env.VUE_APP_API
```

### 3. Docker 配置

- `startup.sh` - 容器启动脚本，设置运行时配置
- `public/index.html` - 添加配置占位符
- `Dockerfile` - 使用启动脚本

## 🚀 使用方法

### 开发环境
正常使用，配置从 `.env.development` 文件读取。

### 生产环境
```bash
docker run -d --name my-app -p 8080:80 \
  -e "VUE_APP_USER_API=https://your-sso.com/api/" \
  -e "VUE_APP_API=https://your-api.com/api/" \
  -e "VUE_APP_MESAPI=https://your-mes.com/api/" \
  -e "VUE_APP_TITLE=Your App Title" \
  your-image:tag
```

## 🧪 测试

使用 `docker-test/` 目录中的脚本进行测试：

```bash
cd docker-test
quick-test.bat        # 快速测试
verify-config.bat     # 验证配置
cleanup.bat          # 清理资源
```

## ✨ 优势

1. **一次构建，到处运行** - 同一个镜像可用于不同环境
2. **动态配置** - 无需重新构建即可修改 API 地址
3. **向后兼容** - 不影响现有的开发流程
4. **最小侵入** - 只修改必要的代码

## 🔍 验证方法

在浏览器控制台运行：
```javascript
console.log("配置检查:", {
  VUE_APP_API: window.VUE_APP_API,
  VUE_APP_USER_API: window.VUE_APP_USER_API,
  VUE_APP_MESAPI: window.VUE_APP_MESAPI,
  VUE_APP_TITLE: window.VUE_APP_TITLE,
});
```

如果看到自定义的配置值，说明运行时配置生效了。
