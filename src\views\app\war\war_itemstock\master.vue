<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->
          <vxe-button @click="exportSelectEvent({name:'物料仓库导出'})" v-if="menuAction.allowPrint" content="导出选中">
            <template #dropdowns>
              <vxe-button @click="exportDBSelectEvent" v-if="menuAction.allowPrint" type="text" content="(大宝)导出选中"></vxe-button>
            </template>
          </vxe-button>
          <vxe-button @click="sorderBomNotEvent" v-if="menuAction.allowEdit" content="Bom不生成">
            <template #dropdowns>
              <vxe-button @click="sorderBomShowEvent" v-if="menuAction.allowPrint" type="text" content="Bom不显示"></vxe-button>
              <vxe-button @click="sorderBomNotEvent({b:null})" v-if="menuAction.allowPrint" type="text" content="Bom生成"></vxe-button>
              <vxe-button @click="sorderBomShowEvent({b:null})" v-if="menuAction.allowPrint" type="text" content="Bom显示"></vxe-button>
            </template>
          </vxe-button>
          <vxe-button status="warning" v-if="menuAction.allowEdit" @click="AllotEvent">调拨</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemClassID">
              <template #default="{ data }">
                <vxe-select v-model="data.itemClassID" placeholder="类别" clearable>
                  <vxe-option v-for="item in ItemClassComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="businessGroupID">
              <template #default="{ data }">
                <el-select v-model="data.businessGroupID" placeholder="业务类型" filterable clearable size="mini">
                  <el-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="technologyGroupID">
              <template #default="{ data }">
                <el-select v-model="data.technologyGroupID" placeholder="工艺属性" filterable clearable size="mini">
                  <el-option v-for="item in TechnologyGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="textureGroupID">
              <template #default="{ data }">
                <el-select v-model="data.textureGroupID" placeholder="面料纹理" filterable clearable size="mini">
                  <el-option v-for="item in ItemTextureGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemGroupID">
              <template #default="{ data }">
                <el-select v-model="data.itemGroupID" placeholder="分类" filterable clearable size="mini">
                  <el-option v-for="item in ItemGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItemstockMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :export-config="{}" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox"></vxe-table-column>
      <vxe-table-column field="ownerClientName" title="归属客户" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="code" title="面料编码" sortable width="100px" cell-type="string"></vxe-table-column>
      <vxe-table-column field="supplierItemCode" title="供应商编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="supplierItemName" title="供应商名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100px" cell-type="string"></vxe-table-column>
      <!-- <vxe-table-column field="number" title="序号" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="itemClassText" title="物料类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemGroupText" title="物料分类" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="businessGroupText" title="业务归属" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="originalItemNo" cell-type="string" title="原始货号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="inventoryQty" title="库存数" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemSize" title="规格型号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="warningInventoryQty" title="警戒库存(禁止出库)" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="safetyInventoryQty" title="安全库存" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="maxInventoryQty" title="最大库存数" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="unitSellingPrice" title="销售单据" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="clientCode" title="客户编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="yearNo" title="年份" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="transparency" title="透胶/光性" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="technologyText" title="工艺属性" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="unitGroupText" title="单位分类" sortable width="100px"></vxe-table-column>

      <vxe-table-column field="textureGroupText" title=" 纹理(CM)" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="width" title="门幅单位" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemComp" title="面料成分" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="yarn" title="纱织" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="weight" title="克重" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="shrink" title="缩率" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemBrand" title="物料品牌" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sorderBomShow" title="Bom清单中不显示" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sorderBomNot" title="Bom清单中不生成" :formatter='formatBool' sortable width="100px"></vxe-table-column>

      <!-- <vxe-table-column field="retailPrice" title="零售价格" sortable width="100px"></vxe-table-column> -->

      <template v-if="showPrice">
        <vxe-table-column field="wholesalePrice" title="采购价格" sortable width="100px"></vxe-table-column>
      </template>
      <vxe-table-column field="itemStockPositionText" title="库位" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sorderOccupyQty" title="订单占用数" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="isElemShow" title="工艺单图片展示？" :formatter='formatBool' sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180px" show-overflow :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit" :disabled="!row.isExist"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete" :disabled="!row.isExist"></vxe-button>
          <vxe-button type="text" icon="fa fa-copy" v-if="menuAction.allowAdd" @click="copyRowEvent(row)"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="物料名称" field="itemID" span="12">
          <template #default="{ data }">
            <el-select v-model.trim="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod" disabled>
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户" field="clientID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword clearable :remote-method="remoteMethod4">
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="供货商" field="supplierItemID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.supplierItemID" filterable placeholder="供货商" size="mini" remote reserve-keyword clearable :remote-method="remoteMethod5">
              <el-option v-for="item in clientComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="业务归属" field="businessGroup" span="12" :item-render="{name: '$select', options: BusinessGroupComboStore}"></vxe-form-item>
        <!-- <vxe-form-item title="零售价格" field="retailPrice" span="12" :item-render="{name: '$input',  props: { type: 'float'}}"></vxe-form-item> -->
        <vxe-form-item v-if="showPrice" title="采购价格" field="wholesalePrice" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <!-- <vxe-form-item title="有效库存数" field="inventoryQty" span="12" :item-render="{name: '$input',  props: { type: 'float'}}"></vxe-form-item> -->
        <vxe-form-item title="警戒库存(禁止出库)" field="warningInventoryQty" title-overflow span="12" :item-render="{name: '$input',   props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="安全库存" field="safetyInventoryQty" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="最大库存" field="maxInventoryQty" span="12" :item-render="{name: '$input', props: { type: 'float'}  }"></vxe-form-item>
        <vxe-form-item title="库位" field="itemStockPositionID" span="12" :item-render="{name: '$select', options: ItemStockPositionComboStore,props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="订单占用数" field="sorderOccupyQty" span="8" :item-render="{name: 'input',   attrs:{disabled:true},}"></vxe-form-item>
        <vxe-form-item title="Bom清单不显示" field="sorderBomShow" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="Bom清单不生成" field="sorderBomNot" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="DBExportTableShow" :title="'大宝数据导出'" width="70%" height="60%" resize destroy-on-close :show="ExportTableShow">
      <db-export-table v-if="DBExportTableShow" :selectTable="DBSelectDataTable" />
    </vxe-modal>
    <vxe-modal v-model="AllotShow" :title="'调拨'" width="70%" height="60%" resize destroy-on-close show-zoom>
      <d2-container>
        <template slot="header">
          <vxe-toolbar perfect custom>
            <template v-slot:tools>
              批量切换业务属性：
              <vxe-select v-model="changeBusinessGroup" @change="changeBusinessGroupEvent">
                <vxe-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
              </vxe-select>
              <vxe-button status="success" @click="CreateAllotEvent"> 调拨</vxe-button>
            </template>
          </vxe-toolbar>
        </template>
        <vxe-table id='WarAllotItemstockMasterTable' ref='allot_master_table' height="auto" :keep-source="true" :data="AllotTableData" :edit-config="{trigger: 'click', mode: 'cell', showStatus: true}" :custom-config="{storage: true}">
          <vxe-table-column field="businessGroupText" title="(旧)业务归属" sortable width="150px"></vxe-table-column>
          <vxe-table-column field="businessGroup1" title="(目标)业务归属" sortable width="130px" :edit-render="{}">
            <template #default="{ row }">
              <!-- <span>{{ formatBusinessGroup(row.businessGroup1) }}</span> -->
              <vxe-select v-model="row.businessGroup1">
                <vxe-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
              </vxe-select>
            </template>
            <template #edit="{ row }">
              <vxe-select v-model="row.businessGroup1">
                <vxe-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
              </vxe-select>
            </template>
          </vxe-table-column>
          <vxe-table-column field="qty" title="调拨数量" sortable width="130px" :edit-render="{}">
            <template #default="{ row }">
              <vxe-input v-model="row.qty" type="float" min="0.1" :max="row.inventoryQty" placeholder="调拨数量"></vxe-input>
            </template>
            <template #edit="{ row }">
              <vxe-input v-model="row.qty" type="float" min="0.1" :max="row.inventoryQty" placeholder="调拨数量"></vxe-input>
            </template>
          </vxe-table-column>
          <vxe-table-column field="ownerClientName" title="归属客户" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="code" title="面料编码" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="supplierItemName" title="供应商名称" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="codeName" title="名称" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="itemClassText" title="物料类别" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="itemGroupText" title="物料分类" sortable width="100px"></vxe-table-column>

          <vxe-table-column field="originalItemNo" title="原始货号" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="inventoryQty" title="库存数" sortable width="100px"></vxe-table-column>
          <!-- <vxe-table-column field="itemSize" title="规格型号" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="warningInventoryQty" title="警戒库存(禁止出库)" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="technologyText" title="工艺属性" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="unitGroupText" title="单位分类" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="textureGroupText" title=" 纹理(CM)" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="width" title="门幅单位" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="itemComp" title="面料成分" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="yarn" title="纱织" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="weight" title="克重" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="shrink" title="纬向缩率" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="lenShrink" title="经向缩率" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="itemBrand" title="物料品牌" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="wholesalePrice" title="采购价格" sortable width="100px"></vxe-table-column> -->
          <vxe-table-column field="itemStockPositionText" title="库位" sortable width="100px"></vxe-table-column>
          <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
          <vxe-table-column title="操作" width="80px" show-overflow :fixed='tableOptFixed'>
            <template v-slot="{ row }">
              <vxe-button type="text" icon="fa fa-trash-o" @click="AllotRemoveEvent(row)"></vxe-button>
            </template>
          </vxe-table-column>
        </vxe-table>
      </d2-container>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import DbExportTable from './compontents/DBExportTable.vue'
import { cloneDeep } from 'lodash'
import XEUtils from 'xe-utils'
export default {
  name: 'war_itemstockmaster',
  mixins: [masterTableMixins],
  props: {
    showPrice: {
      type: Boolean,
      default: false
    }
  },
  components: {
    DbExportTable
  },
  data () {
    return {
      AllotShow: false,
      changeBusinessGroup: null,
      AllotTableData: [],
      formData: {
        itemID: null,
        retailPrice: null,
        wholesalePrice: null,
        inventoryQty: null,
        safetyInventoryQty: null,
        maxInventoryQty: null,
        itemStockPositionID: null,
        sorderOccupyQty: null,
        remark: null,
        isActive: true,
        sorderBomShow: false,
        sorderBomNot: false, // 不生产物料清单
        supplierItemID: null,
        warningInventoryQty: 0
      },
      DBExportTableShow: false,
      DBSelectDataTable: [],
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeNambusinessGroupe: [{ required: true, message: '请选择业务属性' }]
      },
      api: {
        get: '/mtm/war_itemstock/get',
        CreasteAllot: '/mtm/war_itemstock/CreasteAllot',
        add: '/mtm/war_itemstock/adds',
        edit: '/mtm/war_itemstock/updates',
        delete: '/mtm/war_itemstock/deletes',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore',
        ItemTextureGroupComboStore: '/mtm/combo/ItemTextureGroupComboStore',
        ItemGroupComboStore: '/mtm/combo/ItemGroupComboStore',
        TechnologyGroupComboStore: '/mtm/combo/TechnologyGroupComboStore',
        ItemClassComboStore: '/mtm/combo/ItemClassComboStore',
        ItemStockPositionComboStore: '/mtm/combo/ItemStockPositionComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      clientComboStoreByQuery1: [],
      ItemClassComboStore: [],
      BusinessGroupComboStore: [],
      ItemGroupComboStore: [],
      ItemTextureGroupComboStore: [],
      TechnologyGroupComboStore: [],
      ItemStockPositionComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemClassComboStore).then(result => {
        this.ItemClassComboStore = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemTextureGroupComboStore).then(result => {
        this.ItemTextureGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemGroupComboStore).then(result => {
        this.ItemGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.TechnologyGroupComboStore).then(result => {
        this.TechnologyGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemStockPositionComboStore).then(result => {
        this.ItemStockPositionComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
        this.clientComboStoreByQuery1 = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.supplierItemID }).then(result => {
        this.clientComboStoreByQuery1 = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 编辑
    async copyRowEvent (row) {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.selectRow.id = null
        this.showEdit = true
      })
    },
    // 调拨单
    AllotEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要调拨的物料数据', status: 'error' })
        return
      }
      this.AllotTableData = cloneDeep(list)
      this.AllotTableData = this.$utils.filter(this.AllotTableData, item => item.isExist)
      if (this.AllotTableData.length <= 0) {
        this.$XModal.message({ message: '物料仓库中不存在的数据无法调拨', status: 'error' })
        return
      }
      this.AllotShow = true
    },
    // 创建调拨单
    CreateAllotEvent () {
      this.$api.ActionRequest(this.api.CreasteAllot, this.AllotTableData).then(result => {
        this.loadData()
        this.AllotShow = false
        this.$XModal.message({ message: '创建成功清单单证管理中查看', status: 'success' })
      })
    },
    // 调拨删除
    AllotRemoveEvent (row) {
      const xTable = this.$refs.allot_master_table
      xTable.remove(row)
    },
    changeBusinessGroupEvent ({ value }) {
      this.AllotTableData.forEach(item => {
        item.businessGroup1 = value
      })
      this.$refs.allot_master_table.refreshColumn()
    },
    ExportTableShow () {
      this.DBSelectDataTable = []
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    exportDBSelectEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要导出的数据', status: 'error' })
        return
      }
      this.DBSelectDataTable = list
      this.DBExportTableShow = true
    },
    async sorderBomNotEvent ({ b = true }) {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要修改的数据', status: 'error' })
        return
      }
      var data = cloneDeep(list)
      data.forEach(element => {
        element.sorderBomNot = b
      })
      await this.updateData(data)
    },
    async updateData (rows) {
      await this.$api.ActionRequest(this.api.edit, rows).then(result => {
        this.$XModal.message({ message: '修改成功', status: 'success' })
        this.loadData()
      })
    },
    async sorderBomShowEvent ({ b = true }) {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要修改的数据', status: 'error' })
        return
      }
      var data = cloneDeep(list)
      data.forEach(element => {
        element.sorderBomShow = b
      })
      await this.updateData(data)
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    rowClassName ({ row, rowIndex, other }) {
      if (XEUtils.has(row, 'isExist') && !row.isExist) {
        return 'row-isActive-false'
      }
      if (!row.isActive) {
        return 'row-isActive-false'
      }
      if (other !== null && other !== undefined) {
        if (XEUtils.has(row, other) && !row[other]) {
          return 'row-isActive-false'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
