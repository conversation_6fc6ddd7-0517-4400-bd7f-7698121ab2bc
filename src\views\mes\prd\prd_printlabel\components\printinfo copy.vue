<template>
  <d2-container>

    <div id="myPrint" class="myPrint">
      <hr style="border:1px dashed #000" />
      <table style="width:100%">
        <tr>
          <td>
            <div style="text-align: center;font-size: 18px;font-weight: 600;">衣服/马甲</div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize ">系统编号:21033456</div>
          </td>

        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize">客户:陈先生</div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize">货号:陈先生</div>
          </td>
        </tr>
        <tr colspan="2 ">
          <td class="firsttd">
            <div class="printsize">版号:金鸳鸯定制部</div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize">
              <span class="firstspan"> 规格:48A</span> <span> 纽扣: </span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize"> 锁眼线:</div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize"> 里贡针: </div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize"> 面线里:一二三四五六 </div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize"> 套结:一二三四五六 </div>
          </td>
        </tr>

        <tr>
          <td class="firsttd">
            <div class="printsize">
              <span class="firstspan">前长:</span>
              <span>肩宽:</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize"> <span class="firstspan">后长:</span><span>左袖长:</span> </div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize"> <span class="firstspan">胸围:</span><span>右袖长:</span> </div>
          </td>

        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize"> <span class="firstspan">中腰:</span><span>袖口:</span> </div>
          </td>

        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize"> <span class="firstspan">下摆:</span><span>袖肥:</span> </div>
          </td>

        </tr>
        <tr>
          <td style="text-align: center;">
            <img id="barcode" style="width:90%" />
          </td>
        </tr>
        <tr>
          <td>
            <div style="text-align: center;"><strong>{{num}}</strong></div>
          </td>
        </tr>
      </table>
      <hr style="border:1px dashed #000" />
    </div>

    <template slot="footer">
      <vxe-button status="info" content="打印" @click="printEvent"></vxe-button>
      <!-- <vxe-button status="info" content="打印" @click="toImage"></vxe-button> -->
    </template>
  </d2-container>
</template>

<script>
import JsBarcode from 'jsbarcode'
export default {
  name: 'print',
  data () {
    return {
      num: '202103220',
      imgUrl: null
    }
  },
  created () {
    console.clear()

    // 不要在create时调用jsbarcode库，此时相关DOM还未加载。
  },
  mounted () {
    // 生成条形码
    JsBarcode(
      '#barcode',
      this.num,
      {
        displayValue: false, // 是否在条形码下方显示文字
        // format: "pharmacode",  //条形码的格式
        // lineColor: "#0aa",  //线条颜色
        //  width: 2, //线宽
        height: 60 // 条码高度
        // margin:15,
        // fontSize:20
        // displayValue: false //是否显示文字信息
      }
    )
  },
  methods: {
    printEvent () {
      const printStyle = `
              table {
                display: inline-block;
                font-size: 8x;
                -webkit-transform-origin-x: 0;
                font-weight:500;
              }
              .firstspan {
                width: 50%; display: inline-block;
                }
              .printsize {
                width: 100%;
                font-size: 8px;
                -webkit-transform: scale(0.8);
                -webkit-transform-origin-x: 0;
                padding-left: 20px;
            }
              `
      const divEl = document.getElementById('myPrint')
      this.$XPrint({
        sheetName: '打印下面区域',
        style: printStyle,
        content: divEl.innerHTML
      })
    }

  }
}
</script>

<style lang="scss">
.myPrint {
  width: 200px;
  //   border: 1px solid red;
  //   font-size: 8px;
  //   font-size: 12px;

  .printsize {
    width: 100%;
    font-size: 12px;
    -webkit-transform-origin-x: 0;
    -webkit-transform: scale(0.9);
    padding-left: 20px;
  }
  //   tr > td > div>span {
  //     border: 1px solid;
  //   }
  .firstspan {
    width: 50%;
    display: inline-block;
  }
}
</style>
