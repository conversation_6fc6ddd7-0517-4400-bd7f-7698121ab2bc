<template>
  <board-chart :data="data" :settings="settings " :type="type" class="chart2">
    <template slot="chartheader">
      <el-row>
        <el-col :span="8">
          <h2 style="margin-left: 20px;">生产计划指标</h2>
        </el-col>
        <el-col :span="16">

        </el-col>
      </el-row>
    </template>
  </board-chart>
</template>

<script>
import boardChart from '@/components/charts/chart'
export default {
  name: 'productionplanindicator', // 生产计划指标
  components: {
    boardChart
  },
  data () {
    return {
      data: {
        columns: ['label', 'finishCount', 'planCount', 'yesterdayUnFinishedCount', 'planFinishCount', 'prcentageComplete'],
        rows: [

        ]
      },
      settings: {
        labelMap: {
          label: '工段',
          finishCount: '今日已完成数量',
          planCount: '今日计划完成数量',
          yesterdayUnFinishedCount: '昨日未完成数',
          planFinishCount: '今日预计完成数',
          prcentageComplete: '完成率'
        },
        label: {
          normal: {
            show: true,
            position: 'top'
          }
        }
      },
      type: 'histogram', // ['line', 'histogram', 'pie', 'bar']
      api: {
        get: '/mes/boardChart/ProductionPlanIndicator',
        GroupComboStore: '/mtm/combo/groupComboStore'
      },
      groupIDs: [],
      GroupComboStore: []
    }
  },
  mounted () {
    this.timer = setInterval(this.get, 1000 * 60)// 毫秒
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  async created () {
    await this.getCombStore()
    this.get()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
        this.GroupComboStore.forEach(item => {
          this.groupIDs.push(item.value)
        })
      })
    },
    async get () {
      await this.$api.ActionRequest(this.api.get, { groupIDs: this.groupIDs }).then(result => {
        this.data.rows = result
      })
    },
    async change () {
      await this.get()
    }
  }
}
</script>

<style lang="scss" scope>
.chart2 {
  height: 100%;
}
</style>
