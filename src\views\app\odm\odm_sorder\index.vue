<template>
  <d2-container type="ghost" v-loading="mainlaoding" element-loading-text="订单数据量大,正在处理,请稍后....." element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
    <div v-if="activeItem==='master'" class="panel-search" flex="dir:top">
      <div class="panel-search__input-group" flex-box="0" flex="dir:top main:center cross:center">
        <el-select v-model="clientID" style="width:30%;padding-top: 10px;" filterable remote reserve-keyword suffix-icon="fa fa-search" placeholder="请输入客户名称" :remote-method="clientMethod" @change="clientChange" clearable @clear="clientClear" :disabled="info.userType==2||EditState" size="medium">
          <el-option v-for="item in ClientComboStore" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <el-divider content-position="center"><i class="fa fa-address-card-o"></i>基本信息</el-divider>
      <div class="panel-search__results-group" flex-box="1">
        <div class="panel-search__results-group-inner">
          <sorder :client="client" :sorderStore="sorderStore" ref="sorderform" :EditState="EditState" />
        </div>
      </div>
      <el-divider content-position="center">
        <i></i>
        <el-button type="primary" size="mini" @click="changeModelClick" :disabled="EditState"> 版型选择</el-button>
        <el-button type="success" size="mini" @click="selfDesignClick" :disabled="EditState">自主设计</el-button>
      </el-divider>
      <div class="panel-search__results-group" flex-box="1">
        <el-card shadow="never" v-for="(item,index) in sorderDetailModels" :key="index">
          <div class="panel-search__results-group-inner">
            <detail-model-item ref="detailModelItemRef" :EditState="EditState" :sorderDetailModel="item" :sorderDetailModels="sorderDetailModels" :sorderStore="sorderStore!=null?sorderStore:sorderFormData">
              <template slot="next" slot-scope="{data}">
                <i class="el-icon-arrow-right" @click="nextModel(data)"></i>
              </template>
              <template slot="remove" slot-scope="{data}">
                <i class="el-icon-delete " v-if="!EditState" @click="removedetailmodel(data)"></i>
              </template>
            </detail-model-item>
          </div>
        </el-card>
      </div>
      <el-divider content-position="center">
        <div class="sorderbtns">
          <el-button type="primary" size="mini" v-if="SaveShow()" @click="sordersaveEvent">保存</el-button>
          <el-button type="success" size="mini" v-if="SubimtShow()" @click="SubimtEvent(1)"> 提交</el-button>
          <el-button type="info" size="mini" v-if="LockShow()" @click="LockEvent(2)">锁定</el-button>
          <el-button type="warning" size="mini" v-if="ReocationShow()" @click="ReocationEvent(3)">撤回</el-button>
          <el-button type="info" size="mini" v-if="rejectShow()" @click="rejectEvent(4)">驳回</el-button>
          <el-button type="primary" size="mini" v-if="sorderStore.id!==null" @click="printEvent">预览</el-button>
          <!-- <el-button type="warning" size="mini" @click="goList()">跳转</el-button> -->
        </div>
      </el-divider>
    </div>
    <detail-model-index v-if="activeItem==='detail'" :activeModel="activeModel" :sorderStore="sorderStore!=null?sorderStore:sorderFormData" @prevMaster="prevMaster" />
    <vxe-modal v-model="selectModel" title="版型选择" width="60%" height="50%" resize remember>
      <vxe-grid id="selectModelModal" auto-resize border resizable height="auto" align="left" :loading="selectModelLoading" :columns="modelTableColumn" :toolbar="{slots: {buttons: 'toolbar_buttons'}}" :data="modelTableData" :seq-config="{startIndex: (modelTablePage.currentPage - 1) * modelTablePage.pageSize}" :pager-config="modelTablePage" @page-change="modelHandlePageChange" :custom-config="{storage: true}" @cell-dblclick="selectModelClick">
        <template v-slot:toolbar_buttons>
          <vxe-toolbar perfect>
            <template v-slot:tools>
              <vxe-form ref="xForm" :data="searchModel" @submit="modelSearchEvent()" @reset="resetEvent">
                <vxe-form-item field="modelBaseID">
                  <template #default="{ data }">
                    <el-select v-model="data.modelBaseID" placeholder="基本版型" clearable filterable size="mini">
                      <el-option v-for="num in ModelBaseComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                    </el-select>
                  </template>
                </vxe-form-item>
                <vxe-form-item field="groupID">
                  <template #default="{ data }">
                    <el-select v-model="data.groupID" placeholder="类别" clearable filterable size="mini">
                      <el-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                    </el-select>
                  </template>
                </vxe-form-item>
                <vxe-form-item field="genderID">
                  <template #default="{ data }">
                    <el-select v-model="data.genderID" placeholder="性别" clearable size="mini">
                      <el-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></el-option>
                    </el-select>
                  </template>
                </vxe-form-item>
                <vxe-form-item field="designNo">
                  <vxe-input v-model.trim="searchModel.designNo" placeholder="设计号" suffix-icon="fa fa-search" clearable></vxe-input>
                </vxe-form-item>
                <vxe-form-item field="text">
                  <vxe-input v-model.trim="searchModel.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
                </vxe-form-item>
                <vxe-form-item>
                  <template #default>
                    <vxe-button type="submit" status="success">查询</vxe-button>
                    <vxe-button type="reset">重置</vxe-button>
                  </template>
                </vxe-form-item>
              </vxe-form>
            </template>
          </vxe-toolbar>
        </template>
        <template v-slot:operation="{ row }">
          <template v-if="row.imagePath!==null&&row.imagePath!==''">
            <el-popover placement="right-end" :title="row.code" width="400" trigger="hover">
              <el-image style="width: 400px; height: 400px" :src="row.imagePath" fit="scale-down"></el-image>
              <div slot="reference">
                <span>{{ row.code }}</span><i v-if="row.imagePath!==null&&row.imagePath!==''" style="color:#606266" class="el-icon-picture-outline"></i>
              </div>
            </el-popover>
          </template>
          <template v-else>
            <el-tooltip :content="row.code" placement="top-start">
              <div>
                <span> {{row.code}}</span>
              </div>
            </el-tooltip>
          </template>
        </template>
      </vxe-grid>
    </vxe-modal>
    <vxe-modal v-model="selfDesignShow" title="自主设计" width="80%" height="80%" resize remember destroy-on-close>
      <self-design @selectModel="setModel" @selfDesignShowEvent="selfDesignShowEvent" :ClientID="clientID" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import sorderEditState from './sordereditstate'
import config from '@/config.js'
import DetailModelItem from './components/detailmodelitem'
import DetailModelIndex from './detialmodelIndex'
import Sorder from './sorder'
import { mapState, mapMutations, mapActions } from 'vuex'
import SelfDesign from './components/selfdesign'
import { cloneDeep } from 'lodash'
export default {
  name: 'odm_sorder',
  components: {
    DetailModelItem,
    DetailModelIndex,
    Sorder,
    SelfDesign
  },
  mixins: [sorderEditState, config],
  props: {
    sorderData: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      mtmpai: process.env.VUE_APP_API,
      sorderStore: {
        id: null,
        sorderDetailID: null,
        code: null,
        clientID: null,
        address: null,
        contact: null,
        tel: null,
        itemID: null,
        itemText: null,
        finalWidth: 0,
        finalTexture: null, // 纹理
        finalComposition: null, // 面料成分
        // finalFabricLabel: null, // 客供面料表
        finalFabric: null, // 客供面料号
        deliveryDate: null,
        clientPersonID: null,
        clientPersonText: null,
        height: null,
        HalfFitting: false,
        isUrgent: false,
        remark: null,
        statusID: 0,
        sorderDetailModels: [],
        sorderTypeID: 1,
        sorderPsersons: []
      },
      sorderDetailModels: [],
      activeModel: null,
      mainlaoding: false,
      client: null,
      activeItem: 'master',
      clientID: null,
      api: {
        get: '/mtm/odm_sorder/get',
        sorderstatechange: '/mtm/odm_sorder/SorderStateChange',
        sorderClient: '/mtm/odm_sorder/getsorderclients',
        GroupComboStore: '/mtm/combo/groupComboStore',
        getModels: '/mtm/oDM_SorderDetailModel/getModels',
        modelBaseComboStore: '/mtm/combo/modelBaseComboStore',
        modify: '/mtm/odm_sorder/modify',
        sorderDetailModelEdit: '/mtm/oDM_SorderDetailModel/Modify',
        delete: '/mtm/oDM_SorderDetailModel/Deletes'
      },
      ModelBaseComboStore: [],
      GroupComboStore: [],
      ClientComboStore: [],
      selectModel: false,
      selfDesignShow: false,
      selectModelLoading: false,
      modelTableColumn: [
        { type: 'seq', width: 50 },
        { field: 'businessSubTypeText', title: '业务类型', width: 50 },
        { field: 'modelGroupText', title: '版型系列', showHeaderOverflow: true, width: 100 },
        { field: 'groupText', title: '类别', showOverflow: true, width: 100 },
        { field: 'genderText', title: '性别', showOverflow: true, width: 100 },
        { field: 'code', title: '版型编码', showOverflow: true, width: 100, slots: { default: 'operation' } },
        { field: 'designNo', title: '设计号', showOverflow: true, width: 100 },
        { field: 'codeName', title: '款式描述', showOverflow: true, width: 100 },
        { field: 'sizeListText', title: '规格单', showOverflow: true, width: 100 },
        { field: 'sewBaseText', title: '缝份列表', showOverflow: true, width: 100 },
        { field: 'isRuleSize', title: '支持算法', showOverflow: true, width: 100, formatter: ({ row }) => { return row.isRuleSize ? '支持' : '不支持' } },
        { field: 'remark', title: '备注', showOverflow: true, width: 100 }
      ],
      modelTableData: [],
      modelTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        align: 'right',
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
        perfect: true
      },
      searchModel: {
        text: null,
        modelBaseID: null,
        groupID: null,
        genderID: null,
        currentPage: null,
        skipCount: null,
        BusinessSubType: null
      },
      sexList: [
        { label: '女', value: false },
        { label: '男', value: true }
      ]

    }
  },
  watch: {
    sorderData: {
      deep: true,
      handler: async function (newVal, oldVal) {
        if (newVal !== null) {
          await this.setData(newVal, true, this)
          // this.detailmodelEditSet()
        }
      }
    },
    clientID: {
      deep: true,
      handler: function (newVal, oldVal) {
        if (this.sorderStore && newVal !== this.sorderStore.clientID) {
          this.sorderStore.clientID = newVal
          this.sorderStore.contact = null
          this.sorderStore.clientPersonID = null
          this.sorderStore.tel = null
          this.sorderStore.address = null
        }
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ]),
    sorderFormData: function () {
      return this.$refs.sorderform.sorderForm
    }
  },
  async created () {
    this.userTypeSet()
    await this.getCombStore()
    if (this.sorderData) {
      this.sorderStore = Object.assign(this.sorderStore, cloneDeep(this.sorderData))
      this.setEditState(this.sorderStore.statusID)
    }
  },
  methods: {
    ...mapActions('d2admin/page', [
      'close'
    ]),
    ...mapMutations('d2admin/page', [
      'keepAliveRemove'
    ]),
    SaveShow () {
      if (this.info.userType === 2) {
        if (this.sorderStore.statusID !== 0 && this.sorderStore.statusID !== 22) {
          return false
        } else {
          return true
        }
      } else {
        var canReocations = [0, 20, 30]// 待定 //客服锁定 //客服驳回 //技术锁定
        return this.$utils.includes(canReocations, this.sorderStore.statusID)
      }
    },
    setActive () {
      if (this.activeItem === 'detail') {
        this.activeItem = 'master'
      }
    },
    SubimtShow () {
      if (this.info.userType === 2) {
        if (this.sorderStore.statusID !== 0 && this.sorderStore.statusID !== 22) {
          return false
        } else {
          return true
        }
      } else {
        var canReocations = [0, 20, 30]// 待定 //客服锁定 //客服驳回 //技术锁定
        return this.$utils.includes(canReocations, this.sorderStore.statusID)
      }
    },
    // 驳回显示隐藏
    rejectShow () {
      if (this.info.userType === 2) {
        return false
      }
      var canReocations = [20, 30]// 待定 //客服锁定 //客服驳回 //技术锁定
      return this.$utils.includes(canReocations, this.sorderStore.statusID)
    },
    LockShow () {
      if (this.info.userType === 2) {
        return false
      } else {
        var show = false
        switch (this.sorderStore.statusID) {
          case 1:
          case 32:
            show = this.info.userRoles.ElementExist('code', 'Customer')
            break
          case 21:
            show = this.info.userRoles.ElementExist('code', 'PatternMaker')
            break
          default:
            show = false
            break
        }
        return show
      }
    },
    // 撤回
    ReocationShow () {
      if (this.info.userType === 2) {
        if (this.sorderStore.statusID !== 1) {
          return false
        } else {
          return true
        }
      } else {
        var show = false
        switch (this.sorderStore.statusID) {
          case 21:
            show = this.info.userRoles.ElementExist('code', 'Customer')
            break
          case 31:
            show = this.info.userRoles.ElementExist('code', 'PatternMaker')
            break
          default:
            show = false
            break
        }
        return show
      }
      // if (this.info.userType !== 2 && this.sorderStore.statusID === 1) {
      //   return false
      // }
      // var canReocations = [1, 21, 31]// 已确认 //客服审核完成 //技术审核完成
      // return this.$utils.includes(canReocations, this.sorderStore.statusID)
    },
    // 打印预览
    printEvent () {
      var url = this.mtmpai.replace('/api/', '')
      window.open(`${url}/fs/print/sorder/pdf?num=${this.sorderStore.id}`, '_blank')
    },
    // 用户类型设置
    userTypeSet () {
      if (this.info.userType === 2) {
        this.sorderStore.clientID = this.info.clientID
        this.clientID = this.info.clientID
        this.sorderStore.contact = this.info.clientContact
        this.sorderStore.address = this.info.clientAddress
        this.sorderStore.tel = this.info.clientTel
      }
    },

    async setData (sorder, setsorder = false, _this = null) {
      this.clientID = sorder.clientID
      this.sorderDetailModels = cloneDeep(sorder.sorderDetailModels)
      await this.getClient(sorder.clientID)
      if (setsorder) {
        _this.sorderStore = Object.assign(_this.sorderStore, cloneDeep(sorder))
      }
    },
    async nextModel (data) {
      // 不可编辑状态 不保存数据
      var res = this.EditState ? this.EditState : await this.sordersaveEvent()
      if (res) {
        var model = this.sorderDetailModels.GetFirstElement('modelID', data.modelID)
        if (model) {
          this.activeItem = 'detail'
          this.activeModel = model
        }
      }
    },
    async prevMaster ({ sorder }) {
      var _this = this
      if (sorder !== null && sorder.id !== null) {
        await this.$api.ActionRequest(this.api.get, { Id: sorder.id }).then(async result => {
          await this.setData(result, true, _this)
          this.activeItem = 'master'
        })
      } else {
        this.activeItem = 'master'
        this.handleCleanCacheAndRefreshCurrent()
      }
    },
    removedetailmodel (detailModel) {
      this.$XModal.confirm('您确定要删除此版型吗?删除后不可恢复').then(type => {
        if (type === 'confirm') {
          if (detailModel.id && this.sorderDetailModels.ElementExist('id', detailModel.id)) {
            var deletemodel = this.sorderDetailModels.GetFirstElement('id', detailModel.id)
            this.$api.ActionRequest(this.api.delete, [deletemodel]).then(result => {
              this.sorderDetailModels.RemoveElement('groupID', detailModel.groupID)
            })
          } else {
            this.sorderDetailModels.RemoveElement('groupID', detailModel.groupID)
          }
        }
      })
    },
    clientMethod (query) {
      this.$api.ActionRequest(this.api.sorderClient, { text: query }).then(result => {
        this.ClientComboStore = result
      })
    },
    async clientChange (val) {
      console.log(val)
      if (val !== null && val !== '') {
        this.client = this.ClientComboStore.GetFirstElement('id', val)
      } else {
        await this.clientClear()
      }
      // 客户切换版型清空
      this.sorderDetailModels = []
    },
    async clientClear () {
      this.client = null
      this.clientID = null
    },
    async getCombStore () {
      await this.getClient(this.info.clientID)
      await this.$api.ActionRequest(this.api.modelBaseComboStore).then(result => {
        this.ModelBaseComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    async getClient (clientID = null) {
      var clientObj = null
      clientObj = { clientID: clientID }
      await this.$api.ActionRequest(this.api.sorderClient, clientObj).then(result => {
        this.ClientComboStore = result
      })
    },
    changeModelClick () {
      this.modelFindList()
      this.selectModel = !this.selectModel
    },
    checkModelGroup (row) {
      // 大衣不和其他版型共存
      if (row.groupID === this.GroupConfig.dayi && this.sorderDetailModels.length >= 1) {
        this.$message.error('大衣不能和其他版型共存,并且只能有一个版型')
        return true
      }
      var any = this.sorderDetailModels.filter(item => item.groupID === row.groupID)
      if (any.length > 0) {
        this.$message.error('不能添加相同版型')
        return true
      } else {
        return false
      }
    },
    selectModelClick ({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
      var b = this.checkModelGroup(row)
      if (b) {
        return
      }
      this.selectModel = !this.selectModel
      this.setModel(row)
    },
    selfDesignClick () {
      this.selfDesignShow = true
      // this.$message('click on item ' + command)
    },
    selfDesignShowEvent () {
      this.selfDesignShow = !this.selfDesignShow
    },
    setModel (row) {
      console.log(row)
      if (this.checkModelGroup(row)) {
        return
      }
      var detaolmodel = { modelID: row.id, modelText: row.code + ':' + row.codeName, isRuleSize: row.isRuleSize, groupID: row.groupID, qty: 1, isFrontBow: false, isBackUp: false, checkedModelElems: row.checkedModelElems || null }
      if (!row.isRuleSize) {
        detaolmodel.sorderSizeTypeID = '4'
      } else {
        detaolmodel.sorderSizeTypeID = '3'
      }
      this.sorderDetailModels.push(detaolmodel)
    },
    async modelHandlePageChange ({ currentPage, pageSize }) {
      this.modelTablePage.currentPage = currentPage
      this.modelTablePage.pageSize = pageSize
      await this.modelFindList()
    },
    async modelSearchEvent () {
      this.modelTablePage.currentPage = 1
      await this.modelFindList()
    },
    async modelFindList () {
      this.selectModelLoading = true
      this.searchModel.currentPage = this.modelTablePage.currentPage
      this.searchModel.skipCount = (this.modelTablePage.currentPage - 1) * this.modelTablePage.pageSize
      await this.$api.ActionRequest(this.api.getModels, this.searchModel).then(result => {
        this.modelTableData = result.items
        this.modelTablePage.total = result.totalCount
        this.selectModelLoading = false
      })
    },
    async resetEvent () {
      await this.modelFindList()
    },
    // 清空当前页缓存并刷新此页面
    async handleCleanCacheAndRefreshCurrent () {
      this.keepAliveRemove(this.$route.name)
      await this.$nextTick()
      this.$router.replace('/refresh')
    },
    async sordersaveEvent () {
      this.mainlaoding = true
      var _this = this
      var models = []
      if (this.$refs.detailModelItemRef && this.$refs.detailModelItemRef.length > 0) {
        models = this.$refs.detailModelItemRef.map(item => { return item.sorderDetailModelForm })
      }
      var sorderform = this.$refs.sorderform
      return await sorderform.$refs.sorderform.validate().then(async () => {
        var sorderRes = await this.sorderSave(sorderform.sorderForm, _this)
        this.mainlaoding = false
        if (sorderRes) {
          if (models.length > 0) {
            models.forEach(item => {
              if (item.sorderDetailID === undefined || item.sorderDetailID === null) {
                item.sorderDetailID = sorderform.sorderForm.sorderDetailID
              }
              var model = this.sorderDetailModels.GetFirstElement('modelID', item.modelID)
              if (model) {
                item = Object.assign(model, item)
              }
            })
            var detailmodelRes = await this.sorderDetailModelSave(models)
            if (detailmodelRes) {
              return true
            } else {
              return false
            }
          }
        } else {
          return sorderRes
        }
      }).catch((err) => {
        console.log(err)
        this.$notify({
          title: '错误',
          message: '必填项不能为空',
          type: 'error'
        })
        this.mainlaoding = false
        return false
      })
    },
    goList () {
      var name = this.$route.fullPath
      setTimeout(() => {
        this.$router.push({
          name: 'odm_sorderlist',
          params: {
            refresh: true
          }
        })
      }, 1500)
      setTimeout(() => {
        this.close({
          tagName: name
        })
      }, 1500)
      // this.$store.dispatch('d2admin/page/open', { name: 'odm_sorderlist', params: { refresh: true } })
      // this.$store.commit('d2admin/page/currentSet', '/app/odm/odm_sorderlist/index')
    },
    async sorderSave (sorderform, _this) {
      var data = cloneDeep(sorderform)
      delete data.sorderDetailModels
      return await this.$api.ActionRequest(this.api.modify, data).then(async res => {
        // this.$notify({
        //   title: '成功',
        //   message: '订单基础信息保存成功',
        //   type: 'success'
        // })
        _this.sorderStore = Object.assign(sorderform, res)
        // _this.sorderStore.id = res.id
        // _this.sorderStore.sorderDetailID = res.sorderDetailID
        // _this.sorderStore.code = res.code
        // this.clientID = res.clientID
        return true
      }).catch(() => {
        return false
      })
    },
    async sorderDetailModelSave (list) {
      return await this.$api.ActionRequest(this.api.sorderDetailModelEdit, list).then(async res => {
        this.$notify({
          title: '成功',
          message: '保存成功',
          type: 'success'
        })
        this.sorderDetailModels.forEach(item => {
          var m = res.GetFirstElement('modelID', item.modelID)
          if (m) {
            item = Object.assign(item, m)
          }
        })
        return true
      }).catch(() => {
        return false
      })
    },
    // 提交订单
    async SubimtEvent (actionId) {
      this.mainlaoding = true
      var b = await this.sordersaveEvent()
      if (b) {
        this.$api.ActionRequest(this.api.sorderstatechange, { actionID: actionId, SorderId: this.sorderStore.id }).then(result => {
          this.mainlaoding = false
          if (result) {
            this.$message({
              message: '订单提交成功,正在跳转到订单列表',
              type: 'success'
            })
            this.goList()
          }
        })
      } else {
        this.mainlaoding = false
      }
    },
    // 锁定订单
    LockEvent (actionId) {
      this.$api.ActionRequest(this.api.sorderstatechange, { actionID: actionId, SorderId: this.sorderStore.id }).then(result => {
        this.handleCleanCacheAndRefreshCurrent()
      })
    },
    // 撤回
    ReocationEvent (actionId) {
      this.$api.ActionRequest(this.api.sorderstatechange, { actionID: actionId, SorderId: this.sorderStore.id }).then(result => {
        this.handleCleanCacheAndRefreshCurrent()
      })
    },
    // 驳回
    rejectEvent (actionId) {
      this.$api.ActionRequest(this.api.sorderstatechange, { actionID: actionId, SorderId: this.sorderStore.id }).then(result => {
        this.$message({
          message: '订单驳回成功,正在跳转到订单列表',
          type: 'success'
        })
        this.goList()
      })
    }
  }
}
</script>

<style  lang="scss">
.odm_sorderpro {
  .prevbtn {
    position: absolute;
    top: 50%;
    z-index: 999;
  }
  .nextbtn {
    position: absolute;
    top: 50%;
    right: 0px;
    z-index: 999;
  }
  .carouselmain {
    // height: calc(100vh - 70px);
    .el-carousel__container {
      height: 100%;
    }
  }
}
</style>
