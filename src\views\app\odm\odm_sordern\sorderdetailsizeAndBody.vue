<template>
  <el-card class="box-card sizeandbody">
    <slot name="sorderdetailmodel" />
    <split-pane :default-percent='65' split="vertical">
      <template slot="paneL" class="paneL">
        <slot name="paneL" />
        <!-- <sorder-detail-size :SorderDetailModel="sorderDetailModelForm" :sorderDetailBodys="getBodyListData()" :sorderStore="sorderStore" :height='sorderStore.height' ref="sorderDetailSizeRef" /> -->
      </template>
      <template slot="paneR">
        <slot name="paneR" />
        <!-- <sorder-detail-body :SorderDetailModel="sorderDetailModel" ref="sorderDetailBodyRef" :sorderStore="sorderStore" /> -->
      </template>
    </split-pane>
  </el-card>
</template>

<script>
// import SorderDetailSize from './sorderdetailsize'
// import SorderDetailBody from './sorderdetailbody'
export default {
  name: 'OdmSorderDetailSizeAndBody',
  components: {
    // SorderDetailSize,
    // SorderDetailBody
  }
  // props: {
  //   sorderdetailbodySaveEven: {
  //     type: Function,
  //     requited: true
  //   }
  // },
  // watch: {
  //   'SorderDetailModel.modelID': {
  //     deep: true,
  //     handler: function (newVal, oldVal) {
  //       console.log(`规格特体---版型ID${newVal}`)
  //     }
  //   }
  // }

}
</script>

<style lang='scss'>
.sizeandbody {
  padding: 10px;
}
</style>
