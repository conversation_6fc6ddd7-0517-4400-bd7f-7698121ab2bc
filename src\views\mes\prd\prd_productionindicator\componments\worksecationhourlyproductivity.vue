<template>
  <board-chart :data="data" :settings="settings ">
    <template slot="chartheader">
      <el-row>
        <el-col :span="8">
          <h2 style="margin-left: 20px;">工段小时生产效率</h2>
        </el-col>
        <el-col :span="16">

        </el-col>
      </el-row>
    </template>
  </board-chart>
</template>

<script>
import boardChart from '@/components/charts/chart'
export default {
  name: 'worksecationHourlyProductivity', // 工段小时生产效率
  components: {
    boardChart
  },
  data () {
    return {
      data: {
        columns: ['label', 'hour', 'finishCount', 'meanValue'],
        rows: [

        ]
      },
      settings: {
        labelMap: {
          label: '工段',
          hour: '工作时间',
          finishCount: '今日已完成数量',
          meanValue: '平均数'
        },
        label: {
          normal: {
            show: true,
            position: 'top'
          }
        }
      },
      type: 'histogram',
      api: {
        get: '/mes/boardChart/WorkSecationHourlyProductivity',
        GroupComboStore: '/mtm/combo/groupComboStore'
      },
      groupIDs: [],
      GroupComboStore: []
    }
  },
  mounted () {
    this.timer = setInterval(this.get, 1000 * 60)// 毫秒
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  async created () {
    await this.getCombStore()
    this.get()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
        this.GroupComboStore.forEach(item => {
          this.groupIDs.push(item.value)
        })
      })
    },
    async get () {
      await this.$api.ActionRequest(this.api.get, { groupIDs: this.groupIDs }).then(result => {
        this.data.rows = result
      })
    },
    async change () {
      await this.get()
    }
  }
}
</script>

<style>
</style>
