<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <!-- <vxe-form-item field="factoryID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.factoryID" filterable placeholder="工厂" size="mini">
                  <el-option v-for="item in factoryComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item> -->
            <vxe-form-item field="text" :item-render="{}">
              <template #default="params">
                <vxe-input v-model.trim="params.data.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadProductionlintMasterTable' ref='master_table' @cell-dblclick="cellDblClick" height="auto" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="code" title="生产线编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="生产线名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupIds" title="品类" sortable width="100">
        <template v-slot="{ row }">
          {{getGroupName(row.groupIds)}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="factoryTypeText" title="属性" sortable width="100"></vxe-table-column>
      <vxe-table-column field="suitDailyCapacity" title="上衣单日产能\件" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trouserDailyCapacity" title="裤子单日产能\件" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <!-- <vxe-form-item title="绑定工厂" field="factoryID" span="12"><template #default>
            <el-select v-model="selectRow.factoryID" filterable placeholder="绑定工厂" size="mini">
              <el-option v-for="item in factoryComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item> -->
        <vxe-form-item title="绑定品类" span="24" field="groupIds" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.groupIds" multiple placeholder="请选择" size="mini" style="width:100%">
              <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="属性" span="12" field="factoryType" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.factoryType" placeholder="请选择" size="mini" style="width:100%">
              <el-option v-for="item in FactoryTypeStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="上衣单日产能\件" field="suitDailyCapacity" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="裤子单日产能\件" field="trouserDailyCapacity" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='50%' :modal-append-to-body="false">
      <detail-table :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import DetailTable from './detail'
export default {
  name: 'bad_productionseries',
  mixins: [masterTableMixins],
  components: {
    DetailTable
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        dailyCapacity: null,
        // factoryID: null,
        sort: 999,
        groupIds: [],
        factoryType: null
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        groupIds: [{ required: true, message: '请选择绑定品类' }],
        factoryType: [{ required: true, message: '请选择绑定属性' }]
      },
      api: {
        get: '/mes/bad_productionseries/get',
        add: '/mes/bad_productionseries/adds',
        edit: '/mes/bad_productionseries/updates',
        delete: '/mes/bad_productionseries/deletes',
        // factoryComboStore: '/mes/combo/factoryComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        FactoryTypeStore: '/mes/combo/FactoryTypeStore'
      },
      // factoryComboStore: [],
      GroupComboStore: [],
      FactoryTypeStore: []
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.factoryComboStore).then(result => {
      //   this.factoryComboStore = result
      // })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.FactoryTypeStore).then(result => {
        this.FactoryTypeStore = result
      })
    },
    getGroupName (ids) {
      var name = ''
      ids.forEach((item) => {
        var dto = this.GroupComboStore.GetFirstElement('value', item)
        if (dto) {
          name += dto.label + ','
        }
      })
      return name
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
