<template>
  <d2-container class="tableview">
    <section class="section">
      <el-table :data="Data" class="custom-table-2 hidden-tbody">
        <el-table-column prop="sorderNum" label="订单号"></el-table-column>
        <el-table-column prop="serialNumber" label="流水号"></el-table-column>
        <el-table-column prop="groupName" label="类别"></el-table-column>
        <el-table-column prop="workSecationName" label="工段"></el-table-column>
        <el-table-column prop="workSecationEndTime" label="工段截至时间"></el-table-column>
        <el-table-column prop="stateText" label="状态"></el-table-column>
        <el-table-column prop="isUrgent" label="是否加急"></el-table-column>
        <el-table-column prop="issueDate" label="下单时间">
          <!-- <template slot-scope="scope">
            {{formatDate(scope.row.issueDate)}}
          </template> -->
        </el-table-column>
      </el-table>
      <vueSeamless :data="Data" class="auto-scorll-table" :class-option="optionSingleHeight">
        <el-table :data="Data" class="custom-table-2 hidden-thead" :row-class-name="tableRowClassName" :highlight-current-row='false' :select-on-indeterminate='false'>
          <el-table-column prop="sorderNum" label="订单号"></el-table-column>
          <el-table-column prop="serialNumber" label="流水号"></el-table-column>
          <el-table-column prop="groupName" label="类别"></el-table-column>
          <el-table-column prop="workSecationName" label="工段"></el-table-column>
          <el-table-column prop="workSecationEndTime" label="工段截至时间" :formatter="(value)=>formatDate(value.workSecationEndTime,'yyyy-MM-dd HH:mm')"></el-table-column>
          <el-table-column prop="stateText" label="状态"></el-table-column>
          <el-table-column prop="isUrgent" label="是否加急" :formatter='(value)=>formatBool(value.isUrgent)'></el-table-column>
          <el-table-column prop="issueDate" label="下单时间" :formatter="(value)=>formatDate(value.issueDate,'yyyy-MM-dd')"></el-table-column>
        </el-table>
      </vueSeamless>
    </section>
  </d2-container>
</template>

<script>
import vueSeamless from 'vue-seamless-scroll'
export default {
  components: {
    vueSeamless
  },
  data () {
    return {
      Data: [

      ],
      api: {
        get: '/mes/boardChart/ProductionPlanSchedule'
      }
    }
  },
  computed: {
    optionSingleHeight () {
      return {
        singleHeight: 26, // 单行停顿
        waitTime: 1100 // 单行停顿时间
      }
    }
  },
  mounted () {
    this.timer = setInterval(this.get, 1000 * 60)// 毫秒
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  created () {
    this.get()
  },
  methods: {
    async get () {
      await this.$api.ActionRequest(this.api.get).then(result => {
        this.Data = result
      })
    },
    formatBool (cellValue) {
      var b = (cellValue === true ? '是' : '否')
      return b
    },
    formatDate (cellValue, format) {
      if (cellValue === null) {
        return null
      }
      var a = this.$utils.toDateString(this.formatLongDate(cellValue), format || 'yyyy-MM-dd HH:mm:ss')
      return a
    },
    formatLongDate (date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },

    tableRowClassName ({ row, rowIndex }) {
      var str = 'other-row'
      switch (row.state) {
        case 0:
          str = 'error-row' // 错误
          break
        case 1:
          str = 'other-row' // 等待
          break
        case 2:
          str = 'warning-row' // 生产
          break
        case 5:
          str = 'success-row' // 完成
          break
        default:
          str = 'other-row'
          break
      }

      return str
    }
  }
}
</script>

<style lang="scss" scope>
.tableview {
  // background-color: #1e1e2a;
  color: #fff;
  .section {
    // background-color: #1e1e2a;
    color: #fff;
  }
  .d2-container-full__body {
    overflow: hidden !important;
  }
  .el-table {
    // background-color: #1e1e2a;
    color: #fff;
    width: 100%;
  }
  table {
    background-color: #1e1e2a;
    color: #fff;
  }
  // td {
  //   background-color: #1e1e2a;
  //   color: #fff;
  // }
  th {
    background-color: #1e1e2a;
    color: #fff;
  }
  .el-table__header {
    background-color: #1e1e2a;
    color: #fff;
  }
}

.hidden-tbody.el-table {
  height: 34px;
  box-sizing: border-box;
  tbody {
    //隐藏上面表格的tbody
    display: none;
    overflow: hidden;
  }
}
.auto-scorll-table {
  height: calc(100% - 34px);
  overflow: hidden;
  width: 100%;
}
.hidden-thead.el-table {
  border-top: none; //防止边框重叠
  thead {
    //隐藏下面表格的thead
    display: none;
    overflow: hidden;
  }

  .el-table__row {
    width: 100%;
  }
  tbody {
    width: 100%;
  }
  .el-table__row .el-table__row--striped {
    background: #153555 !important;
  }
  .el-table--striped .el-table__body tr.el-table__row--striped td {
    background: #153555 !important;
  }
  .warning-row {
    background: #e6a23c;
  }

  .success-row {
    background: #67c23a;
  }
  .other-row {
    background-color: #1e1e2a;
  }
  .error-row {
    background: #f56c6c;
  }
}
</style>
