<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="warning" @click="printEvent" v-if="menuAction.allowPrint">打印付款发票</vxe-button>
          <vxe-button status="warning" @click="downloadExcelEvent" v-if="menuAction.allowPrint">下载付款发票</vxe-button>
          <vxe-button status="success" @click="customsExportOrderEvent" v-if="menuAction.allowAdd">推送平台</vxe-button>
          <vxe-button status="danger" @click="suitSupplyInvoicePushEvent" v-if="menuAction.allowAdd">SuitSupply单证推送</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="suitSupplyInvoiceIsPush">
              <template #default>
                <vxe-select v-model="searchForm.suitSupplyInvoiceIsPush" placeholder="单证推送状态" clearable>
                  <vxe-option key="2" :value="false" label="未推送"></vxe-option>
                  <vxe-option key="1" :value="true" label="已推送"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="Dates">
              <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarReportformMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="suitSupplyInvoiceIsPush" title="推送状态" :formatter='formatBool' sortable width="100"> </vxe-table-column>
      <vxe-table-column field="shipmentNumber" title="发货单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="shipmentStateText" title="发货状态" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="customsExportOrderAsync" title="推送状态" :formatter='formatBool' sortable width="100"> </vxe-table-column>
      <vxe-table-column field="customsExportOrderNo" title="出口订单编号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="contractNumber" title="合同号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="exchangeRate" title="美元汇率" sortable width="100"></vxe-table-column>
      <vxe-table-column field="transport" title="运输" sortable width="100"></vxe-table-column>
      <vxe-table-column field="makeDate" title="制单日期" sortable width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')"></vxe-table-column>
      <vxe-table-column field="marks" title="唛头" sortable width="100"></vxe-table-column>
      <vxe-table-column field="invoiceNo" title="发票号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="countInvoiceNo" title="汇总发票号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="invoiceDate" title="发票日期" sortable width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')"></vxe-table-column>
      <!-- <vxe-table-column field="shipmentDate1" title="起始装运日期" sortable width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')"></vxe-table-column>
      <vxe-table-column field="shipmentDate2" title="结束装运日期" sortable width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')"></vxe-table-column> -->
      <vxe-table-column field="dueDate" title="账单结算日期" sortable width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')"></vxe-table-column>
      <vxe-table-column field="priceTerm" title="价格条款" sortable width="100"></vxe-table-column>
      <vxe-table-column field="paymentTerm" title="支付条款" sortable width="100"></vxe-table-column>

      <vxe-table-column field="eoriNo" title="备案手册号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="processingTypeText" title="加工/监管方式" sortable width="100"></vxe-table-column>
      <vxe-table-column field="natureOfExemptionTypeText" title="征免性质" sortable width="100"></vxe-table-column>
      <vxe-table-column field="leaveCountryTypeText" title="出境关别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="leaveCustoms" title="离境口岸" sortable width="100"></vxe-table-column>
      <vxe-table-column field="transportationTypeText" title="运输方式" sortable width="100"></vxe-table-column>
      <vxe-table-column field="packingType" title="包装方式" sortable width="100"></vxe-table-column>
      <vxe-table-column field="packingUnit" title="包装单位" sortable width="100"></vxe-table-column>
      <vxe-table-column field="gotoCountry" title="运抵国" sortable width="100"></vxe-table-column>
      <vxe-table-column field="bargainType" title="成交方式" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>

      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="发货单号" field="shipmentID" span="12">
          <template #default="{ data }">
            <el-select v-model.trim="data.shipmentID" filterable placeholder="发货单号" size="mini" remote reserve-keyword :remote-method="remoteMethod">
              <el-option v-for="item in ShipmentComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="合同号" field="contractNumber" span="12" :item-render="{name: '$input'}"></vxe-form-item>
        <vxe-form-item title="美元汇率" field="exchangeRate" span="12" :item-render="{name: 'input',props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="运输" field="transport" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="制单日期" field="makeDate" span="12">
          <template #default>
            <el-date-picker v-model="selectRow.makeDate" type="date" placeholder="制单日期" size="mini">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="唛头" field="marks" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="发票号" field="invoiceNo" span="12" :item-render="{name: '$input',props:{disabled:true}}"></vxe-form-item>
        <vxe-form-item title="汇总发票号" field="countInvoiceNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="发票日期" field="invoiceDate" span="12">
          <template #default>
            <el-date-picker v-model="selectRow.invoiceDate" type="date" placeholder="发票日期" size="mini">
            </el-date-picker>
          </template>
        </vxe-form-item>

        <vxe-form-item title="账单结算日期" field="dueDate" span="12">
          <template #default>
            <el-date-picker v-model="selectRow.dueDate" type="date" placeholder="账单结算日期" size="mini">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="价格条款" field="priceTerm" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="支付条款" field="paymentTerm" span="12" :item-render="{name: 'input'}"></vxe-form-item>

        <vxe-form-item title="海关备案号" field="eoriNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="包装方式" field="packingTypeID" span="12">
          <template #default="{ data }">
            <vxe-select v-model.trim="data.packingTypeID" filterable placeholder="包装方式" size="mini" clearable @change="packingTypChange">
              <vxe-option v-for="item in ReportFormConfigTypePackingTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="包装单位" field="packingUnit" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="运抵国" field="gotoCountryID" span="12">
          <template #default="{ data }">
            <vxe-select v-model.trim="data.gotoCountryID" filterable placeholder="运抵国" size="mini" clearable @change="gotoCountryChange">
              <vxe-option v-for="item in ReportFormConfigTypeGotoCountryTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="离境口岸" field="leaveCustoms" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <vxe-form-item title="离境口岸" field="leaveCustomsID" span="12">
          <template #default="{ data }">
            <vxe-select v-model.trim="data.leaveCustomsID" filterable placeholder="离境口岸" size="mini" clearable @change="leaveCustomsChange">
              <vxe-option v-for="item in ReportFormConfigTypeLeaveCustomsTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="成交方式" field="bargainType" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="加工/监管方式" field="processingTypeID" span="12" :item-render="{name: '$select', options: ReportFormConfigTypeProcessingTypeComboStore, props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="征免性质" field="natureOfExemptionTypeID" span="12" :item-render="{name: '$select', options: ReportFormConfigTypeNatureOfExemptionTypeComboStore, props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="出境关别方式" field="leaveCountryTypeID" span="12" :item-render="{name: '$select', options: ReportFormConfigTypeLeaveCountryTypeComboStore, props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="运输方式" field="transportationTypeID" span="12" :item-render="{name: '$select', options: ReportFormConfigTypeTransportationTypeComboStore, props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="快递公司" field="logisticsCompany" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="快递单号" field="trackingNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="推送状态" field="customsExportOrderAsync" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="downloadExcelShow" :title="'收款发表表格'" width="80%" height="50%" resize destroy-on-close>
      <report-excel if="downloadExcelShow" :Ids="Ids" ref="reportExcelFef" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import ReportExcel from './excel.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_reportformMaster',
  mixins: [masterTableMixins],
  components: {
    ReportExcel
  },
  data () {
    return {
      formData: {
        shipmentID: null,
        contractNumber: null,
        invoiceNo: null,
        exchangeRate: 6.46,
        transport: 'BY EXPRESS',
        makeDate: null,
        marks: 'N/M',
        remark: '',
        isActive: true,

        invoiceDate: null,
        shipmentDate1: null,
        shipmentDate2: null,
        dueDate: null,
        priceTerm: 'EX-WORK  BY AIR',
        paymentTerm: 'T/T 60days',

        eoriNo: null,
        processingTypeID: null,
        leaveCountryTypeID: null,
        transportationTypeID: null,
        packingType: '',
        packingTypeID: null,
        gotoCountry: null,
        leaveCustoms: '上海',
        leaveCustomsID: '',
        bargainType: 'FOB',
        logisticsCompany: null,
        trackingNumber: null,
        packingUnit: '',
        countInvoiceNo: '',
        gotoCountryID: null,
        customsExportOrderAsync: false,
        natureOfExemptionTypeID: null
      },
      formRules: {
        shipmentID: [{ required: true, message: '请选择发货单号' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      Ids: [],
      downloadExcelShow: false,
      api: {
        mtmpai: process.env.VUE_APP_API,
        reportfrom6: '/fs/print/reportfrom6/pdf',
        get: '/mtm/war_reportform/get',
        add: '/mtm/war_reportform/adds',
        edit: '/mtm/war_reportform/updates',
        delete: '/mtm/war_reportform/deletes',
        suitsupplyinvocepush: '/mtm/war_reportform/PushSuitsupplyInvoices',
        customsExportOrder: '/mtm/war_reportform/CustomsExportOrder',
        ShipmentComboStoreByQuery: '/mtm/comboQuery/ShipmentComboStoreByQuery',
        ReportFormConfigTypeProcessingTypeComboStore: '/mtm/combo/ReportFormConfigTypeProcessingTypeComboStore',
        ReportFormConfigTypeLeaveCountryTypeComboStore: '/mtm/combo/ReportFormConfigTypeLeaveCountryTypeComboStore',
        ReportFormConfigTypeTransportationTypeComboStore: '/mtm/combo/ReportFormConfigTypeTransportationTypeComboStore',
        ReportFormConfigTypeLeaveCustomsTypeComboStore: '/mtm/combo/ReportFormConfigTypeLeaveCustomsTypeComboStore',
        ReportFormConfigTypeGotoCountryTypeComboStore: '/mtm/combo/ReportFormConfigTypeGotoCountryTypeComboStore',
        ReportFormConfigTypePackingTypeComboStore: '/mtm/combo/ReportFormConfigTypePackingTypeComboStore', // 包装方式
        ReportFormConfigTypeNatureOfExemptionTypeComboStore: '/mtm/combo/ReportFormConfigTypeNatureOfExemptionTypeComboStore'// 征免方式
      },
      ShipmentComboStoreByQuery: [],
      ReportFormConfigTypeProcessingTypeComboStore: [],
      ReportFormConfigTypeLeaveCountryTypeComboStore: [],
      ReportFormConfigTypeTransportationTypeComboStore: [],
      ReportFormConfigTypeLeaveCustomsTypeComboStore: [],
      ReportFormConfigTypeGotoCountryTypeComboStore: [],
      ReportFormConfigTypeNatureOfExemptionTypeComboStore: [],
      ReportFormConfigTypePackingTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ShipmentComboStoreByQuery).then(result => {
        this.ShipmentComboStoreByQuery = result
      })
      this.$api.ActionRequest(this.api.ReportFormConfigTypeProcessingTypeComboStore).then(result => {
        this.ReportFormConfigTypeProcessingTypeComboStore = result
      })
      this.$api.ActionRequest(this.api.ReportFormConfigTypeLeaveCountryTypeComboStore).then(result => {
        this.ReportFormConfigTypeLeaveCountryTypeComboStore = result
      })
      this.$api.ActionRequest(this.api.ReportFormConfigTypeTransportationTypeComboStore).then(result => {
        this.ReportFormConfigTypeTransportationTypeComboStore = result
      })
      this.$api.ActionRequest(this.api.ReportFormConfigTypeLeaveCustomsTypeComboStore).then(result => {
        this.ReportFormConfigTypeLeaveCustomsTypeComboStore = result
      })
      this.$api.ActionRequest(this.api.ReportFormConfigTypeGotoCountryTypeComboStore).then(result => {
        this.ReportFormConfigTypeGotoCountryTypeComboStore = result
      })
      this.$api.ActionRequest(this.api.ReportFormConfigTypeNatureOfExemptionTypeComboStore).then(result => {
        this.ReportFormConfigTypeNatureOfExemptionTypeComboStore = result
      })
      this.$api.ActionRequest(this.api.ReportFormConfigTypePackingTypeComboStore).then(result => {
        this.ReportFormConfigTypePackingTypeComboStore = result
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ShipmentComboStoreByQuery, { text: query }).then(result => {
        this.ShipmentComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      // this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
      //   this.ModelElemComboStoreByQuery = result
      // })
      await this.$api.ActionRequest(this.api.ShipmentComboStoreByQuery, { gid: row.shipmentID }).then(result => {
        this.ShipmentComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    leaveCustomsChange ({ value }) {
      var aa = this.ReportFormConfigTypeLeaveCustomsTypeComboStore.GetFirstElement('value', value)
      if (aa) {
        // console.log(aa)
        this.selectRow.leaveCustoms = aa.codeName
      }
    },
    gotoCountryChange ({ value }) {
      var aa = this.ReportFormConfigTypeGotoCountryTypeComboStore.GetFirstElement('value', value)
      if (aa) {
        // console.log(aa)
        this.selectRow.gotoCountry = aa.codeName
      }
    },
    packingTypChange ({ value }) {
      var aa = this.ReportFormConfigTypePackingTypeComboStore.GetFirstElement('value', value)
      if (aa) {
        // console.log(aa)
        this.selectRow.packingType = aa.codeName
        this.selectRow.packingUnit = aa.code
      }
    },
    async customsExportOrderEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$message({ type: 'error', message: '请勾选要推送的数据' })
        return
      }
      if (rows.length > 20) {
        this.$message({ type: 'error', message: '一次性最多推送20条数据' })
        return
      }
      await this.$api.ActionRequest(this.api.customsExportOrder, rows).then(result => {
        console.log(result)
        if (result.success) {
          this.$message({
            dangerouslyUseHTMLString: true,
            showClose: true,
            type: 'success',
            message: result.message,
            center: true,
            duration: 1000 * 60 * 1,
            customClass: 'importrResult'
          })
        } else {
          this.$message({
            dangerouslyUseHTMLString: true,
            showClose: true,
            type: 'error',
            message: result.message || '推送成功',
            center: true,
            duration: 1000 * 60 * 1,
            customClass: 'importrResult'
          })
        }
        // this.$message({ type: 'success', message: "推送成功" });
        this.loadData()
      })
    },
    printEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        return
      }
      var ids = rows.map(row => { return row.id })
      var api = this.api.mtmpai.replace('/api/', '') + this.api.reportfrom6
      this.openPostWindow1(api, ids, '_blank')
      // var str = ''
      // ids.forEach(item => {
      //   str += 'ids=' + item + '&'
      // })
      // var url = api  + '?' + str + '&.pdf'
      // window.open(`${url}`, '_blank')
    },
    /// data 数据 datas 数组 type _black新窗口打开，不写用默认的当前窗口打开
    openPostWindow (url, data, datas, targettype) { // 参数列表，怎么写都行。主要要有url。
      var Datas = []
      Datas = datas
      var tempForm = document.createElement('form')
      tempForm.setAttribute('id', 'tempForm1')
      tempForm.setAttribute('style', 'display:none')
      tempForm.setAttribute('target', targettype)
      tempForm.setAttribute('method', 'post')
      tempForm.setAttribute('action', url)
      var input1 = document.createElement('input')
      input1.setAttribute('type', 'hidden')
      // input1.setAttribute("name", "全局变量");
      // input1.setAttribute("value", 全局变量);
      tempForm.append(input1)

      for (var key in Datas) { // 传数组就用个循环
        var a = document.createElement('input')
        a.setAttribute('type', 'hidden')
        a.setAttribute('name', 'a' + key)
        a.setAttribute('value', Datas[key])
        tempForm.append(a)
        // var b = document.createElement("input");
        // b.setAttribute("type", "hidden");
        // b.setAttribute("name", "b" + key);
        // b.setAttribute("value", Datas[key].b);
        // tempForm.append(b);
        // ..........//用什么写什么
      }
      var input3 = document.createElement('input')
      input3.setAttribute('type', 'hidden')
      input3.setAttribute('name', 'data')
      input3.setAttribute('value', data)
      tempForm.append(input3)
      // tempForm.trigger("submit");
      // var body=document.getElementsByTagName("body");
      // body.append(tempForm)
      document.body.append(tempForm)
      // body.append(tempForm)
      tempForm.submit() // trigger("submit")和.submit()都可以
    },
    // 参数列表，怎么写都行。主要要有url。
    openPostWindow1 (url, datas, targetType) {
      var tempForm = document.createElement('form')
      tempForm.setAttribute('id', 'tempForm1')
      tempForm.setAttribute('style', 'display:none')
      tempForm.setAttribute('target', targetType)
      tempForm.setAttribute('method', 'post')
      tempForm.setAttribute('action', url)
      datas.forEach(item => {
        var a = document.createElement('input')
        a.setAttribute('type', 'hidden')
        a.setAttribute('name', 'iDs')
        a.setAttribute('value', item)
        tempForm.append(a)
      })
      document.body.append(tempForm)
      tempForm.submit()
    },
    downloadExcelEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        return
      }
      var ids = rows.map(row => { return row.id })
      this.downloadExcelShow = true
      this.Ids = ids
      // this.$refs.reportExcelFef.loadExcel({IDs:ids})
    },
    async suitSupplyInvoicePushEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$message({ type: 'error', message: '请勾选要推送的数据' })
        return
      }
      if (rows.length > 20) {
        this.$message({ type: 'error', message: '一次性最多推送20条数据' })
        return
      }
      this.tableLoading = true
      await this.$api.ActionRequest(this.api.suitsupplyinvocepush, rows).then(result => {
        this.$message({ type: 'success', message: '推送成功' })
        this.loadData()
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    }

  }

}
</script>

<style lang="scss" scoped>
</style>
