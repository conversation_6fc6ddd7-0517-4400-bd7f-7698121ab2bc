<template>
  <d2-container>
    <template slot="header">Page 2 header</template>
    <div>
      <el-card>
        detail
        <el-button @click="goback">返回</el-button>
      </el-card>
      <div style="height: 500px; margin: -16px;">
        <SplitPane :min-percent="20" :default-percent="30" split="vertical">
          <template slot="paneL">
            <div style="margin: 10px;background-color:red">左</div>
          </template>
          <template slot="paneR">
            <SplitPane split="horizontal">
              <template slot="paneL">
                <div style="margin: 10px;background-color: yellow">右上</div>
              </template>
              <template slot="paneR">
                <div style="margin: 10px;background-color: burlywood">右下</div>
              </template>
            </SplitPane>
          </template>
        </SplitPane>
      </div>
    </div>
    <template slot="footer">Footer</template>
  </d2-container>
</template>

<script>
import Vue from 'vue'
import SplitPane from 'vue-splitpane'
Vue.component('SplitPane', SplitPane)
export default {
  name: 'detail',
  methods: {
    goback () {
      this.$emit('nextpage', { pagename: 'master', data: {}, keepalive: true })
    }
  }
}
</script>

<style lang="scss" >
.splitter-pane-resizer {
  background-color: #dcdfe6 !important;
  background-clip: border-box !important;
}
</style>
