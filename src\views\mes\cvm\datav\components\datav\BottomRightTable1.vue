<template>
  <div class="bottom-right-table-1">
    <dv-border-box-6>
      <div class="table-name">
        <!-- <img src="./img/icon1.png" /> -->
        <h1>主要班组日度计划管理看板</h1>
      </div>

      <dv-scroll-board :config="config" ref="scrollBoard" />
    </dv-border-box-6>
  </div>
</template>

<script>
export default {
  name: 'BottomRightTable1',
  data () {
    return {
      data: [],
      api: {
        get: '/mes/boardChart/GetWeekProductionTeam'
      },
      config: {
        data: [

        ],
        waitTime: 3000,
        header: ['小组', '周计划', '完成', '延期', '占压'],
        index: true,
        columnWidth: [40, 150, 70, 70, 70, 70],
        align: ['center'],
        oddRowBGC: 'rgba(0, 44, 81, 0.8)',
        evenRowBGC: 'rgba(10, 29, 50, 0.8)'
      }
    }
  },
  mounted () {
    const { get } = this
    get()
    setInterval(get, 1000 * 60)
  },
  created () {
    this.get()
  },
  methods: {
    async get () {
      await this.$api.ActionRequest(this.api.get).then(async result => {
        this.data = result
        var arr = []
        await result.forEach(async element => {
          arr.push([
            element.label,
            element.count,
            element.finishCount,
            element.delayCount,
            element.tieUp
          ])
        })
        this.doUpdate(arr)
      })
    },
    doUpdate (rows) {
      this.$refs.scrollBoard.updateRows(rows)
    }
  }
}
</script>

<style lang="scss">
.bottom-right-table-1 {
  width: 20%;
  height: 100%;
  margin: 0 -5px;

  .border-box-content {
    padding: 20px;
    box-sizing: border-box;
  }

  .table-name {
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;

    img {
      width: 40px;
      height: 40px;
      margin-right: 5px;
    }
  }

  .dv-scroll-board {
    height: 100%;
  }
}
</style>
