<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="saveEvent" v-if="menuAction.allowAdd">保存</vxe-button>
          <vxe-button status="success" @click="checkAllEvent" v-if="menuAction.allowAdd">全选</vxe-button>
          <vxe-button @click="clearCheckbox()" status="warning">清除所有选中</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="ClientGroup" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.ClientGroup" placeholder="客户分类" clearable>
                  <vxe-option v-for="item in ClientGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item> <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table ref='master_table' id="SymUserclientDetailTable" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="isChecked" title="关联" sortable width="60px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.isChecked"></vxe-checkbox>
        </template>
      </vxe-table-column>
      <vxe-table-column field="clientCode" title="客户编码" sortable show-overflow width="100px"> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="shortName" title="简称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="tel" title="电话" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="fax" title="传真" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="mobile" title="座机" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="email" title="邮箱" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="address" title="地址" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable show-overflow width="100px"></vxe-table-column>
      <!-- <vxe-table-column title="操作" width="100px" show-overflow :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'SymUserclientDetail',
  mixins: [detailTableMixins],

  data () {
    return {
      footerCompanyInfo: false,
      formData: {},
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codename', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch ' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/sym_userclient/get',
        edit: '/mtm/sym_userclient/updates',
        ClientGroupComboStore: '/mtm/combo/clientGroupComboStore'
      },
      ClientGroupComboStore: [],
      action: {
        get: true,
        add: true,
        edit: true,
        delete: true,
        print: true
      }
    }
  },
  async created () {
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ClientGroupComboStore, { gid: this.formData.parentClientID }).then(result => {
        this.ClientGroupComboStore = result
      })
    },
    clearCheckbox () {
      this.tableData.forEach((item) => {
        item.isChecked = false
      })
    },
    checkAllEvent () {
      this.tableData.forEach((item) => {
        item.isChecked = true
      })
    },
    async saveEvent () {
      // var data = this.tableData.filter(item => { return item.isChecked })
      // if (data.length === 0) return
      await this.$api.ActionRequest(this.api.edit, this.tableData).then(result => {
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.loadData({ id: this.form.id }).then(({ data }) => {
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
