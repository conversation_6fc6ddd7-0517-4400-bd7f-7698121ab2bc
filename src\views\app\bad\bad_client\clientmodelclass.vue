<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" @click="insertEvent()" v-if="menuAction.allowEdit">新增</vxe-button> -->
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">保存</vxe-button>
          <vxe-button status="perfect" @click="selectAllEvent(true)" v-if="menuAction.allowAdd">全选</vxe-button>
          <vxe-button status="perfect" @click="selectAllEvent(false)" v-if="menuAction.allowAdd">取消全选</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="MomModelElemClientDetailTable" ref='master_table' :loading="tableLoading" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" keep-source :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, icon: 'fa fa-pencil-square-o'}"> >
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <!-- <vxe-table-column field="groupText" title="分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelText" title="版型名称" sortable width="250"></vxe-table-column> -->
      <!-- <vxe-table-column field="clientText" title="客户" sortable width="250"></vxe-table-column> -->
      <vxe-table-column field="code" title="版型系列编码" sortable width="150"></vxe-table-column>
      <vxe-table-column field="codeName" title="版型系列名称" sortable width="200"></vxe-table-column>
      <vxe-table-column field="modelCount" title="版型数量" sortable width="200"></vxe-table-column>
      <!-- <vxe-table-column field="stateText" title="状态" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="isChecked" title="选择" sortable width="100" :edit-render="{}">
        <template v-slot:edit="{row}">
          <vxe-checkbox v-model="row.isChecked" size="mini"></vxe-checkbox>
        </template>
        <template v-slot="{ row }">
          <vxe-checkbox v-model="row.isChecked" size="mini"></vxe-checkbox>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'ClientModelClass',
  mixins: [detailMixins],
  data () {
    return {
      tableLoading: false,
      api: {
        get: '/mtm/bAD_ClientModelClass/get',
        edit: '/mtm/bAD_ClientModelClass/updates'

      }

    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ ClientID: this.form.id })
    this.tableData = []
  },
  methods: {
    async getCombStore () {

    },
    async insertEvent () {
      this.tableLoading = true
      const updateRecords = this.$refs.master_table.getUpdateRecords()
      if (updateRecords.length === 0) {
        this.tableLoading = false
        return
      }
      await this.$api.ActionRequest(this.api.edit, updateRecords).then(result => {
        this.tableLoading = false
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.loadData({ ClientID: this.form.id })
      })
    },
    selectAllEvent (b) {
      this.tableData.forEach(item => {
        item.isChecked = b
      })
    }
  }
}
</script>

<style>
</style>
