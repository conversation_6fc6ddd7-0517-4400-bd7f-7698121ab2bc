<template>
  <d2-container>
    <vxe-table border id='WarItemstockdetailMasterTable' height="auto" ref='master_table' :data="tableData" :print-config="{}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column type="seq" width="50"></vxe-table-column>
      <!-- <vxe-table-column field="clientName" title="客户" sortable width="100"> </vxe-table-column> -->
      <vxe-table-column field="clientPersonName" title="顾客" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="qty" title="数量" width="100"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <vxe-button status="warning" @click="printSelectEvent()">发货清单预览</vxe-button>
        </template>
      </vxe-pager>

    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'ShipmentsList', // 发货清单
  mixins: [detailTableMixins],
  props: {
    dataRow: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      api: {
        get: '/mtm/WAR_ShipmentDetail/get',
        createpacking: '/mtm/wAR_ProductPacking/createPacking'
      },
      formData: {
        logisticsCompany: null,
        trackingNumber: null,
        remark: null,
        detailList: ''
      },
      tableData: [],
      // 打印样式
      printStyle: `
        .title {
          text-align: center;
        }
        .my-list-row {
          display: inline-block;
          width: 100%;
        }
        .my-list-col {
          float: left;
          width: 33.33%;
          height: 28px;
          line-height: 28px;
        }
        .my-list-col1 {
          float: left;
          width: 20%;
          height: 28px;
          line-height: 28px;
        }
        .my-list-col2 {
          float: left;
          width: 20%;
          height: 28px;
          line-height: 28px;
        }
        .my-list-col3 {
          float: left;
          width: 60%;
          height: 28px;
          line-height: 28px;
        }
        .my-top,
        .my-bottom {
          font-size: 12px;
        }
        .my-top {
          margin-bottom: 5px;
        }
        .my-bottom {
          margin-top: 30px;
          text-align: right;
        }
        `,
      // 打印顶部内容模板
      topHtml: `
        <h2 class="title">温州市金鸳鸯服装有限公司出库清单</h2>
        <div class="my-top">
            <div class="my-list-row">
            <div class="my-list-col">快递单号:` + this.dataRow.trackingNumber + `</div>
            <div class="my-list-col">快递公司：` + this.dataRow.logisticsCompany + `</div>

          </div>
          <div class="my-list-row">
            <div class="my-list-col">系统单号：` + this.dataRow.shipmentsNumber + `</div>
            <div class="my-list-col">发货日期：` + this.formatDate(this.dataRow.createOn) + `</div>
            <div class="my-list-col">客户：` + this.dataRow.clientName + `</div>

          </div>
          <div class="my-list-row">
            <div class="my-list-col1">收货姓名：` + this.dataRow.contact + `</div>
            <div class="my-list-col2">联系电话：` + this.dataRow.tel + `</div>
            <div class="my-list-col3">收货地址：` + this.dataRow.address + `</div>
          </div>
        </div>
        `,
      // 打印底部内容模板
      bottomHtml: `
        <div class="my-bottom">
          <div class="my-list-row">
            <div class="my-list-col"></div>
            <div class="my-list-col"></div>
            <div class="my-list-col">打印日期：` + this.$utils.toDateString(new Date()) + `</div>
          </div>
        </div>
        `
    }
  },

  components: {},
  async created () {
    // await this.loadData()
    this.loadData({ id: this.dataRow.id })
  },
  methods: {
    // async loadData() {
    //   if (this.dataRow === null || this.dataRow.id === null) {
    //     return
    //   }
    //   await this.$api.ActionRequest(this.api.get, { id: this.dataRow.id }).then(result => {
    //     this.tableData = result.items
    //   })
    // },

    printEvent () {
      this.$refs.xTable.print()
    },
    // printSelectEvent () {
    //   this.$refs.xTable.print({
    //     data: this.$refs.xTable.getCheckboxRecords()
    //   })
    // },
    formatDate (cellValue) {
      if (cellValue === null) {
        return null
      }
      return this.$utils.toDateString(this.formatLongDate(cellValue), 'yyyy-MM-dd HH:mm:ss')
    },
    formatLongDate (date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },

    printSelectEvent () {
      this.$refs.master_table.print({
        sheetName: '打印勾选行',
        style: this.printStyle,
        mode: 'selected',
        columns: [
          { type: 'seq' },
          { field: 'clientName' },
          { field: 'clientPersonName' },
          { field: 'groupText' },
          { field: 'sorderNumber' },
          { field: 'serialNumber' },
          { field: 'qty' }
        ],
        beforePrintMethod: ({ content }) => {
          // 拦截打印之前，返回自定义的 html 内容
          // return this.topHtml + content + this.bottomHtml
          return this.topHtml + content
        }
      })
    }
  }

}
</script>

<style>
</style>
