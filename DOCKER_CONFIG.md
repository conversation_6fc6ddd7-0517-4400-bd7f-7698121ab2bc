# Docker 运行时配置说明

## 概述

本项目支持通过Docker环境变量在运行时配置API地址等关键参数，无需重新构建镜像。

## 支持的配置项

- `VUE_APP_USER_API`: SSO用户API地址
- `VUE_APP_API`: MTM主API地址  
- `VUE_APP_MESAPI`: MES API地址
- `VUE_APP_TITLE`: 应用标题

## 使用方法

### 1. 基本运行（使用默认配置）

```bash
docker run -d --name my-app -p 8080:80 your-image:tag
```

### 2. 自定义配置运行

```bash
docker run -d \
  --name my-app \
  -p 8080:80 \
  -e VUE_APP_USER_API="https://your-sso-api.com/api/" \
  -e VUE_APP_API="https://your-mtm-api.com/api/" \
  -e VUE_APP_MESAPI="https://your-mes-api.com/api/" \
  -e VUE_APP_TITLE="您的应用标题" \
  your-image:tag
```

### 3. 使用配置文件

创建 `app.env` 文件：
```
VUE_APP_USER_API=https://your-sso-api.com/api/
VUE_APP_API=https://your-mtm-api.com/api/
VUE_APP_MESAPI=https://your-mes-api.com/api/
VUE_APP_TITLE=您的应用标题
```

运行容器：
```bash
docker run -d --name my-app -p 8080:80 --env-file app.env your-image:tag
```

### 4. Docker Compose 示例

```yaml
version: '3.8'
services:
  frontend:
    image: your-image:tag
    ports:
      - "8080:80"
    environment:
      - VUE_APP_USER_API=https://your-sso-api.com/api/
      - VUE_APP_API=https://your-mtm-api.com/api/
      - VUE_APP_MESAPI=https://your-mes-api.com/api/
      - VUE_APP_TITLE=您的应用标题
```

## 代码中使用运行时配置

如果需要在代码中使用这些配置，可以引入运行时配置模块：

```javascript
import runtimeConfig from '@/config/runtime'

// 使用配置
const apiUrl = runtimeConfig.VUE_APP_API
const userApiUrl = runtimeConfig.VUE_APP_USER_API
```

## 默认值

如果未设置环境变量，将使用以下默认值：
- VUE_APP_USER_API: "https://api-sso-test.kngintl.com/api/"
- VUE_APP_API: "https://api-mtm-test.kngintl.com/api/"
- VUE_APP_MESAPI: "https://api-mes-test.kngintl.com/api/"
- VUE_APP_TITLE: "C2M智尚工场"
