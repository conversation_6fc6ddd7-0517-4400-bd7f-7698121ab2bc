import { uniqueId } from 'lodash'
// import { version } from 'sortablejs'
export default {
  removeEmptyChildren (menus) {
    return menus.map(item => {
      if (item.children) {
        this.removeEmptyChildren(item.children)
      } else {
        delete item.children
      }
      item.path = item.path || uniqueId('d2-menu-empty-')
      return item
    })
  },
  getSelectNode (menus, path) {
    debugger
    const rebuildData = (arr, value) => {
      const newarr = []
      arr.forEach(element => {
        if (element.children && element.children.length) {
          rebuildData(element.children, value)
        } else {
          if (element.path === value) {
            newarr.push(element)
          }
        }
      })
      return newarr
    }
    return rebuildData(menus, path)
  }
}
