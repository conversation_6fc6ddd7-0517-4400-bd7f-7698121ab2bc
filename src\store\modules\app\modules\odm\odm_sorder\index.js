// import { ActionRequest } from '@/api/modules/base/index'
export default {
  namespaced: true,
  state: {
    pageTagName: null,
    sorder: {
      id: null, num: null
    }
  },
  actions: {
    async setSorder ({ state, dispatch }, { id, num, name }) {
      state.sorder.id = id
      state.sorder.num = num
      state.pageTagName = name
    },
    async clearSorder ({ state, dispatch }, name) {
      if (name === state.pageTagName) {
        state.pageTagName = null
        state.sorder.id = null
        state.sorder.num = null
      }
    }

  },
  mutations: {

  }
}
