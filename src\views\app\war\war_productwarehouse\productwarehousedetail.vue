<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="despatchFinished" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.despatchFinished" placeholder="是否提货" clearable>
                  <vxe-option v-for="item in boolList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="detailType" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.productWarehouseDetailType" placeholder="类别" clearable>
                  <vxe-option v-for="item in ProductWarehouseDetailTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarProductwarehousedetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientPersonName" title="顾客" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientShopCode" title="店铺" sortable width="100"></vxe-table-column>
      <vxe-table-column field="productWarehouseDetailTypeText" title="状态" sortable width="100"></vxe-table-column>
      <vxe-table-column field="despatchFinished" title="是否提货" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="logisticsCompany" title="快递公司" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipmentsNumber" title="发货单号" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <!-- <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <vxe-button status="success" @click="pickUpEvent">选中提货</vxe-button>
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { isEmpty } from 'lodash'
export default {
  name: 'war_productwarehousedetail',
  mixins: [detailTableMixins],
  props: {
    selectRowIds: {
      required: true
    },
    selectType: {
      required: true,
      default: 1
    },
    success: {
      type: Function
    }
  },
  components: {
  },
  data () {
    return {
      searchForm: {
        iDs: this.selectRowIds,
        productWarehouseDetailType: 1,
        despatchFinished: false
      },
      api: {
        get: '/mtm/war_productwarehousedetail/get',
        pickUp: '/mtm/war_productwarehousedetail/pickUp',
        pickUpUps: '/mtm/war_productwarehousedetail/pickUpUps',
        // delete: '/mtm/war_productwarehousedetail/deletes',
        // ProductWarehouseDetailTypeComboStore: '/mtm/combo/ProductWarehouseDetailTypeComboStore',
        ProductWarehouseDetailTypeComboStore: '/mtm/combo/ProductWarehouseDetailTypeComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'

      },
      clientComboStoreByQuery: [],
      ProductWarehouseDetailTypeComboStore: []
      //   ProductWarehouseDetailTypeComboStore: []
    }
  },

  async created () {
    this.loadData()
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ProductWarehouseDetailTypeComboStore).then(result => {
        this.ProductWarehouseDetailTypeComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    pickUpEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要发货的订单', status: 'error' })
        return
      }
      var url = this.api.pickUp
      if (this.selectType === 2) {
        url = this.api.pickUpUps
      }
      const loading = this.$loading({
        lock: true,
        text: '提货中，请稍后！！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.$api.ActionRequest(url, list).then(result => {
        this.$XModal.message({ message: '提货成功', status: 'success' })
        this.loadData()
        this.success()
        loading.close()
      }).catch(() => {
        loading.close()
      })
    },
    // 提交
    submitEvent (close = true) {
      if (isEmpty(this.selectRow.id) || this.selectRow.id === undefined || this.selectRow.id === null || this.selectRow.id === '00000000-0000-0000-0000-000000000000') {
        this.$api.ActionRequest(this.api.add, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '新增成功', status: 'success' })
          this.loadData()
          if (close) {
            this.showEdit = false
          } else {
            this.selectRow.count += 1
            this.selectRow.serialNumber = null
          }
        }).catch(() => {
          this.selectRow.serialNumber = null
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
