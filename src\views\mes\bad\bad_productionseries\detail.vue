<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增
          </vxe-button>
        </template>
        <!-- <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn" ></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template> -->
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadworksecationMasterTable' ref='master_table' height="auto" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <!-- <vxe-table-column field="productionSeriesCode" title="产品线系列编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="productionSeriesCodeName" title="产品线系列名称" sortable width="100"> </vxe-table-column> -->
      <vxe-table-column field="factoryCode" title="工厂编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="factoryCodeName" title="工厂名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isDefault" title="默认排产" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="factoryTypeText" title="属性" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :row-class-name="rowClassName" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="工厂" field="factoryID" span="12" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.factoryID" filterable placeholder="工厂" size="mini">
              <el-option v-for="item in factoryComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="默认排产" field="isDefault" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>

        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'bad_worksecation',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        // code: '',
        // codeName: '',
        remark: '',
        isActive: true,
        isDefault: false,
        productionSeriesID: this.form.id,
        factoryID: null,
        sort: 999
      },
      formRules: {
        productionStationID: [{ required: true, message: '请选择生产工位' }]
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
      },
      api: {
        get: '/mes/bad_productionseriesdetail/get',
        add: '/mes/bad_productionseriesdetail/adds',
        edit: '/mes/bad_productionseriesdetail/updates',
        delete: '/mes/bad_productionseriesdetail/deletes',
        factoryComboStore: '/mes/combo/factoryComboStore'
      },
      factoryComboStore: []
    }
  },
  async created () {
    this.loadData({ productionSeriesID: this.form.id })
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.factoryComboStore).then(result => {
        this.factoryComboStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
