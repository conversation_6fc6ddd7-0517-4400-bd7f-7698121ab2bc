<template>
  <d2-container class="modelelemimage">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button icon="fa fa-files-o" status="perfect" @click="clonePlusShow=!clonePlusShow" v-if="menuAction.allowAdd">批量复制</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelElemBaseID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemBaseID" filterable remote reserve-keyword placeholder="基础款式" :remote-method="ModelElemBaseComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelElemBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">

                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="ModelElemListComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" clearable size="mini">
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemTypeID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemTypeID" filterable placeholder="款式类别" clearable size="mini">
                  <el-option v-for="item in ModelElemTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="positionID">
              <template #default="{ data }">
                <el-select v-model="data.positionID" filterable placeholder="位置" clearable size="mini">
                  <el-option v-for="item in PositionComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelImageTypeID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.modelImageTypeID" placeholder="图片类别" clearable>
                  <vxe-option v-for="num in ModelImageTypeComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelelemimageMasterTable' ref='master_table' @cell-click='tableCellClick' :loading="tableLoading" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="mix" title="合成图" sortable :formatter='formatBool' width="100px"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-colgroup title="款式">
        <vxe-table-column field="modelElemTypeText" title="款式类别" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemBaseText" title="基础款式" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemListText" title="款式" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemCode" title="款式明细" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemName" title="款式明细" sortable width="100px"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="款式1">
        <vxe-table-column field="modelElemCode1" title="明细编码" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemName1" title="明细名称" sortable width="100px"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="款式2">
        <vxe-table-column field="modelElemCode2" title="明细编码" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemName2" title="明细名称" sortable width="100px"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-column field="imageSeq" title="图片顺序" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelImageTypeIDText" title="图片类型" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="position" title="图片位置" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelImage" title="图片编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="imageUrl" title="图片" sortable width="200px">
        <template v-slot="{ row }">
          <el-popover placement="right-end" width="800" trigger="hover">
            <el-image :src="row.imageUrl" fit="contain">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.imageUrl" fit="scale-down" slot="reference" style="height:36px;">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>

        </template>

      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-copy" v-if="menuAction.allowAdd" @click="copyRowEvent(row)"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式明细" field="modelElemID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="ModelElemComboStoreByQueryMethod" clearable>
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式明细1" field="modelElemID1" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelElemID1" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="ModelElemComboStoreByQueryMethod1" clearable>
              <el-option v-for="item in ModelElemComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式明细2" field="modelElemID2" span="12" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.modelElemID2" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="ModelElemComboStoreByQueryMethod2" clearable>
              <el-option v-for="item in ModelElemComboStoreByQuery2" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="图片" field="modelImageID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelImageID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="ModelImageComboStoreByQueryMethod" clearable @change="selectchange">
              <el-option v-for="item in ModelImageComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <el-tooltip class="item" effect="dark" :content=" item.label" placement="top-start">
                  <span style="float: left ;overflow:hidden;width:250px; text-overflow: ellipsis;    white-space: nowrap;   word-break:keep-all;" class="modelimageselect">{{ item.label }}</span>
                </el-tooltip>
                <span style="width:60px;">{{item.position}}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  <el-image :src="item.text" fit="fill" style="width: 100px; height: 100px">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片顺序" field="imageSeq" span="12">
          <vxe-input v-model="selectRow.imageSeq" placeholder="图片顺序" type="number" clearable>
          </vxe-input>
        </vxe-form-item>
        <vxe-form-item title="图片" span="24">
          <template #default>
            <el-image style="width: 200px; height: 200px" :src="imageurl" fit="fill">
            </el-image>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="clonePlusShow" :title="'批量复制'" width="70%" height='50%' resize destroy-on-close>
      <clone-plus v-if="clonePlusShow" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep, find } from 'lodash'
import clonePlus from './components/cloneplus.vue'
export default {
  name: 'mom_modelelemimage',
  mixins: [masterTableMixins],
  components: {
    clonePlus
  },
  watch: {
    'selectRow.modelImageID': function (val, old) {
      if (val === null || val === '') {
        this.imageurl = null
      }
    }
  },
  data() {
    return {
      formData: {
        imageSeq: null,
        modelElemID: null,
        modelElemID1: null,
        modelElemID2: null,
        modelImageID: null,
        positionID: null,
        modelElemTypeID: null,
        mix: true
      },
      clonePlusShow: false,
      imageurl: null,
      formRules: {
        modelElemID: [{ required: true, message: '请选择款式明细' }],
        modelImageID: [{ required: true, message: '请选择图片' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modelelemimage/get',
        add: '/mtm/mom_modelelemimage/adds',
        edit: '/mtm/mom_modelelemimage/updates',
        delete: '/mtm/mom_modelelemimage/deletes',
        ModelImageComboStoreByQuery: '/mtm/comboQuery/ModelImageComboStoreByQuery',
        ModelElemBaseComboStoreByQuery: '/mtm/comboQuery/ModelElemBaseComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        ModelElemTypeComboStore: '/mtm/combo/ModelElemTypeComboStore',
        PositionComboStore: '/mtm/combo/PositionComboStore',
        GroupComboStore: '/mtm/combo/GroupComboStore',
        ModelImageTypeComboStore: '/mtm/combo/ModelImageTypeComboStore',
      },
      footerCompanyInfo: false,
      PositionComboStore: [],
      GroupComboStore: [],
      ModelImageComboStoreByQuery: [],
      ModelElemBaseComboStoreByQuery: [],
      ModelImageTypeComboStore: [],
      ModelElemComboStoreByQuery: [],
      ModelElemComboStoreByQuery1: [],
      ModelElemComboStoreByQuery2: [],
      ModelElemListComboStoreByQuery: [],
      ModelElemTypeComboStore: []
    }
  },
  async created() {
    await this.getCombStore()
    this.ModelElemBaseComboStoreByQueryMethod()
    this.ModelElemListComboStoreByQueryMethod()
    this.ModelImageComboStoreByQueryMethod()
    this.ModelElemComboStoreByQueryMethod()
    this.ModelElemComboStoreByQueryMethod1()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    selectchange(row) {
      var itme = find(this.ModelImageComboStoreByQuery, function (i) { return i.value === row })
      if (itme) {
        this.imageurl = itme.text
      } else { this.imageurl = null }
    },
    async getCombStore() {
      await this.$api.ActionRequest(this.api.PositionComboStore).then(result => {
        this.PositionComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemTypeComboStore).then(result => {
        this.ModelElemTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelImageTypeComboStore).then(result => {
        this.ModelImageTypeComboStore = result
      })
    },
    ModelElemBaseComboStoreByQueryMethod(query) {
      this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
    },
    ModelElemListComboStoreByQueryMethod(query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    ModelImageComboStoreByQueryMethod(query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
    },
    ModelElemComboStoreByQueryMethod(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    ModelElemComboStoreByQueryMethod1(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
      })
    },
    ModelElemComboStoreByQueryMethod2(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery2 = result
      })
    },
    // 编辑
    async editEvent(row) {
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID2 }).then(result => {
        this.ModelElemComboStoreByQuery2 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
        this.selectRow = cloneDeep(row)
        // console.log(row)
        // debugger;
        this.imageurl = row.imageUrl
        this.showEdit = true
      })
      // await this.$api.ActionRequest(this.api.ModelBaseComboStoreByQuery, { gid: row.modelBaseID }).then(result => {
      //   this.ModelBaseComboStoreByQuery = result

      // })
    },
    // 复制
    async copyRowEvent(row) {
      if (this.$utils.has(row, 'id')) {
        row.id = null
      }
      if (this.$utils.has(row, 'code')) {
        row.code = null
      }
      if (this.$utils.has(row, 'codeName')) {
        row.codeName = null
      }
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID2 }).then(result => {
        this.ModelElemComboStoreByQuery2 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
        this.selectRow = cloneDeep(row)
        console.log(row)
        // debugger;
        this.imageurl = row.imageUrl
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" >
.modelelemimage {
  // .el-image__inner {
  //   width: 50%;
  //   height: auto;
  // }
}
</style>
