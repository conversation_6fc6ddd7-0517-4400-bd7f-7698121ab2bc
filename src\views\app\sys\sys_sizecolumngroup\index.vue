<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.groupID" placeholder="类别" clearable size="mini">
                  <el-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id="SysSizecolumngroupMasterTable" ref='master_table' :loading="tableLoading" :row-class-name="rowClassName" :height="TableHeight" align="left" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="sizeColumnCode" title="规格字段编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="sizeColumnName" title="规格字段名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="groupName" title="类别编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column title="量体部位">
        <vxe-table-column field="measureMin" title="最小值" sortable show-overflow width="100px"> </vxe-table-column>
        <vxe-table-column field="measureMax" title="最大值" sortable show-overflow width="100px"></vxe-table-column>
      </vxe-table-column>
      <vxe-table-column title="成衣规格">
        <vxe-table-column field="finishMin" title="最小值" sortable show-overflow width="100px"></vxe-table-column>
        <vxe-table-column field="finishMax" title="最大值" sortable show-overflow width="100px"></vxe-table-column>
      </vxe-table-column>
      <vxe-table-column title="标准修正">
        <vxe-table-column field="fix1Min" title="最小值" sortable show-overflow width="100px"></vxe-table-column>
        <vxe-table-column field="fix1Max" title="最大值" sortable show-overflow width="100px"></vxe-table-column>
      </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" show-overflow :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="规格字段" field="sizeColumnID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.sizeColumnID" filterable remote reserve-keyword placeholder="请输入规格字段" :remote-method="remoteMethod1" clearable size="small">
              <el-option v-for="item in SizeColumnComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="类别" field="groupID" span="12" :item-render="{}"> <template #default="{ data }">
            <vxe-select v-model="data.groupID" placeholder="类别" clearable>
              <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="量体最小值" field="measureMin" span="12" :item-render="{}"> <vxe-input v-model="selectRow.measureMin" placeholder="量体最小值" type="integer"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="量体最大值" field="measureMax" span="12" :item-render="{}"> <vxe-input v-model="selectRow.measureMax" placeholder="量体最大值" type="integer"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="成衣最小值" field="finishMin" span="12" :item-render="{}"> <vxe-input v-model="selectRow.finishMin" placeholder="成衣最小值" type="integer"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="成衣最大值" field="finishMax" span="12" :item-render="{}"> <vxe-input v-model="selectRow.finishMax" placeholder="成衣最大值" type="integer"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="标准修正最小值" field="fix1Min" span="12" :item-render="{}"> <vxe-input v-model="selectRow.fix1Min" placeholder="标准修正最小值" type="integer"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="标准修正大值" field="fix1Max" span="12" :item-render="{}"> <vxe-input v-model="selectRow.fix1Max" placeholder="标准修正大值" type="integer"></vxe-input>
        </vxe-form-item>

        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isActive" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <detail-table :footerCompanyInfo="footerCompanyInfo" :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'sys_sizecolumngroup',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        sizeColumnID: null,
        finishMax: null,
        finishMin: null,
        fix1Max: null,
        fix1Min: null,
        groupID: null,
        isRequired: false,
        measureMax: null,
        measureMin: null,
        remark: '',
        isActive: true
      },
      formRules: {
        sizeColumnID: [{ required: true, message: '请输入规格字段' }],
        groupID: [{ required: true, message: '请输入分类' }]
      },

      api: {
        get: '/mtm/sys_sizecolumngroup/get',
        add: '/mtm/sys_sizecolumngroup/adds',
        edit: '/mtm/sys_sizecolumngroup/updates',
        delete: '/mtm/sys_sizecolumngroup/deletes',
        SizeColumnComboStoreByQuery: '/mtm/comboQuery/SizeColumnComboStoreByQuery',
        GroupComboStore: '/mtm/combo/groupComboStore'

      },
      action: {
        get: true,
        add: true,
        edit: true,
        delete: true,
        print: true
      },
      GroupComboStore: [],
      SizeColumnComboStoreByQuery: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    this.remoteMethod1()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { text: query }).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
    },
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
        this.SizeColumnComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row, code = false, codeName = false) {
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (!code && this.$utils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (!codeName && this.$utils.has(this.selectRow, 'codeName')) {
        this.selectRow.codeName = null
      }
      this.showEdit = true
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
