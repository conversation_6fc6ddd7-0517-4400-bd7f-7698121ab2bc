<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
          <vxe-button status="warning" @click="updatePlusEvent">一键已读</vxe-button>
          <vxe-button status="warning" @click="updatePlus1Event">一键处理</vxe-button>
          <vxe-button status="success" @click="addplus">批量生成</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="workbenchGroup" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.workbenchGroup" placeholder="分类" filterable clearable size="mini">
                  <el-option v-for="item in WorkbenchGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="workbenchType" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.workbenchType" placeholder="类型" filterable clearable size="mini">
                  <el-option v-for="item in WorkbenchTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="receiverObject" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.receiverObject" placeholder="接收对象" filterable clearable size="mini">
                  <el-option v-for="item in ReceiverObjectComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="userID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.userID" filterable remote reserve-keyword placeholder="用户名" size="mini" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in UserComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='SymWorkbenchdetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="workbenchGroupText" title="分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="workbenchTypeText" title="类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="receiverObjectText" title="接收对象" sortable width="100"></vxe-table-column>
      <vxe-table-column field="userName" title="用户" sortable width="100"></vxe-table-column>
      <vxe-table-column field="message" title="内容" sortable width="100"></vxe-table-column>
      <vxe-table-column field="readReceipt" title="已读" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="toWork" title="已处理" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="parameters" title="参数" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button status="primary" v-if="!row.toWork" @click="goEvent(row)">去处理</vxe-button>
          <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">

        <vxe-form-item title="已读?" field="readReceipt" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="已处理?" field="toWork" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="参数" field="parameters" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="URL" field="url" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="消息" field="message" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'sym_workbenchdetail',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        isActive: true,
        parameters: null,
        readReceipt: null,
        toWork: null,
        message: null,
        url: null
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/sym_workbenchdetail/get',
        add: '/mtm/sym_workbenchdetail/adds',
        edit: '/mtm/sym_workbenchdetail/updates',
        delete: '/mtm/sym_workbenchdetail/deletes',
        autoCreate: '/mtm/sym_workbenchdetail/autoCreate',
        WorkbenchGroupComboStore: '/mtm/combo/WorkbenchGroupComboStore',
        WorkbenchTypeComboStore: '/mtm/combo/WorkbenchTypeComboStore',
        ReceiverObjectComboStore: '/mtm/combo/ReceiverObjectComboStore',
        UserComboStoreByQuery: '/mtm/comboQuery/UserComboStoreByQuery'
      },
      WorkbenchGroupComboStore: [],
      WorkbenchTypeComboStore: [],
      ReceiverObjectComboStore: [],
      UserComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.WorkbenchGroupComboStore).then(result => {
        this.WorkbenchGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.WorkbenchTypeComboStore).then(result => {
        this.WorkbenchTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ReceiverObjectComboStore).then(result => {
        this.ReceiverObjectComboStore = result
      })
      await this.$api.ActionRequest(this.api.UserComboStoreByQuery).then(result => {
        this.UserComboStoreByQuery = result
      })
    },
    addplus () {
      this.tableLoading = true
      this.$api.ActionRequest(this.api.autoCreate).then(result => {
        this.$notify.success({
          title: '生成',
          message: '批量生成成功'
        })
        this.loadData()
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.UserComboStoreByQuery, { text: query }).then(result => {
        this.UserComboStoreByQuery = result
      })
    },
    async updatePlusEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$notify.error({
          title: '错误',
          message: '请勾选要处理的数据'
        })
        return
      }
      var data = cloneDeep(rows)
      data = data.map(item => ({
        id: item.id,
        userID: item.userID,
        workbenchID: item.workbenchID,
        roleID: item.roleID,
        userid: item.userid,
        readReceipt: true,
        toWork: item.toWork,
        message: item.message,
        url: item.url
      }))
      await this.readUpdate(data)
    },
    async readUpdate (list) {
      await this.$api.ActionRequest(this.api.edit, list).then(result => {
        this.$message({
          message: '更新成功',
          type: 'success'
        })
        this.loadData()
      })
    },
    async updatePlus1Event () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$notify.error({
          title: '错误',
          message: '请勾选要处理的数据'
        })
        return
      }
      var data = cloneDeep(rows)
      data = data.map(item => ({
        id: item.id,
        userID: item.userID,
        workbenchID: item.workbenchID,
        roleID: item.roleID,
        userid: item.userid,
        readReceipt: item.readReceipt,
        toWork: true,
        message: item.message,
        url: item.url
      }))
      await this.readUpdate(data)
    },
    goEvent (row) {
      console.log(row)
      var params = JSON.parse(row.parameters)
      // console.log(params)
      if (params === null) {
        return
      }
      this.$message({
        message: '正在跳转请稍后.....',
        type: 'success'
      })
      var p = {
        name: row.url,
        params: JSON.parse(row.parameters)
      }
      setTimeout(() => {
        this.$router.push(p)
      }, 500)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
