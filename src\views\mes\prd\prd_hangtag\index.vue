<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">手动新增</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent1" v-if="menuAction.allowAdd">扫描增加</vxe-button>
          <vxe-button status="danger" v-if="menuAction.allowDelete" @click="clearEvent" content="清除所有数据">
            <template #dropdowns>
              <vxe-button status="danger" v-if="menuAction.allowDelete" @click="deletesEvent" content="选择删除"></vxe-button>
            </template>
          </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="HangTagType">
              <template #default="{ data }">
                <el-select v-model="data.hangTagType" filterable placeholder="分类" size="mini" clearable>
                  <el-option v-for="item in HangTagTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PrdHangtagMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="hangTagTypeText" title="分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupName" title="品类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="detailInfo" title="DetailInfo" sortable width="100"></vxe-table-column>
      <vxe-table-column field="receiptNumber" title="ReceiptNumber" sortable width="100"></vxe-table-column>
      <vxe-table-column field="firstName" title="FirstName" sortable width="100"></vxe-table-column>
      <vxe-table-column field="lastName" title="LastName" sortable width="100"></vxe-table-column>
      <vxe-table-column field="size" title="Size" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shopCode" title="ShopCode" sortable width="100"></vxe-table-column>
      <vxe-table-column field="orderNumber" title="OrderNumber" sortable width="100"></vxe-table-column>

      <vxe-table-column field="patternCode" title="PatternCode" sortable width="100"></vxe-table-column>
      <vxe-table-column field="fabricCode" title="FabricCode" sortable width="100"></vxe-table-column>
      <vxe-table-column field="tankingNO" title="TankingNO" sortable width="100"></vxe-table-column>
      <vxe-table-column field="country" title="Country" sortable width="100"></vxe-table-column>
      <vxe-table-column field="city" title="City" sortable width="100"></vxe-table-column>
      <vxe-table-column field="zip" title="Zip" sortable width="100"></vxe-table-column>
      <vxe-table-column field="email" title="Email" sortable width="100"></vxe-table-column>
      <vxe-table-column field="deliveryAddress" title="DeliveryAddress" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="订单号" field="sorderNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="流水号" field="serialNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="品类" field="groupName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="明细数据" field="detailInfo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="ReceiptNumber" field="receiptNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="OrderNumber" field="orderNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="FirstName" field="firstName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="LastName" field="lastName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Size" field="size" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="ShopCode" field="shopCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="PatternCode" field="patternCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="FabricCode" field="fabricCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="TankingNO" field="tankingNO" span="12" :item-render="{name: 'input'}"></vxe-form-item>

        <vxe-form-item title="Country" field="country" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="City" field="city" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Zip" field="zip" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="Email" field="email" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="DeliveryAddress" field="deliveryAddress" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="showEdit1" title="录入数据" width="800" :top='8' resize destroy-on-close :loading="submitLoading">
      <el-tabs v-model="activeName">
        <el-tab-pane label="扫描枪录入" name="first">
          <vxe-form :data="hangtagData" :rules="formRules1" title-align="right" title-width="100">
            <vxe-form-item title="流水号" field="serialNumber" span="24">
              <template v-slot>
                <el-input ref="numinputref" v-model.trim="hangtagData.text" @keyup.enter.native="createEvent(false)"></el-input>
              </template>
            </vxe-form-item>
            <vxe-form-item title="扫描计数" field="count" span="12">
              <template #default>
                <span><strong>{{hangtagData.count}}</strong></span>
              </template>
            </vxe-form-item>
          </vxe-form>
        </el-tab-pane>
        <el-tab-pane label="手动录入" name="second">
          <vxe-form :data="hangtagData" :rules="formRules1" title-align="right" title-width="100" @submit="createEvent">
            <vxe-form-item title="流水号" field="text" span="12" :item-render="{name: 'input'}"></vxe-form-item>
            <vxe-form-item align="center" span="24">
              <template v-slot>
                <vxe-button type="submit" status="primary">保存</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </el-tab-pane>
      </el-tabs>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'prd_hangtag',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
      },
      hangtagData: {
        text: null,
        count: 0
      },
      formRules1: {
        text: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      activeName: 'first',
      showEdit1: false,
      formData: {
        sorderNumber: null,
        orderNumber: null,
        serialNumber: null,
        groupName: null,
        detailInfo: null,
        receiptNumber: null,
        firstName: null,
        lastName: null,
        size: null,
        shopCode: null,
        patternCode: null,
        fabricCode: null,
        tankingNO: null,
        email: null,
        deliveryAddress: null,
        city: null,
        zip: null,
        country: null,
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },

      api: {
        get: '/mes/prd_hangtag/get',
        add: '/mes/prd_hangtag/adds',
        edit: '/mes/prd_hangtag/updates',
        delete: '/mes/prd_hangtag/deletes',
        HangTagTypeComboStore: '/mes/combo/HangTagTypeComboStore',
        create: '/mes/prd_hangtag/create'
      },
      HangTagTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.HangTagTypeComboStore).then(result => {
        this.HangTagTypeComboStore = result
      })
    },
    clearEvent () {
      this.tableLoading = true
      this.$api.ActionRequest(this.api.delete, this.tableData).then(result => {
        this.$XModal.message({ message: '批量删除成功', status: 'success' })
        this.loadData()
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    insertEvent1 () {
      this.showEdit1 = true
    },
    async createEvent (b = true) {
      await this.$api.ActionRequest(this.api.create, { text: this.hangtagData.text }).then(result => {
        if (!b) {
          this.hangtagData.text = null
          this.hangtagData.count += 1
        }
        this.loadData()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
