<template>

  <d2-container>
    <split-pane :min-percent='10' :default-percent='60' split="horizontal">
      <template slot="paneL">
        <split-pane :min-percent='30' :default-percent='50' split="vertical">
          <template slot="paneL">
            <workSecationSetpUp />
          </template>
          <template slot="paneR">
            <workSecationOfDayProduction />
          </template>
        </split-pane>
      </template>
      <template slot="paneR">
        <production-team-step-up />
      </template>
    </split-pane>
  </d2-container>
</template>

<script>
// import realchart from '@/components/charts/realchart'
import workSecationSetpUp from './componments/worksecationstepup'
import productionTeamStepUp from './componments/productionteamstepup'
import workSecationOfDayProduction from './componments/workSecationOfDayProduction'
export default {
  name: 'productionreport', // 报表
  components: {
    workSecationSetpUp,
    productionTeamStepUp,
    workSecationOfDayProduction
  },
  data () {
    return {

    }
  }
}
</script>

<style>
</style>
