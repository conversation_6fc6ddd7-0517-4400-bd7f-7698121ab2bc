// Vue
import Vue from 'vue'
import i18n from './i18n'
import App from './App'
// 核心插件
import d2Admin from '@/plugin/d2admin'
// store
import store from '@/store/index'

// 菜单和路由设置
import router from './router'
import { menuHeader, menuAside } from '@/menu'
import { frameInRoutes } from '@/router/routes'
import '@/plugin/VXETable/index'
// 第一个参数为字符串，指令的名称
// 第二个参数是一个对象，用于接收指令的参数值
Vue.directive('preventReClick', {
  inserted (el, binding) {
    el.addEventListener('click', () => {
      if (!el.disabled) {
        el.style.cursor = 'not-allowed'
        el.disabled = true
        setTimeout(() => {
          el.style.cursor = 'pointer'
          el.disabled = false
        }, binding.value || 500)
        // binding.value可以自行设置。如果设置了则跟着设置的时间走
        // 例如：v-preventReClick='500'
      }
    })
  }
})
// Vue.use(VXETable)
// 第一个参数为字符串，指令的名称
// 第二个参数是一个对象，用于接收指令的参数值
// Vue.directive("preventReClick",function(el,binding){
//   inserted (el, binding) {

// }
// })
Vue.use(d2Admin)
var vue = new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
  created () {
    // 处理路由 得到每一级的路由设置
    this.$store.commit('d2admin/page/init', frameInRoutes)
    // 设置顶栏菜单
    this.$store.commit('d2admin/menu/headerSet', menuHeader)
    // 设置侧边栏菜单
    this.$store.commit('d2admin/menu/asideSet', menuAside)
    // 初始化菜单搜索功能
    this.$store.commit('d2admin/search/init', menuHeader)

    // this.$store.commit('d2admin/vxetable/load')
  },
  async beforeCreate () {
    // 用户登录后从数据库加载一系列的设置
    await this.$store.dispatch('d2admin/menu/loadmenus')
    // 表单默认设置
    await this.$store.dispatch('d2admin/vxetable/load')
    // 生产工艺页面配置显示
    await this.$store.dispatch('mes/ptm/productiontechnology/index/load')
  },
  async mounted () {
    // 展示系统信息
    await this.$store.commit('d2admin/releases/versionShow')
    // 用户登录后从数据库加载一系列的设置
    await this.$store.dispatch('d2admin/account/load')
    // 获取并记录用户 UA
    await this.$store.commit('d2admin/ua/get')
    // 初始化全屏监听
    await this.$store.dispatch('d2admin/fullscreen/listen')
  }
}).$mount('#app')
export default vue
