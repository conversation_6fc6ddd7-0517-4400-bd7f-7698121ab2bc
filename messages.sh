﻿#!/bin/bash

project_name=$1
tag=$2
state=$3
webhoohrul=$4
name=$5
messagetype=$6
committext=$7

function selectMessage(){
      if [ "${messagetype}" = "dingding" ];then
            sendDingTalkNotifications
      else
            sendWeChatNotifications
      fi
}
# 相关脚本
function sendDingTalkErrorNotifications() {
      DEPLOY_STATUS='发布失败！'
      selectMessage
}
function sendDingTalkSuccessNotifications() {
      DEPLOY_STATUS='发布成功！'
      selectMessage
}

# 推送模板发送（模板拼接）
function sendDingTalkNotifications() {
   local title="[程序发布] ${name}"
   local text="### ${title}  \n #### 名称:${project_name}:${tag}\n #### 版本:${tag}\n #### 状态：${DEPLOY_STATUS}\n #### 内容：${committext}  \n"
   curl POST "$webhoohrul" -H 'Content-Type: application/json' -d "{\"msgtype\": \"markdown\",\"markdown\": {\"title\":\"$title\",\"text\": \"$text\"}}"
}
#企业微信
function sendWeChatNotifications()
{
      local text="[程序发布] ${name}  \n名称：${project_name}:${tag}\n版本：${tag}\n状态：更新中(3-5)分钟\n内容：${committext}  \n"
      echo $text
      curl  "$webhoohrul" -H 'Content-Type: application/json' -d "{\"msgtype\": \"text\",\"text\": {\"content\": \"$text\"}}"
}

function log() {
   echo "$(date):$@"
}

# 前一个命令执行状态判断是成功信息还是失败信息
if [ ${state} -eq "0" ];then
       log "[OK]"
       sendDingTalkSuccessNotifications
else
	 log "[Error]"
       sendDingTalkErrorNotifications
fi

