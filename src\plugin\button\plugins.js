
// plugins.js
import Vue from 'vue'
// 在vue上挂载一个指量 preventReClick
// vue防止按钮重复点击
const preventReClick = Vue.directive('preventReClick', {
  inserted: function (el, binding) {
    el.addEventListener('click', () => {
      console.log(el.disabled)
      if (!el.disabled) {
        el.style.cursor = 'not-allowed'
        el.disabled = true
        setTimeout(() => {
          el.style.cursor = 'pointer'
          el.disabled = false
        }, binding.value || 1000)
        // binding.value可以自行设置。如果设置了则跟着设置的时间走
        // 例如：v-preventReClick='500'
      }
    })
  }
})
export { preventReClick }
