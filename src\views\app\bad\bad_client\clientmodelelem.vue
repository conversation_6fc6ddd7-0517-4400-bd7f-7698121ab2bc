<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" @click="insertEvent()" v-if="menuAction.allowEdit">新增</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">保存</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID" :item-render="{}">
              <template #default="{ data }">
                <vxe-select v-model="data.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item> <vxe-form-item field="modelID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model.trim="data.modelID" filterable remote reserve-keyword placeholder="版型" size="mini" :remote-method="modelRemoteMethod" clearable>
                  <el-option v-for="(item,index) in modelComboStore" :key="index" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item> <vxe-form-item field="text" :item-render="{}">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="MomModelElemClientDetailTable" ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" keep-source :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, icon: 'fa fa-pencil-square-o'}"> >
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="groupText" title="分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelText" title="版型名称" sortable width="250"></vxe-table-column>
      <!-- <vxe-table-column field="clientText" title="客户" sortable width="250"></vxe-table-column> -->
      <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemListCodeName" title="款式编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemCode" title="款式明细编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemName" title="款式明细名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemOriginalItemNo" title="物料名称" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="stateText" title="状态" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="stateText" title="类型" sortable width="150"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" :disabled="row.isCommon" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" :disabled="row.isCommon" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <client-person-image :form="selectRow" v-if="drawer" />
    </el-drawer>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="版型" field="modelID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelID" filterable placeholder="版型" size="mini" remote reserve-keyword clearable :remote-method="remoteMethod2">
              <el-option v-for="item in ModelComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>$6
        <vxe-form-item title="款式明细" field="modelElemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable remote reserve-keyword placeholder="款式明细" :remote-method="remoteMethod1" size="mini" @change="ModelElemChange">
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>$6
        <vxe-form-item title="物料名称" field="itemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.itemID" filterable placeholder="物料名称" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>$6
        <vxe-form-item title="类型" field="state" span="12" :item-render="{name: '$select', options: ModelModelElemClientStateEnumsComboStore}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'ClientModelElem',
  mixins: [detailMixins],
  data () {
    return {
      formData: {
        modelID: null,
        modelElemID: null,
        itemID: null,
        clientID: this.form.id,
        state: 1,
        isActive: true,
        remark: null
      },
      formRules: {
        modelElemID: [{ required: true, message: '请选择款式明细' }],
        state: [{ required: true, message: '请选择绑定类型' }]
      },
      api: {
        get: '/mtm/MOM_ModelModelElemClient/get',
        add: '/mtm/mOM_ModelModelElemClient/Adds',
        edit: '/mtm/mOM_ModelModelElemClient/updates',
        delete: '/mtm/mOM_ModelModelElemClient/deletes',
        getModelElem: '/mtm/mom_modelelem/get',
        ModelModelElemClientStateEnumsComboStore: '/mtm/combo/modelModelElemClientStateEnumsComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        modelComboStore: '/mtm/comboQuery/modelComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ModelComboStoreByQuery: '/mtm/comboQuery/ModelComboStoreByQuery',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery'
      },
      ItemComboStore: [],
      ModelElemComboStoreByQuery: [],
      ModelComboStoreByQuery: [],
      ModelModelElemClientStateEnumsComboStore: [],
      GroupComboStore: [],
      modelComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ ClientID: this.form.id })
    this.tableData = []
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelModelElemClientStateEnumsComboStore).then(result => {
        this.ModelModelElemClientStateEnumsComboStore = result
      })
      await this.$api.ActionRequest(this.api.modelComboStore).then(result => {
        this.modelComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelComboStoreByQuery).then(result => {
        this.ModelComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    ModelElemChange (val) {
      this.getModelElem(val).then(async data => {
        if (data.length > 0) {
          await this.$api.ActionRequest(this.api.ItemComboStore, { gid: data[0].itemID }).then(result => {
            this.ItemComboStore = result
            this.selectRow.itemID = data[0].itemID
          })
        }
      })
    },
    async getModelElem (val) {
      return await this.$api.ActionRequest(this.api.getModelElem, { id: val, text: '' }).then(result => {
        return result.items
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelComboStoreByQuery, { text: query }).then(result => {
        this.ModelComboStoreByQuery = result
      })
    },
    modelRemoteMethod (query) {
      this.$api.ActionRequest(this.api.modelComboStore, { text: query }).then(result => {
        this.modelComboStore = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ModelComboStoreByQuery, { gid: row.modelID }).then(result => {
        this.ModelComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
      })
      this.selectRow = cloneDeep(row)
      this.showEdit = true
    },
    // 复制
    async copyRowEvent (row, code = false, codeName = false) {
      await this.$api.ActionRequest(this.api.ModelComboStoreByQuery, { gid: row.modelID }).then(result => {
        this.ModelComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (!code && this.$utils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (!codeName && this.$utils.has(this.selectRow, 'codeName')) {
        this.selectRow.codeName = null
      }
      this.showEdit = true
    }

  }
}
</script>

<style>
</style>
