<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="addRfidEvent" v-if="menuAction.allowAdd">绑定RFID</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="dates">
              <template #default>
                <el-date-picker v-model="searchForm.dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="创建日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="deliveryDates" :item-render="{}">
              <template #default>
                <el-date-picker v-model="searchForm.deliveryDates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="(交期)开始日期" end-placeholder="(交期)结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="productionPlanState" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.productionPlanState" filterable placeholder="订单状态" size="mini" clearable>
                  <el-option v-for="item in productionPlanStateComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="epcTop" :item-render="{}">
              <template #default>
                <vxe-checkbox v-model="searchForm.epcTop" content="EPC置顶"></vxe-checkbox>
              </template></vxe-form-item>
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable style="width:130px">
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" size="mini" clearable>
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PrdProductionplandetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="productionPlanStateText" title="订单状态" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="sorderNum" title="订单号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="productionPlanDetailStateText" title="状态" width="100" sortable> </vxe-table-column>
      <!-- <vxe-table-column field="issueDate" title="下单日期" width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable> </vxe-table-column> -->
      <vxe-table-column field="deliveryDate" title="订单交期" width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable> </vxe-table-column>
      <vxe-table-column field="customerNumber" title="客户订单号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="rfid" title="RFID" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="epc" title="EPC" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="itemName" title="面料号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="groupName" title="类别" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="modelName" title="版型" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="sizeCode" title="规格" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      <vxe-table-column field="productionPlanModifyOn" title="最后修改时间" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="120" sortable> </vxe-table-column>
      <vxe-table-column field="newSetWorkSecation" title="完成工段" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="newSetProductionTeam" title="完成小组" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="newSetProductionStation" title="完成工位" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="newSetProductionProcesses" title="完成工序" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="newSetCreateDateTime" title="创建时间" :formatter="val=>formatDate(val)" width="120" sortable> </vxe-table-column>
      <vxe-table-column field="newSetDateTime" title="最新完成时间" :formatter="val=>formatDate(val)" width="120" sortable> </vxe-table-column>
      <!-- <vxe-table-column field="isPutOffed" title="是否延期" width="100"> </vxe-table-column> -->
      <vxe-table-column title="延期工段" width="100" field='putOffWorkSecation' sortable>
        <template v-slot="{ row }">
          <span :style="row.isPutOffed?'color: red;':''">{{row.putOffWorkSecation}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="putOffWorkSecationDateTime" title="(工段)计划完成时间" :formatter="val=>formatDate(val)" width="120" sortable>
      </vxe-table-column>
      <vxe-table-column title="延期小组" width="100" field='putOfftProductionTeam' sortable>
        <template v-slot="{ row }">
          <span :style="row.isPutOffed?'color: red;':''">{{row.putOfftProductionTeam}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="putOffProductionTeamDateTime" title="（小组）计划完成时间" :formatter="val=>formatDate(val)" width="120" sortable>
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px" sortable> </vxe-table-column>
      <!-- <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="RfidShow" :title="'绑定RFID'" width="800" resize destroy-on-close>
      <rfid v-if="RfidShow" :loadData="loadData" />
    </vxe-modal>
    <el-drawer :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='55%'>
      <production-plan-schedule-log :showfooterCompanyInfo='false' :productionPlanDetailID='selectRow.id' />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import ProductionPlanScheduleLog from '../prd_productionplanschedulelog/index'
import Rfid from './rfid.vue'
export default {
  name: 'prd_productionplandetail',
  mixins: [masterTableMixins],
  components: {
    ProductionPlanScheduleLog,
    Rfid
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      RfidShow: false,
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mes/prd_productionplandetail/get',
        // add: '/mes/prd_productionplandetail/adds',
        // edit: '/mes/prd_productionplandetail/updates',
        // delete: '/mes/prd_productionplandetail/deletes',
        productionPlanStateComboStore: '/mes/combo/productionPlanStateComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      footerCompanyInfo: false,
      productionPlanStateComboStore: [],
      GroupComboStore: [],
      clientComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.productionPlanStateComboStore).then(result => {
        this.productionPlanStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    addRfidEvent () {
      this.RfidShow = true
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
