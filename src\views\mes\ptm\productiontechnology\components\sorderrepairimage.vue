<template>
  <div class="SorderRepairImage">
    <el-row>
      <template v-for="(item,index) in list">
        <el-image style="width: 290px; height: 200px" fit="contain" :src="item" :key="index" :preview-src-list="list">
        </el-image>
      </template>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'SorderRepairImage',
  props: {
    form: {
      require: true,
      type: Object
    }
  },
  watch: {
    form: {
      deep: true,
      handler (newval, oldval) {
        this.list = []
        if (newval !== null && newval.sorderRepairModelID !== null) {
          this.get(newval.sorderRepairModelID)
        }
      }
    }
  },
  data () {
    return {
      api: {
        get: '/mtm/oDM_SorderRepairImage/getById'
      },
      list: []
    }
  },
  created () {
    if (this.form !== null && this.form.sorderRepairModelID !== null) {
      this.get(this.form.sorderRepairModelID)
    }
  },
  methods: {
    async get (id) {
      this.list = []
      await this.$api.ActionRequest(this.api.get, { sorderRepairModelID: id }).then(res => {
        console.log(res)
        res.forEach(elem => {
          this.list.push(elem.imageUrl)
        })
      })
    }
  }

}
</script>

<style lang='scss'>
.SorderRepairImage {
  padding-top: 20px;
}
</style>
