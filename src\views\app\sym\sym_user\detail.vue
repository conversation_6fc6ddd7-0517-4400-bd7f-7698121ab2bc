<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <!-- <vxe-button icon="vxe-icon--d-arrow-left" status="perfect" @click="goback">返回</vxe-button> -->
          <vxe-button icon="fa fa-save" status="perfect" @click="addEvent" v-if="menuAction.allowEdit">保存</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table ref='detail_table' id="SymUserDetailTable" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field='isChecked' type="checkbox" width="60px"></vxe-table-column>
      <vxe-table-column field="roldeCode" title="角色编码" width="150px" sortable show-overflow></vxe-table-column>
      <vxe-table-column field="roleName" title="角色名称" width="150px" sortable show-overflow></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'SymUserDetail',
  mixins: [detailTableMixins],
  data () {
    return {
      footerCompanyInfo: false,
      formData: {},
      tableRef: 'detail_table',
      api: {
        get: '/mtm/sym_userrole/get',
        edit: '/mtm/sym_userrole/updates'
      },
      action: {
        get: true,
        add: true,
        edit: true,
        delete: true,
        print: true
      }
    }
  },
  created () {
    this.loadData({ id: this.form.id }).then(({ data }) => {
      this.checked(data)
    })
  },
  methods: {
    async addEvent () {
      var rows = this.$refs.detail_table.getCheckboxRecords()
      var userRoles = []
      rows.forEach(row => {
        userRoles.push({ userId: row.userId, roleId: row.roleId })
      })
      await this.$api.ActionRequest(this.api.edit, userRoles, true).then(res => {
        this.loadData({ id: this.form.id }).then(({ data }) => {
          this.checked(data)
        })
      })
    },
    checked (data) {
      data.forEach(row => {
        if (row.isChecked) {
          this.$refs.detail_table.setCheckboxRow(row, true)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
