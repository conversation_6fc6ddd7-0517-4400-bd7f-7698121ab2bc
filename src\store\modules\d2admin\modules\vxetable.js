
import setting from '@/setting.js'
// import router from '@/router'
export default {
  namespaced: true,
  state: {
    vxetableconfig: {
      tableOptFixed: setting.vxeTable.tableOptFixed
    }

  },
  actions: {
    async set ({ state, dispatch, commit }, vxetableconfig) {
      // 记录上个值
      const old = state.vxetableconfig
      // store 赋值
      state.vxetableconfig = vxetableconfig || setting.vxeTable
      // 持久化
      await dispatch('d2admin/db/set', {
        dbName: 'sys',
        path: 'vxetable.vxetableconfig',
        value: state.vxetableconfig,
        user: true
      }, { root: true })
      // 应用
      commit('apply', {
        oldval: old,
        newval: state.value
      })
    },
    async load ({ state, dispatch, commit }) {
      // 记录上个值
      const old = state.value
      // store 赋值
      state.vxetableconfig = await dispatch('d2admin/db/get', {
        dbName: 'sys',
        path: 'vxetable.vxetableconfig',
        defaultValue: setting.vxeTable,
        user: true
      }, { root: true })
      // store 赋值
      // 应用
      commit('apply', {
        oldColor: old,
        newColor: state.vxetableconfig
      })
    }
  },
  mutations: {
    apply (state, { oldval, newval }) {
      // router.replace('/refresh')
      // var options = {
      //     oldColors: cloneDeep(forElementUI.getElementUISeries(oldColor)),
      //     newColors: cloneDeep(forElementUI.getElementUISeries(newColor))
      // }
      // client.changer.changeColor(options)
    },
    init () {

    }
  }
}
