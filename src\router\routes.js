import layoutHeaderAside from '@/layout/header-aside'

// 由于懒加载页面太多的话会造成webpack热更新太慢，所以开发环境不使用懒加载，只有生产环境使用懒加载
const _import = require('@/libs/util.import.' + process.env.NODE_ENV)

/**
 * 在主框架内显示
 */
const frameIn = [
  // { path: '*', redirect: './system/home' },
  {
    path: '/',
    redirect: { name: 'index' },
    component: layoutHeaderAside,
    children: [
      // 首页
      { path: 'index', name: 'index', meta: { auth: true }, component: _import('system/index') },
      // 演示页面
      { path: 'page1', name: 'page1', meta: { title: '页面 1', auth: true }, component: _import('demo/page1') },
      { path: 'page2', name: 'page2', meta: { title: '页面 2', auth: true }, component: _import('demo/page2') },
      { path: 'page3', name: 'page3', meta: { title: '页面 3', auth: true }, component: _import('demo/page3') },
      // 系统 前端日志
      { path: 'log', name: 'log', meta: { title: '前端日志', auth: true }, component: _import('system/log') },
      { path: 'personWorkBench', name: 'personWorkBench', meta: { title: '个人工作台', auth: true, cache: true }, component: _import('system/personworkbench') },
      // 系统 个人中心
      { path: 'userinfo', name: 'userinfo', meta: { title: '个人中心', cache: true, hidden: true }, component: _import('system/userinfo') },
      // 刷新页面 必须保留
      { path: 'refresh', name: 'refresh', hidden: true, component: _import('system/function/refresh') },
      // 页面重定向 必须保留
      { path: 'redirect/:route*', name: 'redirect', hidden: true, component: _import('system/function/redirect') },
      /** SYM */
      { path: 'app/sym/sym_user', name: 'sym_user', meta: { title: '用户', cache: true, auth: true }, component: _import('app/sym/sym_user/index.vue') },
      { path: 'app/sym/sym_role', name: 'sym_role', meta: { title: '角色', cache: true, auth: true }, component: _import('app/sym/sym_role/index.vue') },
      { path: 'app/sym/sym_action', name: 'sym_action', meta: { title: '权限', cache: true, auth: true }, component: _import('app/sym/sym_action/index.vue') },
      { path: 'app/sym/sym_actiontype', name: 'sym_actiontype', meta: { title: '权限类别', cache: true, auth: true }, component: _import('app/sym/sym_actiontype/index.vue') },
      { path: 'app/sym/sym_actiongroup', name: 'sym_actiongroup', meta: { title: '权限分组', cache: true, auth: true }, component: _import('app/sym/sym_actiongroup/index.vue') },
      { path: 'app/sym/sym_userclient', name: 'sym_userclient', meta: { title: '客户关系维护', cache: true, auth: true }, component: _import('app/sym/sym_userclient/index.vue') },
      { path: 'app/sym/sym_department', name: 'sym_department', meta: { title: '部门', cache: true, auth: true }, component: _import('app/sym/sym_department/index.vue') },
      { path: 'app/sym/sym_workbench', name: 'sym_workbench', meta: { title: '工作台设置', cache: true, auth: true }, component: _import('app/sym/sym_workbench/index.vue') },
      { path: 'app/sym/sym_workbenchdetail', name: 'sym_workbenchdetail', meta: { title: '工作台记录', cache: true, auth: true }, component: _import('app/sym/sym_workbenchdetail/index.vue') },
      { path: 'app/sym/sym_importsorderbase', name: 'sym_importsorderbase', meta: { title: '订单导入配置', cache: true, auth: true }, component: _import('app/sym/sym_importsorderbase/index.vue') },
      /** SYS */

      { path: 'app/sys/sys_sizeelema', name: 'sys_sizeelema', meta: { title: '身高', cache: true, auth: true }, component: _import('app/sys/sys_sizeelema/index.vue') },
      { path: 'app/sys/sys_sizeelemb', name: 'sys_sizeelemb', meta: { title: '胸围', cache: true, auth: true }, component: _import('app/sys/sys_sizeelemb/index.vue') },
      { path: 'app/sys/sys_sizeelemc', name: 'sys_sizeelemc', meta: { title: '体型', cache: true, auth: true }, component: _import('app/sys/sys_sizeelemc/index.vue') },
      { path: 'app/sys/sys_sizeelemd', name: 'sys_sizeelemd', meta: { title: '臀围', cache: true, auth: true }, component: _import('app/sys/sys_sizeelemd/index.vue') },
      { path: 'app/sys/sys_sizecolumn', name: 'sys_sizecolumn', meta: { title: '规格字段', cache: true, auth: true }, component: _import('app/sys/sys_sizecolumn/index.vue') },
      { path: 'app/sys/sys_itemtexturelength', name: 'sys_itemtexturelength', meta: { title: '面料纹理与CAD耗量', cache: true, auth: true }, component: _import('app/sys/sys_itemtexturelength/index.vue') },
      { path: 'app/sys/sys_sizecolumngroup', name: 'sys_sizecolumngroup', meta: { title: '规格字段->类别', cache: true, auth: true }, component: _import('app/sys/sys_sizecolumngroup/index.vue') },

      // { path: 'app/sys/sys_bank', name: 'sys_bank', meta: { title: '银行', cache: true, auth: true }, component: _import('app/sys/sys_bank/index.vue') },
      // { path: 'app/sys/sys_brand', name: 'sys_brand', meta: { title: '品牌', cache: true, auth: true }, component: _import('app/sys/sys_brand/index.vue') },
      // { path: 'app/sys/sys_brandline', name: 'sys_brandline', meta: { title: '客户线', cache: true, auth: true }, component: _import('app/sys/sys_brandline/index.vue') },
      { path: 'app/sys/sys_class', name: 'sys_class', meta: { title: '系统分类', cache: true, auth: true }, component: _import('app/sys/sys_class/index.vue') },
      { path: 'app/sys/sys_currency', name: 'sys_currency', meta: { title: '货币/汇率', cache: true, auth: true }, component: _import('app/sys/sys_currency/index.vue') },

      { path: 'app/sys/sys_group', name: 'sys_group', meta: { title: '类别', cache: true, auth: true }, component: _import('app/sys/sys_group/index.vue') },
      { path: 'app/sys/sys_subgroup', name: 'sys_subgroup', meta: { title: '子类别', cache: true, auth: true }, component: _import('app/sys/sys_subgroup/index.vue') },
      { path: 'app/sys/sys_movetype', name: 'sys_movetype', meta: { title: '转移类型', cache: true, auth: true }, component: _import('app/sys/sys_movetype/index.vue') },
      // { path: 'app/sys/sys_paymentmethod', name: 'sys_paymentmethod', meta: { title: '付款方式', cache: true, auth: true }, component: _import('app/sys/sys_paymentmethod/index.vue') },
      // { path: 'app/sys/sys_paymentterms', name: 'sys_paymentterms', meta: { title: '付款条款', cache: true, auth: true }, component: _import('app/sys/sys_paymentterms/index.vue') },
      // { path: 'app/sys/sys_season', name: 'sys_season', meta: { title: '季度', cache: true, auth: true }, component: _import('app/sys/sys_season/index.vue') },
      // { path: 'app/sys/sys_status', name: 'sys_status', meta: { title: '状态', cache: true, auth: true }, component: _import('app/sys/sys_status/index.vue') },

      // { path: 'app/sys/sys_unit', name: 'sys_unit', meta: { title: '单位', cache: true, auth: true }, component: _import('app/sys/sys_unit/index.vue') },
      // { path: 'app/sys/sys_vat', name: 'sys_vat', meta: { title: '税率', cache: true, auth: true }, component: _import('app/sys/sys_vat/index.vue') },

      { path: 'app/sys/sys_cadlayout', name: 'sys_cadlayout', meta: { title: 'CAD排料图', cache: true, auth: true }, component: _import('app/sys/sys_cadlayout/index.vue') },
      { path: 'app/sys/sys_cadrule', name: 'sys_cadrule', meta: { title: 'CAD衣片组', cache: true, auth: true }, component: _import('app/sys/sys_cadrule/index.vue') },

      { path: 'app/language/language_model', name: 'language_model', meta: { title: '版型多语言', cache: true, auth: true }, component: _import('app/language/language_model/index.vue') },
      { path: 'app/language/language_body', name: 'language_body', meta: { title: '特体多语言', cache: true, auth: true }, component: _import('app/language/language_body/index.vue') },
      { path: 'app/language/language_bodylist', name: 'language_bodylist', meta: { title: '特体部位多语言', cache: true, auth: true }, component: _import('app/language/language_bodylist/index.vue') },
      { path: 'app/language/language_sizecolumn', name: 'language_sizecolumn', meta: { title: '规格字段多语言', cache: true, auth: true }, component: _import('app/language/language_sizecolumn/index.vue') },
      { path: 'app/language/language_modelelemlist', name: 'language_modelelemlist', meta: { title: '款式多语言', cache: true, auth: true }, component: _import('app/language/language_modelelemlist/index.vue') },
      { path: 'app/language/language_modelelem', name: 'language_modelelem', meta: { title: '款式明细多语言', cache: true, auth: true }, component: _import('app/language/language_modelelem/index.vue') },

      /** BAD 基础数据 */
      { path: 'app/bad/bad_client', name: 'bad_client', meta: { title: '客户管理中心', cache: true, auth: true }, component: _import('app/bad/bad_client/index.vue') },
      { path: 'app/bad/bad_itemml', name: 'bad_itemml', meta: { title: '面料物料编码', cache: true, auth: true }, component: _import('app/bad/bad_itemml/index.vue') },
      { path: 'app/bad/bad_itemfl', name: 'bad_itemfl', meta: { title: '辅料物料编码', cache: true, auth: true }, component: _import('app/bad/bad_itemfl/index.vue') },
      { path: 'app/bad/bad_itemseries', name: 'bad_itemseries', meta: { title: '物料规则组件', cache: true, auth: true }, component: _import('app/bad/bad_itemseries/index.vue') },
      { path: 'app/bad/bad_itemconfig', name: 'bad_itemconfig', meta: { title: '物料分类配置', cache: true, auth: true }, component: _import('app/bad/bad_itemconfig/index.vue') },
      { path: 'app/bad/bad_itemelemitemconfig', name: 'bad_itemelemitemconfig', meta: { title: '配色方案基础模板', cache: true, auth: true }, component: _import('app/bad/bad_itemelemitemconfig/index.vue') },
      { path: 'app/bad/bad_suitsupplymarkwash', name: 'bad_suitsupplymarkwash', meta: { title: 'Suitsupply版型配置', cache: true, auth: true }, component: _import('app/bad/bad_suitsupplymarkwash/index.vue') },
      { path: 'app/bad/bad_suitsupplyitemcomp', name: 'bad_suitsupplyitemcomp', meta: { title: 'Suitsupply物料成分', cache: true, auth: true }, component: _import('app/bad/bad_suitsupplyitemcomp/index.vue') },
      { path: 'app/bad/bad_suitsupplymodelelemlist', name: 'bad_suitsupplymodelelemlist', meta: { title: 'Suitsupply款式配置', cache: true, auth: true }, component: _import('app/bad/bad_suitsupplymodelelemlist/index.vue') },
      { path: 'app/bad/bad_globalcountry', name: 'bad_globalcountry', meta: { title: '全球国家维护', cache: true, auth: true }, component: _import('app/bad/bad_globalcountry/index.vue') },
      /** MOM 版型 */
      { path: 'app/mom/mom_modelelembase', name: 'mom_modelelembase', meta: { title: '款式部位', cache: true, auth: true }, component: _import('app/mom/mom_modelelembase/index.vue') },
      { path: 'app/mom/mom_modelelemlist', name: 'mom_modelelemlist', meta: { title: '款式', cache: true, auth: true }, component: _import('app/mom/mom_modelelemlist/index.vue') },
      { path: 'app/mom/mom_modelelem', name: 'mom_modelelem', meta: { title: '款式明细', cache: true, auth: true }, component: _import('app/mom/mom_modelelem/index.vue') },
      { path: 'app/mom/mom_modelelemprice', name: 'mom_modelelemprice', meta: { title: '款式工艺价格', cache: true, auth: true }, component: _import('app/mom/mom_modelelemprice/index.vue') },
      { path: 'app/mom/mom_modeltype', name: 'mom_modeltype', meta: { title: '版型类型', cache: true, auth: true }, component: _import('app/mom/mom_modeltype/index.vue') },
      { path: 'app/mom/mom_model', name: 'mom_model', meta: { title: '版型', cache: true, auth: true }, component: _import('app/mom/mom_model/index.vue') },
      { path: 'app/mom/mom_modelbase', name: 'mom_modelbase', meta: { title: '基础版型', cache: true, auth: true }, component: _import('app/mom/mom_modelbase/index.vue') },
      { path: 'app/mom/mom_modelsizecolumnbody', name: 'mom_modelsizecolumnbody', meta: { title: '规格与特体', cache: true, auth: true }, component: _import('app/mom/mom_modelsizecolumnbody/index.vue') },
      { path: 'app/mom/mom_sizelist', name: 'mom_sizelist', meta: { title: '规格单', cache: true, auth: true }, component: _import('app/mom/mom_sizelist/index.vue') },
      { path: 'app/mom/mom_sizerulecheck', name: 'mom_sizerulecheck', meta: { title: '规格算法', cache: true, auth: true }, component: _import('app/mom/mom_sizerulecheck/index.vue') },
      { path: 'app/mom/mom_sizerule', name: 'mom_sizerule', meta: { title: '匹配规则', cache: true, auth: true }, component: _import('app/mom/mom_sizerule/index.vue') },
      { path: 'app/mom/mom_bodylist', name: 'mom_bodylist', meta: { title: '特体', cache: true, auth: true }, component: _import('app/mom/mom_bodylist/index.vue') },
      { path: 'app/mom/mom_sewbase', name: 'mom_sewbase', meta: { title: '缝份类别', cache: true, auth: true }, component: _import('app/mom/mom_sewbase/index.vue') },
      { path: 'app/mom/mom_sewlist', name: 'mom_sewlist', meta: { title: '缝份', cache: true, auth: true }, component: _import('app/mom/mom_sewlist/index.vue') },
      { path: 'app/mom/mom_cadlayoutmodelelem', name: 'mom_cadlayoutmodelelem', meta: { title: '排料图物料归属', cache: true, auth: true }, component: _import('app/mom/mom_cadlayoutmodelelem/index.vue') },
      { path: 'app/mom/mom_cadrulelayout', name: 'mom_cadrulelayout', meta: { title: '排料图基本属性', cache: true, auth: true }, component: _import('app/mom/mom_cadrulelayout/index.vue') },
      { path: 'app/mom/mom_modelelemcad', name: 'mom_modelelemcad', meta: { title: '衣片组编码规则', cache: true, auth: true }, component: _import('app/mom/mom_modelelemcad/index.vue') },
      { path: 'app/mom/mom_modelelemvariant', name: 'mom_modelelemvariant', meta: { title: '款式明细->变体', cache: true, auth: true }, component: _import('app/mom/mom_modelelemvariant/index.vue') },
      { path: 'app/mom/mom_modelimage', name: 'mom_modelimage', meta: { title: '版型图片管理', cache: true, auth: true }, component: _import('app/mom/mom_modelimage/index.vue') },
      { path: 'app/mom/mom_modelelemimage', name: 'mom_modelelemimage', meta: { title: '款式明细图片', cache: true, auth: true }, component: _import('app/mom/mom_modelelemimage/index.vue') },
      { path: 'app/mom/mom_modelelemlistimage', name: 'mom_modelelemlistimage', meta: { title: '款式图片', cache: true, auth: true }, component: _import('app/mom/mom_modelelemlistimage/index.vue') },
      { path: 'app/mom/mom_modelbaseimage', name: 'mom_modelbaseimage', meta: { title: '基础版型图片', cache: true, auth: true }, component: _import('app/mom/mom_modelbaseimage/index.vue') },
      { path: 'app/mom/mom_bodyimage', name: 'mom_bodyimage', meta: { title: '特体工艺图片', cache: true, auth: true }, component: _import('app/mom/mom_bodyimage/index.vue') },
      { path: 'app/mom/mom_sizecolumnimage', name: 'mom_sizecolumnimage', meta: { title: '规格字段图片', cache: true, auth: true }, component: _import('app/mom/mom_sizecolumnimage/index.vue') },
      { path: 'app/mom/mom_modelelemliststatus', name: 'mom_modelelemliststatus', meta: { title: '流程节点验证', cache: true, auth: true }, component: _import('app/mom/mom_modelelemliststatus/index.vue') },
      { path: 'app/mom/mom_modelelemrule', name: 'mom_modelelemrule', meta: { title: '智能Bom', cache: true, auth: true }, component: _import('app/mom/mom_modelelemrule/index.vue') },
      { path: 'app/mom/mom_modelelemrule1', name: 'mom_modelelemrule1', meta: { title: '智能Bom(新)', cache: true, auth: true }, component: _import('app/mom/mom_modelelemrule1/index.vue') },
      { path: 'app/mom/mom_modeldesignbyelemlist', name: 'mom_modeldesignbyelemlist', meta: { title: '款式与版型', cache: true, auth: true }, component: _import('app/mom/mom_modeldesignbyelemlist/index.vue') },
      { path: 'app/mom/mom_modeldesignbyelemlistdetail', name: 'mom_modeldesignbyelemlistdetail', meta: { title: '款式明细与版型', cache: true, auth: true }, component: _import('app/mom/mom_modeldesignbyelemlistdetail/index.vue') },

      /** ODM 订单 */
      { path: 'app/odm/odm_sorder/', name: 'odm_sorder', meta: { title: '智能下单', cache: true, auth: true }, component: _import('app/odm/odm_sorder/index.vue') },
      { path: 'app/odm/odm_sordern/', name: 'odm_sordern', meta: { title: '智能下单(图)', cache: true, auth: true }, component: _import('app/odm/odm_sordern/index.vue') },
      { path: 'app/odm/odm_sorder/modify', name: 'odm_sordermodify', meta: { title: '订单查看', cache: true, auth: true }, component: _import('app/odm/odm_sorder/sordermodify.vue') },
      { path: 'app/odm/odm_sordern/modify', name: 'odm_sordernmodify', meta: { title: '订单查看(图)', cache: true, auth: true }, component: _import('app/odm/odm_sordern/sordermodify.vue') },
      { path: 'app/odm/odm_sorderpro/', name: 'odm_sorderpro', meta: { title: '批量下单(团装版)', cache: true, auth: true }, component: _import('app/odm/odm_sorderpro/index.vue') },
      { path: 'app/odm/odm_sorderpro/modify', name: 'odm_sorderpromodify', meta: { title: '订单查看(团装版)', cache: true, auth: true }, component: _import('app/odm/odm_sorderpro/sorderpromodify.vue') },
      // { path: 'app/odm/odm_sorder/list', name: 'odm_sorderlist', meta: { title: '订单列表', cache: true, auth: true }, component: _import('app/odm/odm_sorder/list.vue') },
      { path: 'app/odm/odm_sorderlist/index', name: 'odm_sorderlist', meta: { title: '订单列表', cache: true, auth: true }, component: _import('app/odm/odm_sorderlist/index.vue') },
      { path: 'app/odm/odm_sorderlistexport/index', name: 'odm_sorderlistexport', meta: { title: '订单导出', cache: true, auth: true }, component: _import('app/odm/odm_sorderlistexport/index.vue') },
      { path: 'app/odm/odm_sorderlistpro/index', name: 'odm_sorderlistpro', meta: { title: '团装列表', cache: true, auth: true }, component: _import('app/odm/odm_sorderlistpro/index.vue') },
      { path: 'app/odm/odm_sorderstatechange/index', name: 'odm_sorderstatechange', meta: { title: '订单流转', cache: true, auth: true }, component: _import('app/odm/odm_sorderstatechange/index.vue') },
      { path: 'app/odm/odm_sorderbill/index', name: 'odm_sorderbill', meta: { title: '账单列表', cache: true, auth: true }, component: _import('app/odm/odm_sorderbill/index.vue') },
      { path: 'app/odm/odm_sorderaftersale/index', name: 'odm_sorderaftersale', meta: { title: '售后列表', cache: true, auth: true }, component: _import('app/odm/odm_sorderaftersale/index.vue') },
      { path: 'app/odm/odm_suitsupplyorder/index', name: 'odm_suitsupplyorder', meta: { title: 'Suitsupply订单数据', cache: true, auth: true }, component: _import('app/odm/odm_suitsupplyorder/index.vue') },
      { path: 'app/odm/odm_sordersuitsupply/index', name: 'odm_sordersuitsupply', meta: { title: 'Suitsupply订单接收', cache: true, auth: true }, component: _import('app/odm/odm_sordersuitsupply/index.vue') },
      { path: 'app/odm/odm_suitsupplyoption/index', name: 'odm_suitsupplyoption', meta: { title: 'Suitsupply款式配置', cache: true, auth: true }, component: _import('app/odm/odm_suitsupplyoption/index.vue') },
      { path: 'app/odm/odm_suitsupplyoptionvalue/index', name: 'odm_suitsupplyoptionvalue', meta: { title: 'Suitsupply明细配置', cache: true, auth: true }, component: _import('app/odm/odm_suitsupplyoptionvalue/index.vue') },
      { path: 'app/odm/odm_suitsupplyoptionsize/index', name: 'odm_suitsupplyoptionsize', meta: { title: 'Suitsupply规格配置', cache: true, auth: true }, component: _import('app/odm/odm_suitsupplyoptionsize/index.vue') },

      /** WAR 仓库管理 */
      { path: 'app/war/war_itemstock', name: 'war_itemstock', meta: { title: '物料仓库', cache: true, auth: true }, component: _import('app/war/war_itemstock/index1.vue') },
      { path: 'app/war/war_itemstockprice', name: 'war_itemstockprice', meta: { title: '物料仓库(价格)', cache: true, auth: true }, component: _import('app/war/war_itemstock/index2.vue') },
      { path: 'app/war/war_itembatch', name: 'war_itembatch', meta: { title: '保税仓管理', cache: true, auth: true }, component: _import('app/war/war_itembatch/index.vue') },
      { path: 'app/war/war_itembatcheorino', name: 'war_itembatcheorino', meta: { title: '手册号与项号', cache: true, auth: true }, component: _import('app/war/war_itembatcheorino/index.vue') },
      { path: 'app/war/war_itemstockinvoices', name: 'war_itemstockinvoices', meta: { title: '物料出入库单据', cache: true, auth: true }, component: _import('app/war/war_itemstockinvoices/index.vue') },
      { path: 'app/war/war_itemstockdetail', name: 'war_itemstockdetail', meta: { title: '物料出入库记录', cache: true, auth: true }, component: _import('app/war/war_itemstockdetail/index.vue') },
      { path: 'app/war/war_itemstockposition', name: 'war_itemstockposition', meta: { title: '库位设置', cache: true, auth: true }, component: _import('app/war/war_itemstockposition/index.vue') },
      { path: 'app/war/war_productwarehouse', name: 'war_productwarehouse', meta: { title: '成品仓库', cache: true, auth: true }, component: _import('app/war/war_productwarehouse/index.vue') },
      { path: 'app/war/war_productwarehousedetail', name: 'war_productwarehousedetail', meta: { title: '成品出入库记录', cache: true, auth: true }, component: _import('app/war/war_productwarehousedetail/index.vue') },
      { path: 'app/war/war_itempurchase', name: 'war_itempurchase', meta: { title: '采购单管理', cache: true, auth: true }, component: _import('app/war/war_itempurchase/index.vue') },
      { path: 'app/war/war_shipments', name: 'war_shipments', meta: { title: '发货管理', cache: true, auth: true }, component: _import('app/war/war_shipments/index.vue') },
      { path: 'app/war/war_productpacking', name: 'war_productpacking', meta: { title: 'UPS发货装箱单', cache: true, auth: true }, component: _import('app/war/war_productpacking/index.vue') },
      { path: 'app/war/war_itembom', name: 'war_itembom', meta: { title: '订单物料清单', cache: true, auth: true }, component: _import('app/war/war_itembom/index.vue') },
      { path: 'app/war/war_upsaccountconfig', name: 'war_upsaccountconfig', meta: { title: 'UPS全球地址配置', cache: true, auth: true }, component: _import('app/war/war_upsaccountconfig/index.vue') },
      { path: 'app/war/war_reportform', name: 'war_reportfrom', meta: { title: '报关单证管理', cache: true, auth: true }, component: _import('app/war/war_reportform/index.vue') },
      { path: 'app/war/war_reportformconfig', name: 'war_reportformconfig', meta: { title: '报关单证配置', cache: true, auth: true }, component: _import('app/war/war_reportformconfig/index.vue') },
      { path: 'app/war/war_productboxconfig', name: 'war_productboxconfig', meta: { title: '装箱配置', cache: true, auth: true }, component: _import('app/war/war_productboxconfig/index.vue') },

      /** 工具  */
      /** modeltool版型小工具 */
      { path: 'app/tool/modeltool', name: 'modeltool', meta: { title: '版型工具箱', cache: true, auth: true }, component: _import('app/tool/modeltool/index.vue') },
      { path: 'app/tool/sorder', name: 'Sorder', meta: { title: '已删除订单', cache: true, auth: true }, component: _import('app/tool/sorder/index.vue') },
      { path: 'app/tool/configmanage', name: 'configmanage', meta: { title: '配置文件', cache: true, auth: true }, component: _import('app/tool/configmanage/index.vue') },
      /** MES */
      /** 生产管理中心 */
      /** BAD基础数据 */
      { path: 'mes/bad/bad_factory', name: 'bad_factory', meta: { title: '工厂', cache: true, auth: true }, component: _import('mes/bad/bad_factory/index.vue') },
      { path: 'mes/bad/bad_productionseries', name: 'bad_productionseries', meta: { title: '产品系列', cache: true, auth: true }, component: _import('mes/bad/bad_productionseries/index.vue') },
      { path: 'mes/bad/bad_productionprocesses', name: 'bad_productionprocesses', meta: { title: '生产工序', cache: true, auth: true }, component: _import('mes/bad/bad_productionprocesses/index.vue') },
      { path: 'mes/bad/bad_productionstation', name: 'bad_productionstation', meta: { title: '生产工位', cache: true, auth: true }, component: _import('mes/bad/bad_productionstation/index.vue') },
      { path: 'mes/bad/bad_productionteam', name: 'bad_productionteam', meta: { title: '生产小组', cache: true, auth: true }, component: _import('mes/bad/bad_productionteam/index.vue') },
      { path: 'mes/bad/bad_worksecation', name: 'bad_worksecation', meta: { title: '工段', cache: true, auth: true }, component: _import('mes/bad/bad_worksecation/index.vue') },
      { path: 'mes/bad/bad_worksecationbase', name: 'bad_worksecationbase', meta: { title: '基础工段', cache: true, auth: true }, component: _import('mes/bad/bad_worksecationbase/index.vue') },
      { path: 'mes/bad/bad_basictechnology', name: 'bad_basictechnology', meta: { title: '基础工艺数据', cache: true, auth: true }, component: _import('mes/bad/bad_basictechnology/index.vue') },
      { path: 'mes/bad/bad_printlabelconfig', name: 'bad_printlabelconfig', meta: { title: '打印票头配置', cache: true, auth: true }, component: _import('mes/bad/bad_printlabelconfig/index.vue') },
      { path: 'mes/bad/bad_productionstationqualitydetail', name: 'bad_productionstationqualitydetail', meta: { title: '工位与质检', cache: true, auth: true }, component: _import('mes/bad/bad_productionstationqualitydetail/index.vue') },

      /** PRD生产计划 */
      { path: 'mes/prd/prd_productionplan', name: 'prd_productionplan', meta: { title: '生产计划', cache: true, auth: true }, component: _import('mes/prd/prd_productionplan/index.vue') },
      { path: 'mes/prd/prd_productionplanworksecation', name: 'prd_productionplanworksecation', meta: { title: '排产工段', cache: true, auth: true }, component: _import('mes/prd/prd_productionplanworksecation/index.vue') },
      { path: 'mes/prd/productionindicator', name: 'productionindicator', meta: { title: '生产指标', cache: true, auth: true }, component: _import('mes/prd/prd_productionindicator/index.vue') },
      { path: 'mes/prd/prd_productionreport', name: 'prd_productionreport', meta: { title: '生产报表', cache: true, auth: true }, component: _import('mes/prd/prd_productionreport/index.vue') },
      { path: 'mes/prd/prd_productionplandetail', name: 'prd_productionplandetail', meta: { title: '流水号查询', cache: true, auth: true }, component: _import('mes/prd/prd_productionplandetail/index.vue') },
      { path: 'mes/prd/prd_productionplanschedule', name: 'prd_productionplanschedule', meta: { title: '延期预警', cache: true, auth: true }, component: _import('mes/prd/prd_productionplanschedule/index.vue') },
      { path: 'mes/prd/prd_printlabel', name: 'prd_printlabel', meta: { title: '票头打印', cache: true, auth: true }, component: _import('mes/prd/prd_printlabel/index.vue') },
      { path: 'mes/prd/prd_printwashinglabel', name: 'prd_printwashinglabel', meta: { title: '水洗唛打印', cache: true, auth: true }, component: _import('mes/prd/prd_printwashinglabel/index.vue') },
      { path: 'mes/prd/prd_productionplanschedulelog', name: 'prd_productionplanschedulelog', meta: { title: '扫描查询', cache: true, auth: true }, component: _import('mes/prd/prd_productionplanschedulelog/index.vue') },
      { path: 'mes/prd/prd_washinglabelhistory', name: 'prd_washinglabelhistory', meta: { title: '水洗唛数据', cache: true, auth: true }, component: _import('mes/prd/prd_washinglabelhistory/index.vue') },
      { path: 'mes/prd/prd_hangtag', name: 'prd_hangtag', meta: { title: '吊牌贴纸', cache: true, auth: true }, component: _import('mes/prd/prd_hangtag/index.vue') },
      /** PTM生产工艺管理 */
      // { path: 'mes/ptm/productiontechnology', name: 'productiontechnology', meta: { title: '生产工艺', cache: true, auth: true }, component: _import('mes/ptm/productiontechnology/index.vue') },
      { path: 'mes/ptm/ptm_qualitybase', name: 'ptm_qualitybase', meta: { title: '质检部位', cache: true, auth: true }, component: _import('mes/ptm/ptm_qualitybase/index.vue') },
      { path: 'mes/ptm/ptm_quality', name: 'ptm_quality', meta: { title: '质检分类', cache: true, auth: true }, component: _import('mes/ptm/ptm_quality/index.vue') },
      { path: 'mes/ptm/ptm_qualitydetail', name: 'ptm_qualitydetail', meta: { title: '质检', cache: true, auth: true }, component: _import('mes/ptm/ptm_qualitydetail/index.vue') },
      { path: 'mes/ptm/ptm_productiontechnologyipset', name: 'ptm_productiontechnologyipset', meta: { title: '电脑工位显示设置', cache: true, auth: true }, component: _import('mes/ptm/ptm_productiontechnologyipset/index.vue') },

      /** SYS系统设置 */
      { path: 'mes/sys/sys_printerset', name: 'sys_printerset', meta: { title: '打印机设置', cache: true, auth: true }, component: _import('mes/sys/sys_printerset/index.vue') },
      { path: 'mes/sys/sys_factorycalendar', name: 'sys_factorycalendar', meta: { title: '工厂日历', cache: true, auth: true }, component: _import('mes/sys/sys_factorycalendar/index.vue') }

    ]
  }
]

/**
 * 在主框架之外显示
 */
const frameOut = [
  // 登录
  {
    path: '/login',
    name: 'login',
    component: _import('system/login')
  },
  {
    path: '/sorderprint',
    name: 'sorderprint',
    meta: { title: '订单打印', cache: true, auth: false },
    component: _import('system/print/sorderprint')
  },
  { path: '/productiontechnology', name: 'productiontechnology', meta: { title: '生产工艺', cache: true, auth: true }, component: _import('mes/ptm/productiontechnology/index.vue') },

  /** CVM  chart view management */
  { path: '/charview', name: 'cvm_charview', meta: { title: '大屏显示', cache: true, auth: true }, component: _import('mes/cvm/cvm_chartview/index.vue') },
  { path: '/datav', name: 'datav', meta: { title: '大屏显示', cache: true, auth: true }, component: _import('mes/cvm/datav/index.vue') }
  // {
  //   path: '/',
  //   name: 'home',
  //   meta: { title: '高级定制', cache: true, auth: false },
  //   component: _import('system/home')
  // }
]

/**
 * 错误页面
 */
const errorPage = [
  {
    path: '*',
    name: '404',
    component: _import('system/error/404')
  }
]

// 导出需要显示菜单的
export const frameInRoutes = frameIn

// 重新组织后导出
export default [
  ...frameIn,
  ...frameOut,
  ...errorPage
]
