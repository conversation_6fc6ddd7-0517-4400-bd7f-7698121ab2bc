<template>
  <board-chart :data="data" :settings="settings " :type="type">
    <template slot="chartheader">
      <h3>生产报表 N日生产计划数指标（9:00前和14:00前两次排产）</h3>
    </template>
  </board-chart>
</template>

<script>
import boardChart from './chart'
export default {
  name: 'Real<PERSON><PERSON>', // 实时刷新
  components: {
    boardChart
  },
  props: {
    api: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      data: {
        columns: ['日期', '余额', '年龄'],
        rows: [
          { 日期: '1-1', 余额: 123, 年龄: 3 },
          { 日期: '1-2', 余额: 1223, 年龄: 6 },
          { 日期: '1-3', 余额: 2123, 年龄: 9 },
          { 日期: '1-4', 余额: 4123, 年龄: 12 },
          { 日期: '1-5', 余额: 3123, 年龄: 15 },
          { 日期: '1-6', 余额: 7123, 年龄: 20 }
        ]
      },
      settings: {},
      type: 'histogram'
    }
  },
  methods: {
    async get () {
      if (this.api !== null) {
        await this.$api.ActionRequest(this.api, {}).then(result => {
          this.data.rows = result
        })
      }
    }
  }
}
</script>

<style>
</style>
