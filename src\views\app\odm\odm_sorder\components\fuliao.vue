<template>
  <div class="fuliao">
    <span class="fuliaospan">{{fuliaoData.modelElemListCode+":"+fuliaoData.modelElemListName+(fuliaoData.isPlanShow?"":"")}}</span>
    <el-select :class="elemIsReuired(fuliaoData,'elem')" v-model="fuliaoData.modelElemID" placeholder="请选择" @change="ModelElemChange(fuliaoData.modelElemID,fuliaoData.modelElem,fuliaoData)" filterable clearable size="mini" :disabled="EditState">
      <template>
        <div class="div-table">
          <div class="table-body">
            <div class="table-content">
              <el-option :disabled="true" :key="0" :value="0">
                <div class="table-tr">
                  <div class="table-td table-td1">
                    <span>款式明细</span>
                  </div>
                  <div class="table-td table-td2">
                    <span>货号</span>
                  </div>
                  <!-- <div class="table-td table-td3">
                    <span>单价</span>
                  </div> -->
                  <div class="table-td table-td4">
                    <span>数量</span>
                  </div>
                </div>
              </el-option>
              <el-option v-for="(item,itemindex) in fuliaoData.modelElem" :key="itemindex+item.modelElemID" :value="item.modelElemID" :label="item.modelElemCode+(item.modelElemName==null?'':':'+item.modelElemName)">
                <div class="table-tr">
                  <template v-if="item.imagePath!==null&&item.imagePath!==''">
                    <el-popover placement="right-end" :title="item.modelElemCode+':'+item.modelElemName" width="400" trigger="hover">
                      <el-image style="width: 400px; height: 400px" :src="item.imagePath" fit="scale-down"></el-image>
                      <div class="table-td table-td1" slot="reference">
                        <span>{{ item.modelElemCode }}:{{ item.modelElemName }}</span><i v-if="item.imagePath!==null&&item.imagePath!==''" style="color:red" class="el-icon-picture-outline"></i>
                      </div>
                    </el-popover>
                  </template>
                  <template v-else>
                    <el-tooltip :content="item.modelElemCode+':'+item.modelElemName" placement="top-start">
                      <div class="table-td table-td1">
                        <span>{{ item.modelElemCode }}:{{ item.modelElemName }}</span>
                      </div>
                    </el-tooltip>
                  </template>

                  <el-tooltip :content="item.itemText" placement="top" v-if="item.itemText!=null&&item.itemText!=''">
                    <div class="table-td table-td2">
                      <span>{{item.itemText}}</span>
                    </div>
                  </el-tooltip>
                  <div class="table-td table-td2" v-else>
                    <span>{{item.itemText}}</span>
                  </div>
                  <!-- <div class="table-td table-td3">
                    <span>{{item.price}}</span>
                  </div> -->
                  <div class="table-td table-td4">
                    <span>{{item.qty}}</span>
                  </div>
                </div>
              </el-option>
            </div>
          </div>
        </div>
      </template>
    </el-select>
    <el-input :class="elemIsReuired(fuliaoData,'input')" class="modeleleminput" v-model="elemItem.input" style="width:120px" placeholder="输入内容" v-if="elemItem.isInput&&elemItem.modelElemID!==null" clearable size="mini" suffix-icon="el-icon-info" :disabled="EditState"></el-input>
    <el-tooltip class="item" effect="dark" :content="fuliaoData.itemText" placement="top" :disabled="fuliaoData.itemText===''||fuliaoData.itemText===null">
      <el-select :class="elemIsReuired(fuliaoData,'item')" v-model.trim="fuliaoData.itemID" filterable remote :placeholder="!fuliaoData.isInput?'货号':'货号'" :remote-method="(query)=>querySearchAsync(query,fuliaoData.modelElemID,fuliaoData)" @change="val=>itemchange(val,fuliaoData)" :loading="optionsLoading" clearable :disabled="EditState||!fuliaoData.isInputItem||fuliaoData.modelElemID===null" class="itemclass" size="mini" @clear="itemClear">
        <template>
          <div class="div-table">
            <div class="table-body">
              <div class="table-content">
                <el-option :disabled="true" :key="0" :value="0">
                  <div class="table-tr">
                    <!-- <template v-if="info.userType!==0||info.userType!==1">
                      <div class="table-td table-td2">
                        <span>原始货号</span>
                      </div>
                    </template> -->

                    <div class="table-td table-td2">
                      <span>名称</span>
                    </div>

                    <div class="table-td table-td3">
                      <span>规格</span>
                    </div>
                    <div class="table-td table-td4">
                      <span>纹理</span>
                    </div>
                    <div class="table-td table-td4">
                      <span>成分</span>
                    </div>
                    <div class="table-td table-td-image">
                      <span>图片</span>
                    </div>
                  </div>
                </el-option>
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                  <div class="table-tr">

                    <div class="table-td table-td2">
                      <el-tooltip class="item" effect="dark" :content="item.label" placement="top">
                        <span>{{item.label}}{{item.name!==null?'/'+item.name:''}}</span>
                      </el-tooltip>
                    </div>

                    <div class="table-td table-td3">
                      <span>{{item.itemSize}}</span>
                    </div>
                    <div class="table-td table-td4">
                      <span>{{item.textureGroupText}}</span>
                    </div>
                    <div class="table-td table-td4">
                      <el-tooltip class="item" effect="dark" :content="item.itemComp" placement="top">
                        <span>{{item.itemComp}}</span>
                      </el-tooltip>

                    </div>
                    <div class="table-td table-td-image">
                      <template v-if="item.imageUrl!==null">

                        <el-popover placement="right-end" width="50" trigger="hover">
                          <el-image :src="item.imageUrl" fit="scale-down">
                            <div slot="error" class="image-slot">
                              <i class="el-icon-picture-outline"></i>
                            </div>
                          </el-image>
                          <span slot="reference"> <i class="el-icon-picture"></i></span>
                        </el-popover>

                      </template>
                    </div>
                  </div>
                </el-option>
              </div>
            </div>
          </div>
        </template>
      </el-select>
    </el-tooltip>
    <el-button v-if="fuliaoData.isItemAdd" type="success" icon="el-icon-plus" size="mini" @click="itemShowEvent"></el-button>
    <el-tag v-if="fuliaoData.Unit!=''&&fuliaoData.Unit!=null">{{fuliaoData.Unit}}</el-tag>
    <el-tag type="warning" v-if="fuliaoData.input!==null&&fuliaoData.input!==''">{{fuliaoData.input}}</el-tag>
    <div style="width:60px;display: inline-block">
      <el-tag v-show="fomatFloat(fuliaoData.qty,2)!=0">*{{fomatFloat(fuliaoData.qty,2)}}</el-tag>
    </div>
    <vxe-modal v-model="zbflModal" title="客供辅料" width="400" resize remember :transfer="true">
      <template v-slot>
        <vxe-form ref="zbflFormref" :data="zbflForm" :rules="zbflFormRules" title-align="right" title-width="100" @submit="zbflSave">
          <vxe-form-item title="客供辅料名称" field="input" span="24" :item-render="{name: 'input', attrs: {placeholder: '请输入客供辅料名称'}}"></vxe-form-item>
          <vxe-form-item title="数量" field="qty" span="24">
            <vxe-input v-model="zbflForm.qty" placeholder="请输入数量" type="integer"></vxe-input>
          </vxe-form-item>
          <vxe-form-item align="center" span="24">
            <template v-slot>
              <vxe-button type="submit" status="primary">提交</vxe-button>
              <vxe-button type="reset">重置</vxe-button>
            </template>
          </vxe-form-item>
        </vxe-form>
      </template>
    </vxe-modal>
    <!-- 物料添加 -->
    <el-dialog title="物料编码添加" :visible.sync="itemMaster.show" width="30%" :before-close="handleClose" :append-to-body="true" :destroy-on-close="true">
      <vxe-form :data="itemMaster.itemForm" :rules="itemMaster.formRules" title-align="right" title-width="100" @submit="itemSubmitEvent('ruleForm')" ref="ruleForm">
        <vxe-form-item title="物料分类" field="itemGroupID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.itemForm.itemGroupID" filterable placeholder="物料分类" size="mini" clearable>
              <el-option v-for="item in ItemFLGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="业务归属" field="businessGroup" span="12">
          <template #default="{ data }">
            <el-select v-model="data.itemForm.businessGroup" filterable placeholder="业务归属" size="mini" clearable>
              <el-option v-for="item in BusinessGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="原始货号" field="originalItemNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item title="关联图片">
          <template #default>
            <el-upload ref="upload" :action="mtmapi+api.addItem" list-type="picture-card" :file-list="fileList" :auto-upload="false" :multiple="false" :data="itemMaster.itemForm" :on-change="onchange">
              <i class="el-icon-plus"></i>
            </el-upload>
            <!-- <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="selectRow.imageUrl" alt="">
          </el-dialog> -->
          </template>
        </vxe-form-item>

        <vxe-form-item align="center" span="24">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </el-dialog>

  </div>
</template>

<script>
import detailElemMixins from './detailElemCommon'
import { cloneDeep } from 'lodash'
import { mapState } from 'vuex'
import sorderEditState from '../sordereditstate'
export default {
  name: 'FuLiaoCmp',
  mixins: [detailElemMixins, sorderEditState],
  props: {},
  //   watch: {
  //     elemItem: {
  //       deep: true,
  //       handler: (newVal, oldVal) => {
  //         this.fuliaoData = cloneDeep(newVal)
  //       }
  //     }
  //   },

  data () {
    return {
      zbflModal: false,
      options: [],
      zbflForm: {
        input: this.elemItem.input,
        qty: this.elemItem.qty
      },
      zbflFormRules: {
        input: [{ required: true, message: '请输入客供辅料名称' }]
      },
      fileList: [],
      mtmapi: process.env.VUE_APP_API,
      itemMaster: {
        show: false,
        itemForm: {
          itemGroupID: null,
          businessGroup: null,
          originalItemNo: null,
          remark: null,
          userID: null,
          file: null
        },
        formRules: {
          codeName: [{ required: true, message: '请填写名称' }],
          itemGroupID: [{ required: true, message: '请选择物料分类' }],
          businessGroup: [{ required: true, message: '请选择业务归属' }],
          originalItemNo: [{ required: true, message: '请输入原始货号' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
        }
      },
      optionsLoading: false,
      fuliaoData: {},
      api: {
        addItem: 'mtm/odm_sorder/AddItemFL',
        ItemComboStoreByQuery: '/mtm/comboQuery/itemComboStoreByQuery',
        ItemFLGroupComboStore: '/mtm/combo/ItemFLGroupComboStore',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore',
        TechnologyGroupComboStore: '/mtm/combo/TechnologyGroupComboStore',
        ItemTextureFLGroupComboStore: '/mtm/combo/ItemTextureFLGroupComboStore'
      },
      ItemFLGroupComboStore: [],
      BusinessGroupComboStore: [],
      TechnologyGroupComboStore: [],
      ItemTextureFLGroupComboStore: []
    }
  },
  watch: {
    elemItem: {
      deep: true,
      handler (newval, oldVal) {
        this.fuliaoData = cloneDeep(newval)
      }
    },
    'fuliaoData.itemID': {
      deep: true,
      handler (newval, oldVal) {
        if (newval !== null) {
          var b = this.options.ElementExist('value', newval)
          if (!b) {
            this.get(newval, this.fuliaoData.modelElemID)
          }
        }
      }
    },
    'fuliaoData.isItemAdd': {
      deep: true,
      async handler (newval, oldVal) {
        if (newval) {
          await this.getCombStore()
        }
      }

    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  async created () {
    this.fuliaoData = cloneDeep(this.elemItem)
    if (this.elemItem.itemID) {
      this.get(this.elemItem.itemID, this.elemItem.modelElemID)
    }
    this.itemMaster.itemForm.userID = this.info.userid
    if (this.elemItem.isItemAdd) {
      await this.getCombStore()
    }
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemTextureFLGroupComboStore).then(result => {
        this.ItemTextureFLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemFLGroupComboStore).then(result => {
        this.ItemFLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.TechnologyGroupComboStore).then(result => {
        this.TechnologyGroupComboStore = result
      })
    },
    async itemSubmitEvent (formName) {
      var upload = this.$refs.upload
      this.$refs[formName].validate().then(async v => {
        console.log(upload.uploadFiles[0])
        var file = upload.uploadFiles[0]
        if (upload.uploadFiles.length === 0) {
          return
        }
        if (upload.uploadFiles.length > 1) {
          return
        }
        if (file.raw) {
          this.itemMaster.itemForm.file = file.raw
          await this.$api.ActionFileRequest(this.api.addItem, this.itemMaster.itemForm).then(async res => {
            this.$notify({
              message: '添加成功',
              type: 'success'
            })
            this.itemMaster.show = false
            await this.get(res, this.elemItem.modelElemID, '')
            await this.itemchange(res, this.fuliaoData)
          })
        }
      })
    },

    async itemSubmitEvent1 () {
      // await this.$api.ActionRequest(this.api.addItem, this.itemMaster.itemForm).then(async result => {
      //   this.$notify({
      //     message: '添加成功',
      //     type: 'success'
      //   })
      //   await this.getItembyQuery(this.itemMaster.itemForm.originalItemNo)
      //   this.ItemtableData.forEach(item => {
      //     if (item.originalItemNo.indexOf(this.itemMaster.itemForm.originalItemNo) >= 0) {
      //       this.itemCellClickEvent({ row: item })
      //     }
      //   })
      //   // var first = this.ItemtableData.GetFirstElement("originalItemNo", this.itemMaster.itemForm.originalItemNo)
      //   // if (first && first !== null) {
      //   //   console.log(first)
      //   //   this.itemCellClickEvent({ row: first })
      //   // }
      //   this.itemMaster.show = false
      //   this.$utils.clear(this.itemMaster.itemForm, null)
      //   this.itemMaster.itemForm.isActive = true
      //   this.itemMaster.itemForm.yearNo = new Date().getFullYear().toString().substring(2, 4)
      // })
    },
    CheckIsInputItemDisabled (item2) {
      console.log(item2)
      // let b = !item2.IsInputItem |
      const b = !item2.isInputItem
      // console.log(item2.isInputItem)
      return b
    },
    showPopover (item) {
      return true
    },
    itemClear () {
      this.$emit('itemDataChange', { Type: 'ClearItem', ModelElemID: this.elemItem.modelElemID })
    },
    itemchange (val, _modelitem) {
      if (!val) {
        return
      }
      if (val === null || val === '') {
        this.$emit('itemDataChange', { Type: 'ClearItem', ModelElemID: _modelitem.modelElemID, ModelElem: _modelitem })
      } else {
        var item = this.options.GetFirstElement('value', val)
        if (item) {
          if (item.code === 'KGFL') {
            this.zbflModal = true
            this.zbflForm.ModelElem = _modelitem
            this.zbflForm.Item = item
          } else {
            this.$emit('itemDataChange', { Type: 'Item', Item: item, Qty: _modelitem.qty, ModelElemID: _modelitem.modelElemID, ModelElem: _modelitem })
          }
        }
      }
      //   this.options = []
    },
    async zbflSave () {
      await this.$refs.zbflFormref.validate().then(async () => {
        this.$emit('itemDataChange', { Type: 'Input', Input: this.zbflForm.input, Qty: this.zbflForm.qty, Item: this.zbflForm.Item, ModelElemID: this.zbflForm.ModelElem.modelElemID })
        this.zbflModal = false
        this.zbflForm.qty = 0
        this.zbflForm.input = null
      }).catch(() => {
        this.$notify({
          title: '错误',
          message: '必填项不能为空',
          type: 'error'
        })
      })
    },
    async get (id, modelElemID, text) {
      await this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { Gid: id, Text: text, ModelElemID: modelElemID, clientID: this.sorderStore.clientID, itemClassID: 2 }).then(res => {
        this.options = res
      })
    },
    async querySearchAsync (queryString, modelelemid, index, item) {
      if (queryString == null || queryString.length < 2) {
        return
      }
      await this.get(this.elemItem.itemID, this.elemItem.modelElemID, queryString)
    },
    itemShowEvent () {
      this.itemMaster.show = true
    },
    handleClose (done) {
      this.itemMaster.show = false
      done()
    },
    onchange (file, fileList) {
      if (fileList.length > 1) {
        fileList.shift()
      }
    }
  }
}
</script>

<style lang="scss">
/**divtable**/
.div-table {
  width: 100%;
  margin: 0.5rem auto 0;
  .table-tr {
    overflow: hidden;
    /* border: 1px solid #bfbfbf; */
    border-top: 0;
    width: 700px;
    height: 100%;
  }
  .table-title {
    width: calc(100% - 17px);
    border-top: 1px solid #bfbfbf;
  }
  .v-table-title-cell > .table-title {
    border-top: 0;
  }
  .table-body {
    width: 100%;
    /* max-height: 5rem; */
    min-height: 1.5rem;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  .table-th,
  .table-td {
    /* text-align: center; */
    float: left;
    /* border-right: 1px solid #bfbfbf; */
    margin-right: -1px; /* 抵消右边框宽度 */
    // min-height: 0.3rem; /* 防止内容为空时缺失 */
    // line-height: 0.3rem;
    // padding-bottom: 999px;
    // margin-bottom: -999px;
    overflow: hidden;
  }
  .table-th span,
  .table-td span {
    /* display: block; */
    padding: 0 0.05rem;
    font-size: 13px;
  }
  .table-td1 {
    width: 360px;
  }
  .table-td2 {
    width: 210px;
    span {
      display: inline-block;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .table-td-image {
    width: 30px;
  }
  .table-td3 {
    width: 60px;
    span {
      display: inline-block;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .table-td4 {
    width: 60px;
    span {
      display: inline-block;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .table-td5 {
    width: 120px;
    span {
      display: inline-block;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
