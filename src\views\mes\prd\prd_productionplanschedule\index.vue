<template>
  <d2-container class="prd_productionplanschedule">
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <!-- <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template> -->
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="deliveryDates" :item-render="{}">
              <template #default>
                <el-date-picker v-model="searchForm.deliveryDates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="(交期)开始日期" end-placeholder="(交期)结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.groupID" filterable placeholder="类别" size="mini" clearable>
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <!-- <vxe-form-item field="worksecationID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.worksecationID" filterable placeholder="工段" size="mini" remote reserve-keyword plremoteaceholder="生产工段" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in WorkSecationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="productionTeamID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.productionTeamID" filterable placeholder="小组" size="mini" remote reserve-keyword plremoteaceholder="生产小组" :remote-method="remoteMethod2" clearable>
                  <el-option v-for="item in productionTeamComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="productionStationID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.productionStationID" filterable placeholder="工位" size="mini" remote reserve-keyword plremoteaceholder="生产工位" :remote-method="remoteMethod3" clearable>
                  <el-option v-for="item in productionStationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item> -->
            <vxe-form-item field="sorderTypes" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.sorderTypes" filterable placeholder="订单类型多选" multiple collapse-tags size="mini" clearable>
                  <el-option v-for="item in SorderTypeStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="workSecationIDs" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.workSecationIDs" filterable remote multiple reserve-keyword placeholder="工段多选" clearable :remote-method="remoteMethod1" size="mini">
                  <el-option v-for="item in WorkSecationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>

            <vxe-form-item field="productionTeamIDs" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.productionTeamIDs" filterable remote multiple reserve-keyword placeholder="小组多选" clearable :remote-method="remoteMethod2" size="mini">
                  <el-option v-for="item in ProductionTeamComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="productionStationIDs" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.productionStationIDs" filterable remote multiple reserve-keyword placeholder="工位多选" clearable :remote-method="remoteMethod3" size="mini">
                  <el-option v-for="item in ProductionStationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isWorckSectionPutOffed" :item-render="{}"><template #default>
                <vxe-checkbox v-model="searchForm.isWorckSectionPutOffed" content="工段延期"></vxe-checkbox>
              </template></vxe-form-item>
            <vxe-form-item field="isProductionTeamPutOffed" :item-render="{}"><template #default>
                <vxe-checkbox v-model="searchForm.isProductionTeamPutOffed" content="小组延期"></vxe-checkbox>
              </template></vxe-form-item>
            <!-- <vxe-form-item field="productionProcessesID:"><template #default>
              <el-select v-model="searchForm.productionProcessesID" filterable placeholder="工序" size="mini" remote reserve-keyword plremoteaceholder="生产工序" :remote-method="remoteMethod4" clearable>
                <el-option v-for="item in productionProcessesComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template></vxe-form-item> -->
            <vxe-form-item field="text" :item-render="{}"><template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template></vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PrdProductionplanscheduleMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-column type="expand" width="80">
        <template #content="{ row, rowIndex }">
          <div style="height:350px;">
            <detail :form="row" :key="rowIndex" />
          </div>
        </template>
      </vxe-column>
      <vxe-table-column field="productionPlanStateText" title="生产状态" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="sorderNum" title="订单号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="订单类型" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="customerNumber" title="客户订单号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable> </vxe-table-column>
      <vxe-table-column field="deliveryDate" title="订单交期" width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="120" sortable> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="productionPlanDetailStateText" title="状态" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="clientPersonName" title="顾客姓名" width="100"> </vxe-table-column>
      <vxe-table-column field="gender" title="性别" :formatter="val=>formatSex(val)" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="halfFitting" title="半成品试衣" :formatter="formatBool" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="finalTextureText" title="纹理" width="100"> </vxe-table-column>
      <vxe-table-column field="finalWidth" title="幅宽" width="100"> </vxe-table-column>
      <vxe-table-column field="itemName" title="面料号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="groupName" title="类别" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="modelName" title="版型" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="sizeCode" title="规格" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="productionPlanModifyOn" title="最后修改时间" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="120" sortable> </vxe-table-column>
      <vxe-table-column field="newSetWorkSecation" title="完成工段" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="newSetProductionTeam" title="完成小组" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="newSetProductionStation" title="完成工位" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="newSetProductionProcesses" title="完成工序" width="100" sortable> </vxe-table-column>

      <vxe-table-column field="newSetDateTime" title="最新完成时间" :formatter="val=>formatDate(val)" width="120" sortable> </vxe-table-column>
      <!-- <vxe-table-column field="isPutOffed" title="是否延期" width="100"> </vxe-table-column> -->
      <vxe-table-column title="延期工段" width="100" field='putOffWorkSecation' sortable>
        <template v-slot="{ row }">
          <span :style="row.isPutOffed?'color: red;':''">{{row.putOffWorkSecation}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="putOffWorkSecationDateTime" title="(工段)计划完成时间" :formatter="val=>formatDate(val)" width="120" sortable>
      </vxe-table-column>
      <vxe-table-column title="延期小组" width="100" field='putOfftProductionTeam' sortable>
        <template v-slot="{ row }">
          <span :style="row.isPutOffed?'color: red;':''">{{row.putOfftProductionTeam}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="putOffProductionTeamDateTime" title="（小组）计划完成时间" :formatter="val=>formatDate(val)" width="120" sortable>
      </vxe-table-column>
      <vxe-table-column field="putOffProductionTeamText" title="延期时间" width="100" sortable>
        <template v-slot="{ row }">
          <span :style="row.isPutOffed?'color: red;':''">{{(row.putOffProductionTeamText)}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px" sortable> </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>

  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import Detail from './detail.vue'
export default {
  name: 'prd_productionplanschedule',
  mixins: [masterTableMixins],
  components: {
    Detail
  },
  data () {
    return {
      searchForm: {
        isWorckSectionPutOffed: true,
        isProductionTeamPutOffed: null,
        worksecationID: null,
        productionTeamID: null,
        groupID: null,
        productionStationID: null,
        deliveryDates: [],
        workSecationIDs: [],
        productionTeamIDs: [],
        productionStationIDs: [],
        sorderTypes: []

      },
      api: {
        get: '/mes/prd_productionplandetail/get',
        WorkSecationComboStoreByQuery: '/mes/comboQuery/WorkSecationComboStoreByQuery',
        ProductionTeamComboStoreByQuery: '/mes/comboQuery/ProductionTeamComboStoreByQuery',
        ProductionStationComboStoreByQuery: '/mes/comboQuery/ProductionStationComboStoreByQuery',
        ProductionProcessesComboStoreByQuery: '/mes/comboQuery/ProductionProcessesComboStoreByQuery',
        GroupComboStore: '/mtm/combo/groupComboStore',
        SorderTypeStore: '/mes/combo/SorderTypeStore'
        // add: '/mes/prd_productionplanschedule/adds',
        // edit: '/mes/prd_productionplanschedule/updates',
        // delete: '/mes/prd_productionplanschedule/deletes'
      },
      GroupComboStore: [],
      SorderTypeStore: [],
      WorkSecationComboStoreByQuery: [],
      ProductionTeamComboStoreByQuery: [],
      ProductionStationComboStoreByQuery: [],
      ProductionProcessesComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SorderTypeStore).then(result => {
        this.SorderTypeStore = result
      })
      await this.$api.ActionRequest(this.api.WorkSecationComboStoreByQuery).then(result => {
        this.WorkSecationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ProductionTeamComboStoreByQuery).then(result => {
        this.ProductionTeamComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ProductionStationComboStoreByQuery).then(result => {
        this.ProductionStationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ProductionProcessesComboStoreByQuery).then(result => {
        this.ProductionProcessesComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    tableCellClick ({ row }) {
      // console.log(row)
      const xTable = this.$refs[this.tableRef]
      xTable.toggleRowExpand(row)
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.WorkSecationComboStoreByQuery, { text: query }).then(result => {
        this.WorkSecationComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ProductionTeamComboStoreByQuery, { text: query }).then(result => {
        this.ProductionTeamComboStoreByQuery = result
      })
    },
    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.ProductionStationComboStoreByQuery, { text: query }).then(result => {
        this.ProductionStationComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.ProductionProcessesComboStoreByQuery, { text: query }).then(result => {
        this.ProductionProcessesComboStoreByQuery = result
      })
    }
  }
}
</script>

<style lang="scss" >
.prd_productionplanschedule {
  .vxe-toolbar.size--mini.is--perfect {
    height: auto;
  }
  .is--perfect {
    // .vxe-toolbar.size--mini {
    //   height: auto;
    // }
    .vxe-tools--wrapper {
      width: 97%;
    }
  }
}
</style>
