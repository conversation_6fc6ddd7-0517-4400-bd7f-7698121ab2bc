<template>
  <transition>
    <keep-alive v-if="keepalive">
      <component :is="activepage" :form="masterfrom" @nextpage="nextpage" :masterSeach="masterSeach" />
    </keep-alive>
    <template v-else>
      <component :is="activepage" :form="masterfrom" @nextpage="nextpage" :masterSeach="masterSeach" />
    </template>
  </transition>

</template>

<script>
import master from './master.vue'
import detail from './detail.vue'
export default {
  name: 'odm_sorderbill',
  components: {
    master,
    detail
  },
  data () {
    return {
      activepage: 'master',
      keepalive: true,
      masterfrom: null,
      masterSeach: null
    }
  },
  methods: {
    nextpage (item) {
      this.activepage = item.pagename
      this.masterfrom = item.data
      this.masterSeach = item.masterSeach
      if (item.keepalive) {
        this.keepalive = item.keepalive
      } else {
        this.keepalive = false
      }
    }
  }
}
</script>
