<template>
  <d2-container class="PrdProductionplanDetail">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-print" status="success" v-if="menuAction.allowAdd" @click="printEvent">打印票头</vxe-button>
          <!-- <vxe-button icon="fa fa-print" status="success" v-if="menuAction.allowAdd">打印水洗唛</vxe-button> -->
          <!-- <vxe-button icon="fa fa-print" status="info" v-if="menuAction.allowAdd" @click="showPrintWashedLabel=!showPrintWashedLabel">打印水洗唛</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="productionPlanState" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.productionPlanState" filterable placeholder="订单状态" size="mini" clearable>
                  <el-option v-for="item in productionPlanStateComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="Dates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isPrintLabeled" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.isPrintLabeled" size="mini" placeholder="是否已打印" clearable>
                  <el-option v-for="item in isPrintLabeleds" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" size="mini" clearable>
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <template>
      <vxe-table id='PrdProductionplandetailDetailTable' ref='master_table' height="auto" @cell-click='tableCellClick' :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}" :loading="tableLoading">
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <vxe-table-column field="productionPlanStateText" title="订单状态" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="isPrintLabeled" :formatter='formatBool' title="票头打印" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="sorderNum" title="订单号" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="serialNumber" title="流水号" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="customerNumber" title="客户订单号" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="itemName" title="面料号" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="groupName" title="类别" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="modelCode" title="版型编码" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="modelName" title="版型" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="sizeCode" title="规格" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="remark" title="备注" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
        <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
        <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
        <vxe-table-column title="操作" width="50" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
          <template v-slot="{ row }">
            <vxe-button type="text" status="success" icon="fa fa-play-circle" :key="row.id" v-if="menuAction.allowEdit&&!row.isPrintLabeled" @click="update(row)"></vxe-button>
          </template>
        </vxe-table-column>
      </vxe-table>
      <template slot="footer">
        <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
          <template v-slot:right>
            <mtm-footer-companyinfo v-if="footerCompanyInfo" />
          </template>
        </vxe-pager>
      </template>

      <vxe-modal v-model="showPrintInfo" title='打印票头' width="800" height="500" :position="{top:200}" resize destroy-on-close>
        <print-info v-if='showPrintInfo' :selectRow="selectRow" @updateState="updateState" />
      </vxe-modal>
      <!-- <vxe-modal v-model="showPrintWashedLabel" title='水洗唛' width="800" height="500" :position="{top:200}" resize destroy-on-close>
        <print-washed-label />
      </vxe-modal> -->
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
import PrintInfo from './components/printinfo.vue'
// import PrintWashedLabel from './components/printwashedlabel'
export default {
  name: 'prd_printlabel',
  mixins: [detailTableMixins],
  components: {
    PrintInfo
    // PrintWashedLabel
  },
  data() {
    return {
      formData: {
        remark: '',
        isActive: true

      },
      isPrintLabeleds: [
        { value: true, label: '是' },
        { value: false, label: '否' }
      ],
      searchForm: {
        productionPlanState: 8,
        isPrintLabeled: false
      },
      activeName: 'productPlanSchedule',
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      showPrintInfo: false,
      showPrintWashedLabel: false,
      api: {
        mespai: window.VUE_APP_MESAPI || process.env.VUE_APP_MESAPI,
        get: '/mes/prd_productionplandetail/get',
        add: '/mes/prd_productionplandetail/adds',
        edit: '/mes/prd_productionplandetail/updates',
        delete: '/mes/prd_productionplandetail/deletes',
        productionplanlabel: '/fs/print/productionplanlabel/pdf',
        GroupComboStore: '/mtm/combo/groupComboStore',
        productionPlanStateComboStore: '/mes/combo/productionPlanStateComboStore',
        SorderTypeStore: '/mes/combo/SorderTypeStore',
        ClientPersonComboStoreByQuery: '/mtm/comboQuery/ClientPersonComboStoreByQuery'
      },
      GroupComboStore: [],
      productionPlanStateComboStore: [],
      ClientPersonComboStoreByQuery: [],
      SorderTypeStore: []
    }
  },
  async created() {
    await this.getCombStore()
    this.loadData({ productionPlanState: 8, isPlanning: true }).then(({ data }) => {

    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore() {
      await this.$api.ActionRequest(this.api.productionPlanStateComboStore).then(result => {
        this.productionPlanStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SorderTypeStore).then(result => {
        this.SorderTypeStore = result
      })
      await this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
    },
    showPrintInfoEvent() {
      var b = this.$utils.isEmpty(this.selectRow)
      if (b) {
        this.$message({ type: 'error', message: '请勾选数据' })
        return
      }
      this.showPrintInfo = !this.showPrintInfo
    },
    async printEvent() {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$message({ type: 'error', message: '请勾选数据' })
        return
      }
      const type = await this.$XModal.confirm({ message: '是否修改票头打印状态', cancelButtonText: '否', confirmButtonText: '是' })
      var b = false
      if (type === 'confirm') {
        b = true
        this.reload()
      }
      var ids = rows.map(row => { return row.id })
      var api = this.api.mespai.replace('/api/', '') + this.api.productionplanlabel
      console.log(api)
      this.openPostWindow1(api, ids, b, '_blank')
    },
    // 参数列表，怎么写都行。主要要有url。
    openPostWindow1(url, datas, changeState, targetType) {
      var tempForm = document.createElement('form')
      tempForm.setAttribute('id', 'tempForm1')
      tempForm.setAttribute('style', 'display:none')
      tempForm.setAttribute('target', targetType)
      tempForm.setAttribute('method', 'post')
      tempForm.setAttribute('action', url)
      datas.forEach(item => {
        var a = document.createElement('input')
        a.setAttribute('type', 'hidden')
        a.setAttribute('name', 'iDs')
        a.setAttribute('value', item)
        tempForm.append(a)
      })
      tempForm.append(this.createElem(changeState))
      document.body.append(tempForm)
      tempForm.submit()
    },
    async update(row) {
      row.isPrintLabeled = true
      await this.$api.ActionRequest(this.api.edit, [row]).then(result => {
        this.loadData()
      })
    },
    reload() {
      setTimeout(() => {
        // 3秒后重新加载数据
        this.loadData()
      }, 1000)
    },
    createElem(changeState) {
      var a = document.createElement('input')
      a.setAttribute('type', 'hidden')
      a.setAttribute('name', 'changeState')
      a.setAttribute('value', changeState)
      return a
    },
    updateState() {
      this.showPrintInfo = !this.showPrintInfo
      this.update(this.selectRow)
    },
    tableCellClick({ row, rowIndex, column, columnIndex }) {
      if (column.title === '操作') { return }
      this.drawer = true
      this.selectRow = cloneDeep(row)
      this.$refs.master_table.setRadioRow(row)
    }
  }
}
</script>

<style lang="scss" >
.PrdProductionplanDetail {
  .el-tabs__content {
    height: calc(100vh - 212px);
  }
}
</style>
