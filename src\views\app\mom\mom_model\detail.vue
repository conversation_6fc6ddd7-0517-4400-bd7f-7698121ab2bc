<template>
  <d2-container class="detailmodel">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowEdit">保存</vxe-button> -->
        </template>
        <!-- <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
        </template> -->
      </vxe-toolbar>
    </template>
    <el-tabs type="border-card" v-model="activeName" style="height:99%">
      <el-tab-pane label="款式\元素\工艺" name="detailmodelelem">
        <detail-model-elem :form="form" />
      </el-tab-pane>
      <!-- <el-tab-pane v-if="form.businessSubType!==0" label="物料数据" name="detailplushmodel">
        <plus-model :form="form" />
      </el-tab-pane> -->
      <el-tab-pane v-if="form.businessSubType!==0" label="工艺设计" name="detailplushmodel">
        <design-model-elem :form="form" />
      </el-tab-pane>
      <el-tab-pane label="规格" name="detailmodelsizecolumn">
        <model-size-column :form="form" />
      </el-tab-pane>
      <el-tab-pane label="特体" name="detailmodelbodylist">
        <model-body-list :form="form" />
      </el-tab-pane>
      <el-tab-pane label="关联客户" name="detailmodelclient">
        <model-client :form="form" />
      </el-tab-pane>

    </el-tabs>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import detailModelElem from './detail_modelelem'
import ModelSizeColumn from './detail_modelsizecolumn'
import ModelBodyList from './detail_modelbodylist'
import ModelClient from './detail_modelclient'
// import PlusModel from './detail_plushmodel'
import DesignModelElem from './components/designmodelelem.vue'
export default {
  name: 'MomModelDetail',
  mixins: [detailTableMixins],
  components: {
    detailModelElem,
    ModelSizeColumn,
    ModelBodyList,
    ModelClient,
    // PlusModel,
    DesignModelElem
  },
  props: {
    masterSeach: {
      type: Object
    }
  },
  data () {
    return {
      activeName: 'detailmodelelem',
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codename', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch ' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_model/get',
        add: '/mtm/mom_model/adds',
        edit: '/mtm/mom_model/updates',
        delete: '/mtm/mom_model/deletes'
      }
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    },
    goback () {
      this.$emit('nextpage', { pagename: 'master', data: {}, masterSeach: this.masterSeach, keepalive: true })
    }
  }
}
</script>

<style lang="scss" >
.detailmodel {
  .el-tabs__content {
    height: 92%;
  }
  .d2-container-full__body {
    overflow-x: hidden !important;
  }
  //处理vxe table 头和数据  上下不一致问题
  .vxe-cell {
    margin: 1px !important;
    // background-color: red;
  }
}
</style>
