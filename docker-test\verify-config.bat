@echo off
setlocal enabledelayedexpansion
echo === Configuration Verification Test ===

REM Clean up
docker stop verify-test >nul 2>&1
docker rm verify-test >nul 2>&1

REM Build and start with VERY obvious custom values
cd ..
echo Building image...
docker build -t verify-test:latest .

echo Starting container with OBVIOUS custom values...
docker run -d --name verify-test -p 8080:80 ^
  -e "VUE_APP_USER_API=https://OBVIOUS-SSO-CHANGED.example.com/api/" ^
  -e "VUE_APP_API=https://OBVIOUS-MTM-CHANGED.example.com/api/" ^
  -e "VUE_APP_MESAPI=https://OBVIOUS-MES-CHANGED.example.com/api/" ^
  -e "VUE_APP_TITLE=OBVIOUS-TITLE-CHANGED" ^
  verify-test:latest

timeout /t 5 /nobreak >nul

echo.
echo === Verification Results ===

echo 1. Container logs:
docker logs verify-test | findstr -i "obvious\|changed\|VUE_APP"

echo.
echo 2. Generated config.js content:
docker exec verify-test cat /usr/share/nginx/html/config.js | findstr -i "obvious\|changed"

echo.
echo 3. HTTP response test:
curl -s http://localhost:8080/config.js | findstr -i "obvious\|changed"

echo.
echo 4. Index.html content check:
curl -s http://localhost:8080 | findstr -i "obvious\|changed"

echo.
echo === Easy Visual Test ===
echo 1. Open: http://localhost:8080/config-test.html
echo 2. This page will show you EXACTLY what configuration is loaded
echo 3. Look for GREEN items with "OBVIOUS-*-CHANGED" - that means it's working!
echo 4. If you see YELLOW items with "kngintl.com" - configuration is not working
echo.
echo === Manual Browser Test ===
echo 5. Open: http://localhost:8080 ^(main app^)
echo 6. Press F12 ^(Developer Tools^)
echo 7. Go to Console tab
echo 8. Run this command:
echo    console.log('Config Test:', window.VUE_APP_API)
echo.
echo 9. Expected result: https://OBVIOUS-MTM-CHANGED.example.com/api/
echo 10. If you see the old URL, configuration is NOT working
echo 11. If you see OBVIOUS-MTM-CHANGED, configuration IS working!
echo.

echo === Network Request Test ===
echo 8. Go to Network tab in Developer Tools
echo 9. Refresh the page
echo 10. Look for any API requests
echo 11. Check if they use OBVIOUS-MTM-CHANGED.example.com
echo.

echo === Additional Debug Commands ===
echo Run these in browser console for more info:
echo console.log('All VUE_APP vars:', Object.keys(window).filter(k =^> k.includes('VUE_APP')))
echo console.log('RUNTIME_CONFIG:', window.RUNTIME_CONFIG)
echo console.log('CONFIG_LOADED:', window.CONFIG_LOADED)
echo.

echo Cleanup: docker stop verify-test ^&^& docker rm verify-test
pause
