<template>
  <div class="sorderdeatilmodel">
    <vxe-form element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)" :data="sorderDetailModelForm" :rules="sorderDetailModelFormRules" class="sorderDetailModelForm" title-width="100" ref="sorderDetailModelForm">
      <vxe-form-item title="版型" span="10" :item-render="{}"><template #default>
          <el-input placeholder="点击左侧选择版型" v-model="sorderDetailModelForm.modelText" :disabled='true' class="input-with-select" size="mini">
            <template slot="append">
              <el-button type="primary" @click="changeModelClick" style="background-color:#1E90FF;color: #ffffff" size="mini" :disabled="EditState">
                选择版型
              </el-button>
              <el-button type="primary" @click="selfDesignClick" style="background-color:#1E90FF;color: #ffffff" size="mini" :disabled="EditState">
                自主设计
              </el-button>
              <!-- <el-dropdown-menu slot="dropdown">
              <el-dropdown-item icon="el-icon-circle-plus" command="selfDesign">自主设计</el-dropdown-item>
            </el-dropdown-menu> -->
            </template>
          </el-input>
        </template></vxe-form-item>
      <vxe-form-item title="客户尺码" field="customerSize" :item-render="{}"><template #default>
          <vxe-input v-model="sorderDetailModelForm.customerSize" placeholder="客户尺码" clearable :disabled="EditState"></vxe-input>
        </template></vxe-form-item>
      <vxe-form-item title="长短款" field="designStyleID" :item-render="{}"><template #default>
          <vxe-select v-model="sorderDetailModelForm.designStyleID" placeholder="长款/短款" clearable :disabled="EditState">
            <vxe-option value="2" label="长款"></vxe-option>
            <vxe-option value="1" label="短款"></vxe-option>
          </vxe-select>
        </template></vxe-form-item>
      <!-- <vxe-form-item field="frontBowOrBackUp" title="前弓体/后仰体" :item-render="{}"><template #default>
        <template>
          <vxe-select v-model="sorderDetailModelForm.frontBowOrBackUp" placeholder="前弓体/后仰体" @change="frontBowOrBackUpChange" clearable :disabled="EditState">
            <vxe-option value="1" label="前弓体"></vxe-option>
            <vxe-option value="2" label="后仰体"></vxe-option>
          </vxe-select>
        </template>
      </template></vxe-form-item> -->
      <vxe-form-item title="数量" field="qty" :item-render="{}"><template #default>
          <vxe-input v-model="sorderDetailModelForm.qty" type="number" placeholder="请输入数量" clearable :disabled="EditState"></vxe-input>
        </template></vxe-form-item>
      <vxe-form-item title="备注" field="remark" span="12" :item-render="{}"><template #default>
          <vxe-textarea v-model="sorderDetailModelForm.remark" placeholder="备注" min="5" :disabled="EditState"></vxe-textarea>
        </template></vxe-form-item>
    </vxe-form>
    <el-tabs v-model="detailactiveName" type="border-card">
      <el-tab-pane name="sordermodelelem">
        <span slot="label"><i :class="sorderDetailModelForm.isCheckedElem?'el-icon-success model-check-success':'el-icon-warning model-check-warning'"></i> 版型图片</span>
      </el-tab-pane>
      <el-tab-pane label="规格/特体" name="sordermodelsize" class="sordermodelsize">
        <span slot="label"><i :class="sorderDetailModelForm.isChecked?'el-icon-success model-check-success':'el-icon-warning model-check-warning'"></i> 规格/特体</span>
        <d2-container type="ghost">
          <split-pane :default-percent='65' split="vertical">
            <template slot="paneL">
              <sorder-detail-size :SorderDetailModel="sorderDetailModelForm" :sorderDetailBodys="getBodyListData()" :sorderStore="sorderStore" :height='sorderStore.height' ref="sorderDetailSizeRef" />
            </template>
            <template slot="paneR">
              <sorder-detail-body :SorderDetailModel="sorderDetailModel" ref="sorderDetailBodyRef" :sorderStore="sorderStore" />
            </template>
          </split-pane>
        </d2-container>

      </el-tab-pane>
    </el-tabs>
    <vxe-modal v-model="selectModel" title="版型选择" width="60%" height="50%" resize remember show-zoom>
      <vxe-grid id="selectModelModal" auto-resize border resizable height="auto" align="left" :loading="selectModelLoading" :columns="modelTableColumn" :toolbar="{slots: {buttons: 'toolbar_buttons'}}" :data="modelTableData" :seq-config="{startIndex: (modelTablePage.currentPage - 1) * modelTablePage.pageSize}" :pager-config="modelTablePage" @page-change="modelHandlePageChange" :custom-config="{storage: true}" @cell-dblclick="selectModelClick">
        <template v-slot:toolbar_buttons>
          <vxe-toolbar perfect>
            <template v-slot:tools>
              <vxe-form ref="xForm" :data="searchModel" @submit="modelSearchEvent()" @reset="resetEvent">
                <vxe-form-item field="modelBaseID" :item-render="{}"><template #default>
                    <vxe-select v-model="searchModel.modelBaseID" placeholder="基本版型" clearable>
                      <vxe-option v-for="num in ModelBaseComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                    </vxe-select>
                  </template></vxe-form-item>
                <vxe-form-item field="groupID" :item-render="{}"><template #default>
                    <vxe-select v-model="searchModel.groupID" placeholder="类别" clearable>
                      <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                    </vxe-select>
                  </template></vxe-form-item>
                <vxe-form-item field="genderID" :item-render="{}"><template #default>
                    <vxe-select v-model="searchModel.genderID" placeholder="性别" clearable>
                      <vxe-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                    </vxe-select>
                  </template></vxe-form-item>
                <!-- <vxe-form-item field="sizeListID" :item-render="{}"><template #default>
                    <vxe-select v-model="searchForm.sizeListID" placeholder="规格单" clearable>
                      <vxe-option v-for="num in SizeListComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                    </vxe-select>
                  </template></vxe-form-item>
                  <vxe-form-item field="sewBaseID" :item-render="{}"><template #default>
                    <vxe-select v-model="searchForm.sewBaseID" placeholder="缝份类别" clearable>
                      <vxe-option v-for="num in SewBaseComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                    </vxe-select>
                  </template></vxe-form-item> -->
                <vxe-form-item field="text" :item-render="{}"><template #default>
                    <vxe-input v-model.trim="searchModel.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
                  </template></vxe-form-item>
                <vxe-form-item :item-render="{}"><template #default>
                    <vxe-button type="submit" status="success">查询</vxe-button>
                    <vxe-button type="reset">重置</vxe-button>
                  </template></vxe-form-item>
              </vxe-form>
            </template>
          </vxe-toolbar>
        </template>
        <template v-slot:operation="{ row }">

          <template v-if="row.imagePath!==null&&row.imagePath!==''">
            <el-popover placement="right-end" :title="row.code" width="400" trigger="hover">
              <el-image style="width: 400px; height: 400px" :src="row.imagePath" fit="scale-down"></el-image>
              <div slot="reference">
                <span>{{ row.code }}</span><i v-if="row.imagePath!==null&&row.imagePath!==''" style="color:#606266" class="el-icon-picture-outline"></i>
              </div>
            </el-popover>
          </template>
          <template v-else>
            <el-tooltip :content="row.code" placement="top-start">
              <div>
                <span> {{row.code}}</span>
              </div>
            </el-tooltip>
          </template>

        </template>
      </vxe-grid>
    </vxe-modal>
    <!-- <vxe-modal v-model="selfDesignShow" title="自主设计" width="80%" height="80%" resize remember destroy-on-close>
      <self-design @selectModel="setModel" @selfDesignShowEvent="selfDesignShowEvent" />
    </vxe-modal> -->
  </div>
</template>

<script>

// import SelfDesign from './components/selfdesign'
import XEUtils from 'xe-utils'
import sorderEditState from './sordereditstate'
import SorderDetailSize from './sorderdetailsize'
import SorderDetailBody from './sorderdetailbody'
import { mapState } from 'vuex'
export default {
  name: 'OdmSorderDetailModel',
  mixins: [sorderEditState],
  components:
  {
    SorderDetailSize,
    SorderDetailBody
    // SelfDesign
  },
  data () {
    return {
      selfDesignShow: false,
      sorderDetailModelForm: {
        id: this.sorderDetailModel.id, //
        qty: '1', //
        frontBowOrBackUp: null,
        isFrontBow: false, //
        isBackUp: false, //
        customerSize: null, //
        sorderDetailID: this.sorderDetailModel.sorderDetailID, //
        modelID: this.sorderDetailModel.modelID, //
        remark: null, //
        designStyleID: null, //
        modelText: null,
        sorderSizeTypeID: '3',
        sizeID: null,
        sizeID1: null,
        isChecked: false,
        isCheckedElem: false,
        message: null,
        messageElem: null,
        isRuleSize: this.sorderDetailModel.isRuleSize// 算法支持
      },
      sorderDetailModelFormRules: {
        modelText: [{ required: true, message: '请选择版型' }]
      },
      detailactiveName: 'sordermodelelem',
      selectModel: false,
      selectModelLoading: false,
      modelTableColumn: [
        { type: 'seq', width: 50 },
        { field: 'businessSubTypeText', title: '业务类型', width: 50 },
        { field: 'modelGroupText', title: '版型系列', showHeaderOverflow: true, width: 100 },
        { field: 'groupText', title: '类别', showOverflow: true, width: 100 },
        { field: 'genderText', title: '性别', showOverflow: true, width: 100 },
        { field: 'code', title: '版型编码', showOverflow: true, width: 100, slots: { default: 'operation' } },
        // { field: 'shortName', title: '版型简称', showOverflow: true, width: 100 },
        { field: 'codeName', title: '款式描述', showOverflow: true, width: 100 },
        { field: 'sizeListText', title: '规格单', showOverflow: true, width: 100 },
        { field: 'sewBaseText', title: '缝份列表', showOverflow: true, width: 100 },
        { field: 'isRuleSize', title: '支持算法', showOverflow: true, width: 100, formatter: ({ row }) => { return row.isRuleSize ? '支持' : '不支持' } },
        { field: 'remark', title: '备注', showOverflow: true, width: 100 }
      ],
      modelTableData: [],
      modelTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        align: 'right',
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
        perfect: true
      },
      searchModel: {
        text: null,
        modelBaseID: null,
        groupID: null,
        genderID: null,
        currentPage: null,
        skipCount: null
      },
      api: {
        edit: '/mtm/oDM_SorderDetailModel/ModifyDetailModel',
        getModels: '/mtm/oDM_SorderDetailModel/getModels',
        modelBaseComboStore: '/mtm/combo/modelBaseComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore'
      },
      ModelBaseComboStore: [],
      GroupComboStore: [],
      sexList: [
        { label: '女', value: false },
        { label: '男', value: true }
      ]
    }
  },
  props: {
    sorderStore: {
      type: Object,
      default: null
    },
    sorderDetailModel: {
      type: Object,
      default: null
    },
    sorderDetailModelID: {
      type: String,
      default: null
    },
    getModel: {
      type: Function
    },
    tableName: {
      string: String,
      default: null
    },
    editableTabsValue: {
      string: String,
      default: null
    }
  },
  watch: {
    sorderDetailModel: {
      handler (newVal, oldVal) {
        this.sorderDetailModelForm = XEUtils.assign(this.sorderDetailModelForm, this.sorderDetailModel)
        if (this.sorderDetailModel.isFrontBow) {
          this.sorderDetailModelForm.frontBowOrBackUp = '1'
        }
        if (this.sorderDetailModel.isFrontBow) {
          this.sorderDetailModelForm.frontBowOrBackUp = '2'
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  async created () {
    await this.getCombStore()
    if (this.sorderStore) {
      this.sorderDetailModelForm.sorderDetailID = this.sorderStore.sorderDetailID
    }
    if (this.sorderDetailModel !== null) {
      // this.sorderDetailModelForm = Object.assign(this.sorderDetailModelForm, this.sorderDetailModel)
      this.sorderDetailModelForm = XEUtils.assign(this.sorderDetailModelForm, this.sorderDetailModel)
      if (this.sorderDetailModel.isFrontBow) {
        this.sorderDetailModelForm.frontBowOrBackUp = '1'
      }
      if (this.sorderDetailModel.isFrontBow) {
        this.sorderDetailModelForm.frontBowOrBackUp = '2'
      }
    }
  },
  methods: {
    getBodyListData () {
      var body = this.$refs.sorderDetailBodyRef
      if (body && body.BodyListData.length > 0) {
        return body.BodyListData.filter(item => { return item.bodyID != null })
      } else {
        return []
      }
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.modelBaseComboStore).then(result => {
        this.ModelBaseComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    frontBowOrBackUpChange ({ value }) {
      if (value === '1') {
        this.sorderDetailModelForm.isFrontBow = true
        this.sorderDetailModelForm.isBackUp = false
      } else if (value === '2') {
        this.sorderDetailModelForm.isFrontBow = false
        this.sorderDetailModelForm.isBackUp = true
      } else {
        this.sorderDetailModelForm.isFrontBow = false
        this.sorderDetailModelForm.isBackUp = false
      }
    },
    // get(sorderDetailModelID) {
    //   this.$api.ActionRequest(this.api.get, { id: sorderDetailModelID }).then(result => {
    //     this.sorderDetailModelForm = Object.assign(this.sorderDetailModelForm, result)
    //   })
    // },
    changeModelClick () {
      this.modelFindList()
      this.selectModel = !this.selectModel
    },
    selfDesignClick () {
      this.selfDesignShow = true
      // this.$message('click on item ' + command)
    },
    selfDesignShowEvent () {
      this.selfDesignShow = !this.selfDesignShow
    },
    async modelHandlePageChange ({ currentPage, pageSize }) {
      this.modelTablePage.currentPage = currentPage
      this.modelTablePage.pageSize = pageSize
      await this.modelFindList()
    },
    async modelSearchEvent () {
      this.modelTablePage.currentPage = 1
      await this.modelFindList()
    },
    async modelFindList () {
      this.selectModelLoading = true
      this.searchModel.currentPage = this.modelTablePage.currentPage
      this.searchModel.skipCount = (this.modelTablePage.currentPage - 1) * this.modelTablePage.pageSize
      await this.$api.ActionRequest(this.api.getModels, this.searchModel).then(result => {
        this.modelTableData = result.items
        this.modelTablePage.total = result.totalCount
        this.selectModelLoading = false
      })
    },
    async resetEvent () {
      await this.modelFindList()
    },
    async getSorderDetailModel () {
      var sorderform = this.$refs.sorderDetailModelForm
      return await sorderform.validate().then(async res => {
        return this.sorderDetailModelForm
      }).catch(() => {
        this.$notify({
          title: '版型信息验证失败',
          message: `【${this.sorderDetailModel.title}】中版型信息必填项不能为空`,
          type: 'error'
        })
        return null
      })
    },
    // 获取规格数据
    async getSorderDetailSizeData () {
      return await this.$refs.sorderDetailSizeRef.DetailSizeData
    },
    // 特体数据
    async getSorderDetailBodyData () {
      var data = await this.$refs.sorderDetailBodyRef.BodyListData
      var arr = data.filter(item => { return item.bodyID !== null })
      return arr
    },
    // 款式明细
    async getSorderDetailElemData () {
      var data = await this.$refs.sorderDetailElemRef.SorderDetailElemData
      var arr = data.filter(item => { return item.modelElemID !== null })
      return arr
    },
    selectModelClick ({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
      if (this.checkModelGroup(row)) {
        return
      }
      this.selectModel = !this.selectModel
      this.setModel(row)
    },
    checkModelGroup (row) {
      var any = this.sorderStore.sorderDetailModels.filter(item => item.title === row.groupText && item.groupID === row.groupID)
      if (any.length > 0 && this.editableTabsValue !== row.groupText) {
        this.$message.error('不能添加相同版型')
        return true
      } else {
        return false
      }
    },
    setModel (row) {
      this.sorderDetailModel.modelID = row.id
      this.sorderDetailModel.modelText = row.code + ':' + row.codeName
      this.sorderDetailModel.isRuleSize = row.isRuleSize
      if (!row.isRuleSize) {
        this.sorderDetailModel.sorderSizeTypeID = '5'
      } else {
        this.sorderDetailModel.sorderSizeTypeID = '3'
      }
      row.title = this.editableTabsValue
      this.$emit('getModel', row)
    }
  }
}
</script>

<style lang="scss">
.sorderdeatilmodel {
  .model-search.vxe-input {
    height: 34px;
    width: 300px;
  }
  .model-check-warning {
    color: #e6a23c;
  }
  .model-check-success {
    color: #67c23a;
  }
  .model-search.vxe-input > .vxe-input--suffix {
    width: 60px;
    height: 32px;
    top: 1px;
    text-align: center;
    border-left: 1px solid #dcdfe6;
    background-color: #f5f7fa;
    cursor: pointer;
  }
  .model-search.vxe-input > .vxe-input--inner {
    padding-right: 72px;
    border: 1px solid #dcdfe6;
  }
  .sordermodelelem {
    min-height: 700px;
  }
  .sordermodelsize {
    min-height: 580px;
  }
  .sorderDetailModelForm {
    margin: 5px;
  }
  .el-drawer__wrapper {
    height: 50%;
    top: 20%;
  }
  .splitter-pane-resizer.vertical {
    background-color: #409eff !important;
    border-left: 0px !important;
  }
}
</style>
