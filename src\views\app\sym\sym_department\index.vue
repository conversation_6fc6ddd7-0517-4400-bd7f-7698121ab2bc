<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table ref='master_table' id="SymDepartmentMasterTable" :loading="tableLoading" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="code" title="权限编码" sortable show-overflow width="100px"> </vxe-table-column>
      <vxe-table-column field="codeName" title="编码名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="100px" show-overflow :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'sym_department',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        url: '',
        ActionClass: null
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 12, message: '长度在 3 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '部门编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/sym_department/get',
        add: '/mtm/sym_department/adds',
        edit: '/mtm/sym_department/updates',
        delete: '/mtm/sym_department/deletes'
      }
    }
  },
  created () { },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
</style>
