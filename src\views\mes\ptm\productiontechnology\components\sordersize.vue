<template>
  <d2-container type="ghost">
    <!-- <vxe-table align="center" :data="Sizes">
      <vxe-table-column field="sizeCloumnName" title="规格" width="70"></vxe-table-column>
      <vxe-table-column field="standard" title="标准" width="70"></vxe-table-column>
      <vxe-table-column field="standard1" title="标准修正" width="70"></vxe-table-column>
      <vxe-table-column field="fix1" title="调整" width="70"></vxe-table-column>
      <vxe-table-column field="finish" title="成衣" width="70"></vxe-table-column>
    </vxe-table> -->

    <vxe-grid v-if="show" ref="detailsizegrid" :show-header-overflow='true' border highlight-hover-row class="reverse-table" :show-header="false" :columns="Columns" :data="Sizes" :resizable-config='{minWidth:"auto"}'></vxe-grid>
  </d2-container>
</template>

<script>
export default {
  name: 'detailsize',
  props: {
    form: {
      type: Object
    }
  },
  watch: {
    'form.sizes': {
      deep: true,
      handler (newval, oldval) {
        if (newval !== null) {
          var cols = this.form.sorderType === 0 ? this.myColumns1 : this.myColumns
          this.reverseTable(cols, newval)
        }
      }
    }
  },
  data () {
    return {
      Sizes: [],
      show: true,
      myColumns: [
        { field: 'sizeCloumnName', title: '规格', width: 80 },
        { field: 'standard', title: '标准', width: 80 },
        { field: 'standard1', title: '标准修正', width: 80 },
        { field: 'fix1', title: '调整', width: 80 },
        { field: 'finish', title: '成衣', width: 80 }
      ],
      myColumns1: [
        { field: 'sizeCloumnName', title: '规格', width: 80 },
        // { field: 'standard', title: '标准', width: 80 },
        // { field: 'standard1', title: '标准修正', width: 80 },
        // { field: 'fix1', title: '调整', width: 80 },
        { field: 'repairValue', title: '返修调整', width: 80 },
        { field: 'finish', title: '成衣', width: 80 }
      ],
      Columns: []
    }
  },
  created () {
    if (this.form != null && this.form.sizes.length > 0) {
      var cols = this.form.sorderType === 0 ? this.myColumns1 : this.myColumns
      this.reverseTable(cols, this.form.sizes)
    }
  },
  methods: {
    reverseTable (columns, list) {
      this.show = false
      const buildData = columns.map(column => {
        const item = { col0: column.title }
        list.forEach((row, index) => {
          item[`col${index + 1}`] = row[column.field]
        })
        return item
      })
      const buildColumns = [{
        field: 'col0',
        fixed: 'left',
        width: 80
      }]
      list.forEach((item, index) => {
        var _width = 55
        if (item.sizeCloumnName.length >= 3) {
          _width = _width + (item.sizeCloumnName.length - 2) * 11
        }
        buildColumns.push({
          field: `col${index + 1}`,
          minWidth: 50,
          width: _width
        })
      })
      this.Sizes = buildData
      this.Columns = buildColumns

      this.$refs.detailsizegrid.refreshColumn()
      this.show = true
    }
  }
}
</script>

<style>
</style>
