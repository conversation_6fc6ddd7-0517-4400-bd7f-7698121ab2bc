@echo off
setlocal enabledelayedexpansion
echo Docker Quick Test for Windows

REM Clean up old containers
echo Cleaning old containers...
docker stop test-config >nul 2>&1
docker rm test-config >nul 2>&1

REM Build image
echo Building image...
cd ..
docker build -t config-test:latest .

if %errorlevel% neq 0 (
    echo Build failed
    pause
    exit /b 1
)

echo Build successful

REM Start container
echo Starting container...
docker run -d --name test-config -p 8080:80 -e "VUE_APP_USER_API=https://custom-sso.example.com/api/" -e "VUE_APP_API=https://custom-mtm.example.com/api/" -e "VUE_APP_MESAPI=https://custom-mes.example.com/api/" -e "VUE_APP_TITLE=Windows Test Title" config-test:latest

REM Wait for container to start
echo Waiting for container to start...
timeout /t 5 /nobreak >nul

REM Check container status
docker ps -q -f name=test-config >nul 2>&1
if %errorlevel% neq 0 (
    echo Container failed to start
    echo Error logs:
    docker logs test-config
    pause
    exit /b 1
)

echo Container started successfully

REM Show logs
echo.
echo Container startup logs:
docker logs test-config

REM Check config file
echo.
echo Checking config file:
docker exec test-config cat /usr/share/nginx/html/config.js 2>nul
if %errorlevel% neq 0 (
    echo Warning: config.js file not accessible
)

REM Test HTTP response
echo.
echo Testing HTTP response:
timeout /t 2 /nobreak >nul
curl -s http://localhost:8080/config.js 2>nul
if %errorlevel% neq 0 (
    echo Warning: Cannot access config.js via HTTP
    echo Please manually visit: http://localhost:8080/config.js
)

echo.
echo Test completed!
echo.
echo Next steps:
echo 1. Open browser and visit: http://localhost:8080
echo 2. Press F12 to open developer tools
echo 3. Run in console: console.log(window.VUE_APP_API)
echo 4. Should display: https://custom-mtm.example.com/api/
echo.
echo Cleanup command: docker stop test-config && docker rm test-config
echo.
pause
