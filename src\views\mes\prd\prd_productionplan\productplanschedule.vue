<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="success" icon="fa fa-play-circle" @click="createPlanSchedules()" v-if="menuAction.allowEdit" size="mini">排产</vxe-button>
          <!-- <vxe-button status="warning" icon="vxe-icon--refresh" v-if="menuAction.allowEdit" size="mini">更新数据</vxe-button> -->
          <!-- <vxe-button status="info" content="信息颜色"></vxe-button>
          <vxe-button status="warning" content="警告颜色"></vxe-button>
          <vxe-button status="danger" content="危险颜色"></vxe-button> -->
        </template>
        <template v-slot:tools>
          <!-- <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button> -->
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="loadData()">
          </vxe-button>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PRD_ProductPlanScheduleTable' ref='master_table' height="auto" :cell-style="cellStyle" :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="workSecationName" title="工段" width="150"> </vxe-table-column>
      <vxe-table-column field="workSecationEndTime" title="工段截至期" :formatter="formatDate" width="130"> </vxe-table-column>
      <vxe-table-column field="productionTeamName" title="小组" width="130"> </vxe-table-column>
      <vxe-table-column field="productionTeamEndTime" title="小组截止期" :formatter="formatDate" width="130"> </vxe-table-column>
      <vxe-table-column field="productionStationName" title="工位" width="150"> </vxe-table-column>
      <vxe-table-column field="productionProcessesCode" title="工序编码" width="80"> </vxe-table-column>
      <vxe-table-column field="productionProcessesName" title="工序名称" width="150"> </vxe-table-column>
      <vxe-table-column field="suitSupplyProductionProcessesTypeText" title="suitSupply" width="150"> </vxe-table-column>
      <vxe-table-column field="stateText" title="状态" width="80"> </vxe-table-column>
      <vxe-table-column field="scanTime" title="完成时间" width="130" :formatter="formatDate"> </vxe-table-column>
      <vxe-table-column field="message" title="消息" width="150"> </vxe-table-column>
      <vxe-table-column field="sort" title="排序" width="150"> </vxe-table-column>

      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column> -->
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :row-class-name="rowClassName" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="工段" field="workSecationID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.workSecationID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="工段" :remote-method="remoteMethod1" @change="change1">
              <el-option v-for="item in worksecationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="小组" field="productionTeamID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.productionTeamID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="小组" :remote-method="remoteMethod2" @change="change2">
              <el-option v-for="item in productionTeamComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="工位" field="productionStationID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.productionStationID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="工位" :remote-method="remoteMethod3" @change="change3">
              <el-option v-for="item in productionStationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="工序" field="productionProcessesID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.productionProcessesID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="工序" :remote-method="remoteMethod4" @change="change4">
              <el-option v-for="item in productionProcessesComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="子名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="状态" field="factoryID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.state" filterable placeholder="状态" size="mini">
              <el-option v-for="item in productPlanScheduleComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item> -->
        <vxe-form-item title="消息" field="message" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'prd_productplanschedule',
  mixins: [detailTableMixins],
  props: {
    productPlanDetail: {
      type: Object,
      default: null
    }
  },
  watch: {
    'productPlanDetail.id': {
      deep: true,
      handler (newval, oldval) {
        if (newval && newval !== null) {
          this.formData.productionPlanDetailID = newval
        }
      }
    },
    productPlanDetail: {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== null) {
          this.loadData({ productionPlanDetailID: newVal.id }).then(({ data }) => {
          })
        }
      }
    }
  },
  data () {
    return {
      formData: {
        remark: '',
        sort: 999,
        isActive: true,
        productionPlanDetailID: this.productPlanDetail.id,
        productionProcessesID: null,
        productionStationID: null,
        productionTeamID: null,
        workSecationID: null,
        state: 1,
        message: null
        // worksecationID: this.form.id
      },
      planScheduleValue: [],
      formRules: {
        workSecationID: [{ required: true, message: '请绑定工段' }],
        productionTeamID: [{ required: true, message: '请绑定生产小组' }],
        productionStationID: [{ required: true, message: '请绑定生产工位' }],
        productionProcessesID: [{ required: true, message: '请绑定生产工序' }],
        state: [{ required: true, message: '请选择状态' }],
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mes/pRD_ProductPlanSchedule/get',
        add: '/mes/pRD_ProductPlanSchedule/adds',
        edit: '/mes/pRD_ProductPlanSchedule/updates',
        delete: '/mes/pRD_ProductPlanSchedule/deletes',
        create: '/mes/pRD_ProductPlanSchedule/createPlanSchedules',
        productPlanScheduleComboStore: '/mes/combo/ProductPlanScheduleComboStore',
        worksecationComboStoreByQuery: '/mes/comboQuery/worksecationComboStoreByQuery',
        productionTeamComboStoreByQuery: '/mes/comboQuery/productionTeamComboStoreByQuery',
        productionStationComboStoreByQuery: '/mes/comboQuery/productionStationComboStoreByQuery',
        productionProcessesComboStoreByQuery: '/mes/comboQuery/ProductionProcessesComboStoreByQuery'

      },
      productPlanScheduleComboStore: [],
      worksecationComboStoreByQuery: [],
      productionTeamComboStoreByQuery: [],
      productionStationComboStoreByQuery: [],
      productionProcessesComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    this.$nextTick(() => {
      if (this.productPlanDetail.id) {
        this.formData.productionPlanDetailID = this.productPlanDetail.id
      }
    })
    // this.loadData({ worksecationID: this.form.id }).then(({ data }) => {
    // })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.productPlanScheduleComboStore).then(result => {
        this.productPlanScheduleComboStore = result
      })
      await this.$api.ActionRequest(this.api.worksecationComboStoreByQuery).then(result => {
        this.worksecationComboStoreByQuery = result
      })
    },
    // // 新增
    // insertEvent() {
    //   this.selectRow = cloneDeep(this.productPlanDetail)
    //   this.showEdit = true
    // },
    checkSelectRow (msg = true) {
      if (this.productPlanDetail.id === undefined || this.productPlanDetail.id === null || this.productPlanDetail === null) {
        if (msg) {
          this.$XModal.message({ message: '请先选中左侧单行数据', status: 'error' })
        }
        return false
      } else {
        return true
      }
    },
    async createPlanSchedules () {
      if (!this.checkSelectRow()) {
        return
      }
      this.$XModal.confirm('更新数据会清空已有数据,您确定要更新吗？').then(async type => {
        if (type === 'confirm') {
          await this.$api.ActionRequest(this.api.create, { productionPlanDetailID: this.productPlanDetail.id }).then(result => {
            this.$XModal.message({ message: '创建成功', status: 'success' })
            this.loadData({ productionPlanDetailID: this.productPlanDetail.id })
          })
        } else {
          this.$XModal.message({ message: '取消操作' })
        }
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.worksecationComboStoreByQuery, { text: query }).then(result => {
        this.worksecationComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      if (this.selectRow.worksecationID === null) {
        this.$XModal.message({ message: '请先选择工段', status: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery, { parentID: this.selectRow.worksecationID, text: query }).then(result => {
        this.productionTeamComboStoreByQuery = result
      })
    },
    remoteMethod3 (query) {
      if (this.selectRow.productionTeamID === null) {
        this.$XModal.message({ message: '请先选择生产小组', status: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.productionStationComboStoreByQuery, { parentID: this.selectRow.productionTeamID, text: query }).then(result => {
        this.productionStationComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      if (this.selectRow.productionStationID === null) {
        this.$XModal.message({ message: '请先选择生产工位', status: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.productionProcessesComboStoreByQuery, { parentID: this.selectRow.productionStationID, text: query }).then(result => {
        this.productionProcessesComboStoreByQuery = result
      })
    },
    async change1 (val) {
      await this.productionTeamComboClear(val)
      await this.productionStationComboClear()
      await this.productionProcessesComboClear()
    },
    async change2 (val) {
      await this.productionStationComboClear(val)
      await this.productionProcessesComboClear()
    },
    async change3 (val) {
      await this.productionProcessesComboClear(val)
    },
    change4 () {

    },
    async productionTeamComboClear (val = null) {
      this.selectRow.productionTeamID = null
      if (val !== null) {
        await this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery, { parentID: val }).then(result => {
          this.productionTeamComboStoreByQuery = result
        })
      } else {
        this.productionTeamComboStoreByQuery = null
      }
    },
    async productionStationComboClear (val = null) {
      this.selectRow.productionStationID = null
      if (val !== null) {
        await this.$api.ActionRequest(this.api.productionStationComboStoreByQuery, { parentID: val }).then(result => {
          this.productionStationComboStoreByQuery = result
        })
      } else {
        this.productionStationComboStoreByQuery = null
      }
    },
    async productionProcessesComboClear (val = null) {
      this.selectRow.productionProcessesID = null
      if (val !== null) {
        await this.$api.ActionRequest(this.api.productionProcessesComboStoreByQuery, { parentID: val }).then(result => {
          this.productionProcessesComboStoreByQuery = result
        })
      } else {
        this.productionProcessesComboStoreByQuery = null
      }
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.worksecationComboStoreByQuery, { gid: row.workSecationID }).then(result => {
        this.worksecationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery, { gid: row.productionTeamID, parentID: row.worksecationID }).then(result => {
        this.productionTeamComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.productionStationComboStoreByQuery, { gid: row.workSecationID, parentID: row.productionTeamID }).then(result => {
        this.productionStationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.productionProcessesComboStoreByQuery, { gid: row.productionProcessesID, parentID: row.productionStationID }).then(result => {
        this.productionProcessesComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    cellStyle ({ row, rowIndex, column }) {
      if (column.property === 'stateText') {
        var a = { backgroundColor: '', color: '#ffffff' }
        switch (row.state) {
          case 0:
            a.backgroundColor = 'red' // 错误
            break
          case 1:
            a.backgroundColor = '#f60' // 等待中
            break
          case 2:
            a.backgroundColor = '#2db7f5' // 生产中
            break
          case 5:
            a.backgroundColor = '#187' // 完成
            break
          default:
            a.backgroundColor = ''
            break
        }
        return a
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
