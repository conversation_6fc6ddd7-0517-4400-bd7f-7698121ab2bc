// 表格列拖动
import Sortable from 'sortablejs'
export default {
  name: 'tableSortColumnMixins',
  data () {
    return {
      tableSortColumn: true,
      tableRef: 'master_table'
    }
  },
  beforeDestroy () {
    if (this.sortable && this.tableSortColumn) {
      this.sortable.destroy()
    }
  },
  created () {
    if (this.tableSortColumn) {
      this.columnDrop()
    }
  },
  methods: {
    columnDrop () {
      this.$nextTick(() => {
        const xTable = this.$refs[this.tableRef]
        if (xTable === undefined) {
          return
        }
        this.sortable = Sortable.create(xTable.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
          handle: '.vxe-header--column:not(.col--fixed)',
          onEnd: ({ item, newIndex, oldIndex }) => {
            const { fullColumn, tableColumn } = xTable.getTableColumn()
            const targetThElem = item
            const wrapperElem = targetThElem.parentNode
            const newColumn = fullColumn[newIndex]
            if (newColumn.fixed) {
              // 错误的移动
              if (newIndex > oldIndex) {
                wrapperElem.insertBefore(targetThElem, wrapperElem.children[oldIndex])
              } else {
                wrapperElem.insertBefore(wrapperElem.children[oldIndex], targetThElem)
              }
              return this.$XModal.message({ message: '固定列不允许拖动！', status: 'error' })
            }
            // 转换真实索引
            const oldColumnIndex = xTable.getColumnIndex(tableColumn[oldIndex])
            const newColumnIndex = xTable.getColumnIndex(tableColumn[newIndex])
            // 移动到目标列
            const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
            fullColumn.splice(newColumnIndex, 0, currRow)
            xTable.loadColumn(fullColumn)
          }
        })
      })
    }
  }

}
