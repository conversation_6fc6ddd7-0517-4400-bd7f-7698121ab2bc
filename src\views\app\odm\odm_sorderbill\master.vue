<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button @click="exportSelectEvent" status="perfect" v-if="menuAction.allowPrint">导出选中</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates">
              <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <template v-if="!(info.userType==2)">
              <vxe-form-item field="clientID">
                <template #default="{ data }">
                  <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                    <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>
            </template>
            <vxe-form-item field="billType">
              <template #default="{ data }">
                <el-select v-model.trim="data.billType" filterable placeholder="类别" size="mini" clearable>
                  <el-option v-for="item in BillTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="SorderBillTypes">
              <template #default="{ data }">
                <el-select v-model.trim="data.SorderBillTypes" placeholder="账单类型" size="mini" multiple collapse-tags clearable>
                  <el-option v-for="item in SorderBillTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='OdmSorderbillMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}" :footer-method="footerMethod" show-footer>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="订单类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderBillTypeText" title="账单类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="说明" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"></vxe-table-column>
      <vxe-table-column field="billTypeText" title="类别" sortable width="100">
        <template v-slot="{ row }">
          <el-tag v-if="row.billType===0" type="danger">{{row.billTypeText}}</el-tag>
          <el-tag v-else type="success">{{row.billTypeText}}</el-tag>
        </template>
      </vxe-table-column>

      <vxe-table-column field="clientPersonName" title="洗唛货号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="面料号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="businessGroupName" title="业务属性" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemTotal" title="面料成本" sortable width="100"></vxe-table-column>
      <vxe-table-column field="otherTotal" title="基本价格" sortable width="100"></vxe-table-column>

      <vxe-table-column field="count" title="合计" sortable width="100"></vxe-table-column>
      <vxe-table-column field="total" title="实际付款金额" sortable width="100">
        <template v-slot="{ row }">
          <span v-if="row.billType===0" style="color:red">{{row.total}}</span>
          <span v-else style="color:green">{{row.total}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="currentBalance" title="余额" sortable width="100"></vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100"></vxe-table-column>
      <vxe-table-column field="deliveryDate" title="要求交期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumberTime" title="发货日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>

      <vxe-table-column field="customerNumber" title="客户订单号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <template v-if="!(info.userType==2)">
        <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
          <template v-slot="{ row }">
            <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
            <vxe-button type="text" @click="updateEvent(row)" v-if="menuAction.allowEdit&&row.sorderBillType!==9&&row.sorderBillType!==20">结算</vxe-button>
            <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
            <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
          </template>
        </vxe-table-column>
      </template>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-sizes="[50,200,500,1000,2000,3000,5000]" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="客户" span="24" field="clientID">
          <template #default="{ data }">
            <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
              <el-option v-for="item in clientComboStoreByQuery" :key="item.label+item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="账单类型" field="sorderBillType" span="12" :item-render="{name: '$select', options: SorderBillTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="类别" field="billType" span="12" :item-render="{name: '$select', options: BillTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="金额" field="count" span="12" :item-render="{name: '$input', attrs: {type: 'float',}}"></vxe-form-item>
        <vxe-form-item title="实际付款金额" field="total" span="12" :item-render="{name: '$input', attrs: {type: 'float',}}"></vxe-form-item>
        <vxe-form-item title="时间" field="createOn" span="12">
          <template #default>
            <el-date-picker v-model="selectRow.createOn" type="datetime" placeholder="时间" size="mini">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="说明" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { mapState } from 'vuex'
export default {
  name: 'odm_sorderbillmaster',
  mixins: [masterTableMixins],
  components: {
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      formData: {
        sorderID: null,
        sorderBillType: 20,
        clientID: null,
        billType: 0,
        count: null,
        total: null,
        remark: '',
        isActive: true
      },
      formRules: {
        clientID: [{ required: true, message: '请选择客户' }],
        sorderBillType: [{ required: true, message: '请选择账单类型' }],
        billType: [{ required: true, message: '请选择类别' }],
        total: [{ required: true, message: '清楚如实际金额' }]
      },
      api: {
        get: '/mtm/odm_sorderbill/get',
        add: '/mtm/odm_sorderbill/adds',
        edit: '/mtm/odm_sorderbill/updates',
        delete: '/mtm/odm_sorderbill/deletes',
        SorderBillTypeComboStore: '/mtm/combo/SorderBillTypeComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        BillTypeComboStore: '/mtm/combo/BillTypeComboStore'
      },
      BillTypeComboStore: [],
      SorderBillTypeComboStore: [],
      clientComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SorderBillTypeComboStore).then(result => {
        this.SorderBillTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.BillTypeComboStore).then(result => {
        this.BillTypeComboStore = result
      })
    },

    updateEvent (row) {
      row.sorderBillType = 9
      this.$api.ActionRequest(this.api.edit, [row]).then(result => {
        this.$XModal.message({ message: '结算完成', status: 'success' })
        this.loadData()
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        if (item.billType === 1) {
          count += Number(item[field])
        }
        if (item.billType === 0) {
          count += Number(item[field])
        }
      })
      return count
    },
    footerMethod ({ columns, data }) {
      const footerData = [
        columns.map((column, _columnIndex) => {
          if (_columnIndex === 0) {
            return '合计'
          }
          if (['total'].includes(column.property)) {
            return this.sumNum(data, 'total')
          }
          return null
        })
      ]
      return footerData
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
