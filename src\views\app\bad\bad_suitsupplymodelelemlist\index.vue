<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="washingLabelPosition" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.washingLabelPosition" filterable remote reserve-keyword placeholder="位置" size="mini" clearable>
                  <el-option v-for="item in WashingLabelPositionComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="washingLabelPositionType" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.washingLabelPositionType" filterable remote reserve-keyword placeholder="类型" size="mini" clearable>
                  <el-option v-for="item in WashingLabelPositionTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadSuitsupplymodelelemlistMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="groupName" title="类别" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemListCodeName" title="款式名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="washingLabelPositionText" title="位置" sortable width="100"></vxe-table-column>
      <vxe-table-column field="washingLabelPositionTypeText" title="类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemListShowTypeText" title="显示类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElem1" title="款式明细1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElem2" title="款式明细2" sortable width="100"></vxe-table-column>
      <vxe-table-column field="otherPositionText" title="其他位置" sortable width="100"></vxe-table-column>
      <vxe-table-column field="info" title="固定显示" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="mlInfo" title="面料固定显示" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式" span="24" field="modelElemListID" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.modelElemListID" filterable placeholder="款式" size="mini" remote reserve-keyword :remote-method="remoteMethod2" clearable>
              <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.label+item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式明细1" span="12" field="modelElemID1" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.modelElemID1" filterable placeholder="款式明细1" size="mini" remote reserve-keyword :remote-method="remoteMethod1" clearable>
              <el-option v-for="item in ModelElemComboStoreByQuery1" :key="item.label+item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式明细2" span="12" field="modelElemID2" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.modelElemID2" filterable placeholder="款式明细2" size="mini" remote reserve-keyword :remote-method="remoteMethod3" clearable>
              <el-option v-for="item in ModelElemComboStoreByQuery2" :key="item.label+item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="位置" field="washingLabelPosition" span="12" :item-render="{ name: '$select', options: WashingLabelPositionComboStore}"></vxe-form-item>
        <vxe-form-item title="类别" field="washingLabelPositionType" span="12" :item-render="{ name: '$select', options: WashingLabelPositionTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="显示类型" field="modelElemListShowType" span="12" :item-render="{ name: '$select', options: ModelElemListShowTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="其他位置" :title-prefix="{ message: '生成顺序为6,8,7,23,14,15,16,17,24,只能绑定此模板之前的位置,否则无法输出相应数据', icon: 'fa fa-exclamation-circle' }" field="otherPosition" span="12" :item-render="{ name: '$select', options: WashingLabelPositionComboStore, props: { clearable:true}}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: '$input', props: { type: 'number',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="固定显示" field="info" span="24" :item-render="{name: '$input'}"></vxe-form-item>
        <!-- <vxe-form-item title="面料固定显示" field="mlInfo" span="24" :item-render="{name: '$input'}"></vxe-form-item> -->
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
// import XEUtils from 'xe-utils'
export default {
  name: 'bad_suitsupplymodelelemlist',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        groupID: null,
        modelElemListID: null,
        washingLabelPosition: null,
        washingLabelPositionType: null,

        info: null
      },
      formData: {
        modelElemListID: null,
        washingLabelPosition: null,
        washingLabelPositionType: null,
        remark: '',
        isActive: true,
        modelElemID1: null,
        modelElemID2: null,
        modelElemListShowType: 0,
        sort: 0,
        otherPosition: null
      },
      formRules: {
        modelElemListID: [{ required: true, message: '请选择款式' }],
        washingLabelPosition: [{ required: true, message: '请选择位置' }],
        washingLabelPositionType: [{ required: true, message: '请选择类别' }],
        modelElemListShowType: [{ required: true, message: '请选择显示类型' }],
        sort: [{ required: true, message: '请输入排序' }]
      },
      api: {
        get: '/mtm/bad_suitsupplymodelelemlist/get',
        add: '/mtm/bad_suitsupplymodelelemlist/adds',
        edit: '/mtm/bad_suitsupplymodelelemlist/updates',
        delete: '/mtm/bad_suitsupplymodelelemlist/deletes',
        GroupComboStore: '/mtm/combo/groupComboStore',
        WashingLabelPositionComboStore: '/mtm/combo/WashingLabelPositionComboStore',
        WashingLabelPositionTypeComboStore: '/mtm/combo/WashingLabelPositionTypeComboStore',
        ModelElemListShowTypeComboStore: '/mtm/combo/ModelElemListShowTypeComboStore',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/modelElemListComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery'
      },
      GroupComboStore: [],
      ModelElemComboStoreByQuery1: [],
      ModelElemComboStoreByQuery2: [],
      ModelElemListComboStoreByQuery: [],
      WashingLabelPositionComboStore: [],
      ModelElemListShowTypeComboStore: [],
      WashingLabelPositionTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.WashingLabelPositionComboStore).then(result => {
        this.WashingLabelPositionComboStore = result
      })
      await this.$api.ActionRequest(this.api.WashingLabelPositionTypeComboStore).then(result => {
        this.WashingLabelPositionTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListShowTypeComboStore).then(result => {
        this.ModelElemListShowTypeComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
      })
    },
    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery2 = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    async copyRowEvent (row) {
      // if (XEUtils.has(this.selectRow, 'id')) {

      // }
      // if (XEUtils.has(this.selectRow, 'code')) {

      // }
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.selectRow.id = null
        this.selectRow.code = null
        this.showEdit = true
      })
    },
    // 编辑
    async editEvent (row) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
      })
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID2 }).then(result => {
        this.ModelElemComboStoreByQuery2 = result
      })
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
