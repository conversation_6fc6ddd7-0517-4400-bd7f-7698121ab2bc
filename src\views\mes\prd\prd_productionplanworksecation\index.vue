<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->
          <vxe-button status="success" icon="vxe-icon--refresh" v-if="menuAction.allowEdit" @click="FactoryWorkSectionShow=!FactoryWorkSectionShow">批量修改工段</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="issueDates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.issueDates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="下单开始日期" end-placeholder="下单结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID" :item-render="{}"><template #default>
                <el-select v-model.trim="searchForm.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID" :item-render={}>
              <template #default>
                <el-select v-model="searchForm.groupID" filterable placeholder="品类" size="mini" clearable>
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="workSecationID" :item-render={}>
              <template #default>
                <el-select v-model="searchForm.workSecationID" filterable placeholder="请选择工段" size="mini" remote reserve-keyword plremoteaceholder="工段" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in worksecationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="订单号/MES号" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PrdProductionplanworksecationMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNum" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="stateText" title="订单状态" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="issueDate" title="下单时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="MES号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupName" title="品类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="factoryName" title="生产工厂" sortable width="100"></vxe-table-column>
      <vxe-table-column field="workSecationText" title="工段名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center"
        v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close show-zoom :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', props: { type: 'float',clearable:true}}"></vxe-form-item>

        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='50%'>
      <detail-table :form="selectRow" v-if="drawer" />
    </el-drawer>
    <vxe-modal v-model="FactoryWorkSectionShow" title="批量修改工段" width="600" resize destroy-on-close>
      <vxe-form :data="FactoryWorkSectionForm" :rules="FactoryWorkSectionRules" title-align="right" title-width="100" @submit="FactoryWorkSectionSubmitEvent">
        <vxe-form-item title="工厂工段" field="factoryWorkSection" span="24" :item-render="{}">
          <template #default>
            <el-cascader v-model="FactoryWorkSectionForm.factoryWorkSection" :options="FactoryWorkSectionComboStore" :props="{ expandTrigger: 'hover' }" size="mini" style="width: 100%;"></el-cascader>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">确定</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import DetailTable from './detail'
// import { cloneDeep } from 'lodash'
export default {
  name: 'prd_productionplanworksecation',
  mixins: [masterTableMixins],
  components: {
    DetailTable
  },
  data () {
    return {
      searchForm: {
        clientID: null,
        groupID: null,
        workSecationID: null
      },
      FactoryWorkSectionShow: false,
      FactoryWorkSectionForm: {
        factoryWorkSection: []
      },
      FactoryWorkSectionRules: {
        factoryWorkSection: [{ required: true, message: '请选择要调整的工段' }]
      },
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mes/prd_productionplanworksecation/get',
        add: '/mes/prd_productionplanworksecation/adds',
        edit: '/mes/prd_productionplanworksecation/updates',
        delete: '/mes/prd_productionplanworksecation/deletes',
        UpdateFactoryWorkSection: '/mes/prd_productionplanworksecation/UpdateFactoryWorkSection',
        FactoryWorkSectionComboStore: '/mes/prd_productionplanworksecation/GetFactoryWorkSection',
        GroupComboStore: '/mtm/combo/groupComboStore',
        worksecationComboStoreByQuery: '/mes/comboQuery/worksecationComboStoreByQuery',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      GroupComboStore: [],
      FactoryWorkSectionComboStore: [],
      worksecationComboStoreByQuery: [],
      clientComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.worksecationComboStoreByQuery).then(result => {
        this.worksecationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.FactoryWorkSectionComboStore).then(result => {
        this.FactoryWorkSectionComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.worksecationComboStoreByQuery, { text: query }).then(result => {
        this.worksecationComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    FactoryWorkSectionSubmitEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length <= 0) {
        this.$XModal.message({ message: '请选择要更改的工段', status: 'error' })
        return
      }
      var rowIds = checks.map(item => { return item.id })
      if (this.FactoryWorkSectionForm.factoryWorkSection.length !== 3) {
        this.$XModal.message({ message: '工段选择失败,请重新选择', status: 'error' })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '请稍后.....',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.$api.ActionRequest(this.api.UpdateFactoryWorkSection, { productionPlanWorkSecationIDs: rowIds, factoryWorkSectionIDs: this.FactoryWorkSectionForm.factoryWorkSection }).then(result => {
        this.$XModal.message({ message: '工段选择成功', status: 'success' })
        this.FactoryWorkSectionShow = false
        this.loadData()
        loading.close()
      }).catch(() => {
        loading.close()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
