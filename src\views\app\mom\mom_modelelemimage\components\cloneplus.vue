<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>

        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="cloneForm" @reset="resetEvent">
            <vxe-form-item title="替换项"></vxe-form-item>
            <vxe-form-item field="modelElemID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemID" filterable remote reserve-keyword placeholder="款式明细" :remote-method="ModelElemComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemID1">
              <template #default="{ data }">
                <el-select v-model="data.modelElemID1" filterable remote reserve-keyword placeholder="款式明细1" :remote-method="ModelElemComboStoreByQueryMethod1" clearable size="mini">
                  <el-option v-for="item in ModelElemComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemID2" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.modelElemID2" filterable remote reserve-keyword placeholder="款式明细2" :remote-method="ModelElemComboStoreByQueryMethod2" clearable size="mini">
                  <el-option v-for="item in ModelElemComboStoreByQuery2" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
          </vxe-form>
          <vxe-button @click="cloneplushEvent">批量复制</vxe-button>

        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelElemBaseID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemBaseID" filterable remote reserve-keyword placeholder="基础款式" :remote-method="ModelElemBaseComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelElemBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">

                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="ModelElemListComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" clearable size="mini">
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemTypeID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemTypeID" filterable placeholder="款式类别" clearable size="mini">
                  <el-option v-for="item in ModelElemTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="positionID">
              <template #default="{ data }">
                <el-select v-model="data.positionID" filterable placeholder="位置" clearable size="mini">
                  <el-option v-for="item in PositionComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelbaseimageMasterTable' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="mix" title="合成图" sortable :formatter='formatBool' width="100px"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="款式">
        <vxe-table-column field="modelElemTypeText" title="款式类别" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemBaseText" title="基础款式" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemListText" title="款式" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemCode" title="款式明细" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemName" title="款式明细" sortable width="100px"></vxe-table-column>
      </vxe-table-column>
      <vxe-table-column title="款式1">
        <vxe-table-column field="modelElemCode1" title="明细编码" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemName1" title="明细名称" sortable width="100px"></vxe-table-column>
      </vxe-table-column>
      <vxe-table-column field="imageSeq" title="图片顺序" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="position" title="图片位置" sortable width="100px"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
  </d2-container>
</template>
<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'clonepush',
  mixins: [masterTableMixins],
  data () {
    return {
      cloneForm: {
        modelElemID1: null,
        modelElemID2: null,
        modelElemID: null
      },
      api: {
        get: '/mtm/mom_modelelemimage/get',
        clone: '/mtm/mom_modelelemimage/clones',
        ModelImageComboStoreByQuery: '/mtm/comboQuery/ModelImageComboStoreByQuery',
        ModelElemBaseComboStoreByQuery: '/mtm/comboQuery/ModelElemBaseComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        ModelElemTypeComboStore: '/mtm/combo/ModelElemTypeComboStore',
        PositionComboStore: '/mtm/combo/PositionComboStore',
        GroupComboStore: '/mtm/combo/GroupComboStore'

      },
      PositionComboStore: [],
      GroupComboStore: [],
      ModelImageComboStoreByQuery: [],
      ModelElemBaseComboStoreByQuery: [],
      ModelElemComboStoreByQuery: [],
      ModelElemComboStoreByQuery1: [],
      ModelElemComboStoreByQuery2: [],
      ModelElemListComboStoreByQuery: [],
      ModelElemTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.ModelElemBaseComboStoreByQueryMethod()
    this.ModelElemListComboStoreByQueryMethod()
    this.ModelImageComboStoreByQueryMethod()
    this.ModelElemComboStoreByQueryMethod()
    this.ModelElemComboStoreByQueryMethod1()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.PositionComboStore).then(result => {
        this.PositionComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemTypeComboStore).then(result => {
        this.ModelElemTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    ModelElemBaseComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
    },
    ModelElemListComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    ModelImageComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
    },
    ModelElemComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    ModelElemComboStoreByQueryMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
      })
    },
    ModelElemComboStoreByQueryMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery2 = result
      })
    },
    cloneplushEvent () {
      const selectRecords = this.$refs.master_table.getCheckboxRecords()
      if (selectRecords.length === 0) {
        this.$XModal.message({ message: '请先勾选下方要替换的数据', status: 'error' })
        return
      }
      if (this.cloneForm.modelElemID === null && this.cloneForm.modelElemID1 === null && this.cloneForm.modelElemID2 === null) {
        this.$XModal.message({ message: '请选择替换项中要替换的数据', status: 'error' })
        return
      }
      selectRecords.forEach(element => {
        if (this.cloneForm.modelElemID !== null) {
          element.modelElemID = this.cloneForm.modelElemID
        }
        if (this.cloneForm.modelElemID1 !== null) {
          element.modelElemID1 = this.cloneForm.modelElemID1
        }
        if (this.cloneForm.modelElemID2 !== null) {
          element.modelElemID2 = this.cloneForm.modelElemID2
        }
      })
      this.$api.ActionRequest(this.api.clone, selectRecords).then(result => {
        this.$XModal.message({ message: '复制成功', status: 'success' })
        this.loadData()
      })
    }
  }
}
</script>
