<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h3>顾客图片</h3>
    </div>
    <div class="sorderdetailimage">
      <slot name="sorderdetailmodel" />
      <div style="text-align:center">
        <el-button type="success" size="mini" @click="personImageEvent" v-if="!EditState">选择顾客图片</el-button>
      </div>
      <template v-if="!EditState">
        <div v-loading="EditState" element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)">
          <el-upload v-loading="imageloding" :action="baseurl+api.add" list-type="picture-card" :on-error="onerror" :on-success="onsuccess" :file-list="fileList" :before-upload="beforeAvatarUpload" :auto-upload="true" :data="{SorderDetialID:sorderStore.sorderDetailID,PseronID:sorderStore.clientPersonID}">
            <i slot="default" class="el-icon-plus" v-if="!EditState"></i>
            <div slot="file" slot-scope="{file}">
              <el-popover placement="top-start" title="标题" width="200" trigger="hover" content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
                <img class="el-upload-list__item-thumbnail" slot="reference" :src="file.url" alt="">
              </el-popover>

              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" v-show="!EditState" @click="handleDownload(file)">
                  <i class="el-icon-download"></i>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" v-show="!EditState" @click="handleRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" :modal="false" top="5vh" :append-to-body="true">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </div>
      </template>
      <template v-else>
        <div class="block" v-for="fit in fileList" :key="fit.id">
          <el-image style="width: 200px;" :src="fit.url" :preview-src-list="fileList.map(a=>{return a.url})">
          </el-image>
        </div>

      </template>
      <vxe-modal v-model="personImage.show" title="顾客图片" width="500" resize destroy-on-close>
        <vxe-grid border resizable height="530" :seq-config="{startIndex: (personImage.tablePage.currentPage - 1) * personImage.tablePage.pageSize}" :pager-config="personImage.tablePage" :columns="personImage.tableColumn" :data="personImage.tableData" @page-change="personImagehandlePageChange" @cell-dblclick="personImageTableCellClick" row-class-name="personimagerow" cell-class-name="personimagerowcell" :showOverflow="showOverflow">
          <template v-slot:img_default="{ row }">
            <!-- <img v-if="row.url" :src="row.url" style="width: 100px;" /> -->
            <el-image v-if="row.url" :src="row.url" fit='fill' style="width: 200px;height:200px"></el-image>
            <span v-else>无</span>
          </template>
        </vxe-grid>
      </vxe-modal>
    </div>
  </el-card>
</template>

<script>
import sorderEditState from './sordereditstate'
import { mapState } from 'vuex'
export default {
  name: 'SorderDetailImage',
  mixins: [sorderEditState],
  props: {
    SorderDetailModel: {
      type: Object,
      required: true
    },
    sorderStore: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      api: {
        get: '/mtm/oDM_SorderDetailImage/get',
        add: 'mtm/oDM_SorderDetailImage/adds',
        delete: '/mtm/oDM_SorderDetailImage/deletes',
        personImageGet: '/mtm/BAD_ClientPersonImage/get',
        insert: 'mtm/oDM_SorderDetailImage/add'
      },
      baseurl: process.env.VUE_APP_API,
      fileList: [],
      showOverflow: false,
      imageloding: false,
      personImage: {
        show: false,
        tableData: [],
        tableColumn: [
          { type: 'seq', width: 60 },
          { field: 'url', title: '图片', slots: { default: 'img_default' } }
        ],
        tablePage: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          align: 'left',
          pageSizes: [10, 20, 50, 100, 200, 500],
          layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
          // perfect: true,
          personID: null,
          maxResultCount: 10,
          skipCount: 0
        }
      }
    }
  },
  created () {
    this.get()
    this.personImage.tablePage.personID = this.sorderStore.clientPersonID
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  methods: {
    get () {
      if (this.sorderStore.clientPersonID === null) {
        return
      }
      this.$api.ActionRequest(this.api.get, { SorderDetailID: this.sorderStore.sorderDetailID, PersonID: this.sorderStore.clientPersonID }).then(result => {
        this.fileList = result
      })
    },
    handleRemove (file) {
      console.log(file)
      this.$XModal.confirm('您确定要删除此版型吗?删除后不可恢复').then(type => {
        if (type === 'confirm' && file.id && file.id !== null) {
          this.$api.ActionRequest(this.api.delete, [file.id]).then(result => {
            this.get()
          })
        }
      })
    },
    beforeAvatarUpload (file) {
      this.imageloding = true
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG 和PNG格式!')
        this.imageloding = false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        this.imageloding = false
      }
      return isJPG && isLt2M
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleDownload (file) {
      console.log(file)
    },
    onsuccess (response, file, fileList) {
      this.imageloding = false
      this.get()
    },
    onerror () {
      this.$message.error('上传失败！')
      this.imageloding = false
    },
    personImageEvent () {
      if (this.sorderStore.clientPersonID === null) {
        this.$message.error('请先绑定顾客')
        return
      }
      this.personImage.show = true
      this.personImageGet()
    },
    personImagehandlePageChange ({ currentPage, pageSize }) {
      this.personImage.tablePage.currentPage = currentPage
      this.personImage.tablePage.pageSize = pageSize
      this.personImage.tablePage.maxResultCount = pageSize
      this.personImage.tablePage.skipCount = (currentPage - 1) * pageSize
      this.personImageGet()
    },
    personImageTableCellClick ({ row }) {
      this.$api.ActionRequest(this.api.insert, { SorderDetailID: this.sorderStore.sorderDetailID, PersonID: this.sorderStore.clientPersonID, ImageID: row.id }).then(result => {
        this.get()
        this.personImage.show = false
      })
    },
    personImageGet () {
      this.$api.ActionRequest(this.api.personImageGet, this.personImage.tablePage).then(result => {
        this.personImage.tableData = result.items
        this.personImage.tablePage.total = result.totalCount
      })
    }
  }
}
</script>

<style lang="scss">
.sorderdetailimage {
  width: 80%;
  margin: 0 auto;
  .personimagerowcell {
    height: 200px !important;
    .c--tooltip {
      height: 200px !important;
    }
  }
}
</style>
