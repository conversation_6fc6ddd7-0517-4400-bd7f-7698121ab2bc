<template>
  <div>
    <div>
      <vxe-form element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)" :data="sorderForm" :rules="sorderformRules" class="sorderformRules" title-width="100" title-align="right" ref="sorderform">
        <vxe-form-item title="商户信息" span="24" :title-prefix="{  icon: 'fa fa-address-card-o' }" title-align="left" :item-render="{}"> <template #default>
            <span class="sordernum" style="font-size:20px;font-weight:600;color:#909399"> {{sorderForm.code}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="联系人" field="contact" span="8" :item-render="{}">
          <template v-slot>
            <!-- <vxe-input v-model="sorderForm.contact" placeholder="" :disabled="EditState" :size="size">></vxe-input> -->
            <el-input placeholder="请输入联系人" v-model="sorderForm.contact" size="mini" :disabled="EditState">
              <el-button slot="append" icon="el-icon-search" @click="personAddressShowEvent" :disabled="EditState"></el-button>
            </el-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="电话" field="tel" span="8" :item-render="{}">
          <template v-slot>
            <vxe-input v-model="sorderForm.tel" placeholder="请输入地址" :disabled="EditState" :size="size">></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="收货地址" field="address" span="6" :item-render="{}">
          <template v-slot>
            <vxe-input v-model="sorderForm.address" placeholder="请输入地址" :disabled="EditState" :size="size"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="基本数据" span="24" :title-prefix="{  icon: 'fa fa-info-circle' }" title-align="left" :item-render="{}"> </vxe-form-item>

        <vxe-form-item title="顾客" field="clientPersonID" :item-render="{}">
          <template v-slot>
            <vxe-pulldown ref="personDown" :size="size" :transfer="true">
              <template v-slot>
                <vxe-input v-model="sorderForm.clientPersonText" suffix-icon="fa fa-search" placeholder="点击搜索顾客" @keyup="personkeyupEvent" @focus="personFocusEvent" @suffix-click="personSuffixClick" clearable @clear="personClear" :disabled="EditState"></vxe-input>
              </template>
              <template v-slot:dropdown>
                <div class="dropdownperson">
                  <vxe-grid Id="sorderperson" keep-source highlight-hover-row auto-resize height="300" width="800" min-height="300px;" ref="personGrid" :loading="personLoading" :pager-config="personTablePage" :data="PsersontableData" :columns="PsersontableColumn" @cell-click="PsersonCellClickEvent" @page-change="PsersonPageChange" :edit-config="{trigger: 'manual', mode: 'row', showStatus: true, icon: 'fa fa-pencil'}" :toolbar="tableToolbar" :custom-config="{storage: false}">
                    <template v-slot:toolbar_buttons>
                      <vxe-button @click="clientPersonShow=!clientPersonShow">新增</vxe-button>
                    </template>
                    <template v-slot:sex_default="{ row }">
                      <template>
                        {{row.gender?"男":"女"}}
                      </template>
                    </template>
                    <template v-slot:operate="{ row }">
                      <template v-if="$refs.personGrid.isEditByRow(row)">
                        <vxe-button icon="fa fa-save" status="primary" title="保存" circle @click="savePsersonRowEvent(row)"></vxe-button>
                      </template>
                      <template v-else>
                        <vxe-button icon="fa fa-edit" title="编辑" circle @click="editPsersonRowEvent(row)"></vxe-button>
                      </template>
                      <vxe-button icon="fa fa-trash" title="删除" circle @click="removePsersonRowEvent(row)"></vxe-button>
                      <vxe-button icon="fa fa-copy" title="复制" circle @click="copyRowEvent(row)"></vxe-button>
                    </template>
                  </vxe-grid>
                </div>
              </template>
            </vxe-pulldown>
          </template>
        </vxe-form-item>

        <vxe-form-item title="身高" field="height" :item-render="{}">
          <template #default="{data}">
            <vxe-input v-model="data.height" placeholder="顾客身高" type="number" :size="size" :disabled="EditState"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="业务归属" field="itemBusinessGroupID" :item-render="{}">
          <template v-slot>
            <vxe-select v-model="sorderForm.itemBusinessGroupID" placeholder="业务归属" clearable :size="size" :disabled="EditState">
              <vxe-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label" :disabled="EditState"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料编码" field="itemID" :item-render="{}">
          <template v-slot>
            <vxe-pulldown ref="itemDown" :size="size">
              <template v-slot>
                <vxe-input v-model="sorderForm.itemText" class="my-search" placeholder="点击搜索面料" @keyup="itemKeyupEvent" @focus="itemFocusEvent" @suffix-click="itemSuffixClick" clearable @clear="itemClear" :disabled="EditState">
                </vxe-input>
                <el-button v-if="ItemtableData.length===0&&info.userType !== 2" type="success" icon="el-icon-plus" size="mini" @click="itemMasterEvent"></el-button>
              </template>
              <template v-slot:dropdown>
                <div class="dropdownitem">
                  <vxe-grid id="sorderitemgrid" highlight-hover-row auto-resize height="300" :loading="itemLoading" :pager-config="itemTablePage" :data="ItemtableData" :columns="ItemtableColumn" @cell-click="itemCellClickEvent" @page-change="itemPageChange">
                  </vxe-grid>
                </div>
              </template>
            </vxe-pulldown>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料描述" field="finalFabric" :item-render="{}">
          <template #default="{data}">
            <vxe-input v-model="data.finalFabric" clearable :size="size" :disabled="true"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料门幅" field="finalWidth" :item-render="{}">
          <template #default="{data}">
            <vxe-input v-model="data.finalWidth" type="number" clearable :size="size" :disabled="true"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料纹理" field="finalTexture" :item-render="{}">
          <template #default="{data}">
            <vxe-select v-model="data.finalTexture" placeholder="面料纹理" clearable :size="size" :disabled="true" class="finalTexture">
              <vxe-option v-for="item in ItemTextureMLGroupComboStore" :key="item.value" :value="item.value" :label="item.label" :disabled="EditState"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料成分" field="finalComposition" :item-render="{}">
          <template #default="{data}">
            <vxe-input v-model="data.finalComposition" clearable :size="size" :disabled="true"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料长度" field="itemLength" :item-render="{}">
          <template #default="{data}">
            <vxe-input v-model="data.itemLength" type="number" clearable :size="size" :disabled="EditState"></vxe-input>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="客供面料标" field="finalFabricLabel" :item-render="{}">        <vxe-input v-model="sorderForm.finalFabricLabel" clearable :size="size"></vxe-input>
      </vxe-form-item> -->

        <vxe-form-item title="期望交期" field="deliveryDate" :item-render="{}">
          <template #default>
            <!-- <vxe-input v-model="sorderForm.deliveryDate" type="date" placeholder="请选择日期" clearable :size="size"></vxe-input> -->
            <el-date-picker v-model="sorderForm.deliveryDate" type="date" placeholder="选择日期" clearable :size="size" :disabled="EditState">
            </el-date-picker>
          </template>
        </vxe-form-item>
        <vxe-form-item title="排料要求" field="layoutMethod" v-if="info.userType !== 2" :item-render="{}">
          <template #default>
            <el-select v-model="sorderForm.layoutMethod" placeholder="排料要求" size="mini" :disabled="EditState">
              <el-option v-for="item in LayoutMethodComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="是否加急" field="isUrgent" :item-render="{}">
          <template #default>
            <vxe-switch v-model="sorderForm.isUrgent" open-label="是" close-label="否" :size="size" :disabled="EditState"></vxe-switch>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="客户订单号" field="customerServicer" :item-render="{}">          <vxe-input v-model="sorderForm.customerServicer" clearable :size="size" :disabled="EditState"></vxe-input>
        </vxe-form-item> -->
        <vxe-form-item title="备注" field="remark" :item-render="{}"> <template #default>
            <vxe-textarea v-model="sorderForm.remark" placeholder="订单备注" maxlength="100" show-word-count :size="size" :disabled="true"></vxe-textarea>
          </template>
        </vxe-form-item>
      </vxe-form>
    </div>

    <vxe-modal v-model="clientPersonShow" title="顾客" width="800" resize destroy-on-close>
      <vxe-form :data="clientPsersonSelectRow" :rules="personValidRules" title-align="right" title-width="100" @submit="ClientPersonSubmitEvent">
        <vxe-form-item title="顾客编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顾客名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>

        <vxe-form-item title="身高" field="height" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="体重" field="weight" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>

        <vxe-form-item title="手机号" field="tel" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="性别" field="gender" span="12" :item-render="{name: '$switch',props:{
          openLabel:'男',closeLabel:'女'
        }}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="personAddress.Show" title="收货地址" width="1000" resize destroy-on-close>
      <vxe-toolbar>
        <template #buttons>
          <vxe-button status="success" @click="personAddressAddShowEvent()">新增地址</vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-grid border resizable height="530" :seq-config="{startIndex: (personAddress.tablePage.currentPage - 1) * personAddress.tablePage.pageSize}" :pager-config="personAddress.tablePage" :columns="personAddress.tableColumn" :data="personAddress.tableData" @page-change="handlePageChange" @cell-dblclick="personAddressTableCellClick">
      </vxe-grid>
    </vxe-modal>
    <vxe-modal v-model="itemMaster.show" title="添加面料" width="600" resize destroy-on-close>
      <vxe-form :data="itemMaster.itemForm" :rules="itemMaster.formRules" title-align="right" title-width="100" @submit="itemSubmitEvent">
        <vxe-form-item title="物料分类" field="itemGroupID" span="12" :item-render="{ name: '$select', options: ItemMLGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="业务归属" field="businessGroup" span="12" :item-render="{ name: '$select', options: BusinessGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="原始货号" field="originalItemNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="年份" field="yearNo" span="12" :item-render="{name: 'input',attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="纹理(CM)" field="textureGroupID" span="12" :item-render="{ name: '$select', options: ItemTextureMLGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="门幅" field="width" span="12" :item-render="{}"> <vxe-input v-model="itemMaster.itemForm.width" type="float"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="供应商" field="supplierItemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.supplierItemID" filterable placeholder="类别" size="mini">
              <el-option v-for="item in SupplierItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="原产地" field="placeOfOrigin" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="工艺属性" field="technologyGroupID" span="12" :item-render="{ name: '$select', options: TechnologyGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="单位" field="unitGroupID" span="12" :item-render="{ name: '$select', options: ItemUnitGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="面料成分" field="itemComp" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="personAddress.personAddressAddShow" title="添加新地址" width="40%" resize destroy-on-close mask-closable>
      <vxe-form :data="personAddress.personAddressAddForm" :rules="personAddress.formRules" @submit="personAddressAddSubmitEvent(personAddress.personAddressAddForm)">
        <vxe-form-item title="联  系  人" field="contact" :item-render="{}" span="12">
          <template #default="{ data }">
            <vxe-input v-model="data.contact" placeholder="请输入联系人名称" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="联系电话" field="contact" :item-render="{}" span="12">
          <template #default="{ data }">
            <vxe-input v-model="data.tel" placeholder="请输入联系人电话" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="收货地址" field="contact" :item-render="{}" span="24">
          <template #default="{ data }">
            <!-- <vxe-input v-model="data.address" placeholder="请输入联系人收货地址" clearable></vxe-input> -->
            <vxe-textarea v-model="data.address" placeholder="请输入联系人收货地址" clearable :autosize="{ minRows: 3, maxRows: 5 }"></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"><template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
          </template></vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </div>
</template>
<script>
// import sorderEditState from './sordereditstate'
import { mapState } from 'vuex'
import { difference, cloneDeep, isEmpty } from 'lodash'
import config from '@/config.js'
export default {
  name: 'sordermaster',
  mixins: [config],
  components: {
    // SorderDetailModel
  },
  props: {
    client: {
      type: Object,
      default: null
    },
    sorderStore: {
      type: Object,
      default: null
    },
    EditState: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      itemMaster: {
        show: false,
        itemForm: {
          id: null,
          code: null,
          itemGroupID: null,
          businessGroup: null,
          originalItemNo: null,
          yearNo: new Date().getFullYear().toString().substring(0, 4),
          technologyGroupID: null,
          textureGroupID: null,
          width: null,
          itemComp: null,
          remark: null,
          isActive: true,
          createByClientID: this.sorderStore.clientID,
          supplierItemID: null, // 供应商
          placeOfOrigin: null,
          unitGroupID: null
        },
        formRules: {
          itemGroupID: [{ required: true, message: '请选择物料分类' }],
          businessGroup: [{ required: true, message: '请选择业务归属' }],
          originalItemNo: [{ required: true, message: '请输入原始货号' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
          colorNo: [{ required: true, message: '请输入色号' }, { min: 1, max: 20, message: '长度在 1 到 20 个字符' }],
          yearNo: [{ required: true, message: '年份必填' }],
          textureGroupID: [{ required: true, message: '纹理必须选择' }],
          width: [{ required: true, message: '幅宽必须输入' }],
          technologyGroupID: [{ required: true, message: '工艺属性必须选择' }]
          // itemComp: [{ required: true, message: '成分必须输入' }]
        }
      },
      sordernum: '订单号',
      sorderForm: {
        clientID: null,
        address: null,
        contact: null,
        tel: null,
        itemID: null,
        itemLength: null,
        itemText: null,
        finalWidth: 0,
        finalTexture: null, // 纹理
        finalComposition: null, // 面料成分
        // finalFabricLabel: null, // 客供面料表
        finalFabric: null, // 客供面料号
        deliveryDate: null,
        clientPersonID: null,
        clientPersonText: null,
        height: null,
        // HalfFitting: false,
        isUrgent: false,
        remark: null,
        id: null,
        sorderDetailID: null,
        statusID: 0,
        sorderDetailModels: [],
        sorderTypeID: 1,
        itemBusinessGroupID: null, // 面料业务归属
        sorderPsersons: [],
        layoutMethod: 1// 排料方式

      },
      size: 'mini',
      sorderPsersonsColumn: [],
      sorderformRules: {
        clientID: [{ required: true, message: '请输入客户信息' }],
        itemID: [{ required: true, message: '请输入面料信息' }],
        finalTexture: [{ required: true, message: '请选择纹理' }],
        finalWidth: [{ required: true, message: '幅宽必填' }],
        height: [{ required: true, message: '请输入身高' }],
        // finalFabric: [{ required: true, message: '面料描述不能为空' }],
        // finalComposition: [{ required: true, message: '面料成分不能为空' }],
        // sorderTypeID: [{ required: true, message: '请选择订单类型' }],
        tel: [{ required: true, message: '联系电话不能为空' }],
        contact: [{ required: true, message: '联系人不能为空' }],
        address: [{ required: true, message: '收货地址不能为空' }],
        itemBusinessGroupID: [{ required: true, message: '面料业务属性必填' }]
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },

      itemLoading: false,
      personLoading: false,
      ItemtableColumn: [
        { field: 'label', title: '面料', width: '200' },
        { field: 'originalItemNo', title: '原始货号', width: '120' },
        { field: 'stock', title: '库存', width: '80' },
        { field: 'width', title: '幅宽', width: '100' },
        { field: 'finalTexture', title: '纹理', width: '130' }
      ],

      PsersontableColumn: [
        { field: 'code', title: '顾客编码', width: 120, editRender: { name: 'input' } },
        { field: 'tel', title: '手机号', width: 120, editRender: { name: '$input', props: { type: 'number', min: 0, max: 500 } } },
        { field: 'codeName', title: '顾客名称', width: 120, editRender: { name: 'input' } },
        { field: 'height', title: '身高', width: 150, editRender: { name: '$input', props: { type: 'number', min: 0, max: 500 } } },
        {
          field: 'gender',
          title: '性别',
          width: 100,
          editRender: { name: '$switch', props: { onLabel: '男', offLabel: '女' } },
          slots: {
            default: 'sex_default'
          }
        },
        { field: 'weight', title: '体重', width: 100, editRender: { name: '$input', props: { type: 'number', min: 1, max: 500 } } },
        { title: '操作', width: 150, slots: { default: 'operate' } }
      ],
      personValidRules: {
        code: [
          { required: true, message: '顾客编码必须填写' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }
        ],
        codeName: [
          { required: true, message: '顾客名称必须填写' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }
        ],
        height: [
          { required: true, message: '身高必须填写' }
        ]
        // weight: [
        //   { required: true, message: '体重必须填写' }
        // ]
        // tel: [
        //   { required: true, message: '手机号必须填写' },
        //   { message: '请填写正确11位手机号', type: 'string', pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/ }
        // ]
      },
      ItemtableData: [],
      PsersontableData: [],
      itemTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      personAddress: {
        personAddressAddShow: false,
        personAddressAddForm: {
          id: null,
          clientID: null,
          contact: null,
          tel: null,
          address: null,
          remark: null
        },
        formRules: {
          contact: [{ required: true, message: '请输入联系人名称' }, { min: 2, max: 30, message: '长度在 2 到 30 个字符' }],
          tel: [{ required: true, message: '请输入电话号码' }, { min: 7, max: 15, message: '长度在 7 到 15 个字符' }],
          address: [{ required: true, message: '请输入地址' }, { min: 1, max: 100, message: '长度在 1 到 100 个字符' }]
        },
        Show: false,
        tableData: [],
        tableColumn: [
          { type: 'seq', width: 60 },
          { field: 'contact', title: '联系人' },
          { field: 'tel', title: '电话' },
          { field: 'address', title: '地址' },
          // { field: 'contactDesc', title: '联系人描述' },
          // { field: 'fax', title: '传真' },
          // { field: 'mobile', title: '座机' },
          // { field: 'email', title: '邮件' },
          // { field: 'state', title: '国家' },
          // { field: 'province', title: '省' },
          // { field: 'city', title: '市' },
          // { field: 'county', title: '县' },
          // { field: 'street', title: '街道' },
          // { field: 'port', title: '港口' },
          // { field: 'transport', title: '运送方式' },
          { field: 'remark', title: '备注' },
          {
            field: '',
            title: '操作',
            showOverflow: true,
            slots: {
              // 使用 JSX 渲染
              default: ({ row }) => {
                return [
                  // <span style="color: blue" onClick={() => addressClickEvent(row)}>自定义模板内容</span>
                  <vxe-button type="text" icon="fa fa-files-o" onClick={() => this.addressCopyEvent(row)}></vxe-button>,
                  <vxe-button type="text" icon="fa fa-edit" onClick={() => this.addressEditEvent(row)}></vxe-button>,
                  <vxe-button type="text" icon="fa fa-trash-o" onClick={() => this.addressDeleteEvent(row)} ></vxe-button>
                ]
              }
            }
          }
        ],
        tablePage: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          align: 'left',
          pageSizes: [10, 20, 50, 100, 200, 500],
          layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
          perfect: true,
          id: null,
          maxResultCount: 10,
          skipCount: 0
        }
      },
      personTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      editableTabsValue: '',
      api: {
        get: '/mtm/odm_sorder/get',
        addItem: '/mtm/odm_sorder/AddItem',
        modify: '/mtm/odm_sorder/modify',
        sorderstatechange: '/mtm/odm_sorder/SorderStateChange',
        sorderItem: '/mtm/odm_sorder/GetSorderItems',
        sorderPserson: '/mtm/odm_sorder/GetSorderPsersons',
        clientPsersonAdd: '/mtm/bAD_ClientPerson/adds',
        clientPsersonEdit: '/mtm/bAD_ClientPerson/updates',
        clientPsersonDelete: '/mtm/bAD_ClientPerson/deletes',
        sorderDetailModelEdit: '/mtm/oDM_SorderDetailModel/Modify',
        sorderDetailSizeEdit: '/mtm/oDM_SorderDetailSize/modify',
        sorderDetailElemEdit: '/mtm/oDM_SorderDetailElem/modify',
        sorderDetailBodyEdit: '/mtm/oDM_SorderDetailBody/modify',
        ItemTextureMLGroupComboStore: '/mtm/combo/ItemTextureMLGroupComboStore',
        ItemUnitGroupComboStore: '/mtm/combo/ItemUnitGroupComboStore',
        SorderStatusComboStore: '/mtm/combo/sorderStatusComboStore',
        groupComboStore: '/mtm/combo/groupComboStore',
        SorderTypeComboStore: '/mtm/combo/SorderTypeComboStore',
        ClientAddressAdd: '/mtm/bAD_ClientAddress/adds',
        ClientAddressEdit: '/mtm/bAD_ClientAddress/updates',
        ClientAddressDelete: '/mtm/bAD_ClientAddress/Deletes',
        PersonAddressGet: '/mtm/bAD_ClientAddress/get',
        ItemMLGroupComboStore: '/mtm/combo/ItemMLGroupComboStore',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore',
        TechnologyGroupComboStore: '/mtm/combo/TechnologyGroupComboStore',
        SupplierItemComboStore: '/mtm/combo/SupplierItemComboStore',
        LayoutMethodComboStore: '/mtm/combo/LayoutMethodComboStore'
      },
      ItemUnitGroupComboStore: [],
      LayoutMethodComboStore: [],
      SupplierItemComboStore: [],
      ItemMLGroupComboStore: [],
      BusinessGroupComboStore: [],
      TechnologyGroupComboStore: [],
      GroupComboStore: [],
      ItemTextureMLGroupComboStore: [],
      SorderTypeComboStore: [],
      clientPersonShow: false,
      clientPsersonSelectRow: {
        id: null,
        code: null,
        tel: null,
        codeName: null,
        height: null,
        weight: null,
        gender: true,
        clientID: null
      }
    }
  },
  watch: {
    sorderStore: {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== null) {
          this.sorderForm = Object.assign(this.sorderForm, newVal)
          this.personAddress.tablePage.id = newVal.clientID
          this.clientPsersonSelectRow.clientID = newVal.clientID
        }
      }
    },
    client: {
      deep: true,
      async handler(newVal, oldVal) {
        await this.personClear()
        if (newVal !== null) {
          this.sorderForm.clientID = newVal.id
          this.clientPsersonSelectRow.clientID = newVal.id
          this.sorderForm.contact = newVal.contact
          this.sorderForm.address = newVal.address
          this.sorderForm.tel = newVal.tel
        } else {
          this.sorderForm.clientID = null
          this.sorderForm.contact = null
          this.sorderForm.address = null
          this.sorderForm.tel = null
        }
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  async created() {
    this.userTypeSet()
    await this.getCombStore()
    await this.getItembyQuery(null, this.sorderForm.itemId)
    this.$utils.find(this.PsersontableColumn, item => item.field === 'gender').editRender.options = this.sexList
    this.sorderForm = cloneDeep(Object.assign(this.sorderForm, this.sorderStore))
    this.clientPsersonSelectRow.clientID = this.sorderStore.clientID
    this.personAddress.tablePage.id = this.sorderStore.clientID
  },
  methods: {
    async personClear() {
      this.sorderForm.clientPersonID = null
      this.sorderForm.clientPersonText = null
      if (this.sorderForm.clientID === null || this.sorderForm.clientID === '') return
      await this.getPsersonbyQuery(null, this.sorderForm.clientPersonID)
    },
    personAddressAddShowEvent() {
      this.personAddress.personAddressAddShow = true
      this.personAddress.personAddressAddForm.clientID = this.sorderForm.clientID
    },
    async personAddressAddSubmitEvent(data) {
      var url = data.id === null ? this.api.ClientAddressAdd : this.api.ClientAddressEdit
      await this.$api.ActionRequest(url, [data]).then(async result => {
        this.$XModal.message({ message: '提交成功！', status: 'success' })
        this.personAddressGet()
        this.clearAddress()
        this.personAddress.personAddressAddShow = false
      })
    },
    async addressCopyEvent(data) {
      this.personAddress.personAddressAddForm.id = null
      this.personAddress.personAddressAddForm.contact = data.contact
      this.personAddress.personAddressAddForm.clientID = this.sorderForm.clientID
      this.personAddress.personAddressAddForm.tel = data.tel
      this.personAddress.personAddressAddForm.address = data.address
      this.personAddress.personAddressAddForm.remark = data.remark
      this.personAddress.personAddressAddShow = true
    },
    async addressEditEvent(data) {
      this.personAddress.personAddressAddForm.id = data.id
      this.personAddress.personAddressAddForm.contact = data.contact
      this.personAddress.personAddressAddForm.clientID = data.clientID
      this.personAddress.personAddressAddForm.tel = data.tel
      this.personAddress.personAddressAddForm.address = data.address
      this.personAddress.personAddressAddForm.remark = data.remark
      this.personAddress.personAddressAddShow = true
    },
    async addressDeleteEvent(data) {
      await this.$api.ActionRequest(this.api.ClientAddressDelete, [data]).then(async result => {
        this.$XModal.message({ message: '删除成功！', status: 'success' })
        this.personAddressGet()
      })
    },
    clearAddress() {
      this.personAddress.personAddressAddForm.id = null
      this.personAddress.personAddressAddForm.contact = null
      this.personAddress.personAddressAddForm.tel = null
      this.personAddress.personAddressAddForm.address = null
      this.personAddress.personAddressAddForm.remark = null
    },
    personSuffixClick() {
      this.$refs.personDown.togglePanel()
    },
    itemSuffixClick() {
      this.$refs.itemDown.togglePanel()
    },
    async personFocusEvent() {
      if (this.sorderForm.clientID === null) {
        this.$notify({
          message: '请先选择客户',
          type: 'error'
        })
        return
      }
      if (this.PsersontableData.length === 0) {
        await this.getPsersonbyQuery()
      }
      this.$refs.personDown.showPanel()
    },
    itemMasterEvent() {
      this.itemMaster.itemForm.originalItemNo = this.sorderForm.itemText
      this.itemMaster.show = !this.itemMaster.show
      if (this.ItemUnitGroupComboStore.length > 0) {
        this.itemMaster.itemForm.unitGroupID = this.ItemUnitGroupComboStore[0].value
      }
    },
    async itemSubmitEvent() {
      await this.$api.ActionRequest(this.api.addItem, this.itemMaster.itemForm).then(async result => {
        this.$notify({
          message: '添加成功',
          type: 'success'
        })
        await this.getItembyQuery(this.itemMaster.itemForm.originalItemNo)
        this.ItemtableData.forEach(item => {
          if (item.originalItemNo.indexOf(this.itemMaster.itemForm.originalItemNo) >= 0) {
            this.itemCellClickEvent({ row: item })
          }
        })
        // var first = this.ItemtableData.GetFirstElement("originalItemNo", this.itemMaster.itemForm.originalItemNo)
        // if (first && first !== null) {
        //   console.log(first)
        //   this.itemCellClickEvent({ row: first })
        // }
        this.itemMaster.show = false
        this.$utils.clear(this.itemMaster.itemForm, null)
        this.itemMaster.itemForm.isActive = true
        this.itemMaster.itemForm.yearNo = new Date().getFullYear().toString().substring(2, 4)
      })
    },
    async personkeyupEvent(query) {
      if (!query) return
      const { value } = query || ''
      this.personTablePage.currentPage = 1
      if (this.sorderForm.clientID === null || this.sorderForm.clientID === '') {
        this.$notify({
          message: '请先选择客户',
          type: 'error'
        })
        return
      };
      await this.getPsersonbyQuery(value)
    },
    async itemClear() {
      this.sorderForm.itemText = null
      this.sorderForm.itemID = null
      this.sorderForm.finalWidth = null
      this.sorderForm.finalTexture = null
      this.sorderForm.finalComposition = null
      // this.sorderForm.finalFabricLabel = null
      this.sorderForm.finalFabric = null
      // this.ZB.KGML.id = null
      await this.getItembyQuery(null, this.sorderForm.itemID)
    },
    itemFocusEvent() {
      this.$refs.itemDown.showPanel()
    },
    editPsersonRowEvent(row) {
      this.clientPsersonSelectRow = Object.assign(this.clientPsersonSelectRow, cloneDeep(row))
      this.clientPersonShow = true
      // this.$refs.personGrid.setActiveRow(row)
    },

    copyRowEvent(row) {
      this.clientPsersonSelectRow = Object.assign(this.clientPsersonSelectRow, cloneDeep(row))
      this.clientPsersonSelectRow.id = null
      this.clientPersonShow = true
    },
    async itemKeyupEvent(query) {
      if (!query) return
      const { value } = query || ''
      this.itemTablePage.currentPage = 1
      await this.getItembyQuery(value)
    },
    async getCombStore() {
      await this.$api.ActionRequest(this.api.ItemTextureMLGroupComboStore).then(result => {
        this.ItemTextureMLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.LayoutMethodComboStore).then(result => {
        this.LayoutMethodComboStore = result
      })
      await this.$api.ActionRequest(this.api.SorderStatusComboStore).then(result => {
        this.SorderStatusComboStore = result
      })
      await this.$api.ActionRequest(this.api.groupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SorderTypeComboStore).then(result => {
        this.SorderTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemMLGroupComboStore).then(result => {
        this.ItemMLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.TechnologyGroupComboStore).then(result => {
        this.TechnologyGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SupplierItemComboStore).then(result => {
        this.SupplierItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemUnitGroupComboStore).then(result => {
        this.ItemUnitGroupComboStore = result
      })
    },
    // 用户类型设置
    userTypeSet() {
      this.itemColumns()
      if (this.info.userType === 2) {
        this.sorderForm.clientID = this.info.clientID
        this.sorderForm.contact = this.info.clientContact
        this.sorderForm.address = this.info.clientAddress
        this.sorderForm.tel = this.info.clientTel
      }
    },
    itemColumns() {
      if (this.info.userType !== 0 && this.info.userType !== 1) {
        this.ItemtableColumn = this.ItemtableColumn.filter(item => { return item.field !== 'originalItemNo' })
      }
    },
    async getItembyQuery(query = null, itemId = null) {
      this.itemLoading = true
      await this.$api.ActionRequest(this.api.sorderItem, { Id: itemId, Text: query, clientID: this.sorderForm.clientID, MaxResultCount: this.itemTablePage.pageSize, SkipCount: (this.itemTablePage.currentPage - 1) * this.itemTablePage.pageSize }).then(result => {
        this.itemTablePage.total = result.totalCount
        this.ItemtableData = result.items
        this.itemLoading = false
      })
    },
    async savePsersonRowEvent(row) {
      var b = await this.fullValidEvent()
      if (!b) {
        return
      }
      this.$refs.personGrid.clearActived().then(() => {
        this.loading = true
        var url = row.id == null ? this.api.clientPsersonAdd : this.api.clientPsersonEdit
        this.$api.ActionRequest(url, [row]).then(result => {
          this.loading = false
          this.getPsersonbyQuery()
          this.$XModal.message({ message: '保存成功！', status: 'success' })
        })
      })
    },
    ClientPersonSubmitEvent() {
      if (isEmpty(this.clientPsersonSelectRow.id) || this.clientPsersonSelectRow.id === undefined || this.clientPsersonSelectRow.id === null) {
        this.$api.ActionRequest(this.api.clientPsersonAdd, [this.clientPsersonSelectRow]).then(result => {
          this.$XModal.message({ message: '新增成功', status: 'success' })
          // this.loadData()
          this.getPsersonbyQuery()
          this.clientPersonShow = false
          this.$utils.clear(this.clientPsersonSelectRow, null)
          this.clientPsersonSelectRow.clientID = this.sorderStore.clientID
        })
      } else {
        this.$api.ActionRequest(this.api.clientPsersonEdit, [this.clientPsersonSelectRow]).then(result => {
          this.$XModal.message({ message: '保存成功', status: 'success' })
          // this.loadData()
          this.getPsersonbyQuery()
          this.clientPersonShow = false
          this.$utils.clear(this.clientPsersonSelectRow, null)
          this.clientPsersonSelectRow.clientID = this.sorderStore.clientID
        })
      }
      this.$refs.personDown.showPanel()
    },
    async removePsersonRowEvent(row) {
      await this.$api.ActionRequest(this.api.clientPsersonDelete, [row]).then(result => {
        this.$XModal.message({ message: '删除成功', status: 'success' })
        this.getPsersonbyQuery()
        this.$refs.personDown.showPanel()
      })
    },
    async fullValidEvent() {
      var xtable = this.$refs.personGrid
      const errMap = await xtable.fullValidate().catch(errMap => errMap)
      if (errMap) {
        const msgList = []
        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            const { rowIndex, column, rules } = params
            rules.forEach(rule => {
              msgList.push(`第 ${rowIndex} 行 ${column.title} 校验错误：${rule.message}`)
            })
          })
        })
        this.$XModal.message({
          status: 'error',
          message: () => {
            return [
              <div class="red" style="max-height: 400px;overflow: auto;">
                {
                  msgList.map(msg => <div>{msg}</div>)
                }
              </div>
            ]
          }
        })
        return false
      } else {
        return true
      }
    },
    async getPsersonbyQuery(query = null, personId = null) {
      this.personLoading = true
      await this.$api.ActionRequest(this.api.sorderPserson, { ClientID: this.sorderForm.clientID, isActive: true, Id: personId, Text: query, MaxResultCount: this.personTablePage.pageSize, SkipCount: (this.personTablePage.currentPage - 1) * this.personTablePage.pageSize }).then(result => {
        this.personTablePage.total = result.totalCount
        this.PsersontableData = result.items
        this.personLoading = false
      })
    },
    PsersonCellClickEvent({ row, column }) {
      if (column.title !== '操作') {
        this.sorderForm.clientPersonID = row.id
        this.sorderForm.clientPersonText = row.label
        this.sorderForm.height = row.height + ''
        this.$refs.personDown.hidePanel()
      }
    },
    async personInsertEvent(row) {
      var sorderclientid = this.sorderForm.clientID
      const record = { clientID: sorderclientid, gender: true, Id: null, isActive: 1, height: 0 }
      const { row: newRow } = await this.$refs.personGrid.insertAt(record)
      await this.$refs.personGrid.setActiveCell(newRow, 'code')
    },
    async PsersonPageChange({ currentPage, pageSize }) {
      this.personTablePage.pageSize = pageSize
      this.personTablePage.currentPage = currentPage
      await this.getPsersonbyQuery()
    },
    async itemPageChange({ currentPage, pageSize }) {
      this.itemTablePage.pageSize = pageSize
      this.itemTablePage.currentPage = currentPage
      await this.getItembyQuery()
    },
    async itemCellClickEvent({ row }) {
      this.sorderForm.itemText = row.label
      this.sorderForm.itemID = row.itemID
      this.sorderForm.finalWidth = row.width
      this.sorderForm.finalTexture = row.finalTextureID
      this.sorderForm.finalComposition = row.finalComposition
      var id = row.itemID.toLocaleLowerCase()
      if (id === this.ZB.KGML.id) {
        this.sorderForm.finalFabric = null
      } else {
        this.sorderForm.finalFabric = row.label
      }
      this.$refs.itemDown.hidePanel()
    },
    modelgroupchange(groups) {
      console.log(groups)
      var _modelgroups = this.sorderForm.sorderDetailModels.map(a => { return a.title })
      var res = null
      if (this.sorderForm.sorderDetailModels.length < groups.length) {
        res = difference(groups, _modelgroups)
        const newTabName = res[0]
        this.detailPsersonColumnChange(res[0], 'add')
        this.sorderForm.sorderDetailModels.push({
          title: newTabName,
          name: newTabName,
          id: null,
          modelID: null,
          sorderSizeTypeID: '2',
          qty: 1,
          isChecked: false,
          isCheckedElem: false,
          message: null,
          messageElem: null
        })
        this.editableTabsValue = newTabName
      }
      if (this.sorderForm.sorderDetailModels.length > groups.length) {
        res = difference(_modelgroups, groups)
        const activeName = groups[groups.length - 1]
        this.detailPsersonColumnChange(res[0], 'remove')
        this.editableTabsValue = activeName
        this.sorderForm.sorderDetailModels = this.sorderForm.sorderDetailModels.filter(tab => tab.title !== res[0])
      }
    },
    detailPsersonColumnChange(group, action) {
      // debugger
      // if (action === 'add') {
      //   this.sorderPsersonsColumn.push({})
      // }
      // if (action === 'remove') {
      //   this.$utils.remove(this.sorderPsersonsColumn, item => item.title === name)
      // }
    },
    personAddressShowEvent() {
      if (this.sorderStore.clientID === null || this.sorderStore.clientID === '') {
        return
      }
      this.personAddress.Show = true
      this.personAddressGet()
    },
    personAddressGet() {
      this.$api.ActionRequest(this.api.PersonAddressGet, this.personAddress.tablePage).then(result => {
        this.personAddress.tableData = result.items
        this.personAddress.total = result.totalCount
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.personAddress.tablePage.currentPage = currentPage
      this.personAddress.tablePage.pageSize = pageSize
      this.personAddress.tablePage.maxResultCount = pageSize
      this.personAddress.tablePage.skipCount = (currentPage - 1) * pageSize
      this.personAddressGet()
    },
    personAddressTableCellClick({ row }) {
      this.sorderForm.contact = row.contact
      this.sorderForm.tel = row.tel
      this.sorderForm.address = row.address
      this.sorderForm.clientAddressID = row.clientAddressID
      this.personAddress.Show = false
    }
  }
}
</script>

<style lang="scss">
.model-check-warning {
  color: #e6a23c;
}
.my-dropdownitem {
  z-index: 999;
}
.my-green {
  color: green;
}
// .finalTexture {
//   z-index: 999;
//   .vxe-select--panel{
//      z-index: 999;
//   }
//   .vxe-select-option--wrapper{
//      z-index: 999;
//   }
// }
.sorderformRules {
  padding: 10px;
}
// .dropdownitem {
//   z-index: 999;
//   .vxe-table {
//     z-index: 999;
//     .vxe-table--main-wrapper{
//         z-index: 999;
//     }
//   }
// }
</style>
