<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="masterSubmitEvent" v-if="menuAction.allowEdit">保存</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
        </template>
      </vxe-toolbar>

    </template>

    <template>
      <d2-container>
        <split-pane :min-percent='15' :default-percent='30' split="vertical">
          <template slot="paneL">
            <vxe-form :data="form" :items="masterformItems" :rules="masterformRules" title-align="right" title-width="100"></vxe-form>
          </template>
          <template slot="paneR">
            <d2-container>
              <template slot="header">
                <vxe-toolbar perfect custom>
                  <template v-slot:tools>
                    <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
                      <!-- <vxe-form-item field="modelElemTypeID" :item-render="{}">                        <template #default="{ data }">
<el-select v-model="data.modelElemTypeID" placeholder="款式类别" size="mini" clearable>
                          <el-option v-for="item in modelElemTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                        </el-select>
</template>
                    </vxe-form-item>-->

                      <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
                      </vxe-form-item>
                      <vxe-form-item :item-render="{}"> <template #default>
                          <vxe-button type="submit" status="success">查询</vxe-button>
                          <vxe-button type="reset">重置</vxe-button>
                        </template>
                      </vxe-form-item>
                    </vxe-form>
                    <!-- <vxe-button type="submit" status="success" @click="loadData()">查询</vxe-button>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button> -->
                    <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
                  </template>
                  <!-- <template v-slot:tools>

                  </template> -->

                </vxe-toolbar>
              </template>
              <vxe-table ref='detail_table' id="mom_modelelemlist_detail_table" :custom-config="{storage: true}" :row-class-name="rowClassName" :height="TableHeight" :data="tableData">
                <!-- <vxe-table-column type="seq" width="60"></vxe-table-column> -->
                <vxe-table-column field="default" title="默认" sortable :formatter='formatBool' width="100px"></vxe-table-column>
                <vxe-table-column field="code" title="款式明细编码" sortable width="100px"></vxe-table-column>
                <vxe-table-column field="codeName" title="款式明细名称" sortable width="100px"></vxe-table-column>
                <vxe-table-column field="isInputItem" title="是否选择物料" :formatter='formatBool' sortable width="100px"></vxe-table-column>
                <vxe-table-column field="isInput" title="是否输入内容" :formatter='formatBool' sortable width="100px"></vxe-table-column>
                <vxe-table-column field="isItemAdd" title="是否添加物料？" :formatter='formatBool' sortable width="100px"></vxe-table-column>
                <vxe-table-column field="isItemImageAdd" title="是否添加物料图片？" :formatter='formatBool' sortable width="100px"></vxe-table-column>
                <vxe-table-column field="isItemElem" title="是否配色方案？" :formatter='formatBool' sortable width="100px"></vxe-table-column>
                <vxe-table-column field="itemName" title="默认货号" sortable width="100px"></vxe-table-column>
                <!-- <vxe-table-column field="price" title="默认单价" sortable width="100px"></vxe-table-column> -->
                <vxe-table-column field="qty" title="默认耗量" sortable width="100px"></vxe-table-column>
                <vxe-table-column field="sort" title="排序" sortable width="100px"></vxe-table-column>
                <vxe-table-column field="itemSeriesText" title="规则系列" sortable width="100px"></vxe-table-column>
                <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
                <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
                <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
                <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
                <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
                <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px"></vxe-table-column>
                <vxe-table-column title="操作" width="150px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
                  <template v-slot="{ row }">
                    <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
                    <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
                    <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
                  </template>
                </vxe-table-column>
              </vxe-table>
              <template slot="footer">
                <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
                  <template v-slot:right>
                    <mtm-footer-companyinfo v-if="footerCompanyInfo" />
                  </template>
                </vxe-pager>
              </template>
            </d2-container>
          </template>
        </split-pane>
      </d2-container>
    </template>

    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="detailformRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式" span="24" :item-render="{}"> <vxe-input v-model="form.code" clearable disabled></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="款式明细编码" field="code" span="12" :item-render="{}"> <vxe-input v-model="selectRow.code" placeholder="请输入名称" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="名称" field="codeName" span="12" :item-render="{}"> <vxe-input v-model="selectRow.codeName" placeholder="请输入名称" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="是否选择物料" title-width="150" field="isInputItem" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isInputItem"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="是否输入内容" title-width="150" field="isInput" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isInput"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="允许添加物料?" title-width="150" field="isItemAdd" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isItemAdd"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="允许添加物料图片?" title-width="150" field="isItemImageAdd" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isItemImageAdd"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="默认货号" field="itemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod" :loading="loading" clearable size="mini">
              <el-option v-for="item in itemoptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="允许配色方案?" title-width="150" field="isItemElem" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isItemElem"></vxe-switch>
          </template>
        </vxe-form-item>

        <!-- <vxe-form-item title="默认单价" field="price" span="12" :item-render="{}">          <vxe-input v-model="selectRow.price" placeholder="请输入名称" clearable type="number"></vxe-input>
        </vxe-form-item> -->
        <vxe-form-item title="默认耗量" field="qty" span="12" :item-render="{}"> <vxe-input v-model="selectRow.qty" placeholder="请输入名称" clearable type="number"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="排序" field="sort" span="12" :item-render="{}"> <vxe-input v-model="selectRow.sort" placeholder="请输入名称" clearable type="number"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="物料分类" span="24" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemGroupIDs" multiple placeholder="请选择" size="mini" style="width:100%" :disabled="!selectRow.isInputItem">
              <el-option v-for="item in ItemGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{}"> <template #default>
            <vxe-textarea v-model="selectRow.remark" placeholder="请输入备注" clearable></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'MomModelelemlistDetail',
  mixins: [detailTableMixins],

  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        isInputItem: false,
        isInput: false,
        price: 0,
        qty: 0,
        itemSeriesText: '', // 系列
        itemID: '',
        modelElemListID: null,
        isItemImageAdd: false, // 是否允许添加物料图片
        isItemAdd: false, // 是否允许添加物料,
        sort: null
      },
      tableRef: 'detail_table',
      masterformRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      masterformItems: [
        { field: 'groupID', title: '类别', span: 24, itemRender: { name: '$select', options: [], props: { disabled: true } } },
        { field: 'modelElemTypeID', title: '工艺', span: 24, itemRender: { name: '$select', options: [], props: { disabled: true } } },
        { field: 'modelElemBaseID', title: '款式部位', span: 24, itemRender: { name: '$select', options: [], props: { disabled: true } } },
        { field: 'code', title: '编码', span: 24, itemRender: { name: '$input', props: { disabled: true } } },
        { field: 'codeName', title: '编码名称', span: 24, itemRender: { name: '$input', props: { disabled: true } } },

        { field: 'cadSeq', title: 'CAD顺序', span: 12, itemRender: { name: '$input', props: { type: 'number', disabled: true } } },
        { field: 'marketSeq', title: '市场顺序', span: 12, itemRender: { name: '$input', props: { type: 'number', disabled: true } } },
        { field: 'elemSeq', title: '报表顺序', span: 12, itemRender: { name: '$input', props: { type: 'number', disabled: true } } },
        { field: 'genderID', title: '性别', span: 12, itemRender: { name: '$select', options: [], props: { disabled: true } } },
        { field: 'isPlus', title: '深定制', span: 12, itemRender: { name: '$switch', props: { disabled: true } } },
        { field: 'isPlanShow', title: '配色显示', span: 12, itemRender: { name: '$switch', props: { disabled: true } } },

        // { field: 'isItem', title: '属于辅料', span: 12, itemRender: { name: '$switch', props: { disabled: true } } },
        { field: 'isClientShow', title: '客户端显示', span: 12, itemRender: { name: '$switch', props: { disabled: true } } },
        { field: 'isCustomerShow', title: '基础显示', span: 12, itemRender: { name: '$switch', props: { disabled: true } } },
        // { field: 'iscustomerEdit', title: '客服编辑', span: 12, itemRender: { name: '$switch', props: { disabled: true } } },
        { field: 'reportShow', title: '报表显示', span: 12, itemRender: { name: '$select', options: [], props: { disabled: true } } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch', props: { disabled: true } } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$textarea', props: { disabled: true } } }
      ],
      detailformItems: [
        { field: 'code', title: '编码', span: 24, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 24, itemRender: { name: '$input' } }
      ],
      detailformRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        isInputItem: [{ required: true }],
        isInput: [{ required: true }]
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      api: {
        masteredit: '/mtm/mom_modelelemlist/updates',
        get: '/mtm/mOM_ModelElem/getByModelElemListID',
        add: '/mtm/mOM_ModelElem/adds',
        edit: '/mtm/mOM_ModelElem/updates',
        delete: '/mtm/mOM_ModelElem/deletes',
        GroupComboStore: '/mtm/comboQuery/groupComboStoreByQuery',
        ModelElemBaseComboStore: '/mtm/combo/modelElemBaseComboStore',
        ModelElemTypeComboStore: '/mtm/combo/modelElemTypeComboStore',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        ItemMLGroupComboStore: '/mtm/combo/ItemMLGroupComboStore',
        ItemFLGroupComboStore: '/mtm/combo/ItemFLGroupComboStore'
      },
      ItemMLGroupComboStore: [],
      ItemFLGroupComboStore: [],
      footerCompanyInfo: false,
      GroupComboStore: [],
      ModelElemBaseComboStore: [],
      ModelElemTypeComboStore: [],
      ReportShowComboStore: [{ value: 0, label: '不显示' }, { value: 1, label: '需要时显示' }, { value: 2, label: '永远显示' }],
      sexList: [
        { label: '女', value: 'false' },
        { label: '男', value: 'true' }
      ],
      itemoptions: [],
      loading: false
    }
  },
  watch: {
    'form.id': {
      deep: true,
      async handler (newVal, oldVal) {
        // console.log(`form---newVal:${newVal},oldVal:${oldVal}`)
        this.formData.modelElemListID = newVal
        if (newVal !== oldVal) {
          await this.loadData(this.form)
        }
      }
    }
  },
  computed: {
    ItemGroupComboStore: function () {
      return this.$utils.union(this.ItemMLGroupComboStore, this.ItemFLGroupComboStore)
    }
  },
  async created () {
    this.form.genderID = '' + this.form.genderID + ''
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
      this.tableData = data
    })
    this.formData.modelElemListID = this.form.id
    this.$utils.find(this.masterformItems, item => item.field === 'genderID').itemRender.options = this.sexList
    this.$utils.find(this.masterformItems, item => item.field === 'groupID').itemRender.options = this.GroupComboStore
    this.$utils.find(this.masterformItems, item => item.field === 'modelElemTypeID').itemRender.options = this.ModelElemTypeComboStore
    this.$utils.find(this.masterformItems, item => item.field === 'modelElemBaseID').itemRender.options = this.ModelElemBaseComboStore
    this.$utils.find(this.masterformItems, item => item.field === 'reportShow').itemRender.options = this.ReportShowComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore, { classID: 1 }).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemBaseComboStore).then(result => {
        this.ModelElemBaseComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemTypeComboStore).then(result => {
        this.ModelElemTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemMLGroupComboStore).then(result => {
        this.ItemMLGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemFLGroupComboStore).then(result => {
        this.ItemFLGroupComboStore = result
      })
    },
    masterSubmitEvent () {
      this.$api.ActionRequest(this.api.masteredit, [this.form]).then(result => {
        this.$XModal.message({ message: '保存成功', status: 'success' })
        this.loadData({ id: this.form.id })
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.itemoptions = result
      })
    },
    goback () {
      this.searchForm.text = null
      this.$emit('nextpage', { pagename: 'master', data: {}, masterSeach: this.masterSeach, keepalive: true })
    },
    // 新增
    insertEvent () {
      console.log(this.formData.modelElemListID)
      this.selectRow = cloneDeep(this.formData)
      this.showEdit = true
    },
    // 编辑
    editEvent (row) {
      this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.itemoptions = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
