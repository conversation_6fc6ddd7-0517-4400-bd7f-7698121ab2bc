docker login -u admin -p xm123456 *************:80

docker build -t test:t1 .


docker run -p 3000:80 -d --name test test:t1



rancher login https://192.168.1.211/v3  --token token-srw4n:7jbq8lwt6p2zgxqjv5j6v6w695q2gprc72z7vw4ttwtvmh9jx8hbpf

rancher context switch mtm-front-end

rancher kubectl set image deploy mtm-front-end mtm-front-end=*************:80/mtm-front-end/mtm-front-end:t0.0.19 -n devservice

*************:80/mtm-front-end/mtm-front-end:t0.0.18

##build 失败
```
$env:NODE_OPTIONS="--openssl-legacy-provider" 
或
set NODE_OPTIONS=--openssl-legacy-provider
 
补充:
在终端输入一次只能本次生效，如果想永久生效可以在 package.json 文件中修改对应的 scripts 节点的内容如下：
 
"serve": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve",
"build": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build",
```

 docker build -t font .
	 docker run -d --name font -p 127.0.0.1:30000:80 font
	 docker rm font
	 docker rmi font