<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="perfect" @click="TreeExpandEvent()">{{TreeExpand?'折叠所有':'展开所有'}}</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="ClientGroup" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.ClientGroup" placeholder="客户分类" clearable>
                  <vxe-option v-for="item in ClientGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="ClientGrade" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.ClientGrade" placeholder="客户等级" clearable>
                  <vxe-option v-for="item in ClientGradeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table ref='master_table' :row-class-name="rowClassName" id='BadClientMasterTable' :tree-config="{children: 'childs', expandAll: true}" :loading="tableLoading" @cell-dblclick="cellDblClick" @cell-click='tableCellClick' :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px" title="序号"></vxe-table-column>
      <vxe-table-column field="code" title="客户编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="编码名称" sortable show-overflow width="100px" tree-node :formatter='formatName'></vxe-table-column>
      <vxe-table-column field="shortName" title="简称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="clientGroupText" title="分类" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="clientGradeText" title="等级" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="tel" title="手机" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="fax" title="传真" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="mobile" title="电话" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="accountPaymentGroupText" title="支付模式" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="currencyText" title="货币" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="accountBalance" title="余额" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="accountLimit" title="额度" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="email" title="Email" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="address" title="地址" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="discount" title="专属折扣" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="itemDiscount" title="面料耗量折扣" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="inheritedParentModel" title="继承总店" :formatter='formatBool' sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="inheritedParentBill" title="继承总店账单" :formatter='formatBool' sortable show-overflow width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="source" title="来源" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="customerCode" title="自编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="supplierCode" title="供应商编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="bankID" title="银行" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="currencyID" title="货币" sortable show-overflow width="100px"> </vxe-table-column>
      <vxe-table-column field="vatID" title="税率" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="paymentTermsID" title="付款条款" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="paymentMethodID" title="付款方式ID" sortable show-overflow width="100px"></vxe-table-column> -->
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" show-overflow v-if="menuAction.allowEdit||menuAction.allowDelete" fixed="left">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{}">
          <template #default> <vxe-input v-model="selectRow.codeName" placeholder="请输入编码" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户分类" field="clientGroup" span="12" :item-render="{}">
          <template #default="{ data }">
            <vxe-select v-model="data.clientGroup" placeholder="客户分类" clearable>
              <vxe-option v-for="num in ClientGroupComboStore" :key="num.label" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户等级" field="clientGrade" span="12" :item-render="{}">
          <template #default="{ data }">
            <vxe-select v-model="data.clientGrade" placeholder="系统分类" clearable>
              <vxe-option v-for="num in ClientGradeComboStore" :key="num.label" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="简称" field="shortName" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.shortName" placeholder="请输入简称" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="联系人" field="contact" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.contact" placeholder="请输入编码" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="手机号" field="tel" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.tel" placeholder="请输入编码" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="地址" field="address" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.address" placeholder="请输入编码" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="支付模式" field="accountPaymentGroup" span="12" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.accountPaymentGroup" filterable remote reserve-keyword placeholder="支付模式" size="mini" clearable>
              <el-option v-for="item in AccountPaymentGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="货币" field="currencyID" span="12" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.currencyID" filterable remote reserve-keyword placeholder="货币" size="mini" clearable>
              <el-option v-for="item in CurrencyComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <template v-if="info.userType===0">
          <vxe-form-item title="余额" field="accountBalance" span="12" :item-render="{}">
            <template #default>
              <vxe-input v-model="selectRow.accountBalance" placeholder="余额" clearable type="float">
                <template #suffix>
                  ￥
                </template>
              </vxe-input>
            </template>
          </vxe-form-item>
        </template>
        <vxe-form-item title="额度" field="accountLimit" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.accountLimit" placeholder="额度" clearable type="float">
              <template #suffix>
                ￥
              </template>
            </vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="专属折扣" field="discount" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.discount" placeholder="专属折扣" clearable type="float">
              <template #suffix>
                %
              </template>
            </vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料耗量折扣" field="discount" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.itemDiscount" placeholder="面料耗量折扣" clearable type="float">
              <template #suffix>
                %
              </template>
            </vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="传真" field="fax" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.fax" placeholder="请输入编码" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="电话号码" field="mobile" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.mobile" placeholder="请输入编码" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="邮箱" field="email" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.email" placeholder="请输入编码" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{}">
          <template #default>
            <vxe-switch v-model="selectRow.isActive" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="继承总店版型" field="inheritedParentModel" span="12" :item-render="{}">
          <template #default>
            <vxe-switch v-model="selectRow.inheritedParentModel" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="继承总店账单" field="inheritedParentBill" span="12" :item-render="{}">
          <template #default>
            <vxe-switch v-model="selectRow.inheritedParentBill" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>

        <vxe-form-item title="备注" field="remark" span="24" :item-render="{}">
          <template #default>
            <vxe-textarea v-model="selectRow.remark" placeholder="备注" show-word-count></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="总公司" field="parentClientID" span="12" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model="data.parentClientID" filterable remote reserve-keyword placeholder="父级客户" size="mini" :remote-method="ClientComboMethod" clearable>
              <el-option v-for="item in ClientComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item span="24" align='center' :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { mapState } from 'vuex'
// import { cloneDeep } from 'lodash'
export default {
  name: 'BadClientMaster',
  mixins: [masterTableMixins],
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        shortName: '',
        contact: null,
        tel: '',
        fax: '',
        mobile: '',
        email: '',
        address: '',
        source: '',
        customerCode: '',
        supplierCode: '',
        ownerID: '',
        owner: '',
        classID: '',
        clientCode1: '',
        clientTypeCode: '',
        currencyID: null,
        channelCode: '',
        salerCode: '',
        salerName: '',
        parentClientID: null,
        clientGroup: '',
        clientGrade: '',
        inheritedParentModel: false,
        inheritedParentBill: true,
        discount: 100,
        itemDiscount: 103,
        accountBalance: 0,
        accountLimit: 0,
        accountPaymentGroup: null
        // isActive: true
      },
      TreeExpand: true,
      formRules: {
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 3, max: 50, message: '长度在 3 到 50 个字符' }],
        shortName: [{ required: true, message: '请输入简称' }, { min: 2, max: 25, message: '长度在 2 到 25 个字符' }],
        clientGroup: [{ required: true, message: '请选择客户分类' }],
        clientGrade: [{ required: true, message: '请选择客户等级' }],
        contact: [{ required: true, message: '请输入联系人' }],
        tel: [{ required: true, message: '请输入手机号' }],
        address: [{ required: true, message: '请输入地址' }],
        currencyID: [{ required: true, message: '请选择货币' }]
      },
      formItems: [
        // { field: 'BankID', title: '银行', span: 12, itemRender: { name: '$input' } },
        // { field: 'CurrencyID', title: '货币', span: 12, itemRender: { name: '$input' } },
        // { field: 'VatID', title: '税率', span: 12, itemRender: { name: '$input' } },
        // { field: 'PaymentTermsID', title: '付款条款', span: 12, itemRender: { name: '$input' } },
        // { field: 'PaymentMethodID', title: '付款方式', span: 12, itemRender: { name: '$input' } },
      ],
      api: {
        get: '/mtm/bad_client/get',
        add: '/mtm/bad_client/adds',
        edit: '/mtm/bad_client/updates',
        delete: '/mtm/bad_client/deletes',
        ClientComboStore: '/mtm/comboQuery/clientComboStoreByQuery',
        ClientGroupComboStore: '/mtm/combo/clientGroupComboStore',
        CurrencyComboStore: '/mtm/combo/CurrencyComboStore',
        ClientGradeComboStore: '/mtm/combo/clientGradeComboStore',
        AccountPaymentGroupComboStore: '/mtm/combo/AccountPaymentGroupComboStore'

      },
      ClientGroupComboStore: [],
      ClientGradeComboStore: [],
      AccountPaymentGroupComboStore: [],
      ClientComboStore: [],
      CurrencyComboStore: []
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ClientComboStore, { gid: this.formData.parentClientID }).then(result => {
        this.ClientComboStore = result
      })
      await this.$api.ActionRequest(this.api.CurrencyComboStore, { gid: this.formData.parentClientID }).then(result => {
        this.CurrencyComboStore = result
      })
      await this.$api.ActionRequest(this.api.ClientGroupComboStore, { gid: this.formData.parentClientID }).then(result => {
        this.ClientGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ClientGradeComboStore, { gid: this.formData.parentClientID }).then(result => {
        this.ClientGradeComboStore = result
      })
      await this.$api.ActionRequest(this.api.AccountPaymentGroupComboStore).then(result => {
        this.AccountPaymentGroupComboStore = result
      })
    },
    TreeExpandEvent () {
      if (!this.TreeExpand) {
        this.TreeExpand = true
        this.$refs.master_table.setAllTreeExpand(true)
      } else {
        this.TreeExpand = false
        this.$refs.master_table.clearTreeExpand()
      }
    },
    ClientComboMethod (query) {
      this.$api.ActionRequest(this.api.ClientComboStore, { text: query }).then(result => {
        this.ClientComboStore = result
      })
    },
    formatName ({ row }) {
      return row.childs && row.childs.length > 0 ? `${row.codeName} (${row.childs.length})` : row.codeName
    },
    formatCurrency (value) {
      if (value) {
        console.log(value)
        var dto = this.CurrencyComboStore.find(item => item.value == value)
        console.log(dto)
        return dto?.label
      }
    },
    tableCellClick ({ row }) {
      // this.$refs.master_table.setTreeExpand(row, true)
      this.$refs.master_table.toggleTreeExpand(row, true)
    }
    // cellDblClick ({ row }) {
    //   this.$emit('nextpage', {
    //     pagename: 'detail',
    //     data: cloneDeep(row),
    //     keepalive: false,
    //     action: 'edit'
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
