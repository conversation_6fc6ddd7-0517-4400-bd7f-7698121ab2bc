<template>
  <d2-container>
    <split-pane :min-percent='10' :default-percent='50' split="horizontal">
      <template slot="paneL">
        <split-pane :min-percent='10' :default-percent='50' split="vertical">
          <template slot="paneL">
            <production-plan-indicator />
          </template>
          <template slot="paneR">
            <production-plan-finish-indicator />
          </template>
        </split-pane>

      </template>
      <template slot="paneR">
        <split-pane :min-percent='10' :default-percent='50' split="vertical">
          <template slot="paneL">
            <work-secation-postpone />
          </template>
          <template slot="paneR">
           <work-secation-hourly-productivity />
          </template>
        </split-pane>
      </template>
    </split-pane>
  </d2-container>
</template>

<script>
import ProductionPlanIndicator from './componments/productionplanindicator'
import ProductionPlanFinishIndicator from './componments/productionplanfinishindicator'
import WorkSecationPostpone from './componments/worksecationpostpone'
import WorkSecationHourlyProductivity from './componments/worksecationhourlyproductivity'
export default {
  name: 'productionindicator', // 生产指标
  components: {
    ProductionPlanIndicator,
    ProductionPlanFinishIndicator,
    WorkSecationPostpone,
    WorkSecationHourlyProductivity
  },
  data () {
    return {

    }
  }
}
</script>

<style>
</style>
