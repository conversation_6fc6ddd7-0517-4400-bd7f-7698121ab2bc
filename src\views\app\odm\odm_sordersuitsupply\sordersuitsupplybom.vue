<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadProductionprocessesMasterTable' ref='master_table' height='auto' :row-class-name="rowClassName" :loading="tableLoading" @cell-dblclick="cellDblClick" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="sales_orders_id" title="订单编号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="no" title="行号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groups" title="类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="types" title="所在部位" sortable width="100"></vxe-table-column>
      <vxe-table-column field="bom_item" title="物料编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="bom_qty" title="耗量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemName" title="系统物料名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="系统原始货号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="inventoryQty" title="有效库存" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="订单编号" field="sales_orders_id" span="12" :item-render="{name: '$input',props:{disabled:true}}"></vxe-form-item>
        <vxe-form-item title="行号" field="no" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="类别" field="groups" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="所在部位" field="types" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="物料编码" field="bom_item" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="耗量" field="bom_qty" span="12" :item-render="{name: '$input', props: { type: 'float'}  }"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="物料名称" field="itemStockID" span="24">
          <template #default="{ data }">
            <el-select v-model.trim="data.itemStockID" filterable placeholder="物料名称" size="mini" remote reserve-keyword :remote-method="remoteMethod1">
              <el-option v-for="item in ItemStockComboStoreByQuery" :key="item.value" :label="item.label" :value="item.id">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.clientName }} {{item.businessGroupText}}</span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item> -->
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>

  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'SorderSuitSupplyBom',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        sorderSuitSupplyID: this.form.id,
        sales_orders_id: this.form.sales_orders_id,
        no: null,
        groups: null,
        types: null,
        bom_item: null,
        bom_qty: null,
        itemID: null,
        itemStockID: null,
        remark: '',
        isActive: true
      },
      formRules: {
        // code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
        // action: [{ required: true, message: '请选择工序类型' }]
      },
      api: {
        get: '/mtm/oDM_SorderSuitSupplyBom/get',
        add: '/mtm/oDM_SorderSuitSupplyBom/adds',
        edit: '/mtm/oDM_SorderSuitSupplyBom/updates',
        delete: '/mtm/oDM_SorderSuitSupplyBom/deletes',
        ItemStockComboStoreByQuery: '/mtm/comboQuery/ItemStockComboStoreByQuery'
      },
      ItemStockComboStoreByQuery: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    this.loadData({ sorderSuitSupplyID: this.form.id })
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      //   await this.$api.ActionRequest(this.api.productionProcessesActionComboStore).then(result => {
      //     this.productionProcessesActionComboStore = result
      //   })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery, { text: query }).then(result => {
        this.ItemStockComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery, { gid: row.itemStockID }).then(result => {
        this.ItemStockComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
