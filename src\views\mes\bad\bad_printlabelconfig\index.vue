<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="sorderType" :item-render="{name: '$select', options: SorderTypeStore,props:{placeholder:'订单类型',clearable:true,}}"></vxe-form-item>
            <vxe-form-item field="groupID" :item-render="{name: '$select', options: GroupComboStore,props:{placeholder:'品类',clearable:true}}"></vxe-form-item>
            <vxe-form-item field="gender" :item-render="{name: '$select', options: sexList,props:{placeholder:'性别',clearable:true}}"></vxe-form-item>
            <vxe-form-item field="printLabelConfigType" :item-render="{}"><template #default>
                <el-select v-model="searchForm.printLabelConfigType" filterable placeholder="类型" size="mini" clearable>
                  <el-option v-for="item in PrintLabelConfigTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadPrintlabelconfigMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="订单类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="groupID" title="品类" sortable width="100" :formatter="formatterGroup"> </vxe-table-column>
      <vxe-table-column field="gender" title="性别" sortable width="100" :formatter="formatterGender"> </vxe-table-column>
      <vxe-table-column field="name" title="名称" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="name1" title="绑定内容" sortable width="100"></vxe-table-column>
      <vxe-table-column field="printLabelConfigTypeText" title="类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isShow" title="是否显示" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="isRow" title="显示整行" :formatter='formatBool1' sortable width="100"></vxe-table-column>
      <vxe-table-column field="fontSzie" title="字体大小" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close show-zoom :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="订单类型" field="sorderType" span="12" :item-render="{name: '$select', options: SorderTypeStore,props:{placeholder:'品类',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="品类" field="groupID" span="12" :item-render="{name: '$select', options: GroupComboStore,props:{placeholder:'品类',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="性别" field="gender" span="12" :item-render="{name: '$select', options: sexList,props:{placeholder:'性别',clearable:true}}"></vxe-form-item>
        <!-- <vxe-form-item title="类型" field="state" span="12" :item-render="{name: '$select', options: SorderTypeStore}"></vxe-form-item> -->
        <vxe-form-item title="名称" field="name" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="显示内容" field="name1" span="12" :item-render="{name: '$input',props:{disabled:true}}"></vxe-form-item>
        <vxe-form-item title="整行显示" field="isRow" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="字体大小" field="fontSzie" span="12" :item-render="{name: 'input', props: { type: 'number',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="绑定内容" field="xid" span="24" :item-render="{}">
          <template #default>
            <el-select v-model.trim="selectRow.xid" filterable placeholder="绑定内容" size="mini" remote reserve-keyword :remote-method="remoteMethod" @change="xidChangeEvent" clearable>
              <el-option v-for="item in DataComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="类型" field="printLabelConfigType" span="12" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.printLabelConfigType" filterable placeholder="类型" size="mini" @change="printLabelConfigTypeChangeEvent">
              <el-option v-for="item in PrintLabelConfigTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', props: { type: 'float',clearable:true}}"></vxe-form-item>

        <vxe-form-item title="是否显示" field="isShow" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
// import { cloneDeep, isEmpty, toInteger } from 'lodash'
import { cloneDeep } from 'lodash'
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'bad_printlabelconfig',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        text: null,
        sorderType: null,
        printLabelConfigType: null,
        groupID: null,
        gender: null
      },
      formData: {
        name: null,
        name1: null,
        remark: '',
        printLabelConfigType: 1,
        xid: null,
        gender: true,
        sort: 999,
        sorderType: null,
        isShow: true,
        groupID: null,
        fontSzie: null,
        isRow: false,
        isActive: true
      },
      formRules: {
        name: [{ required: true, message: '请输入标题内容' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        printLabelConfigType: [{ required: true, message: '请选择类型' }],
        xid: [{ required: true, message: '请选择绑定内容' }]
      },
      sexList: [
        { value: true, label: '男' },
        { value: false, label: '女' }
      ],
      api: {
        get: '/mes/bad_printlabelconfig/get',
        add: '/mes/bad_printlabelconfig/adds',
        edit: '/mes/bad_printlabelconfig/updates',
        delete: '/mes/bad_printlabelconfig/deletes',
        PrintLabelConfigTypeComboStore: '/mes/combo/PrintLabelConfigTypeComboStore',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        ProductionStationComboStoreByQuery: '/mes/comboQuery/ProductionStationComboStoreByQuery',
        GroupComboStore: '/mtm/combo/groupComboStore',
        SorderTypeStore: '/mes/combo/SorderTypeStore'
      },
      GroupComboStore: [],
      DataComboStore: [],
      SorderTypeStore: [],
      PrintLabelConfigTypeComboStore: []
    }
  },
  computed: {
    configTypeUrl () {
      return this.getUrl(this.selectRow)
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    printLabelConfigTypeChangeEvent (value) {
      this.DataComboStore = []
      this.selectRow.xid = null
      this.selectRow.name1 = null
      // console.log(value)
    },
    xidChangeEvent (value) {
      var dto = this.$utils.find(this.DataComboStore, item => item.value === value)
      if (dto && dto !== null) {
        this.selectRow.name1 = dto.label
      }
    },
    formatterGroup ({ cellValue }) {
      const item = this.GroupComboStore.find(item => item.value === cellValue)
      return item ? item.label : ''
    },
    formatterGender ({ cellValue }) {
      const item = this.sexList.find(item => item.value === cellValue)
      return item ? item.label : ''
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.PrintLabelConfigTypeComboStore).then(result => {
        this.PrintLabelConfigTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.SorderTypeStore).then(result => {
        this.SorderTypeStore = result
      })
    },
    getUrl (data) {
      var url = ''
      if (data.printLabelConfigType === 1 || data.printLabelConfigType === 2) {
        url = this.api.ModelElemListComboStoreByQuery
      }
      if (data.printLabelConfigType === 0) {
        url = this.api.ProductionStationComboStoreByQuery
      }
      return url
    },
    remoteMethod (query) {
      var url = this.getUrl(this.selectRow)
      this.$api.ActionRequest(url, { text: query }).then(result => {
        this.DataComboStore = result
      })
    },
    // 编辑
    async editEvent (row) {
      var url = this.getUrl(row)
      await this.$api.ActionRequest(url, { gid: row.xid }).then(result => {
        this.DataComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
