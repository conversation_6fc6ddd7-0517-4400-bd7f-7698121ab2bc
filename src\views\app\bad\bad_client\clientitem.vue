<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="MomModelElemClientDetailTable" ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" keep-source :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, icon: 'fa fa-pencil-square-o'}"> >
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="250"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" show-overflow v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="物料名称" field="itemID" span="24" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model.trim="data.itemID" filterable placeholder="物料" size="mini" remote reserve-keyword :remote-method="remoteMethod">
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item> <vxe-form-item span="24" align='center' :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'ClientItem',
  mixins: [detailMixins],
  data () {
    return {
      api: {
        get: '/mtm/bAD_ClientItem/get',
        add: '/mtm/bAD_ClientItem/adds',
        edit: '/mtm/bAD_ClientItem/updates',
        delete: '/mtm/bAD_ClientItem/deletes',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery'
      },
      formData: {
        clientID: this.form.id,
        itemID: null

      },
      ItemComboStore: [],
      formRules: {
        itemID: [{ required: true, message: '请选择物料名称' }]
      }
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ clientID: this.form.id })
    this.tableData = []
  },
  methods: {
    async getCombStore () {

    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query, isClientItem: true }).then(result => {
        this.ItemComboStore = result
      })
    }

  }
}
</script>

<style>
</style>
