<template>
  <d2-container>
    <div style="text-align: center;">
      <el-image :src="fileurl" fit='scale-down' style=" height: 500px">
        <div slot="placeholder" class="image-slot">
          加载中<span class="dot">...</span>
        </div>
      </el-image>
    </div>
  </d2-container>
</template>
<script>
export default {
  name: 'ResearchDevelopmentImage',
  props: {
    form: {
      type: Object
    }
  },
  data () {
    return {
      mtmapi: process.env.VUE_APP_API,
      // mtmapi: 'http://api-mtm-test.indlike.cn/api/',
      api: {
        get: '/mtm/oDM_SorderDetailFile/GetByID',
        fileurl: '/fs/getfile'
      },
      list: [],
      fileID: null
    }
  },
  watch: {
    form: {
      deep: true,
      handler (newval, oldval) {
        this.list = []
        if (newval !== null && newval.sorderDetailModelID !== null) {
          this.get(newval.sorderDetailModelID)
        }
      }
    }
  },
  created () {
    if (this.form !== null && this.form.sorderDetailModelID !== null) {
      this.get(this.form.sorderDetailModelID)
    }
  },
  computed: {
    fileurl () {
      if (this.fileID == null) {
        return null
      }
      var api = this.mtmapi.replace('/api/', '')
      var url = api + this.api.fileurl + '?FileID=' + this.fileID
      return url
    }
  },
  methods: {
    async get (id) {
      this.list = []
      this.fileID = null
      await this.$api.ActionRequest(this.api.get, { sorderDetailModelID: id }).then(res => {
        this.list = res
        if (res.length > 0) {
          this.fileID = res[0].fileManagementID
        }
        console.log(res)
      })
    }
  }
}
</script>
