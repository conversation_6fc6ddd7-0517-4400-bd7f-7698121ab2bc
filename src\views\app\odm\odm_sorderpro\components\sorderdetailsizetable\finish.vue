<template>
  <vxe-table class="sorderDetailSizeFinish" ref="sorderDetialSizeTable" :loading="sizeTableLoding" keep-source :data="DetailSizeData" :edit-config="{trigger: 'click', mode: 'cell',showStatus: true,activeMethod: activeCellMethod}" :height="tableHeight" :row-class-name="sizeRequitedClass" :cell-class-name="cellClassName" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}" :auto-resize="true">
    <vxe-table-column type="seq" width="40"></vxe-table-column>
    <vxe-table-column field="sizeColumnName" title="字段" width="100">
      <template v-slot="{ row }">
        <!-- 量体图片展示 -->
        <template v-if="SorderDetailModel.sorderSizeTypeID==='1'">
          <template v-if="row.imagePath1!==null&&row.imagePath1!==''">
            <el-popover placement="right-end" :title="row.sizeColumnName" width="400" trigger="hover">
              <el-image style="width: 400px; height: 400px" :src="row.imagePath1" fit="scale-down"></el-image>
              <div slot="reference">
                <span v-if="row.isRequired" style="color:red">*</span> <span >{{ row.sizeColumnName}}</span><i style="color:red" class="el-icon-picture-outline"></i>
              </div>
            </el-popover>
          </template>
          <template v-else>
            <span v-if="row.isRequired" style="color:red">*</span><span> {{row.sizeColumnName}}</span>
          </template>
        </template>
        <!-- 成衣图片显示 -->
        <template v-if="SorderDetailModel.sorderSizeTypeID!=='1'">
          <template v-if="row.imagePath!==null&&row.imagePath!==''">
            <el-popover placement="right-end" :title="row.sizeColumnName" width="400" trigger="hover">
              <el-image style="width: 400px; height: 400px" :src="row.imagePath" fit="scale-down"></el-image>
              <div slot="reference">
                <span v-if="row.isRequired" style="color:red">*</span> <span>{{ row.sizeColumnName}}</span><i style="color:red" class="el-icon-picture-outline"></i>
              </div>
            </el-popover>
          </template>
          <template v-else>
            <span v-if="row.isRequired" style="color:red">*</span> {{row.sizeColumnName}}
          </template>
        </template>
      </template>
    </vxe-table-column>
    <!-- 量体算法 -->
    <template v-if="SorderDetailModel.sorderSizeTypeID==='1'">
      <vxe-table-column field="measure" title="量体" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100">
        <template #edit="{ row }">
          <el-popover placement="right" width="200" trigger="focus" :content="'最大值:'+row.measureMax+' 最小值:'+row.measureMin">
            <vxe-input v-model="row.measure" :placeholder="'最大值:'+row.measureMax+' 最小值:'+row.measureMin" type="float" :min="row.measureMin" :max="row.measureMax" clearable slot="reference" @focus="inputFocus('measure',row)"></vxe-input>
          </el-popover>
        </template>
        <!-- <template v-slot="{ row }">{{ row.measure}}</template> -->
      </vxe-table-column>
      <vxe-table-column field="ease" title="量体加放量" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100">
        <template #edit="{ row }">
          <el-popover placement="right" width="200" trigger="focus" :content="'最大值:'+row.maxEase+' 最小值:'+row.minEase">
            <vxe-input v-model="row.ease" :placeholder="'最大值:'+row.maxEase+' 最小值:'+row.minEase" type="float" :min="row.minEase" :max="row.maxEase" clearable slot="reference" @focus="inputFocus('ease',row)"></vxe-input>
          </el-popover>
        </template>
      </vxe-table-column>
    </template>
    <template v-if="SorderDetailModel.sorderSizeTypeID==='4'||SorderDetailModel.sorderSizeTypeID==='5'">
      <vxe-table-column field="standard1" title="标准规格" width="100"></vxe-table-column>
      <vxe-table-column field="fix1" title="标准修正" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100">
        <template v-slot:edit="{ row }">
          <el-popover ref="popover" placement="right" width="200" trigger="focus" :content="'最大值:'+row.fix1Max+' 最小值:'+row.fix1Min">
            <vxe-input v-model="row.fix1" :placeholder="'最大值:'+row.fix1Max+'最 小值:'+row.fix1Min" type="float" :min="row.fix1Min" :max="row.fix1Max" clearable slot="reference" @blur="fix1Blur(row)" @focus="inputFocus('fix1',row)"></vxe-input>
          </el-popover>
        </template>
      </vxe-table-column>
    </template>

    <!-- sorderSizeTypeID==='3' 成衣规格 -->
    <vxe-table-column field="finish" title="成衣" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100">
      <template v-slot:edit="{ row }">
        <el-popover ref="popover" placement="right" width="200" trigger="focus" :content="'最大值:'+row.finishMax+' 最小值:'+row.finishMin">
          <vxe-input v-model="row.finish" :placeholder="'最大值:'+row.finishMax+' 最小值:'+row.finishMin" type="float" :min="row.finishMin" :max="row.finishMax" clearable slot="reference" @focus="inputFocus('finish',row)"></vxe-input>
        </el-popover>
      </template>
    </vxe-table-column>
    <vxe-table-column field="standard" title="标准规格" width="100"></vxe-table-column>
    <vxe-table-column field="bodyValue" title="特体调整值" width="100"></vxe-table-column>
    <vxe-table-column field="standard1" title="参考规格" width="100"></vxe-table-column>
    <vxe-table-column field="isManual" title="手工?" :edit-render="{autofocus: '.vxe-input--inner'}" width="100">
      <template v-slot:edit="{ row }">
        <vxe-switch v-model="row.isManual" open-label="是" close-label="否"></vxe-switch>
      </template>
      <template v-slot="{ row }">{{ row.isManual?'是':'否' }}</template>
    </vxe-table-column>
    <vxe-table-column field="fixVariant" title="变体" width="200"></vxe-table-column>
  </vxe-table>
</template>

<script>
import checkSize from './index.js'
export default {
  name: 'Finish', // 成衣算法,
  mixins: [checkSize]

}
</script>

<style>
</style>
