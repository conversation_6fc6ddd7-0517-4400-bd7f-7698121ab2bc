<template>
  <d2-container class="productpacking">

    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <!-- <vxe-button status="warning" @click="createEvent()">重新生成装箱明细</vxe-button>
          <vxe-button status="success" @click="save()">保存修改</vxe-button>
          <vxe-button status="success" @click="packinglist=!packinglist">查看装箱清单</vxe-button> -->
        </template>

      </vxe-toolbar>
    </template>
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" style="height:99%">
      <el-tab-pane label="发货清单" name="ProductPackingList">
        <product-packing-list :form="form" ref='ProductPackingList' @reload="reload" />
      </el-tab-pane>
      <el-tab-pane label="装箱清单" name="ProductPackingDetailList">
        <product-packing-detail-list :form="form" ref='ProductPackingDetailList' />
      </el-tab-pane>
    </el-tabs>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import ProductPackingList from './productpackinglist.vue'
import ProductPackingDetailList from './productpackingdetaillist.vue'
// Vue.config.errorCaptured = function (msg, vm, trace) {
//   console.log(msg)
// }
export default {
  name: 'Detail', // 发货清单
  mixins: [detailTableMixins],
  components: { ProductPackingList, ProductPackingDetailList },
  props: {
  },
  data () {
    return {
      activeName: 'ProductPackingList'
    }
  },

  async created () {

  },

  methods: {
    reload () {
      this.$refs.ProductPackingDetailList.reload()
    },
    handleClick () {

    }

  }

}
</script>

<style lang="scss" >
.productpacking {
  .el-tabs__content {
    height: 90%;
  }
  .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }
  .d2-container-full__body {
    overflow-x: hidden !important;
  }
}
</style>
