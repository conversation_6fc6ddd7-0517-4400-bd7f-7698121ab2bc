/* #searchresults{
    background-color: beige;
} */
html {
    font-size: 10px;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    display:block;
    margin-top:0;
}
body{
    display:block;
    margin-top:0;
    }
.bodyclass {
    background-color: #fff;
    color: #080808;
    text-align: center;
    /* text-shadow: 0 1px 3px rgba(0,0,0,.5); */
     max-width: 796px;
    margin: 0 auto;
    
    /* padding: 0; */
    /* padding: 10px; */
    /* font-size: 1.3em; */
}
.container-body {
        width: 798px;
        margin: 0 auto;
}
.table-bordered {
    border: 1px solid #ddd;
}
.table {
    width: 100%;
    max-width: 100%;
    /* margin-bottom: 20px; */
}
table {
    background-color: transparent;
}
table {
    border-spacing: 0;
    border-collapse: collapse;
}
.table-title .order-no span:first-child {
    font-size: 12px;
    position: absolute;
    margin: 10px 10px;
    width: 35px;
}
.table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border: 1px solid #ddd;
}
.table-bordered>tr>td, .table-bordered>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border: 1px solid #ddd;
}
.table-bordered > tr:first-child > td,
.table > tfoot > tr:first-child > td {
    border-top: none;
}
.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
    /* padding: 8px; */
    line-height: 1.42857143;
    vertical-align: top;
    /* border-top: 1px solid #ddd; */
}
.table>tr>td, .table>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
    /* padding: 8px; */
    line-height: 1.42857143;
    vertical-align: top;
    /* border-top: 1px solid #ddd; */
    border: 1px solid #000000;
}
.table th, .table td {
    vertical-align: middle;
    /* box-sizing: border-box; */
}
td, th {
    padding: 0;
}
.table-striped>tbody>tr:nth-of-type(odd) {
    background-color: #f5f5f5;
}
.table-title .order-no span:nth-child(2) {
    font-size: 38px;
    margin-left: 58px;
}
.table-title .main-title .title-content {
    width: 440px;
    margin: 0 auto;
}
.table-title .main-title .title-content span:first-child {
    font-size: 22px;
}
.table-title .main-title .title-content span:nth-child(2) {
    font-size: 16px;
}
.container-body {
    display: table;
    width: 100%;
    height: 100%;
    /* margin-top: 40px; */
    min-height: 100%;
    -webkit-box-shadow: inset 0 0 100px rgba(0,0,0,.5);
    box-shadow: inset 0 0 100px rgba(0,0,0,.5);
}
.table-title {
    position: relative;
}
.text-bold {
    font-weight: bold;
}
.main-container {
width: 100%;
}
.container-body .main-container .main-content table {
    margin-bottom: 0;
}
.table-bordered {
    border: 1px solid #ddd;
}
.main-content .table>tbody>tr>td, .main-content .table>thead>tr>th, .main-content.table>tbody>tr>th, .main-content.table>tfoot>tr>td, .main-content.table>tfoot>tr>th, .main-content.table>thead>tr>td {
    padding: 0!important;
}
.HalfFitting {
    color: red;
    font-weight: bolder;
    font-size: larger;
}
.text{
    display: flex;
            align-items: center;        /* 垂直居中 */
            justify-content: center;    /* 水平居中 */
}

.main-picture-split {
    width: 65em;
}
.main-picture img{
    vertical-align: middle
}

 .main-picture {
    max-width: 100%;
    height: auto;
    display: inline-block;
    position: relative;
}
/* 上衣 */
.main-picture .main-list, .main-picture .main-list-pants {
    display: inline-block;
    padding: 2px;
}

     .main-picture .main-list .item, .main-picture .main-list-pants .item {
        float: left;
    }

         .main-picture .main-list .item img, .main-picture .main-list-pants .item img {
            position: absolute;
        }

     .main-picture .main-list:first-child {
        width: 24.88%;
        height: 460px;
    }

     .main-picture .main-list:nth-child(2) {
        width: 13.43%; height: 460px;
    }

    .main-picture .main-list:nth-child(3) {
        width: 13.43%; height: 460px;
    }

     .main-picture .main-list:nth-child(4) {
        width:41.25% ;height: 460px;
    }

     .main-picture .main-list:first-child .item:first-child, .main-picture .main-list:first-child .item:first-child img {
        height: 285.4957824086px;
        width: 189.3731352px;
        display: inherit;
    }

     .main-picture .main-list:first-child .item:nth-child(2), .main-picture .main-list:first-child .item:nth-child(2) img,  .main-picture .main-list:first-child .item:nth-child(3), .main-picture .main-list:first-child .item:nth-child(3) img {
        height: 61.8934530212px;
        width: 145.97512505px;
        display: inherit;
    }

     .main-picture .main-list:nth-child(2) .item:first-child, .main-picture .main-list:nth-child(2) .item:first-child img {
        height:172.3453341266px;
        width:106.52238855px;
        display: inherit;
    }

     .main-picture .main-list:nth-child(2) .item:nth-child(2), .main-picture .main-list:nth-child(2) .item:nth-child(2) img {
        height:162.8687868193px;
        width: 100.99900544px;
        display: inherit;
    }

     .main-picture .main-list:nth-child(2) .item:nth-child(3), .main-picture .main-list:nth-child(2) .item:nth-child(3) img {
        height:119.7627269194;
        width: 106.52238855px;
        display: inherit;
    }


    .main-picture .main-list:nth-child(3) .item:first-child, .main-picture .main-list:nth-child(3) .item:first-child img {
        height:201.1931750554px;
        width:106.52238855px;
        display: inherit;
    }

     .main-picture .main-list:nth-child(3) .item:nth-child(2), .main-picture .main-list:nth-child(3) .item:nth-child(2) img {
        height: 201.1931750554px;
        width:106.52238855px;
        display: inherit;
    }


     .main-picture .main-list:nth-child(4) .item:first-child, .main-picture .main-list:nth-child(4) .item:first-child img {
        height: 322.1947179009px;
        width:350.6368177px;
        display: inherit;
    }

     .main-picture .main-list:nth-child(4) .item:nth-child(2), .main-picture .main-list:nth-child(4) .item:nth-child(2) img {
        height: 59.3053535068px;
        width: 157.810946px;
        display: inherit;
    }

     .main-picture .main-list:nth-child(4) .item:nth-child(3), .main-picture .main-list:nth-child(4) .item:nth-child(3) img {
       height:61.9565773996px;
        width: 153.86567235px; 
		/* height:auto;
		width: 253.86567235px; */
        display: inherit;
    }

     .main-picture .main-list-pants:first-child {
        width: 381px;
        height: 500px;
    }
/* 下装 */
     .main-picture .main-list-pants:nth-child(2) {
        width:170px;
        height: 500px;
    }
    /* xin */
    .main-picture .main-list-pants:nth-child(3) {
        width:170px;
    }
     .main-picture .main-list-pants:first-child .item:first-child, .main-picture .main-list-pants:first-child .item:first-child img,  .main-picture .main-list-pants:first-child .item:nth-child(2),  .main-picture .main-list-pants:first-child .item:nth-child(2) img {
        height:227.2635433346px;
        width: 380.58905692px;
        display: inherit;
    }

     .main-picture .main-list-pants:nth-child(2) .item, .main-picture .main-list-pants:nth-child(2) .item img {
        height: 126.2487568px;  
        width: 157.810946px;
        display: inherit;
    }  /* xin */
    .main-picture .main-list-pants:first-child .item:first-child, .main-picture .main-list-pants:first-child .item:first-child img,  .main-picture .main-list-pants:first-child .item:nth-child(3),  .main-picture .main-list-pants:first-child .item:nth-child(2) img {
        height:227.2635433346px;
        width: 380.58905692px;
        display: inherit;
    }
  /* xin */
     .main-picture .main-list-pants:nth-child(3) .item, .main-picture .main-list-pants:nth-child(3) .item  {
        height: 126.2487568px;  
        width: 157.810946px;
        display: inherit;
    }
    .main-picture .main-list-pants:nth-child(3) .item, .main-picture .main-list-pants:nth-child(3) .item img {
        height: 126.2487568px;  
        width: 154.810946px;
        display: inherit;
    }
        .container-body>.main-content table tr td {
            white-space: pre-line;
    }
    .main-content .table>tbody>tr>td, .main-content .table>thead>tr>th, .main-content.table>tbody>tr>th, .main-content.table>tfoot>tr>td, .main-content.table>tfoot>tr>th, .main-content.table>thead>tr>td {
        padding: 0!important;
}
.container-body>.main-content table tr td {
    white-space: pre-line;
}
.main-content .table>tbody>tr>td, .main-content .table>thead>tr>th, .main-content.table>tbody>tr>th, .main-content.table>tfoot>tr>td, .main-content.table>tfoot>tr>th, .main-content.table>thead>tr>td {
    padding: 0!important;
}
.table-footer {
    font-size: 13px;
    font-weight: 600;
}
.table-thead{
    z-index: 9999;
    background: white;
    /* border-bottom: 1px solid #000000; */
    width: 100%;
    border-top:0px;
}
.table-footer div {
    z-index: 9999 !important;background: white;width: 23%; display: inline-block
}


.table-elem {
    width: 100%;
}
.table-elem td div p {
    font-size: 12px;
    -webkit-transform-origin-x: 0;
    -webkit-transform: scale(1.0);
    /* style="font-size:10px;-webkit-transform:scale(0.8);display:block；" */
}
.td-border {
    /* border: 1px dotted #999; */
}
.font-transform-normal {
    padding: 2px 0 0 2px;
    display: inline-block;
    -webkit-transform: scale(1.0);
    -webkit-transform-origin:top left;
    width: 100%;
}