<template>
  <vxe-form :data="formdata" title-align="right" title-width="100" @submit="SubmitEvent('ruleForm')" ref="ruleForm">
    <!-- <vxe-form-item title="名称" field="name" span="24" :item-render="{name: '$textarea'}" :item-render="{}"></vxe-form-item> -->
    <vxe-form-item title="昵称" span="12" :item-render="{}"> <template #title>
        <span style="color: red;">注意:</span>
      </template>
      <template #default>
        <span style="color: red;">表格行如果重复数据会被覆盖掉</span>
      </template>
    </vxe-form-item>
    <vxe-form-item title="添加文件" :item-render="{}"><template #default>
        <!-- <el-upload ref="upload" :action="mtmapi+api.update" list-type="picture-card" :file-list="fileList" :auto-upload="false" :multiple="false" :data="formdata" :on-change="onchange">
      </el-upload> -->

        <el-upload class="upload-demo" ref="upload" :action="uplaodurl()" name="files" v-loading="loading" :on-preview="handlePreview" :data="formdata" :on-remove="handleRemove" :file-list="fileList" :auto-upload="false" :on-success="success" :on-error="error">
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
        </el-upload>
        <!-- <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="selectRow.imageUrl" alt="">
          </el-dialog> -->
      </template></vxe-form-item>

    <vxe-form-item align="center" span="24" :item-render="{}"><template #default>
        <vxe-button type="submit" status="primary">提交</vxe-button>
        <vxe-button type="reset">重置</vxe-button>
      </template></vxe-form-item>
  </vxe-form>
</template>

<script>
export default {
  name: 'UpLoadExcel',
  props: {
    SorderID: {
      type: String
    },
    UserId: {
      type: String
    },
    SorderProModelID: {
      type: String,
      default: null
    },
    ClientID: {
      type: String
    },
    upLoadResult: {
      type: Function,
      default: null
    }
  },
  computed: {

  },
  data () {
    return {
      mtmapi: process.env.VUE_APP_API,
      formdata: {
        name: null,
        text: 'text',
        sorderID: this.SorderID,
        userId: this.UserId,
        clientID: this.ClientID,
        sorderProModelID: this.SorderProModelID || ''
      },
      loading: false,
      api: {
        upload: 'fs/excel/importsorderpro'
      },
      fileList: []
    }
  },
  methods: {
    uplaodurl () {
      var url = this.mtmapi.replace('/api/', '/') + this.api.upload
      // console.log(url)
      return url
    },
    async SubmitEvent (formName) {
      this.loading = true
      this.submitUpload()
    },
    submitUpload () {
      this.loading = true
      this.$refs.upload.submit()
    },
    success (response, file, fileList) {
      this.fileList = []
      var success = response.result.success
      var info = response.result.info
      var error = response.error
      this.loading = false
      this.upLoadResult({ success: success, info: info, error: error, close: true })
    },
    error (err, file, fileList) {
      console.log(err)
      this.fileList = []
      var error = '上传失败!' + err
      var info = null
      try {
        // var success = err.result.success
        var josn = JSON.parse(this.$utils.toValueString(err).replace('Error: ', ''))
        info = josn.result?.info
        error = josn.result?.error
        if (josn.error === '' || josn.error === null) {
          info = josn.result.info
          error = josn.result.error
        } else {
          error = josn.error.message
        }
      } catch (error) {

      }
      this.loading = false
      this.upLoadResult({ success: false, info: info, error: error, close: false })
    },
    handleRemove (file, fileList) {
      console.log(file, fileList)
    },
    handlePreview (file) {
      console.log(file)
    },
    onchange (file, fileList) {
      if (fileList.length > 1) {
        fileList.shift()
      }
    }
  }
}
</script>

<style>
</style>
