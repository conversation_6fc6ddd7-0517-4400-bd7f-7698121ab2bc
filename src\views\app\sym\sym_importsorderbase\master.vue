<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <!-- <vxe-button status="wrining" @click="importEvent" v-if="menuAction.allowAdd">导入测试</vxe-button> -->
          <!-- <vxe-button status="wrining" @click="TestEvent" v-if="menuAction.allowAdd">测试</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id='SymImportsorderbaseMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="code" title="编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="modelGroupText" title="版型系列" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="importSorderBaseGroupText" title="分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isSystem" title="系统配置" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button status="warning" @click="importEvent(row)" v-if="menuAction.allowAdd&&row.importSorderBaseGroup=='1'">导入</vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="deepCopyEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="客户" field="clientID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4">
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value+item.label" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="类型" field="importSorderBaseGroup" span="12" :item-render="{name: '$select', options: ImportSorderBaseGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',},props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="系统组件" field="isSystem" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="importShow" :title="'excel导入测试' " width="60%" height="70%" resize destroy-on-close>
      <excel-import-table :on-success="handleSuccess" :before-upload="beforeUpload" />
      <!-- <el-table v-if=" pluData.length>=1" :data="pluData" border highlight-current-row style="width: 100%;margin-top:20px;height: 380px;overflow-y: auto"> -->
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import ExcelImportTable from '@/components/excel/excelimporttable.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'sym_importsorderbase',
  mixins: [masterTableMixins],
  components: {
    ExcelImportTable
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isSystem: false,
        sort: 1,
        importSorderBaseGroup: null,
        isActive: true,
        clientID: null
        // modelGroupID: null
      },
      importShow: false,
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 30, message: '长度在 2 到 30 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        importSorderBaseGroup: [{ required: true, message: '请选择导入模板类型' }]
      },
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/sym_importsorderbase/get',
        add: '/mtm/sym_importsorderbase/adds',
        deepclone: '/mtm/sym_importsorderbase/deepclone',
        edit: '/mtm/sym_importsorderbase/updates',
        delete: '/mtm/sym_importsorderbase/deletes',
        ImportText: '/fs/excel/import',
        ImportSorderBaseGroupComboStore: '/mtm/combo/ImportSorderBaseGroupComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
        // ModelGroupComboStore: '/mtm/combo/ModelGroupComboStore'
      },
      // ModelGroupComboStore: [],
      ImportSorderBaseGroupComboStore: [],
      clientComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    importEvent (row) {
      this.selectRow = cloneDeep(row)
      this.importShow = true
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ImportSorderBaseGroupComboStore).then(result => {
        this.ImportSorderBaseGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      // await this.$api.ActionRequest(this.api.ModelGroupComboStore).then(result => {
      //   this.ModelGroupComboStore = result
      // })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    // 编辑
    editEvent (row) {
      // this.$api.ActionRequest(this.api.ModelGroupComboStore, { gid: row.modelGroupID }).then(result => {
      //   this.ModelGroupComboStore = result
      // })
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },

    beforeUpload (file) {
      const isLt1M = file.size / 1024 / 1024 < 1
      if (isLt1M) {
        return true
      }
      this.$message({
        message: '只能上传小于1M的文件',
        type: 'warning'
      })
      return false
    },
    async importServer (data, type = 1) {
      const loading = this.$loading({
        lock: true,
        text: '导入中,请稍后.....',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      var baseurl = this.mtmpai.replace('/api/', '')
      await this.$api.ActionRequest(this.api.ImportText, { group: 1, type: type, data: data }, false, 'post', baseurl).then(result => {
        loading.close()
        if (result.success) {
          this.$message({
            dangerouslyUseHTMLString: true,
            showClose: true,
            type: 'success',
            message: result.info,
            center: true,
            duration: 1000 * 60 * 60,
            customClass: 'importrResult'
          })
        } else {
          this.$message({
            type: 'error',
            dangerouslyUseHTMLString: true,
            showClose: true,
            message: result.error,
            center: true,
            duration: 1000 * 60
            // customClass: 'importrResult'
          })
        }
      }).catch(() => {
        loading.close()
      })
    },
    handleSuccess ({ results, header }) {
      //  console.log(header)
      //   console.log(results)

      //   this.pluData = results
      //   this.pluHeader = header
      var type = this.selectRow.importSorderBaseGroup
      this.importServer(results, type)
    },
    TestEvent () {
      this.$message({
        showClose: true,
        type: 'success',
        dangerouslyUseHTMLString: true,
        message: '导入成功!</br>1111',
        center: true,
        duration: 1000 * 60
      })
    }
  }
}
</script>

<style lang="scss">
.importrResult {
  max-height: 300px;
  overflow-y: auto;
}
</style>
