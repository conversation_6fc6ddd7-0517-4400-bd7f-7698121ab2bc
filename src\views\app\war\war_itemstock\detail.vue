<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button @click="exportSelectEvent" v-if="menuAction.allowPrint">导出选中</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="供应商" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="Dates">
              <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemClassID">
              <template #default="{ data }">
                <vxe-select v-model="data.itemClassID" placeholder="类别" clearable>
                  <vxe-option v-for="item in ItemClassComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item> <vxe-form-item field="businessGroupID">
              <template #default="{ data }">
                <el-select v-model="data.businessGroupID" placeholder="业务类型" filterable clearable size="mini">
                  <el-option v-for="item in BusinessGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemGroupID">
              <template #default="{ data }">
                <el-select v-model="data.itemGroupID" placeholder="分类" filterable clearable size="mini">
                  <el-option v-for="item in ItemGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="detailType">
              <template #default="{ data }">
                <vxe-select v-model="data.detailType" placeholder="类别" clearable>
                  <vxe-option v-for="item in ItemStockDetailTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItemstockdetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox"></vxe-table-column>
      <vxe-table-column field="invoicesNumber" title="单据" sortable width="100">
        <template v-slot="{ row }">
          <template v-if="row.invoicesNumber!==''">
            <vxe-button type="text" status="success" @click="gotoitemstockinvoicesEvent(row.invoicesNumber)" :content="row.invoicesNumber"></vxe-button>
          </template>
        </template>
      </vxe-table-column>
      <vxe-table-column field="ownerClientName" title="归属客户" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="itemCode" title="编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="itemName" title="名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemBatchCode" title="海关合同号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="supplierItemCode" title="供应商编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="supplierItemName" title="供应商名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemClassText" title="分类" sortable width="100"></vxe-table-column>

      <vxe-table-column field="wholesalePrice" title="采购价" sortable width="100"></vxe-table-column>
      <vxe-table-column field="businessGroupText" title="业务归属" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemGroupText" title="物料分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="100"></vxe-table-column>

      <vxe-table-column field="itemSize" title="规格尺寸" sortable width="100"></vxe-table-column>
      <vxe-table-column field="detailTypeText" title="类别" sortable width="100">
        <template v-slot="{ row }">
          <template v-if="row.detailType===1">
            <el-tag type="success">{{row.detailTypeText}}</el-tag>
          </template>
          <template v-else>
            <el-tag type="danger">{{row.detailTypeText}}</el-tag>
          </template>
        </template>
      </vxe-table-column>
      <vxe-table-column field="qty" title="数量" sortable width="100">
        <template v-slot="{ row }">
          <template v-if="row.detailType===1">
            <el-tag type="success">{{row.qty}}</el-tag>
          </template>
          <template v-else>
            <el-tag type="danger">{{row.qty}}</el-tag>
          </template>
        </template>
      </vxe-table-column>
      <vxe-table-column field="originalQty" title="原始库存" sortable width="100"></vxe-table-column>
      <vxe-table-column field="unitGroupText" title="单位" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button> -->
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button> -->
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-sizes="[50,200,500,1000,2000,3000,5000]" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="物料名称" field="itemID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.itemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="物料" :remote-method="remoteMethod">
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.code+'【'+item.label+'/'+item.name+'】'+'规格:'+item.itemSize" :value="item.value"> </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户" field="clientID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword clearable :remote-method="remoteMethod4">
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="类型" field="detailType" span="12" :item-render="{name: '$select', options: ItemStockDetailTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="数量" field="qty" span="12" :item-render="{name: '$input', props: { type: 'float'}  }"></vxe-form-item>
        <!-- <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item> -->
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import XEUtils from 'xe-utils'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_itemstockdetail',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      formData: {
        itemID: null,
        itemStockID: null,
        detailType: 1,
        qty: null,
        Remark: null,
        isActive: true,
        clientID: null
      },
      formRules: {
        itemID: [{ required: true, message: '请输入物料编码' }],
        qty: [{ required: true, message: '请输入数量' }]
      },
      api: {
        get: '/mtm/war_itemstockdetail/get',
        add: '/mtm/war_itemstockdetail/adds',
        edit: '/mtm/war_itemstockdetail/updates',
        delete: '/mtm/war_itemstockdetail/deletes',
        ItemStockDetailTypeComboStore: '/mtm/combo/ItemStockDetailTypeComboStore',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        ItemClassComboStore: '/mtm/combo/ItemClassComboStore',
        BusinessGroupComboStore: '/mtm/combo/BusinessGroupComboStore',
        ItemGroupComboStore: '/mtm/combo/ItemGroupComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      clientComboStoreByQuery: [],
      ItemStockDetailTypeComboStore: [],
      ItemComboStore: [],
      ItemGroupComboStore: [],
      BusinessGroupComboStore: [],
      ItemClassComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemStockDetailTypeComboStore).then(result => {
        this.ItemStockDetailTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemGroupComboStore).then(result => {
        this.ItemGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.BusinessGroupComboStore).then(result => {
        this.BusinessGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemClassComboStore).then(result => {
        this.ItemClassComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    gotoitemstockinvoicesEvent (text) {
      this.$router.push({
        name: 'war_itemstockinvoices',
        params: {
          refresh: true,
          text: text
        }
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    // attributenames 要清除的属性明细 code 编码 codename 编码名称
    copyRowEvent (row, attributenames = [], code = false, codeName = false) {
      this.selectRow = cloneDeep(row)
      if (XEUtils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (XEUtils.has(this.selectRow, 'createBy')) {
        this.selectRow.createBy = null
      }
      if (XEUtils.has(this.selectRow, 'createID')) {
        this.selectRow.createID = null
      }
      if (XEUtils.has(this.selectRow, 'createOn')) {
        this.selectRow.createOn = null
      }
      if (XEUtils.has(this.selectRow, 'modifyBy')) {
        this.selectRow.modifyBy = null
      }
      if (XEUtils.has(this.selectRow, 'modifyID')) {
        this.selectRow.modifyID = null
      }
      if (XEUtils.has(this.selectRow, 'modifyOn')) {
        this.selectRow.modifyOn = null
      }
      if (XEUtils.has(this.selectRow, 'itemStockID')) {
        this.selectRow.itemStockID = null
      }
      // if (!code && XEUtils.has(this.selectRow, 'code')) {
      //   this.selectRow.code = null
      // }
      // if (!codeName && XEUtils.has(this.selectRow, 'codeName')) {
      //   this.selectRow.codeName = null
      // }
      if (attributenames.length > 1) {
        attributenames.forEach(name => {
          this.selectRow[name] = null
        })
      }
      this.showEdit = true
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
