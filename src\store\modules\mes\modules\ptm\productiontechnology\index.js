// import { ActionRequest } from '@/api/modules/base/index'
export default {
  namespaced: true,
  state: {
    productstationconfig: {
      code: null,
      codeName: null,
      id: null,
      viewType: null,
      viewTypeStr: null,
      isShowSorderSize: true
    }
  },
  actions: {
    async set ({ state, dispatch }, productstationconfig) {
      state.productstationconfig = productstationconfig
      await dispatch('d2admin/db/set', {
        dbName: 'sys',
        path: 'mes.ptm.productstationconfig',
        value: state.productstationconfig,
        user: false
      }, { root: true })
    },
    async clear ({ state, dispatch }) {

    },
    async load ({ state, dispatch, commit }) {
      // store 赋值
      state.productstationconfig = await dispatch('d2admin/db/get', {
        dbName: 'sys',
        path: 'mes.ptm.productstationconfig',
        defaultValue: state.productstationconfig,
        user: false
      }, { root: true })
    }

  },
  mutations: {

  }
}
