<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="danger" @click="deletesEvent" v-if="menuAction.allowDelete">删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelElemRule1Type">
              <template #default="{ data }">
                <el-select v-model="data.modelElemRule1Type" filterable placeholder="计算类型" size="mini" clearable>
                  <el-option v-for="item in ModelElemRule1TypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" size="mini" clearable>
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="genderID">
              <template #default="{ data }">
                <el-select v-model="data.genderID" filterable placeholder="性别" size="mini" clearable>
                  <el-option v-for="item in sexList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID1">
              <template #default="{ data }">
                <el-select v-model="data.modelElemListID1" filterable placeholder="款式1(条件)" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod2_1" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery1_3" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable placeholder="款式(结果)" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod2" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery1_2" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelelemruleMasterTable' ref='master_table' :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}" :height="TableHeight">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="genderID" title="性别" sortable width="100" :formatter="val=>formatSex(val)"> </vxe-table-column>
      <vxe-table-column field="groupText" title="款式类别" sortable width="100"> </vxe-table-column>
      <vxe-colgroup title="条件" align="center">
        <vxe-table-column field="clientName" title="绑定客户" sortable width="100"> </vxe-table-column>
        <vxe-table-column field="groupTexts" title="绑定品类" sortable width="100"> </vxe-table-column>
        <vxe-colgroup title="款式1" align="center">
          <vxe-table-column field="modelElemListCode1" title="款式编码" sortable width="95px"></vxe-table-column>
          <vxe-table-column field="modelElemListCodeName1" title="款式名称" sortable width="95px"></vxe-table-column>
          <vxe-table-column field="modelElemCode1" title="款式明细编码" sortable width="95px"></vxe-table-column>
          <vxe-table-column field="modelElemCodeName1" title="款式明细名称" sortable width="95px"></vxe-table-column>
        </vxe-colgroup>
        <vxe-colgroup title="款式2" align="center">
          <vxe-table-column field="modelElemCode2" title="款式明细编码" sortable width="95px"></vxe-table-column>
          <vxe-table-column field="modelElemCodeName2" title="款式明细名称" sortable width="95px"></vxe-table-column>
        </vxe-colgroup>
        <vxe-colgroup title="款式3" align="center">
          <vxe-table-column field="modelElemCode3" title="款式明细编码" sortable width="95px"></vxe-table-column>
          <vxe-table-column field="modelElemCodeName3" title="款式明细名称" sortable width="95px"></vxe-table-column>
        </vxe-colgroup>
      </vxe-colgroup>
      <vxe-table-column field="modelElemRule1TypeText" title="计算类型" sortable width="100"> </vxe-table-column>
      <vxe-colgroup title="结果" align="center">
        <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelElemListCodeName" title="款式名称" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelElemCode" title="款式明细编码" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelElemCodeName" title="款式明细名称" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="itemCode" title="物料编码" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="originalItemNo" title="物料货号" sortable width="95px"></vxe-table-column>
      </vxe-colgroup>
      <vxe-table-column field="sort" title="顺序" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-copy" v-if="menuAction.allowAdd" @click="copyRowEvent(row)"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">

      <el-row>
        <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
          <vxe-form-item title="明细1(条件)" field="modelElemID1" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemID1" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod3_1">
                <el-option v-for="item in ModelElemComboStoreByQuery_1" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="明细2(条件)" field="modelElemID2" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemID2" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod3_2" clearable>
                <el-option v-for="item in ModelElemComboStoreByQuery_2" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="明细3(条件)" field="modelElemID3" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemID3" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod3_3" clearable>
                <el-option v-for="item in ModelElemComboStoreByQuery_3" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="客户" field="ClientID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.clientID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="客户" clearable :remote-method="remoteMethod4">
                <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>

          <vxe-form-item title="品类绑定" field="groupIDs" span="12">
            <template #default="{ data }">
              <el-select v-model="data.groupIDs" filterable multiple placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="品类" clearable>
                <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="计算类型" field="modelElemRule1Type" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemRule1Type" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="计算类型" clearable>
                <el-option v-for="item in ModelElemRule1TypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>

          <vxe-form-item title="款式(结果)" field="modelElemListID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemListID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod">
                <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="款式明细(结果)" field="modelElemID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod3" clearable>
                <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="物料" field="itemID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.itemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="物料" clearable :remote-method="remoteMethod5">
                <el-option v-for="item in ItemComboStoreByQuery" :key="item.value" :label="'['+item.code+']'+item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="顺序" field="sort" span="8" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
          <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
          <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
          <vxe-form-item align="center" span="24">
            <template v-slot>
              <vxe-button type="submit" status="primary">保存</vxe-button>
              <vxe-button type="reset">重置</vxe-button>
            </template>
          </vxe-form-item>
        </vxe-form>
      </el-row>
      <el-divider content-position="left"></el-divider>
      <el-row>
        <vxe-form :data="dome" title-align="right" title-width="100">
          <vxe-form-item title="款式查询" field="modelElemListID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemListID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" clearable :remote-method="remoteMethod1">
                <el-option v-for="item in ModelElemListComboStoreByQuery1_1" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="款式明细查询" field="modelElemID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.modelElemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" clearable :remote-method="remoteMethod3_0">
                <el-option v-for="item in ModelElemComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="客户查询" field="clientID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.clientID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" clearable :remote-method="remoteMethod4">
                <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="物料查询" field="itemID" span="12">
            <template #default="{ data }">
              <el-select v-model="data.itemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="物料" clearable :remote-method="remoteMethod5">
                <el-option v-for="item in ItemComboStoreByQuery" :key="item.value" :label="'['+item.code+']'+item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
        </vxe-form>

      </el-row>
      <el-divider content-position="left"></el-divider>

    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modelelemrule1',
  mixins: [masterTableMixins],
  components: {
  },
  data() {
    return {
      dome: {
        modelElemListID: null,
        modelElemID: null,
        clientID: null
      },
      formData: {
        modelElemListID: null,
        modelElemID: null,
        modelElemID1: null,
        modelElemID2: null,
        modelElemID3: null,
        modelElemRule1Type: null,
        clientID: null,
        itemID: null,
        groups: null,
        groupIDs: null,
        sort: 1,
        remark: '',
        isActive: true
      },
      formRules: {
        modelElemListID: [{ required: true, message: '请选择款式' }],
        modelElemID1: [{ required: true, message: '请选择款式明细' }],
        modelElemRule1Type: [{ required: true, message: '请选择计算类型' }],
        // groupIDs: [{ required: true, message: '请选择品类' }]
      },

      api: {
        get: '/mtm/mom_modelelemrule1/get',
        add: '/mtm/mom_modelelemrule1/adds',
        edit: '/mtm/mom_modelelemrule1/updates',
        delete: '/mtm/mom_modelelemrule1/deletes',
        SorderTypeComboStore: '/mtm/combo/SorderTypeComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ItemComboStoreByQuery: '/mtm/comboQuery/ItemComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        SizeColumnComboStoreByQuery: '/mtm/comboQuery/SizeColumnComboStoreByQuery',
        ModelElemRule1TypeComboStore: '/mtm/combo/ModelElemRule1TypeComboStore',

      },
      GroupComboStore: [],
      clientComboStoreByQuery: [],
      ItemComboStoreByQuery: [],
      ModelElemListComboStoreByQuery: [],
      ModelElemListComboStoreByQuery1_1: [],
      ModelElemListComboStoreByQuery1_2: [],
      ModelElemListComboStoreByQuery1_3: [],
      ModelElemComboStoreByQuery: [],
      ModelElemComboStoreByQuery_1: [],
      ModelElemComboStoreByQuery_2: [],
      ModelElemComboStoreByQuery_3: [],
      ModelElemComboStoreByQuery1: [],
      SizeColumnComboStoreByQuery: [],
      ModelElemRule1TypeComboStore: [],
      footerCompanyInfo: false
    }
  },
  async created() {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore() {

      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStoreByQuery).then(result => {
        this.ItemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemRule1TypeComboStore).then(result => {
        this.ModelElemRule1TypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.ModelElemListComboStoreByQuery1_1 = result
        this.ModelElemListComboStoreByQuery1_2 = result
        this.ModelElemListComboStoreByQuery1_3 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
        this.ModelElemComboStoreByQuery1 = result
        this.ModelElemComboStoreByQuery_1 = result
        this.ModelElemComboStoreByQuery_2 = result
        this.ModelElemComboStoreByQuery_3 = result
      })
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
    },
    remoteMethod(query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    remoteMethod1(query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery1_1 = result
      })
    },
    remoteMethod2(query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery1_2 = result
      })
    },
    remoteMethod2_1(query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery1_3 = result
      })
    },
    remoteMethod3(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    remoteMethod3_0(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
      })
    },
    remoteMethod3_1(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery_1 = result
      })
    },
    remoteMethod3_2(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery_2 = result
      })
    },
    remoteMethod3_3(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery_3 = result
      })
    },
    remoteMethod4(query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5(query) {
      this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { text: query }).then(result => {
        this.ItemComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent(row) {

      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery_1 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID2 }).then(result => {
        this.ModelElemComboStoreByQuery_2 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID3 }).then(result => {
        this.ModelElemComboStoreByQuery_3 = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { gid: row.itemID }).then(result => {
        this.ItemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent(row) {
      if (this.$utils.has(row, 'id')) {
        row.id = null
      }
      if (this.$utils.has(row, 'code')) {
        row.code = null
      }
      if (this.$utils.has(row, 'codeName')) {
        row.codeName = null
      }
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery_1 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID2 }).then(result => {
        this.ModelElemComboStoreByQuery_2 = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID3 }).then(result => {
        this.ModelElemComboStoreByQuery_3 = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { gid: row.itemID }).then(result => {
        this.ItemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
