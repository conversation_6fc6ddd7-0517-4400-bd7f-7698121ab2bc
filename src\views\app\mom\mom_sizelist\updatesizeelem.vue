<template>

  <vxe-form title-align="right" title-width="100">
    <vxe-form-item title="身高" span="12" :item-render="{}">
      <template #default>
        <vxe-button status="primary" @click="Update('A')">自动匹配选中的身高型A</vxe-button>
      </template>
    </vxe-form-item>
    <vxe-form-item title="胸围" span="12" :item-render="{}">
      <template #default>
        <vxe-button status="info" @click="Update('B')">自动匹配选中的胸围型B</vxe-button>
      </template>
    </vxe-form-item>
    <vxe-form-item title="体型" span="12" :item-render="{}">
      <template #default>
        <vxe-button status="warning" @click="Update('C')">自动匹配选中的体型C</vxe-button>
      </template>
    </vxe-form-item>
    <vxe-form-item title="臀围" span="12" :item-render="{}">
      <template #default>
        <vxe-button status="danger" @click="Update('D')">自动匹配选中的臀围型D</vxe-button>
      </template>
    </vxe-form-item>
    <vxe-form-item title="身高" span="6" :item-render="{}">
      <template #default>
        <el-select v-model="data.sizeElemaID" filterable remote reserve-keyword placeholder="身高" :remote-method="SizeElemAMethod" clearable size="mini" style="width:90px">
          <el-option v-for="item in SizeElemAComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </template>
    </vxe-form-item>
    <vxe-form-item span="6" :item-render="{}">
      <template #default>
        <vxe-button status="primary" @click="Update('A',data.sizeElemaID,true)">更新选中</vxe-button>
      </template>
    </vxe-form-item>
    <vxe-form-item title="胸围" span="6" :item-render="{}">
      <template #default>
        <el-select v-model="data.sizeElembID" filterable remote reserve-keyword placeholder="胸围" :remote-method="SizeElemBMethod" clearable size="mini" style="width:90px">
          <el-option v-for="item in SizeElemBComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </template>
    </vxe-form-item>
    <vxe-form-item span="6" :item-render="{}">
      <template #default>
        <vxe-button status="primary" @click="Update('B',data.sizeElembID,true)">更新选中</vxe-button>
      </template>
    </vxe-form-item>
    <vxe-form-item title="体型" span="6" :item-render="{}">
      <template #default>
        <el-select v-model="data.sizeElemcID" filterable remote reserve-keyword placeholder="体型" :remote-method="SizeElemCMethod" clearable size="mini" style="width:90px">
          <el-option v-for="item in SizeElemCComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </template>
    </vxe-form-item>
    <vxe-form-item span="6" :item-render="{}">
      <template #default>
        <vxe-button status="primary" @click="Update('C',data.sizeElemcID,true)">更新选中</vxe-button>
      </template>
    </vxe-form-item>
    <vxe-form-item title="臀围" span="6" :item-render="{}">
      <template #default>
        <el-select v-model="data.sizeElemdID" filterable remote reserve-keyword placeholder="臀围" :remote-method="SizeElemDMethod" clearable size="mini" style="width:90px">
          <el-option v-for="item in SizeElemDComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </template>
    </vxe-form-item>
    <vxe-form-item span="6" :item-render="{}">
      <template #default>
        <vxe-button status="primary" @click="Update('D',data.sizeElemdID,true)">更新选中</vxe-button>
      </template>
    </vxe-form-item>
    <vxe-form-item span="24" :item-render="{}" align="center">
      <template #default>
        <!-- <vxe-button status="success" @click="updates(true)">更新选中(体型无法匹配时按自动默认A型)</vxe-button> -->
        <vxe-button status="success" @click="updateSelectedSizeElem()">更新选中</vxe-button>
        <vxe-button status="success" @click="updateSizeListSizeElem()">全自动更新</vxe-button>
      </template>
    </vxe-form-item>
  </vxe-form>
</template>

<script>
export default {
  name: 'UpdateSizeElem',
  props: {
    rows: {
      type: Array,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    updateSuccess: {
      type: Function
    }
  },
  data () {
    return {
      data: {
        sizeElemaID: null,
        sizeElembID: null,
        sizeElemcID: null,
        sizeElemdID: null
      },
      api: {
        SizeElemAComboStoreByQuery: '/mtm/comboQuery/SizeElemAComboStoreByQuery',
        SizeElemBComboStoreByQuery: '/mtm/comboQuery/SizeElemBComboStoreByQuery',
        SizeElemCComboStoreByQuery: '/mtm/comboQuery/SizeElemCComboStoreByQuery',
        SizeElemDComboStoreByQuery: '/mtm/comboQuery/SizeElemDComboStoreByQuery',
        updatesizeelem: '/mtm/mOM_Size/updatesizeelem',
        updatesizelistsizeelem: '/mtm/mOM_SizeList/SetSizeListSizeElem'
      },
      SizeElemAComboStoreByQuery: [],
      SizeElemBComboStoreByQuery: [],
      SizeElemCComboStoreByQuery: [],
      SizeElemDComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SizeElemAComboStoreByQuery).then(result => {
        this.SizeElemAComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SizeElemBComboStoreByQuery).then(result => {
        this.SizeElemBComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SizeElemCComboStoreByQuery).then(result => {
        this.SizeElemCComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SizeElemDComboStoreByQuery).then(result => {
        this.SizeElemDComboStoreByQuery = result
      })
    },
    async SizeElemAMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemAComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemAComboStoreByQuery = result
      })
    },
    async SizeElemBMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemBComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemBComboStoreByQuery = result
      })
    },
    async SizeElemCMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemCComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemCComboStoreByQuery = result
      })
    },
    async SizeElemDMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemDComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemDComboStoreByQuery = result
      })
    },
    submitEvent () {

    },
    Update (SizeElem, SizeElemID, selected = false) {
      var a = {
        sizeListID: this.form.id,
        sizeElemaID: null,
        sizeElema: null,
        sizeElembID: null,
        sizeElemb: null,
        sizeElemcID: null,
        sizeElemc: null,
        sizeElemdID: null,
        sizeElemd: null,
        sizeIDs: this.rows.map(item => { return item.id })
      }
      if (SizeElem === 'A') {
        a.sizeElemaID = SizeElemID
        a.sizeElema = SizeElem
      }
      if (SizeElem === 'B') {
        a.sizeElembID = SizeElemID
        a.sizeElemb = SizeElem
      }
      if (SizeElem === 'C') {
        a.sizeElemcID = SizeElemID
        a.sizeElemc = SizeElem
      }
      if (SizeElem === 'D') {
        a.sizeElemdID = SizeElemID
        a.sizeElemd = SizeElem
      }
      if (this.rows.length === 0) {
        this.$XModal.message({ message: '请勾选要更新的规格单', status: 'error' })
        return
      }
      if (selected) {
        if (SizeElemID === undefined || SizeElemID == null) {
          this.$XModal.message({ message: '请选择型', status: 'error' })
          return
        }
      }
      this.updateData(a)
    },
    updates (selected = false) {
      if (this.rows.length === 0) {
        this.$XModal.message({ message: '请勾选要更新的规格单', status: 'error' })
        return
      }
      var a = {
        sizeListID: this.form.id,
        sizeIDs: this.rows.map(item => { return item.id }),
        sizeElema: 'A',
        sizeElemb: 'B',
        sizeElemc: 'C',
        sizeElemd: 'D'
      }
      this.updateData(a)
    },
    async updateData (data) {
      const loading = this.$loading({
        lock: true,
        text: '更新中请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      await this.$api.ActionRequest(this.api.updatesizeelem, data).then(result => {
        loading.close()
        this.updateSuccess()
      }).catch(() => {
        loading.close()
      })
    },
    updateSelectedSizeElem () {
      if (this.rows.length === 0) {
        this.$XModal.message({ message: '请勾选要更新的规格单', status: 'error' })
        return
      }
      var sizeIDs = this.rows.map(item => { return item.id })
      this.updateSizeListSizeElem(sizeIDs)
    },
    async updateSizeListSizeElem (sizeIds = []) {
      const loading = this.$loading({
        lock: true,
        text: '更新中请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.form.sizeIDs = sizeIds
      await this.$api.ActionRequest(this.api.updatesizelistsizeelem, this.form).then(result => {
        loading.close()
        this.updateSuccess()
      }).catch(() => {
        loading.close()
      })
    }
  }

}
</script>

<style>
</style>
