<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="size" :item-render="{}"> <vxe-input v-model.trim="searchForm.size" placeholder="规格" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item field="markWashValue1" :item-render="{}"> <vxe-input v-model.trim="searchForm.markWashValue1" placeholder="落差1" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item field="markWashValue2" :item-render="{}"> <vxe-input v-model.trim="searchForm.markWashValue2" placeholder="落差2" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="客户版型名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadSuitsupplymarkwashMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="code" title="PatternCode" sortable width="150"> </vxe-table-column>
      <vxe-table-column field="codeName" title="内部版型编码简称" sortable width="150"></vxe-table-column>
      <vxe-table-column field="modelCode" title="客户版型名称" sortable width="150"></vxe-table-column>
      <!-- <vxe-table-column field="sModelCode" title="其他版型编码" sortable width="150"></vxe-table-column> -->
      <vxe-table-column field="size" title="规格单规格" sortable width="100"></vxe-table-column>
      <vxe-table-column field="markWashSize" title="洗唛规格" sortable width="100"></vxe-table-column>
      <vxe-table-column field="markWashSize1A" title="洗唛号型1A" sortable width="100"></vxe-table-column>
      <vxe-table-column field="markWashSize1B" title="洗唛号型1B(套装)" sortable width="100"></vxe-table-column>
      <vxe-table-column field="markWashSize2" title="洗唛号型2" sortable width="100"></vxe-table-column>
      <vxe-table-column field="markWashSize3" title="洗唛号型3" sortable width="100"></vxe-table-column>
      <vxe-table-column field="markWashSize4" title="洗唛号型4" sortable width="100"></vxe-table-column>
      <vxe-table-column field="markWashValue1" title="洗唛落差1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="markWashValue2" title="洗唛落差2" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="规格单规格" field="size" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="洗唛规格" field="markWashSize" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="洗唛号型1A" field="markWashSize1A" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="洗唛号型1B(套装)" field="markWashSize1B" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="洗唛号型2" field="markWashSize2" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="洗唛号型3" field="markWashSize3" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="洗唛号型4" field="markWashSize4" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="洗唛落差1" field="markWashValue1" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="洗唛落差2" field="markWashValue2" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="排序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="6" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'BadSuitsupplyMarkwashDetail',
  mixins: [detailTableMixins],
  components: {
  },
  props: {
    form: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      searchForm: {
        size: null,
        markWashValue1: null,
        markWashValue2: null
      },
      formData: {
        suitsupplyMarkWashID: null,
        remark: '',
        isActive: true,
        isSuit: false
      },
      formRules: {
        size: [{ required: true, message: '请输入规格单规格' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        markWashSize: [{ required: true, message: '请输入洗唛规格' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/bad_suitsupplymarkwashdetail/get',
        add: '/mtm/bad_suitsupplymarkwashdetail/adds',
        edit: '/mtm/bad_suitsupplymarkwashdetail/updates',
        delete: '/mtm/bad_suitsupplymarkwashdetail/deletes'
      }
    }
  },
  async created () {
    await this.getCombStore()
    this.formData.suitsupplyMarkWashID = this.form.id
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
