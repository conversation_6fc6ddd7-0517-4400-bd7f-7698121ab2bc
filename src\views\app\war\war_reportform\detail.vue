<template>
  <d2-container class="reportfromdetail">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <!-- <vxe-radio-group v-model="formtable"> -->
          <!-- <vxe-radio-button label="0" content="单据明细数据"></vxe-radio-button> -->
          <vxe-button status="danger" content="单据明细数据" @click="showpfd('0')"></vxe-button>
          <!-- <vxe-radio-button label="1" content="报关装箱单"></vxe-radio-button> -->
          <!-- <vxe-radio-button label="1_1" content="清关装箱单"></vxe-radio-button> -->
          <!-- <vxe-radio-button label="2" content="(进料加工)报关单"></vxe-radio-button> -->
          <!-- <vxe-radio-button label="4" content="(进料加工)发票"></vxe-radio-button> -->
          <!-- <vxe-radio-button label="3" content="海关申报要素"></vxe-radio-button> -->
          <!-- <vxe-radio-button label="4_4" content="清关发票"></vxe-radio-button> -->
          <!-- <vxe-radio-button label="2_1" content="(来料加工)报关单"></vxe-radio-button> -->
          <!-- <vxe-radio-button label="2_2" content="(一般贸易)报关单"></vxe-radio-button> -->
          <!-- <vxe-radio-button label="4_1" content="(来料加工)发票"></vxe-radio-button> -->
          <vxe-button size="mini" status="primary" transfer>
            <template #default>进料加工</template>
            <template #dropdowns>
              <vxe-button type="text" content="(进料加工)报关单" @click="showpfd('2','(进料加工)报关单')"></vxe-button>
              <vxe-button type="text" content="(进料加工)发票" @click="showpfd('4','(进料加工)发票')"></vxe-button>
              <vxe-button type="text" content="报关装箱单" @click="showpfd('1','报关装箱单')"></vxe-button>
              <vxe-button type="text" content="清关装箱单" @click="showpfd('1_1','清关装箱单')"></vxe-button>
              <vxe-button type="text" content="海关申报要素" @click="showpfd('3','海关申报要素')"></vxe-button>
              <vxe-button type="text" content="清关发票" @click="showpfd('4_4','清关发票')"></vxe-button>
            </template>
          </vxe-button>
          <vxe-button size="mini" status="success" transfer>
            <template #default>来料加工</template>
            <template #dropdowns>
              <vxe-button type="text" content="(来料加工)报关单" @click="showpfd('2_1','(来料加工)报关单')"></vxe-button>
              <vxe-button type="text" content="(来料加工)发票" @click="showpfd('4_1','(来料加工)发票')"></vxe-button>
              <vxe-button type="text" content="报关装箱单" @click="showpfd('1','报关装箱单')"></vxe-button>
              <vxe-button type="text" content="清关装箱单" @click="showpfd('1_1','清关装箱单')"></vxe-button>
              <vxe-button type="text" content="海关申报要素" @click="showpfd('3','海关申报要素')"></vxe-button>
              <vxe-button type="text" content="清关发票" @click="showpfd('4_4','清关发票')"></vxe-button>
            </template>
          </vxe-button>
          <vxe-button size="mini" status="warning" transfer>
            <template #default>一般贸易</template>
            <template #dropdowns>
              <vxe-button type="text" content="(一般贸易)报关单" @click="showpfd('2_2','(一般贸易)报关单')"></vxe-button>
              <vxe-button type="text" content="(进料加工)发票" @click="showpfd('4','(进料加工)发票')"></vxe-button>
              <vxe-button type="text" content="报关装箱单" @click="showpfd('1','报关装箱单')"></vxe-button>
              <vxe-button type="text" content="清关装箱单" @click="showpfd('1_1','清关装箱单')"></vxe-button>
              <vxe-button type="text" content="海关申报要素" @click="showpfd('3','海关申报要素')"></vxe-button>
              <vxe-button type="text" content="清关发票" @click="showpfd('4_4','清关发票')"></vxe-button>
            </template>
          </vxe-button>
          <!-- <vxe-radio-button label="5" content="付款发票"></vxe-radio-button> -->
          <!-- </vxe-radio-group> -->
        </template>
      </vxe-toolbar>
    </template>
    <report-form-detail v-if="formtable==='0'" :form="form" />
    <report-form1 v-if="formtable==='1'" :form="form" :tablename="tablename" />
    <report-form4 v-if="formtable==='4'" :form="form" :tablename="tablename" />
    <report-form4 v-if="formtable==='4_1'" :type="2" :form="form" :tablename="tablename" />
    <report-form44 v-if="formtable==='4_4'" :form="form" :tablename="tablename" />
    <report-form5 v-if="formtable==='5'" :form="form" :tablename="tablename" />
    <report-form11 v-if="formtable==='1_1'" :form="form" :tablename="tablename" />
    <report-form2 v-if="formtable==='2'" :form="form" :tablename="tablename" />
    <report-form2 v-if="formtable==='2_1'" :type="2" :form="form" :tablename="tablename" />
    <report-form2 v-if="formtable==='2_2'" :type="3" :form="form" :tablename="tablename" />
    <report-form3 v-if="formtable==='3'" :form="form" :tablename="tablename" />
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import ReportFormDetail from './reportformdetail.vue'
import ReportForm1 from './components/reportfrom1.vue'
import ReportForm4 from './components/reportfrom4.vue'
import ReportForm44 from './components/reportfrom4_4.vue'
import ReportForm5 from './components/reportfrom5.vue'
import ReportForm11 from './components/reportfrom1_1.vue'
import ReportForm2 from './components/reportfrom2.vue'
import ReportForm3 from './components/reportfrom3.vue'
// import { cloneDeep } from 'lodash'
export default {
  name: 'reportFormDetailMaster',
  mixins: [detailTableMixins],
  components: {
    ReportFormDetail,
    ReportForm1,
    ReportForm11,
    ReportForm2,
    ReportForm3,
    ReportForm4,
    ReportForm44,
    ReportForm5

  },
  // props: {
  //   form: {
  //     type: Object,
  //     required: true
  //   }
  // },
  errorCaptured (msg, vm, trace) {
    return false
  },
  data () {
    return {
      formtable: '0',
      tablename: '',
      formData: {
        remark: '',
        isActive: true,
        itemID1: null,
        itemID: null,
        sorderType: 1,
        modelElemID: null,
        clientID: null
      },
      update: true,
      isdel: true,
      formRules: {
        itemID1: [{ required: true, message: '货号必填' }],
        modelElemID: [{ required: true, message: '款式明细必选' }]
      },

      api: {
        get: '/mtm/bAD_ItemElemItem/getml',
        add: '/mtm/bAD_ItemElemItem/updates',
        edit: '/mtm/bAD_ItemElemItem/updates',
        delete: '/mtm/bAD_ItemElemItem/deletes'

      }

    }
  },
  async created () {
    await this.getCombStore()
    // this.loadData({ itemID: this.form.id }).then(({ data }) => {
    // })
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.SorderTypeComboStore).then(result => {
      //   this.SorderTypeComboStore = result
      // })
    },
    showpfd (name, tname) {
      this.formtable = name
      this.tablename = tname
      console.log(tname)
    },
    printEvent (name) {
      // const printStyle = `@page { size: portrait;}`  //size: landscape;//横向     size: portrait;//纵向
      const divEl1 = document.getElementById(name)
      this.$XPrint({
        sheetName: '打印下面区域',
        // style: printStyle,
        content: divEl1.innerHTML
      })
    },
    printEvent1 () {
      const printStyle = '@page { size: portrait;}' // size: landscape;//横向     size: portrait;//纵向
      const divEl1 = document.getElementById('myPrint1')
      this.$XPrint({
        sheetName: '打印下面区域',
        style: printStyle,
        content: divEl1.innerHTML
      })
    },
    printEvent2 () {
      const printStyle = '@page { size: landscape;}' // size: landscape;//横向     size: portrait;//纵向
      const divEl = document.getElementById('myPrint2')
      this.$XPrint({
        sheetName: '打印下面区域',
        style: printStyle,
        content: divEl.innerHTML
      })
    }
  }
}
</script>

<style  lang="scss" >
.root {
  border: 1px solid;
  margin: 0 auto;
}
</style>
