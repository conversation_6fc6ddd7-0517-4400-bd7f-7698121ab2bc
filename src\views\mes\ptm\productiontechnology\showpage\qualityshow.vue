<template>
  <d2-container class="qalityShow">
    <template slot="header">
      <div style="position: absolute;    display: flex;">
        <h2 style="float: left;"> 质检显示:</h2>
        <h2>{{productstationconfigData.codeName}}</h2>
      </div>
      <div style="position: absolute;    display: flex;right:20px">
        <slot name="scanqty" />
      </div>
      <div class="sordernum">
        <slot name="numinput" />
      </div>
      <show-header :form="form" />
    </template>
    <template>
      <d2-container type="ghost">

        <split-pane :min-percent='10' :default-percent='35' split="vertical">
          <template slot="paneL">
            <model-elem :form="form" />
          </template>
          <template slot="paneR">
            <template>
              <div>
                <el-row>
                  <el-col :span="21">
                    <sorder-size :form="form" v-if="productstationconfigData.isShowSorderSize" />
                  </el-col>
                  <el-col :span="2">
                    <quality-set :form="form" v-if="productstationconfigData.isShowSorderSize" :productstationconfig="productstationconfigData" />
                  </el-col>
                </el-row>
                <template v-if="form!==null&&form.sorderType===0">
                  <!-- 返修数据 -->
                  <sorder-repair-part-price :form="form" />
                  <sorder-repair-image :form="form" />
                </template>
                <template v-else>
                  <model-elem-image :form="form" :productstationconfigData="productstationconfigData" />
                </template>

              </div>

            </template>
          </template>
        </split-pane>
      </d2-container>
    </template>
  </d2-container>
</template>

<script>
import defaultset from '../components/index'
import showHeader from '../components/showheader'
import ModelElem from '../components/modelelem'
import ModelElemImage from '../components/modelelemimage'
import SorderSize from '../components/sordersize'
import QualitySet from '../components/qualityset'
import SorderRepairImage from '../components/sorderrepairimage.vue'
import SorderRepairPartPrice from '../components/sorderrepairpartprice.vue'
export default {
  name: 'QualityShow', // 质检显示
  mixins: [defaultset],
  components: {
    showHeader,
    ModelElem,
    ModelElemImage,
    SorderSize,
    QualitySet,
    SorderRepairImage,
    SorderRepairPartPrice
  },
  props: {
    form: {
      type: Object
    }
    // productstationconfig: {
    //   type: Object,
    //   default: null,
    // }
  },
  data () {
    return {
      number: ''
    }
  },

  created () {

  },
  methods: {

  }
}
</script>

<style lang='scss'>
.qalityShow {
  .sordernum {
    // position: absolute;
    // top: 0px;
    margin: 0 auto;
    text-align: center;
    width: 300px;
  }
}
</style>
