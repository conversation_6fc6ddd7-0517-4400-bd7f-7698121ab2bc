<template>
  <transition>
    <keep-alive v-if="keepalive">
      <component :is="activepage" :form="masterfrom" @nextpage="nextpage" :masterSeach="masterSeach" :showPrice="showPrice" />
    </keep-alive>
    <template v-else>
      <component :is="activepage" :form="masterfrom" @nextpage="nextpage" :masterSeach="masterSeach" :showPrice="showPrice" />
    </template>
  </transition>

</template>

<script>
import master from './master.vue'
import detail from './detail.vue'
export default {
  name: 'war_itemstock',
  components: {
    master,
    detail
  },
  props: {
    showPrice: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      activepage: 'master',
      keepalive: true,
      masterSeach: null,
      masterfrom: null
    }
  },
  methods: {
    nextpage (item) {
      this.activepage = item.pagename
      this.masterfrom = item.data
      this.masterSeach = item.masterSeach
      if (item.keepalive) {
        this.keepalive = item.keepalive
      } else {
        this.keepalive = false
      }
    }
  }
}
</script>
