import axios from 'axios'
import { get } from 'lodash'
import util from '@/libs/util'
import { errorLog, errorCreate } from './tools'
import router from '@/router'
// import Qs from 'qs'
/**
 * @description 创建请求实例
 */
function createService() {
  // 创建一个 axios 实例
  const service = axios.create()
  service.defaults.timeout = 300000
  // 请求拦截
  service.interceptors.request.use(
    config => {
      if (config.method === 'post') {
        // config.headers['Content-Type']='application/x-www-form-urlencoded'
        // config.data = Qs.stringify(config.data)
        // var readyData = JSON.stringify(config.data);
        // config.data = readyData
        // config.headers["Content-Type"]='application/json-patch+json';
        // config.headers["Accept"]='*/*';

      }
      return config
    },
    error => {
      // 发送失败
      console.log(error)
      return Promise.reject(error)
    }
  )
  // 响应拦截
  service.interceptors.response.use(
    response => {
      // dataAxios 是 axios 返回数据中的 data
      const dataAxios = response.data
      // 这个状态码是和后端约定的
      const { success, result, error } = dataAxios
      // 根据 code 进行判断
      if (success) {
        // 如果没有 code 代表这不是项目后端开发的接口 比如可能是 D2Admin 请求最新版本
        return result
      } else {
        if (success === false) {
          return result
        }
        if (dataAxios.access_token || dataAxios.access_token !== '') {
          return dataAxios
        }
        errorCreate(`${error}: ${response.config.url}`)
        // 有 code 代表这是一个后端接口 可以进行进一步的判断
        // switch (code) {
        //   case 0:
        //     // [ 示例 ] code === 0 代表没有错误
        //     return dataAxios.data
        //   case 'xxx':
        //     // [ 示例 ] 其它和后台约定的 code
        //     errorCreate(`[ code: xxx ] ${dataAxios.msg}: ${response.config.url}`)
        //     break
        //   default:
        //     // 不是正确的 code
        //     errorCreate(`${dataAxios.errot}: ${response.config.url}`)
        //     break
        // }
      }
    },
    error => {
      const status = get(error, 'response.status')
      // const result = get(error, 'response.request.response')
      switch (status) {
        case 400: error.message = '请求错误'; break
        case 401:
          error.message = '未授权，请尝试重新登录/或联系管理者'
          setTimeout(() => {
            router.push({ name: 'login' })
          }, 2000)
          // 跳转路由
          break
        case 403: error.message = '拒绝访问'; break
        case 404: error.message = `请求地址出错: ${error.response.config.url}`; break
        case 408: error.message = '请求超时'; break
        case 429:
          error.message = '访问太频繁!请稍后再试！！！'; break
        case 500:
          var result = JSON.parse(get(error, 'response.request.response'))
          error.message = result.error.message
          break
        case 501: error.message = '服务未实现'; break
        case 502: error.message = '网关错误'; break
        case 503: error.message = '服务不可用'; break
        case 504: error.message = '网关超时'; break
        case 505: error.message = 'HTTP版本不受支持'; break
        default:
          error.message = '网络连接失败，请重试！'
          break
      }

      // if (config && config.errormsg !== undefined) {
      //   if (config.errormsg) {
      //     errorLog(error, status)
      //   }
      // } else {
      //   errorLog(error, status)
      // }
      errorLog(error, status)
      return Promise.reject(error)
    }
  )
  return service
}

/**
 * @description 创建请求方法
 * @param {Object} service axios 实例
 */
function createRequestFunction(service) {
  return function (config) {
    const token = util.cookies.get('token')
    var authorization = get(config, 'headers.Authorization', config.Authorization || 'Bearer ' + token)
    const configDefault = {
      headers: {
        // Authorization: 'Bearer ' + token,
        Authorization: authorization,
        'Content-Type': get(config, 'headers.Content-Type', config.ContentType || 'application/json;charset=UTF-8')
      },
      timeout: 300000,
      baseURL: config.baseurl || window.VUE_APP_API || process.env.VUE_APP_API,
      data: {}
    }

    return service(Object.assign(configDefault, config))
  }
}
// function getApiUrl (str) {
//   var url = process.env.VUE_APP_API
//   if (str.indexOf('/sso') >= 0) {
//     url = process.env.VUE_APP_USER_API
//   }
//   return url
// }
// 用于真实网络请求的实例和请求方法
export const service = createService()
export const request = createRequestFunction(service)

// // 用于模拟网络请求的实例和请求方法
// export const serviceForMock = createService()
// export const requestForMock = createRequestFunction(serviceForMock)

// // 网络请求数据模拟工具
// export const mock = new Adapter(serviceForMock)
