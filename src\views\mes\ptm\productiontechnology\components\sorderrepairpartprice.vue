<template>
  <div class="SorderRepairPartPrice">
    <el-row>
      <template v-for="(item,index) in list">
        <el-button type="warning" :key="index">{{item.partPriceName}}</el-button>
      </template>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'SorderRepairPartPrice',
  props: {
    form: {
      require: true,
      type: Object
    }
  },
  watch: {
    form: {
      deep: true,
      handler (newval, oldval) {
        this.list = []
        if (newval !== null && newval.sorderRepairModelID !== null) {
          this.get(newval.sorderRepairModelID)
        }
      }
    }
  },
  data () {
    return {
      api: {
        get: '/mtm/oDM_SorderRepairPartPrice/get'
      },
      list: []
    }
  },
  created () {
    if (this.form !== null && this.form.sorderRepairModelID !== null) {
      this.get(this.form.sorderRepairModelID)
    }
  },
  methods: {
    async get (id) {
      this.list = []
      await this.$api.ActionRequest(this.api.get, { sorderRepairModelID: id }).then(res => {
        this.list = res.items
      })
    }
  }
}
</script>

<style lang='scss'>
.SorderRepairPartPrice {
  padding-top: 20px;
}
</style>
