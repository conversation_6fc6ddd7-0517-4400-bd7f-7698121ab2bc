<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="modelShowEvent()" v-if="menuAction.allowAdd">版型选择</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" placeholder="品类" clearable size="mini" style="width:110px">
                  <el-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelGroupID">
              <template #default="{ data }">
                <el-select v-model="data.modelGroupID" filterable placeholder="版型系列" size="mini" clearable>
                  <el-option v-for="item in ModelGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <!-- <vxe-form-item field="modelDesignByElemListTypeComboStore" :item-render="{name: '$select', options: ModelDesignByElemListTypeComboStore,props:{clearable:true,placeholder:'层级'}}" /> -->
            <vxe-form-item field="modelElemListID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.modelElemListID" filterable placeholder="款式" size="mini" remote reserve-keyword :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.label+item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.modelElemID" filterable placeholder="款式明细" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
                  <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.label+item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>

            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'版型编码/版型名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModeldesignbyelemlistdetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60" />
      <vxe-table-column type="checkbox" width="60" />
      <vxe-table-column field="groupText" title="品类" width="60" />
      <vxe-table-column field="modelCode" title="版型编码" sortable width="130" />
      <vxe-table-column field="modelCodeName" title="版型名称" sortable width="150" />
      <vxe-table-column field="modelDesignByElemListTypeText" title="层级" sortable width="150" />
      <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="130" />
      <vxe-table-column field="modelElemListCodeName" title="款式名称" sortable width="150" />
      <vxe-table-column field="modelElemCode" title="款式明细编码" sortable width="130" />
      <vxe-table-column field="modelElemCodeName" title="款式明细名称" sortable width="150" />
      <!-- <vxe-table-column field="remark" title="备注" sortable width="100" /> -->
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100" /> -->
      <!-- <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100" /> -->
      <vxe-table-column field="createBy" title="创建人" sortable width="100px" />
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100" />
      <vxe-table-column title="操作" width="50" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showModel" :title="'版型选择' " width="65%" height="50%" show-zoom resize destroy-on-close>
      <model v-if="showModel" @getSelelctModel="getSelelctModel" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import Model from './components/model.vue'
// import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modeldesignbyelemlistdetail',
  mixins: [masterTableMixins],
  components: {
    Model
  },
  data () {
    return {
      showModel: false,
      searchForm: {
        modelDesignByElemListTypeComboStore: null,
        modelElemID: null,
        modelGroupID: null,
        modelElemListID: null
      },
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/mom_modeldesignbyelemlistdetail/get',
        add: '/mtm/mom_modeldesignbyelemlistdetail/adds',
        edit: '/mtm/mom_modeldesignbyelemlistdetail/updates',
        delete: '/mtm/mom_modeldesignbyelemlistdetail/deletes',
        CreateDetails: '/mtm/mom_modeldesignbyelemlistdetail/CreateDetails',
        ModelDesignByElemListTypeComboStore: '/mtm/combo/ModelDesignByElemListTypeComboStore',
        // departmentCombStore: '/mtm/combo/departmentCombStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ModelGroupComboStore: '/mtm/combo/ModelGroupComboStore'
      },
      ModelElemListComboStoreByQuery: [],
      GroupComboStore: [],
      ModelElemComboStoreByQuery: [],
      ModelGroupComboStore: [],
      ModelDesignByElemListTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelDesignByElemListTypeComboStore).then(result => {
        this.ModelDesignByElemListTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelGroupComboStore).then(result => {
        this.ModelGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    modelShowEvent () {
      this.showModel = true
    },
    getSelelctModel (data) {
      var models = data.map(a => { return { modelID: a.id } })
      if (models.length <= 0) {
        return
      }
      this.$api.ActionRequest(this.api.CreateDetails, models).then(result => {
        this.$XModal.message({ message: '保存成功', status: 'success' })
        this.loadData()
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    }
    // remoteMethod(query,gid) {
    // return new Promise(resolve => {
    // this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query, gid: gid }).then(result => {
    // this.ModelElemListComboStoreByQuery = result
    // return resolve(true)
    // })
    // })
    // })

    // editEventThen(row) {
    // this.remoteMethod(null, row.id).then(res => { this.showEdit = true })
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
