<template>
  <d2-container class="mom_modelelemlist_master">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--download" status="success" @click="exportDataEvent">导出</vxe-button>
          <vxe-button icon="vxe-icon--refresh" status="warning" @click="add">生成清单</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="loadData()"></vxe-button>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table ref='master_table' id="bomitem" :custom-config="{storage: true}" height="auto" :data="tableData">
      <vxe-table-colgroup :title="'订单号:'+this.sorder.code+'【' +this.sorder.clientText+'】Bom清单'" align='center'>
        <vxe-table-column type="seq" width="60px"></vxe-table-column>
        <!-- <vxe-table-column field="cadRuleText" title="衣片组" sortable width="70px"></vxe-table-column>
        <vxe-table-column field="cadLayoutText" title="排料图" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelElemCode" title="工艺编码" sortable width="70px"></vxe-table-column> -->
        <vxe-table-column field="groupName" title="分类" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelElemListName" title="款式名称" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelElemName" title="工艺名称" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="itemText" title="货号" sortable width="70px"></vxe-table-column>
        <vxe-table-column field="originalItemNo" title="原始货号" sortable width="80px"></vxe-table-column>
        <vxe-table-column field="qty" title="耗量/件" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="modelQty" title="件/数" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="countQty" title="合计耗量" sortable width="200px">
          <template v-slot="{ row }">
            <span>{{ row.countQty }}</span>
            <span v-if="row.itemMLText!==null" style="color:red;font-weight: 500;">({{ row.itemMLText }}cm)</span>
          </template>
        </vxe-table-column>
        <vxe-table-column field="inventoryQty" title="有效库存" sortable width="95px"></vxe-table-column>
        <vxe-table-column field="itemStockPositionText" title="库位" sortable width="95px"></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-column title="操作" width="100" :fixed='tableOptFixed' align="center" v-if="isPatternMaker&&info.userType!==2">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="工艺信息" field="modelElemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable remote reserve-keyword placeholder="工艺信息" :remote-method="remoteMethod" size="mini">
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="物料信息" field="itemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod">
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="耗量" field="qty" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"> </vxe-form-item>
        <!-- <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item> -->
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { mapState } from 'vuex'
import { cloneDeep } from 'lodash'
export default {
  name: 'BomItems',

  mixins: [masterTableMixins],
  watch: {
    sorder: {
      handler: function (newVal, olVal) {
        if (newVal !== null) {
          this.get(newVal)
        }
      }
    }
  },
  props: {
    sorder: {
      required: true,
      type: Object
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ]),
    isPatternMaker () {
      var p = this.$utils.findIndexOf(this.info.userRoles, item => item.code === 'PatternMaker')
      if (p >= 0) {
        return true
      } else {
        return false
      }
    }
  },
  data () {
    return {
      tableData: [],
      api: {
        get: '/mtm/wAR_ItemBomDetail/get',
        add: '/mtm/wAR_ItemBom/createSorderBom',
        edit: '/mtm/war_itembomdetail/updates',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery'
      },
      searchForm: {
        currentPage: 1,
        totalCount: 0,
        text: '',
        skipCount: 0,
        sorderID: this.sorder.id
      },
      ItemComboStore: [],
      ModelElemComboStoreByQuery: [],
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        itemID: [{ required: true, message: '请输入物料信息' }]
      }
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    add () {
      this.$XModal.confirm('已有数据会全部情况,确认重新生成吗?').then(type => {
        if (type === 'confirm') {
          this.$api.ActionRequest(this.api.add, { sorderID: this.sorder.id }).then(res => {
            this.$message({ message: '生成成功', type: 'success' })
            this.loadData()
          })
        }
      })
    },
    exportDataEvent () {
      var filename = '【' + this.sorder.code + '】Bom清单' + this.timeFormate(new Date())
      this.$refs.master_table.exportData({ type: 'csv', filename: filename })
    },
    timeFormate (timeStamp) {
      const year = new Date(timeStamp).getFullYear()
      const month = new Date(timeStamp).getMonth() + 1 < 10 ? '0' + (new Date(timeStamp).getMonth() + 1) : new Date(timeStamp).getMonth() + 1
      const date = new Date(timeStamp).getDate() < 10 ? '0' + new Date(timeStamp).getDate() : new Date(timeStamp).getDate()
      const hh = new Date(timeStamp).getHours() < 10 ? '0' + new Date(timeStamp).getHours() : new Date(timeStamp).getHours()
      const mm = new Date(timeStamp).getMinutes() < 10 ? '0' + new Date(timeStamp).getMinutes() : new Date(timeStamp).getMinutes()
      const ss = new Date(timeStamp).getSeconds() < 10 ? '0' + new Date(timeStamp).getSeconds() : new Date(timeStamp).getSeconds()
      return (year + '年' + month + '月' + date + '日' + hh + ':' + mm + ':' + ss)
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }

}
</script>

<style>
</style>
