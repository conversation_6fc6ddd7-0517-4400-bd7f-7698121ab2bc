<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="bodyId" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model.trim="data.bodyId" filterable placeholder="特体" remote reserve-keyword :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in BodyComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sizeColumnId" :item-render="{name: '$select', options: SizeColumnComboStore,props:{placeholder:'规格',clearable:true,filterable:true}}" />
            <vxe-form-item field="productType" :item-render="{name: '$select', options: SuitSupplyOptionSizeProductTypeComboStore,props:{placeholder:'Type',clearable:true}}" />
            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='OdmSuitsupplyoptionsizeMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="productTypeText" title="ProductTypeText" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="measurementsId" title="MeasurementsId" sortable width="100"></vxe-table-column>
      <vxe-table-column field="editorTitle" title="EditorTitle" sortable width="100"></vxe-table-column>
      <vxe-table-column field="maximumAlteration" title="MaximumAlteration" sortable width="100"></vxe-table-column>
      <vxe-table-column field="minimumAlteration" title="MinimumAlteration" sortable width="100"></vxe-table-column>
      <vxe-table-column field="factoryIntegrationAlterationId" title="FactoryIntegrationAlterationId" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sizeColumnCode" title="规格编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sizeColumnCodeName" title="规格名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="bodyCode" title="特体编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="bodyName" title="特体名称" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="MeasurementsId" title="MeasurementsId" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row,editEventThen)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="productType" field="productType" span="12" :item-render="{ name: '$select', options: SuitSupplyOptionSizeProductTypeComboStore,props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="measurementsId" field="measurementsId" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="editorTitle" field="editorTitle" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="factoryIntegrationAlterationId" field="factoryIntegrationAlterationId" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="maximumAlteration" field="maximumAlteration" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="minimumAlteration" field="minimumAlteration" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="绑定规格" field="sizeColumnId" span="12" :item-render="{ name: '$select', options: SizeColumnComboStore,props:{clearable:true,placeholder:'规格',filterable:true}}"></vxe-form-item>
        <vxe-form-item title="绑定特体" field="bodyId" span="12">
          <template #default="{ data }">
            <el-select v-model="data.bodyId" filterable placeholder="特体" size="mini" remote reserve-keyword clearable :remote-method="remoteMethod2">
              <el-option v-for="item in BodyComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'odm_suitsupplyoptionsize',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        productType: null,
        sizeColumnId: null,
        bodyId: null
      },
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/odm_suitsupplyoptionsize/get',
        add: '/mtm/odm_suitsupplyoptionsize/adds',
        edit: '/mtm/odm_suitsupplyoptionsize/updates',
        delete: '/mtm/odm_suitsupplyoptionsize/deletes',
        SuitSupplyOptionSizeProductTypeComboStore: '/mtm/combo/SuitSupplyOptionSizeProductTypeComboStore',
        SizeColumnComboStore: '/mtm/combo/SizeColumnComboStore',
        BodyComboStoreByQuery: '/mtm/comboQuery/BodyComboStoreByQuery'
      },
      SuitSupplyOptionSizeProductTypeComboStore: [],
      SizeColumnComboStore: [],
      BodyComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SuitSupplyOptionSizeProductTypeComboStore).then(result => {
        this.SuitSupplyOptionSizeProductTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.SizeColumnComboStore).then(result => {
        this.SizeColumnComboStore = result
      })
      await this.$api.ActionRequest(this.api.BodyComboStoreByQuery).then(result => {
        this.BodyComboStoreByQuery = result
      })
    },
    remoteMethod2 (query, gid) {
      return new Promise(resolve => {
        this.$api.ActionRequest(this.api.BodyComboStoreByQuery, { text: query, gid: gid }).then(result => {
          this.BodyComboStoreByQuery = result
          return resolve(true)
        })
      })
    },
    copyRowEventThen (row) {
      this.remoteMethod2(null, row.bodyId).then(res => { this.showEdit = true })
    },
    editEventThen (row) {
      this.remoteMethod2(null, row.bodyId).then(res => { this.showEdit = true })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
