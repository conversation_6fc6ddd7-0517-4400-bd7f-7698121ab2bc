<template>

  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <!-- <strong>订单状态分布</strong> -->
      <div style="float: right; padding: 3px 0" type="text">
        <el-radio-group v-model="day" size="mini" @change="change">
          <el-radio-button label="7">7天</el-radio-button>
          <el-radio-button label="30">30天</el-radio-button>
          <el-radio-button label="180">6个月</el-radio-button>
          <el-radio-button label="365">1年</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div>
      <div ref="main" style="width: 500px;height:400px;"></div>
    </div>
  </el-card>

</template>

<script>
import commonjs from './common.js'
export default {
  mixins: [commonjs],
  data () {
    return {
      timer: '',
      myChart: null,
      api: {
        get: 'mtm/indexBoard/sorderByStatus'
      },
      option: {
        title: {
          text: '订单状态分布'
          // subtext: 'Living Expenses in Shenzhen'
        },

        legend: {
          data: ['订单量']
        }
      },
      day: 30
    }
  },

  methods: {
    setOptions (data) {
      var xAxis = []
      var series = []
      data.forEach(a => {
        xAxis.push(a.statusText)
        series.push(a.count)
      })
      // 填入数据
      this.myChart.setOption({
        xAxis: {
          data: xAxis || []
        },
        series: [
          {
            type: 'bar',
            data: series || []
          }
        ]
      })
    }
  }

}
</script>
