<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="itemConfigBaseID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.itemConfigBaseID" placeholder="分类" clearable>
                  <vxe-option v-for="item in itemConfigBaseStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadItemconfigMasterTable' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="itemConfigBaseText" title="分类" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="code" title="编码" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sequence" title="顺序" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="100px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'bad_itemconfig',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        sequence: null,
        itemConfigBase: null
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 1, max: 20, message: '长度在 1 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 1, max: 20, message: '长度在 1 到 20 个字符' }],
        itemConfigBase: [{ required: true, message: '请选择配置分类' }]
      },
      formItems: [
        { field: 'itemConfigBase', title: '配置分类', span: 24, itemRender: { name: '$select', options: [] } },
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'sequence', title: '顺序', span: 12, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/bad_itemconfig/get',
        add: '/mtm/bad_itemconfig/adds',
        edit: '/mtm/bad_itemconfig/updates',
        delete: '/mtm/bad_itemconfig/deletes',
        itemConfigBaseStore: '/mtm/combo/ItemConfigBaseComboStore'
      },
      itemConfigBaseStore: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    this.$utils.find(this.formItems, item => item.field === 'itemConfigBase').itemRender.options = this.itemConfigBaseStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.itemConfigBaseStore).then(result => {
        this.itemConfigBaseStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
