<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelelemlistimageMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60" />
      <vxe-table-column type="checkbox" width="60" />
      <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="100" />
      <vxe-table-column field="modelElemListCodeName" title="款式名称" sortable width="100" />
      <vxe-table-column field="imageUrl" title="图片" sortable width="200px">
        <template v-slot="{ row }">
          <el-popover placement="right-end" width="800" trigger="hover">
            <el-image :src="row.imageUrl" fit="contain">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.imageUrl" fit="scale-down" slot="reference" style="height:36px;">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>

        </template>

      </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100" />
      <vxe-table-column field="imageSeq" title="顺序" sortable width="100" />
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100" />
      <vxe-table-column field="createBy" title="创建人" sortable width="100px" />
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100" />
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式" field="modelElemListID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="请输入关编码名称" :remote-method="ModelElemListComboStoreByQueryMethod" clearable size="mini">
              <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片" field="modelImageID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelImageID" filterable remote reserve-keyword placeholder="请输入图片编码" :remote-method="ModelImageComboStoreByQueryMethod" clearable @change="selectchange" size="mini">
              <el-option v-for="item in ModelImageComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <el-tooltip class="item" effect="dark" :content=" item.label" placement="top-start">
                  <span style="float: left ;overflow:hidden;width:250px; text-overflow: ellipsis;    white-space: nowrap;   word-break:keep-all;" class="modelimageselect">{{ item.label }}</span>
                </el-tooltip>
                <span style="width:60px;">{{item.position}}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  <el-image :src="item.text" fit="fill" style="width: 100px; height: 100px">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片顺序" field="imageSeq" span="12">
          <vxe-input v-model="selectRow.imageSeq" placeholder="图片顺序" type="number" clearable>
          </vxe-input>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="图片" span="24">
          <template #default>
            <el-image style="width: 200px; height: 200px" :src="selectRow.imageUrl" fit="fill">
            </el-image>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>

  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modelelemlistimage',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
      },
      formData: {
        imageSeq: 999,
        modelElemListID: null,
        modelImageID: null,
        remark: '',
        isActive: true,
        imageUrl: null
      },
      // imageurl: null,
      formRules: {
        modelElemListID: [{ required: true, message: '请选择款式' }],
        modelImageID: [{ required: true, message: '请选择图片' }]
      },
      api: {
        get: '/mtm/mom_modelelemlistimage/get',
        add: '/mtm/mom_modelelemlistimage/adds',
        edit: '/mtm/mom_modelelemlistimage/updates',
        delete: '/mtm/mom_modelelemlistimage/deletes',
        ModelImageComboStoreByQuery: '/mtm/comboQuery/ModelImageComboStoreByQuery',
        ModelElemBaseComboStoreByQuery: '/mtm/comboQuery/ModelElemBaseComboStoreByQuery',
        GroupComboStore: '/mtm/combo/GroupComboStore',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
        // departmentCombStore: '/mtm/combo/departmentCombStore',
        // ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      GroupComboStore: [],
      ModelImageComboStoreByQuery: [],
      ModelElemBaseComboStoreByQuery: [],
      ModelElemListComboStoreByQuery: []
      // departmentCombStore:[]
      // ModelElemListComboStoreByQuery
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    ModelElemListComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    ModelImageComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
    },
    selectchange (row) {
      var itme = this.ModelImageComboStoreByQuery.find(item => item.value === row)
      console.log(itme)
      if (itme) {
        this.selectRow.imageUrl = itme.text
        // this.imageurl = itme.text
      } else {
        this.selectRow.imageUrl = null
        // this.imageurl = null
      }
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.selectRow.imageUrl = row.imageUrl
        // this.imageurl = row.imageUrl
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row) {
      if (this.$utils.has(row, 'id')) {
        row.id = null
      }
      if (this.$utils.has(row, 'code')) {
        row.code = null
      }
      if (this.$utils.has(row, 'codeName')) {
        row.codeName = null
      }
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.selectRow.imageUrl = row.imageUrl
        // this.imageurl = row.imageUrl
        this.showEdit = true
      })
    }
    // remoteMethod(query,gid) {
    // return new Promise(resolve => {
    // this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query, gid: gid }).then(result => {
    // this.ModelElemListComboStoreByQuery = result
    // return resolve(true)
    // })
    // })
    // })

    // editEventThen(row) {
    // this.remoteMethod(null, row.id).then(res => { this.showEdit = true })
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
