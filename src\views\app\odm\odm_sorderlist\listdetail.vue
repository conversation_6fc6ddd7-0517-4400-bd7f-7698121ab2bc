<template>
  <vxe-grid border resizable height="300" size="mini" :loading="loading" :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}" :pager-config="tablePage" :columns="tableColumn" :data="tableData" @page-change="handlePageChange"></vxe-grid>
</template>

<script>
export default {
  name: 'detailList',
  props: {
    sorder: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        sorderId: null,
        align: 'left',
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
        perfect: true
      },
      listForm: {
        sorderId: this.sorder.id,
        skipCount: 0,
        maxResultCount: 10
      },
      tableColumn: [
        // { type: 'seq', width: 60 },
        // { type: 'checkbox', width: 50 },
        { field: 'lineNum', title: '行号', width: 60 },
        { field: 'personName', title: '顾客', width: 100 },
        { field: 'height', title: '身高', width: 100 },
        { field: 'halfFitting', title: '半成品试衣', width: 100, formatter: ({ cellValue }) => { return cellValue ? '是' : '否' } },
        { field: 'itemText', title: '面料', width: 200 },
        { field: 'finalTextureText', title: '纹理', width: 150 }
        // { field: 'describe', title: 'Describe', showOverflow: true }
      ],
      tableData: [],
      api: {
        getdetail: '/mtm/odm_sorder/GetSorderDetail'
      }
    }
  },

  created () {
    this.findList()
  },
  methods: {
    async findList () {
      this.loading = true
      await this.$api.ActionRequest(this.api.getdetail, this.listForm).then(result => {
        this.tableData = result.items
        this.tablePage.total = result.totalCount
        this.loading = false
      })
    },
    searchEvent () {
      this.tablePage.currentPage = 1
      this.findList()
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.listForm.maxResultCount = pageSize
      this.listForm.skipCount = pageSize * (currentPage - 1)

      this.findList()
    }
  }
}
</script>

<style>
</style>
