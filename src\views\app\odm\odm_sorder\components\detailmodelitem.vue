<template>
  <div class="d2-panel-search-item" :class="hoverMode ? 'can-hover' : ''" flex>
    <div class="d2-panel-search-item__icon" flex-box="0">
      <div class="d2-panel-search-item__icon-box" flex="main:center cross:center">
        <d2-icon name="home" />
      </div>
    </div>
    <div class="d2-panel-search-item__info" flex-box="1" flex="dir:top">
      <div class="d2-panel-search-item__info-title" flex-box="1" flex="cross:center">
        <span>{{sorderDetailModelForm.modelText}}</span>
      </div>
      <div>
        <vxe-form element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)" :data="sorderDetailModelForm" class="sorderDetailModelForm" title-width="100" ref="sorderDetailModelForm">
          <vxe-form-item title="半成品试衣" field="halfFitting">
            <template #default>
              <vxe-switch v-model="sorderDetailModelForm.halfFitting" open-label="是" close-label="否" size="mini" :disabled="EditState"></vxe-switch>
            </template>
          </vxe-form-item>
          <vxe-form-item title="客户尺码" field="customerSize">
            <template v-slot>
              <vxe-input v-model="sorderDetailModelForm.customerSize" placeholder="客户尺码" clearable :disabled="EditState"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="客户订单号" field="customerNumber">
            <template v-slot>
              <vxe-input v-model="sorderDetailModelForm.customerNumber" placeholder="客户订单号" clearable :disabled="EditState"></vxe-input>
            </template>
          </vxe-form-item>

          <vxe-form-item title="长短款" field="designStyleID">
            <template v-slot>
              <vxe-select v-model="sorderDetailModelForm.designStyleID" placeholder="长款/短款" clearable :disabled="EditState">
                <vxe-option value="2" label="长款"></vxe-option>
                <vxe-option value="1" label="短款"></vxe-option>
              </vxe-select>
            </template>
          </vxe-form-item>
          <!-- <vxe-form-item field="frontBowOrBackUp" title="前弓体/后仰体">
            <template>
              <vxe-select v-model="sorderDetailModelForm.frontBowOrBackUp" placeholder="前弓体/后仰体" @change="frontBowOrBackUpChange" clearable :disabled="EditState">
                <vxe-option value="1" label="前弓体"></vxe-option>
                <vxe-option value="2" label="后仰体"></vxe-option>
              </vxe-select>
            </template>
          </vxe-form-item> -->
          <vxe-form-item title="数量" field="qty">
            <vxe-input v-model="sorderDetailModelForm.qty" type="number" placeholder="请输入数量" clearable :disabled="EditState"></vxe-input>
          </vxe-form-item>
          <vxe-form-item title="备注" field="remark" span="12">
            <template #default>
              <vxe-textarea v-model="sorderDetailModelForm.remark" placeholder="备注" min="5" :disabled="EditState"></vxe-textarea>
            </template>
          </vxe-form-item>
        </vxe-form>
      </div>
    </div>
    <div class="d2-panel-search-item__icon" flex-box="0" style="width:100px;">
      <p>
        <template v-if="!sorderDetailModelForm.isCheckedElem">
          <el-popover placement="top" width="400" trigger="click">
            <div>{{sorderDetailModelForm.messageElem===""||sorderDetailModelForm.messageElem===null?"请先检验款式明细":sorderDetailModelForm.messageElem}}</div>
            <el-button slot="reference" type="warning" size="mini">款式明细<i class="el-icon-warning-outline el-icon--right"></i></el-button>
          </el-popover>
        </template>
        <template v-else>
          <el-button type="success" size="mini">款式明细</el-button>
        </template>
      </p>
      <p>
        <template v-if="!sorderDetailModelForm.isChecked">
          <el-popover placement="top" width="400" trigger="click">
            <div>{{sorderDetailModelForm.message===""||sorderDetailModelForm.message===null?"请先检验规格":sorderDetailModelForm.message}}</div>
            <el-button slot="reference" type="warning" size="mini">规格<i class="el-icon-warning-outline el-icon--right"></i></el-button>
          </el-popover>
        </template>
        <template v-else>
          <el-button :type="sorderDetailModelForm.isChecked?'success':'warning'" size="mini">规格<i v-if="!sorderDetailModelForm.isChecked" class="el-icon-warning-outline el-icon--right"></i></el-button>
        </template>
      </p>
    </div>
    <div class="d2-panel-search-item__icon" flex-box="0">
      <div class="d2-panel-search-item__icon-box" flex="main:center cross:center">
        <slot name="next" :data="sorderDetailModelForm" />

      </div>
    </div>
    <div class="d2-panel-search-item__icon" flex-box="0">
      <div class="d2-panel-search-item__icon-box" flex="main:center cross:center">
        <slot name="remove" :data="sorderDetailModelForm" />
      </div>
    </div>
  </div>
</template>

<script>
// import sorderEditState from '../sordereditstate'
import { cloneDeep } from 'lodash'
export default {
  // mixins: [sorderEditState],
  props: {
    sorderDetailModel: {
      type: Object,
      requerid: true
    },
    sorderDetailModels: {
      type: Array,
      requerid: true
    },
    hoverMode: {
      default: false
    },
    EditState: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      sorderDetailModelForm: {
        id: null, //
        qty: '1', //
        frontBowOrBackUp: null,
        isFrontBow: false, //
        isBackUp: false, //
        customerSize: null, //
        sorderDetailID: null, //
        modelID: '', //
        remark: null, //
        designStyleID: null, //
        modelText: null,
        sorderSizeTypeID: '3',
        sizeID: null,
        sizeID1: null,
        isChecked: false,
        isCheckedElem: false,
        message: null,
        messageElem: null,
        isRuleSize: '', // 算法支持,
        customerNumber: null,
        halfFitting: false
      }
    }
  },

  watch: {
    sorderDetailModel: {
      deep: true,
      immediate: true,
      handler: function (newVal, oldVal) {
        this.sorderDetailModelForm = Object.assign(this.sorderDetailModelForm, cloneDeep(newVal))
      }
    }
    // sorderDetailModels: {
    //   deep: true,
    //   immediate: true,
    //   handler: function (newVal, oldVal) {
    //     console.log(newVal)
    //   }
    // }

  },
  async created () {
    this.sorderDetailModelForm = Object.assign(this.sorderDetailModelForm, cloneDeep(this.sorderDetailModel))
    if (this.sorderDetailModelForm !== null) {
      if (this.sorderDetailModelForm.isFrontBow) {
        this.sorderDetailModelForm.frontBowOrBackUp = '1'
      }
      if (this.sorderDetailModelForm.isBackUp) {
        this.sorderDetailModelForm.frontBowOrBackUp = '2'
      }
    }
  },
  methods: {
    frontBowOrBackUpChange ({ value }) {
      if (value === '1') {
        this.sorderDetailModelForm.isFrontBow = true
        this.sorderDetailModelForm.isBackUp = false
      } else if (value === '2') {
        this.sorderDetailModelForm.isFrontBow = false
        this.sorderDetailModelForm.isBackUp = true
      } else {
        this.sorderDetailModelForm.isFrontBow = false
        this.sorderDetailModelForm.isBackUp = false
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.d2-panel-search-item {
  &.can-hover {
    @extend %unable-select;
    margin: 0px;
    &:hover {
      background-color: #f5f7fa;
      .d2-panel-search-item__icon {
        .d2-panel-search-item__icon-box {
          i {
            font-size: 24px;
            color: $color-primary;
          }
        }
      }
      .d2-panel-search-item__info {
        .d2-panel-search-item__info-title {
          color: $color-text-main;
        }
        .d2-panel-search-item__info-fullTitle {
          color: $color-text-normal;
        }
        .d2-panel-search-item__info-path {
          color: $color-text-normal;
        }
      }
    }
  }
  .d2-panel-search-item__icon {
    width: 64px;
    .d2-panel-search-item__icon-box {
      height: 64px;
      width: 64px;
      border-right: 1px solid $color-border-3;
      i {
        font-size: 20px;
        color: $color-text-sub;
      }
      svg {
        height: 20px;
        width: 20px;
      }
    }
  }
  .d2-panel-search-item__info {
    margin-left: 10px;
    .d2-panel-search-item__info-title {
      font-size: 16px;
      line-height: 16px;
      font-weight: bold;
      color: $color-text-normal;
    }
    .d2-panel-search-item__info-fullTitle {
      font-size: 10px;
      line-height: 14px;
      color: $color-text-placehoder;
    }
    .d2-panel-search-item__info-path {
      margin-bottom: 4px;
      font-size: 10px;
      line-height: 14px;
      color: $color-text-placehoder;
    }
  }
}
</style>
