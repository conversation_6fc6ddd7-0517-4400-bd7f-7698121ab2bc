<template>
  <el-card shadow="always">
    <el-row>
      <el-col :span="24">
        <vxe-toolbar>
          <template v-slot:buttons>
            <!-- <vxe-button icon="vxe-icon--question" status="perfect" v-if="menuAction.allowAdd"></vxe-button> -->
            <el-tooltip class="item" effect="dark" content="编辑" placement="top">
              <vxe-button icon="el-icon-edit-outline" status="success" v-if="menuAction.allowAdd" @click="showEdit = !showEdit" :disabled="EditState"></vxe-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="批量检验" placement="top">
              <!-- 批量检验 -->
              <vxe-button icon="el-icon-circle-check" status="warning" @click="SorderDetailSizeChecksEvent" v-if="menuAction.allowAdd" :disabled="EditState"></vxe-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="上传数据" placement="top">
              <!-- <vxe-button status="warning">批量检验</vxe-button> -->
              <vxe-button icon="el-icon-upload2" status="warning" @click="showUpLoad" :disabled="EditState"></vxe-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="导出模板(包含数据)" placement="top">
              <!-- 导出模板(包含数据) -->
              <vxe-button icon="el-icon-download" status="success" @click="exportClick" :disabled="EditState"></vxe-button>
            </el-tooltip>

          </template>
        </vxe-toolbar>
        <vxe-table id='BadFactoryMasterTable' ref='master_table' height="800" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{ storage: true }" :filter-config="{ showIcon: false }">
          <vxe-table-column field="lineNum" title="行号" width="60" align="center" :class-name="checkCellClassEvent">
            <template v-slot:header>
              <vxe-button type="text" icon="vxe-icon--refresh" @click="reLoad(null)"></vxe-button>
            </template>
          </vxe-table-column>

          <vxe-colgroup title="顾客">
            <vxe-table-column field="clientPersonText" :filters="[{ data: '' }]" :filter-method="filterClientPersonMethod">
              <template #header="{ column }">
                <vxe-input type="text" v-for="(option, index) in column.filters" :key="index" v-model="option.data" @change="filterEvent(option)" placeholder="顾客" clearable></vxe-input>
              </template>
            </vxe-table-column>
          </vxe-colgroup>

          <vxe-table-column field="height" title="身高" sortable width="80"></vxe-table-column>
          <vxe-table-column field="sorderSizeTypeText" title="算法" sortable width="100"></vxe-table-column>
          <vxe-table-column field="sizeIDText" title="尺码" sortable width="100"></vxe-table-column>
          <vxe-table-column field="qty" title="数量" sortable width="100"></vxe-table-column>
          <vxe-table-column field="message" title="消息" sortable width="100"></vxe-table-column>
        </vxe-table>
      </el-col>
      <el-col :span="24">
        <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevPage', 'NextPage', 'Total', 'Sizes']">
        </vxe-pager>
      </el-col>
    </el-row>
    <vxe-modal v-model="showEdit" :title="'新增&编辑'" width="80%" height="60%" resize destroy-on-close show-zoom :loading="submitLoading" :before-hide-method="closeEvent">
      <table-person-data-edit :activeModel="activeModel" :sorderStore='sorderStore' ref="sorderDetailModelRef" />
    </vxe-modal>
    <el-dialog title="导入表格" :visible.sync="showUpLoadShow" width="30%" :before-close="handleClose" :append-to-body="true" :destroy-on-close="true">
      <up-load-excel v-if="showUpLoadShow" :upLoadResult="upLoadResult" :SorderID="sorderStore.id" :SorderProModelID="activeModel.id" :ClientID="sorderStore.clientID" :UserId="info.userid" />
    </el-dialog>
  </el-card>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import TablePersonDataEdit from './tablepersondataedit.vue'
import UpLoadExcel from './uploadexcel.vue'
import sorderEditState from '../sordereditstate'
import { mapState } from 'vuex'
export default {
  name: 'TablePersonDataSelect',
  mixins: [masterTableMixins, sorderEditState],
  components: {
    TablePersonDataEdit,
    UpLoadExcel
  },
  props: {
    activeModel: {
      type: Object,
      required: true
    },
    sorderStore: {
      type: Object
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      searchForm: {
        sorderId: this.sorderStore.id,
        modelId: this.activeModel.modelId
      },
      showUpLoadShow: false,
      showEdit: false,
      formData: {
        // code: '',
        // codeName: '',
        // remark: '',
        // sort: 999,
        // factoryType: 0,
        // isActive: true,
        // deliveryTime: null,
        // mesNumberCreate: null,
        // maxOrderCount: null
        // isDefault: false
      },
      formRules: {
        // code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
        // factoryType: [{ required: true, message: '请选择类型' }]
      },
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/oDM_SorderDetail/getPro',
        delete: '/mtm/oDM_SorderDetail/deletePro',
        checkSizes: '/mtm/oDM_SorderDetail/CheckAllDetailSizes',
        sorderProExportAsync: '/fs/export/sorderpro/excel'
      }

    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      //   await this.$api.ActionRequest(this.api.FactoryTypeStore).then(result => {
      //     this.FactoryTypeStore = result
      //   })
    },
    checkCellClassEvent ({ row, column }) {
      var checkedCellClass = 'errorChecked'
      if (row.isChecked) {
        checkedCellClass = 'successChecked'
      }
      return checkedCellClass
    },
    tableCellClick ({ row }) {
      this.tableLoading = true

      this.$emit('selectDetail', { data: row }, val => {
        // this.selectRow = row
        console.log('处理点选顾客结果')
        console.log(`${val}`)
        if (val) {
          this.loadData().then(res => {
            const xTable = this.$refs.master_table
            xTable.updateData()
            xTable.refreshColumn()
            if (res) {
              this.selectRow = row
              xTable.setCurrentRow(row)
            }
          })
        }
        this.tableLoading = false
      })
      // res.then(r => {

      // })
    },
    filterEvent (option) {
      const xTable = this.$refs.master_table
      // 设置为选中状态
      // option.checked = true
      // 修改条件之后，需要手动调用 updateData 处理表格数据
      this.searchForm.text = option.data
      this.loadData()
      xTable.updateData()
    },
    reLoad (data) {
      console.log(data)
      this.loadData().then(result => {
        var newData
        if (data) {
          newData = this.tableData.GetFirstElement('id', data.id)
        } else {
          newData = this.tableData[0]
        }
        const xTable = this.$refs.master_table
        xTable.updateData()
        xTable.refreshColumn()
        if (newData) {
          console.log('发现新数据')
          this.selectRow = newData
          xTable.setCurrentRow(newData)
        } else {
          newData = this.tableData[0]
        }
        this.$emit('selectDetail', { data: newData }, val => { })
      })
    },
    showUpLoad () {
      this.showUpLoadShow = true
    },
    handleClose (done) {
      this.showUpLoadShow = false
      done()
    },
    upLoadResult ({ success, info, error }) {
      console.log(success, info, error)
      var message = '导入成功'
      var su = 'success'
      if (success) {
        su = 'success'
        if (info !== null && info !== '') {
          message = info
        }
        this.loadData()
        this.showUpLoadShow = false
      } else {
        su = 'error'
        message = error
      }
      this.$message({
        dangerouslyUseHTMLString: true,
        showClose: true,
        type: su,
        message: message,
        center: true,
        duration: 1000 * 5,
        customClass: 'importrResult'
      })
      this.showUpLoadShow = false
    },
    filterClientPersonMethod ({ option, row }) {
      if (option.data) {
        this.searchForm.text = option.data
        this.loadData()
      }
      return true
    },
    async closeEvent () {
      await this.$refs.sorderDetailModelRef.saveAllData().then(res => {
        this.showEdit = false
        this.loadData()
      }).catch(() => {
        this.showEdit = true
        return new Error()
      })
    },
    exportClick () {
      const loading = this.$loading({
        lock: true,
        text: '导出中请稍等',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      var api = this.mtmpai.replace('/api/', '')
      var url = api + this.api.sorderProExportAsync
      // console.log(url)
      this.$api.ActionExcelRequest(url, { sorderID: this.sorderStore.id, hasData: true }).then(res => {
        const url = window.URL.createObjectURL(res) // 创建一个新的 URL 对象
        // console.log(url)
        // 以下代码一句话解释，在页面上生成一个a标签并指定href为上面的url,然后模拟点击，以实现自动下载
        var a = document.createElement('a')
        document.body.appendChild(a)
        a.href = url
        a.download = `订单[${this.sorderStore.code}]-单品类-大批量.xlsx`
        a.click()
        window.URL.revokeObjectURL(url)
        loading.close()
      }).catch(() => {
        loading.close()
      })
    },
    async SorderDetailSizeChecksEvent () {
      // const xTable = this.$refs.master_table
      // console.log(this.tableData)
      const loading = this.$loading({
        lock: true,
        text: '根据顾客数量的多少,计算花费时间也不同,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      await this.$api.ActionRequest(this.api.checkSizes, this.tableData).then(res => {
        this.$message({ message: '批量检验完成', type: 'success' })
        this.loadData()
        loading.close()
      }).catch(() => {
        loading.close()
      })
    }

  }
}
</script>

<style lang="scss">
.errorChecked {
  background-color: #f56c6c;
}

.successChecked {
  background-color: #67c23a;
}
</style>
