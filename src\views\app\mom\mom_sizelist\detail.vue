<template>
  <d2-container class="detailmodel">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
    </template>
    <template>
      <d2-container>
        <split-pane :min-percent='15' :default-percent='20' split="vertical">
          <template slot="paneL">
            <vxe-form :data="form" :items="formItems" title-align="right" title-width="100"></vxe-form>
          </template>
          <template slot="paneR">
            <el-tabs type="border-card" v-model="activeName" style="height:99%">
              <el-tab-pane label="规格" name="size">
                <detail-size :form="form" />
              </el-tab-pane>
              <el-tab-pane label="规格组成" name="sizeelem">
                <detail-size-elem :form="form" />
              </el-tab-pane>
              <el-tab-pane label="基础规格单" name="sizelistbase">
                <detail-size-list-base :form="form" />
              </el-tab-pane>
            </el-tabs>
          </template>
        </split-pane>
      </d2-container>
    </template>

  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import detailSize from './detail_size'
import DetailSizeElem from './detail_sizeelem'
import DetailSizeListBase from './detail_sizelistbase'
export default {
  name: 'MomModelDetail',
  mixins: [detailTableMixins],
  components: {
    detailSize,
    DetailSizeElem,
    DetailSizeListBase
  },
  data () {
    return {
      activeName: 'size',
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 24, itemRender: { name: '$input', props: { disabled: true } } },
        { field: 'codeName', title: '编码名称', span: 24, itemRender: { name: '$input', props: { disabled: true } } },
        { field: 'groupID', title: '类别', span: 24, itemRender: { name: '$select', options: [], props: { disabled: true } } },
        { field: 'sizeElemListID', title: '规格元素', span: 24, itemRender: { name: '$select', options: [], props: { disabled: true } } },
        { field: 'relax', title: '加放量', span: 24, itemRender: { name: '$input', props: { type: 'number', disabled: true } } },
        { field: 'isActive', title: '活动', span: 24, itemRender: { name: '$switch' }, props: { disabled: true } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$textarea' }, props: { disabled: true } }
        // { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_model/get',
        add: '/mtm/mom_model/adds',
        edit: '/mtm/mom_model/updates',
        delete: '/mtm/mom_model/deletes',
        GroupComboStore: '/mtm/combo/groupComboStore'
      },
      GroupComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.$utils.find(this.formItems, item => item.field === 'groupID').itemRender.options = this.GroupComboStore
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    }
  }
}
</script>

<style lang="scss" >
.detailmodel {
  .el-tabs__content {
    height: 92%;
  }
  .d2-container-full__body {
    overflow-x: hidden !important;
  }
}
</style>
