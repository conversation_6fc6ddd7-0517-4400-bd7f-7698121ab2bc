import JSZip from 'jszip'
import FileSaver from 'file-saver'
import XEUtils from 'xe-utils'
class Download {
  // 引用调用的方法，传入文件名称和bas64格式的文件
  downloadFile (fileName, content) {
    // 这里是获取到的图片base64编码,这里只是个例子哈，要自行编码图片替换这里才能测试看到效果
    const imgUrl = `data:image/png;base64,${content}`
    // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片
    if (window.navigator.msSaveOrOpenBlob) {
      const bstr = atob(imgUrl.split(',')[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      const blob = new Blob([u8arr])
      window.navigator.msSaveOrOpenBlob(blob, fileName + '.' + 'png')
    } else {
      console.log(fileName)
      // 这里就按照chrome等新版浏览器来处理
      const a = document.createElement('a')
      a.href = imgUrl
      a.setAttribute('download', fileName)
      a.click()
    }
  }

  // npm install jszip --S --D
  // npm install file-saver --S --D
  // 批量下载图片
  // 浏览器限制一次只能下载10张 所以采取压缩包的形式,突破10张图片的限制
  handleDownloadQrIMg (imageList) {
    // console.log(imageList.length)
    // 每次进来清空数组  否则会出现上一次图片不清空的问题
    // this.imgList = []
    // //把5张图片传进来
    // imageList.forEach((item) => {
    //     if (item.data != null) {
    //         this.imgList.push(item.data);
    //     }
    // });
    // let promises = [];
    const zip = new JSZip()
    // const cache = {};
    for (const item of imageList) {
      // var data = this.base64ToBlob(item.data);
      // 下载文件, 并存成ArrayBuffer对象(blob)
      //    zip.file(item.name, data, { binary: true }); // 逐个添加文件
      zip.file(item.name + '.png', item.data, { base64: true })
      // cache[item.name] = item.data;
    }
    zip.generateAsync({ type: 'blob' }).then(content => {
      // 生成二进制流
      var data = XEUtils.toDateString(new Date(), 'yyyyMMddHHmmssSSS')
      FileSaver.saveAs(content, data + '.zip') // 利用file-saver保存文件 自定义文件名
    }).catch(res => {
      console.log(res)
    })
  }

  base64ToBlob (code) {
    var bstr = atob(code)
    var n = bstr.length
    var u8arr = new Uint8Array(n)
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    // 下载的是excel格式的文件
    return new Blob([u8arr], { type: 'application/vnd.ms-excel' })
  }
}
export { Download }
