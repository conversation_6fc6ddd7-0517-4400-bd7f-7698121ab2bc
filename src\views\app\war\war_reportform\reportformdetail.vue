<template>
  <d2-container class="reportformdetail">
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="success" @click="insertEvent()" v-if="menuAction.allowAdd">新增 </vxe-button>
          <vxe-button status="success" @click="saveEvent" v-if="menuAction.allowEdit">保存</vxe-button>
          <vxe-button status="warning" @click="updateEvent" v-if="menuAction.allowEdit" icon="vxe-icon--refresh">重新生成</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <!-- <vxe-table keep-source id='WarReportformdetailDetailTable' ref='master_table' height="auto" :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}"  :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}" :edit-config="{trigger: 'click', selected: true, mode: 'cell',showStatus: true, icon: 'fa fa-pencil',showUpdateStatus:true,showInsertStatus:true,selected: true,}"> -->

    <vxe-table keep-source id="WarReportformdetailDetailTable" ref="WarReportformdetailDetailTable" height="auto" :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}" :edit-config="{trigger: 'click', selected: true, mode: 'cell',showStatus: true, icon: 'fa fa-pencil'}" :edit-rules="validRules">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="productBoxNumber" title="箱号" sortable width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
      <vxe-table-colgroup title="基本信息" align="center" header-class-name="baseinfo">
        <vxe-table-column field="sorderNumber" title="订单号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="nos" title="序号" width="100" :edit-render="{name: '$input', immediate: true,  props: {type: 'integer'}}"> </vxe-table-column>
        <vxe-table-column field="producthDescriptionsID" title="商品描述" width="100" :edit-render="{name: '$select', options: ReportFormConfigTypeProductGroupComboStore}"> </vxe-table-column>
        <vxe-table-column field="unitPrice" title="单价" width="100" :edit-render="{name: '$input', immediate: true, props: {type: 'float', digits: 2}}"> </vxe-table-column>
        <vxe-table-column field="amount" title="合计价格" width="100" :edit-render="{name: '$input', immediate: true,  props: {type: 'float', digits: 2}}"> </vxe-table-column>
        <vxe-table-column field="qty" title="数量" width="100" :edit-render="{name: '$input', immediate: true, props: {type: 'float', digits: 2},}"> </vxe-table-column>
        <vxe-table-column field="bookNo" title="备案手册/账册号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="archProdNo" title="备案料号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="装箱单" align="center" header-class-name="packinginfo">
        <vxe-table-column field="grossWeight" title="总重" width="100" :edit-render="{name: '$input', immediate: true,  props: {type: 'float', digits: 2}}"> </vxe-table-column>
        <vxe-table-column field="netWeight" title="净重" width="100" :edit-render="{name: '$input', immediate: true,  props: {type: 'float', digits: 2}}"> </vxe-table-column>
        <vxe-table-column field="meas" title="体积尺寸" width="100" :edit-render="{name: '$input', immediate: true,  props: {type: 'float', digits: 3}}"> </vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="海关" align="center" header-class-name="customsinfo">
        <!-- <vxe-table-column field="projectNo" title="项号" width="100" :edit-render="{name: '$select', options: ReportFormConfigTypeProjectNoComboStore}"> </vxe-table-column> -->
        <vxe-table-column field="projectNoText" title="项号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="producthNumber" title="商品编号" width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="itemComp" title="面料成分" width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="ciqCode" title="CIQ编码" width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="quantityDetailInfo" title="数量明细" width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="quantityUnit" title="数量及单位" width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="isHave1" title="是否有纽扣" width="100px" :edit-render="{name: '$select', options: boolList, props:{clearable:true}}"> </vxe-table-column>
        <vxe-table-column field="isHave2" title="是否有开襟" width="100px" :edit-render="{name: '$select', options: boolList,props:{clearable:true}}"> </vxe-table-column>
        <vxe-table-column field="isHave3" title="是否有衬里" width="100px" :edit-render="{name: '$select', options: boolList,props:{clearable:true}}"> </vxe-table-column>
        <vxe-table-column field="isHave4" title="是否有拉链" width="100px" :edit-render="{name: '$select', options: boolList,props:{clearable:true}}"> </vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="发票" align="center" header-class-name="invoiceinfo">

        <vxe-table-column field="customerName" title="顾客" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="shopCode" title="店铺编码" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="shopArea" title="区域" width="100" :edit-render="{name: '$select', options: ShopAreaTypeComboStore}"> </vxe-table-column>
        <vxe-table-column field="quantityDetails" title="数量明细" width="100" :edit-render="{name: 'input', immediate: true,  attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="hsCode" title="HsCode" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="deliveryNote" title="交货单号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="deliveryDate" title="交货日期" width="100" :formatter="val=>formatDate(val,'yyyy-MM-dd')" :edit-render="{name: '$input', immediate: true,  props: {type: 'date'}}"> </vxe-table-column>
        <vxe-table-column field="groupText" title="品名/描述" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="jacketNo" title="上衣订单号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="trouserNo" title="裤子订单号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="waistcoatNo" title="马甲订单号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="ticketNo" title="小票号" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="fabricCode" title="面料编码" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
        <vxe-table-column field="fabricCost" title="材料费" width="100" :edit-render="{name: '$input', immediate: true,  props: {type: 'float', digits: 2,clearable:true},events: { blur: ($event) => this.fabricCostBlurEvent($event)}}"> </vxe-table-column>
        <vxe-table-column field="cmt" title="加工费" width="100" :edit-render="{name: '$input', immediate: true,  props: {type: 'float', digits: 2,clearable:true},events: {blur: cmtBlurEvent}}"> </vxe-table-column>
        <vxe-table-column field="extPrice" title="额外费用" width="100" :edit-render="{name: '$input', immediate: true,  props: {type: 'float', digits: 2,clearable:true},events: {blur: extPriceBlurEvent}}"> </vxe-table-column>
      </vxe-table-colgroup>

      <!-- <vxe-table-column field="productEnglishDescriptions" title="商品英文描述" width="100"> </vxe-table-column> -->

      <vxe-table-column field="remark" title="备注" width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
      <vxe-table-colgroup title="其他" align="center">
        <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column> -->
        <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"> </vxe-table-column>
        <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
        <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
        </vxe-table-column>
        <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
        <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
        </vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-column title="操作" width="50" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button> -->
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>

    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'WarReportformDetail',
  mixins: [detailTableMixins],

  data () {
    return {
      tableRef: 'WarReportformdetailDetailTable',
      formData: {
        sorderID: null,
        sorderNumber: null,
        customerName: null,
        shopCode: null,
        fabricCost: null,
        cMT: null,
        quantityDetails: null,
        deliveryNote: null,
        deliveryDate: null,
        groupText: null,
        ticketNo: null,
        qty: null,
        grossWeight: null,
        netWeight: null,
        meas: null,
        nos: null,
        productEnglishDescriptions: null,
        projectNo: null,
        producthDescriptions: null,
        producthDescriptionsID: null,
        fabricCode: null,
        unitPrice: null,
        amount: null,
        reportFormID: this.form.id,
        remark: '',
        isActive: true,
        archProdNo: null,
        bookNo: null,
        shopArea: null
      },
      validRules: {

      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        update: '/mtm/war_reportform/CreateReportFormDetail',
        get: '/mtm/war_reportformdetail/get',
        add: '/mtm/war_reportformdetail/adds',
        edit: '/mtm/war_reportformdetail/updates',
        delete: '/mtm/war_reportformdetail/deletes',
        ShopAreaTypeComboStore: '/mtm/combo/ShopAreaTypeComboStore',
        ReportFormConfigTypeProductGroupComboStore: '/mtm/combo/ReportFormConfigTypeProductGroupComboStore',
        ReportFormConfigTypeProjectNoComboStore: '/mtm/combo/ReportFormConfigTypeProjectNoComboStore'
      },
      ShopAreaTypeComboStore: [],
      ReportFormConfigTypeProductGroupComboStore: [],
      ReportFormConfigTypeProjectNoComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ReportFormConfigTypeProductGroupComboStore).then(result => {
        this.ReportFormConfigTypeProductGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ReportFormConfigTypeProjectNoComboStore).then(result => {
        this.ReportFormConfigTypeProjectNoComboStore = result
      })
      await this.$api.ActionRequest(this.api.ShopAreaTypeComboStore).then(result => {
        this.ShopAreaTypeComboStore = result
      })
    },
    async fullValidEvent () {
      var xtable = this.$refs[this.tableRef]
      const errMap = await xtable.fullValidate().catch(errMap => errMap)
      if (errMap) {
        const msgList = []
        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            const { rowIndex, column, rules } = params
            rules.forEach(rule => {
              msgList.push(`第 ${rowIndex} 行 ${column.title} 校验错误：${rule.message}`)
            })
          })
        })
        this.$XModal.message({
          status: 'error',
          message: () => {
            return [
              <div class="red" style="max-height: 400px;overflow: auto;">
                {
                  msgList.map(msg => <div>{msg}</div>)
                }
              </div>
            ]
          }
        })
        return false
      } else {
        // this.$XModal.message({ status: 'success', message: '校验成功！' })
        return true
      }
    },
    async insertEvent (row) {
      const record = {
        nos: 0,
        sorderNumber: null,
        remark: '',
        isActive: true,
        reportFormID: this.form.id
      }
      var xtable = this.$refs[this.tableRef]
      const { row: newRow } = await xtable.insertAt(record, row)
      newRow.nos = 0
      newRow.sorderNumber = null
      await xtable.setActiveCell(newRow, 'sorderNumber')
    },
    async saveEvent () {
      var b = this.fullValidEvent()
      if (b) {
        var xtable = this.$refs[this.tableRef]
        var insertData = xtable.getInsertRecords()
        var updateData = xtable.getUpdateRecords()
        if (insertData.length > 0) {
          await this.$api.ActionRequest(this.api.add, insertData).then(res => {
            this.$message({ message: '操作成功', type: 'success' })
            this.loadData({ id: this.form.id })
          })
        }
        if (updateData.length > 0) {
          await this.$api.ActionRequest(this.api.edit, updateData).then(res => {
            this.$message({ message: '操作成功', type: 'success' })
            this.loadData({ id: this.form.id })
          })
        }
      }
    },
    async updateEvent () {
      const type = await this.$XModal.confirm('原有数据不会保存,您确定要重新生成数据吗？')
      if (type === 'confirm') {
        await this.$api.ActionRequest(this.api.update, this.form).then(result => {
          this.loadData({ id: this.form.id })
          this.$message({ message: '操作成功', type: 'success' })
        })
      } else {

      }
    },
    fabricCostBlurEvent ({ row, column, $event }) {
      if (row.fabricCost === null || row.fabricCost === '') {
        row.fabricCost = 0
      }
      var count = parseFloat(row.fabricCost) + parseFloat(row.cmt)
      row.unitPrice = count
      row.amount = count
      this.$notify({ title: '注意', message: `单价和总价发生变化:${row.amount}`, type: 'warning' })
    },
    cmtBlurEvent ({ row, column, $event }) {
      if (row.cmt === null || row.cmt === '') {
        row.cmt = 0
      }
      var count = parseFloat(row.fabricCost) + parseFloat(row.cmt)
      row.unitPrice = count
      row.amount = count
      this.$notify({ title: '注意', message: `单价和总价发生变化:${row.amount}`, type: 'warning' })
    },
    extPriceBlurEvent () {
    }
  }
}
</script>

<style lang="scss" >
.reportformdetail {
  .baseinfo {
    background-color: #67c23a;
  }
  .packinginfo {
    background-color: #e6a23c;
  }
  .customsinfo {
    background-color: #f56c6c;
  }
  .invoiceinfo {
    color: #fff;
    background-color: #909399;
  }
}
</style>
