
<template>
  <d2-container class="modelelemshowpage">
    <template slot="header" v-if="info.userType===0">
      <vxe-button type="text" icon="fa fa-cog" size='medium' v-if="info.userType===0" @click="componentsname='productionstationset'"></vxe-button>
      <!-- <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button> -->
    </template>
    <audio :src="successsrc" controls="controls" ref="sucessaudio" style="display: none;"></audio>
    <audio :src="errorsrc" controls="controls" ref="erroraudio" style="display: none;"></audio>
    <component :is="componentsname" :userinfo='info' :form="form">
      <template slot="numinput">
        <el-input maxlength="10" v-model="number" :disabled="isDisable" show-word-limit ref="numinputref" class="numinput" placeholder="扫描条形码" @blur="inputblur" @input="inputchange" @keyup.enter.native="submit" type="number">
        </el-input>
      </template>
    </component>
  </d2-container>
</template>

<script>
import sucessaudio from '@/assets/voice/success.mp3'
import erroraudio from '@/assets/voice/error.mp3'
import notset from './components/notset'
import modelelemshow from './showpage/modelelemshow'
import qualityshow from './showpage/qualityshow'
import washinglable from './showpage/washinglable'
import productionstationset from './components/productionstationset'
import { mapState } from 'vuex'
export default {
  name: 'productiontechnology', // 生产工艺
  components: {
    notset,
    modelelemshow,
    qualityshow,
    washinglable, // washinglable
    productionstationset

  },
  computed: {
    ...mapState('mes/ptm/productiontechnology/index', [
      'productstationconfig'
    ]),
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  watch: {
    productstationconfig: {
      deep: true,
      handler (newval, oldval) {
        this.componentschange()
      }
    }
  },
  data () {
    return {
      successsrc: sucessaudio,
      errorsrc: erroraudio,
      componentsname: 'notset',
      number: '', // 201200020002
      isDisable: false,
      api: {
        get: '/mes/pTM_ProductionTechnology/getModelElem'
      },
      form: null
    }
  },
  mounted () {
    this.timer = setInterval(() => {
      var numinput = this.$refs.numinputref
      if (numinput) {
        numinput.focus()
      }
    }, 1000)
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  created () {
    this.componentschange()
  },
  methods: {
    componentschange () {
      if (this.productstationconfig.viewTypeStr === null) {
        this.componentsname = 'notset'
      } else {
        this.componentsname = this.productstationconfig.viewTypeStr
      }
    },
    // 回车
    async submit () {
      if (this.input === null) {
        return
      }
      await this.getData()
      this.number = null
    },
    inputchange (val) {
      if (val.length >= 20) {
        this.number = val.substr(0, 20)
      }
    },
    inputblur () {
      this.getinputfocus()
      console.log('失去焦点')
    },
    getinputfocus () {
      this.$nextTick(() => {
        var numinput = this.$refs.numinputref
        numinput.focus()
      })
    },
    async getData () {
      this.isDisable = true
      await this.$api.ActionRequest(this.api.get, { number: this.number, productionStationID: this.productstationconfig.id }).then(result => {
        this.voiceplay(true)
        this.form = result
        this.getinputfocus()
        this.isDisable = false
      }).catch(res => {
        this.voiceplay(false)
        this.isDisable = false
      })
    },
    voiceplay (b) {
      if (b) {
        this.$refs.sucessaudio.play()
      } else {
        this.$refs.erroraudio.play()
      }
    }
  }
}
</script>

<style lang='scss' scope>
html {
  // overflow: hidden;
}
.modelelemshowpage {
  // height: 100%;
  .numinput input::-webkit-outer-spin-button,
  .numinput input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  .sordernum {
  }
  .d2-container-full__body {
    height: 100%;
  }
  .d2-container-full {
    height: 100%;
  }
  // .d2-container-full {
  //   .d2-container-full__body {
  //     height: calc(100vh);
  //   }
  // }
  .numinput {
    font-size: 20px;
    font-weight: 700;
  }
  .numinput input[type="number"] {
    -moz-appearance: textfield;
  }
  .vxe-input {
    height: 61px !important;
    width: 280px !important;

    input: {
      padding: 4px 0px;
      font-size: 30px !important;
      height: 60px !important;
      line-height: 60px !important;
      -ms-line-height: 40px;
      width: 260px !important;
    }
    input[type="number"] {
      -moz-appearance: textfield;
    }
    .el-input {
      width: 300px;
    }
    .el-input__inner {
      font-size: 30px !important;
      font-weight: 500 !important;
      border: 3px solid #dcdfe6;
      width: 260px !important;
    }
    .vxe-input:not(.is--disabled).is--active .vxe-input--inner {
      border: 3px solid #409eff !important;
    }
    .el-input.is-active .el-input__inner,
    .el-input__inner:focus {
      border: 3px solid #409eff;
    }
  }
}
</style>
