<template>
  <div>
    <el-select v-model="modelElemID" :remote-method="remoteMethod" @change="modelElemChange" size="mini" remote filterable placeholder="款式明细">
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'modelElem',
  data () {
    return {
      options: [],
      modelElemID: null
    }
  },
  props: {
    api: {
      type: Object,
      requited: true
    },
    modelElem: {
      type: Object,
      requited: true
    },
    modelElemSet: {
      type: Function,
      requited: true
    }
  },
  watch: {
    'modelElem.modelElemID': {
      handler: async function (newval, oldval) {
        if (newval === null) {
          this.modelElemID = null
        } else {
          this.modelElemID = newval
          await this.get('', newval)
        }
      }
    }
  },
  async created () {
    await this.get('', this.modelElem.modelElemID)
    this.modelElemID = this.modelElem.modelElemID
  },
  methods: {
    async get (query, id = null) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query, gid: id }).then(result => {
        this.options = result
      })
    },
    async remoteMethod (query) {
      await this.get(query, null)
    },
    modelElemChange (val) {
      this.modelElemSet(this.modelElem, val)
    }

  }
}
</script>

<style>
</style>
