<template>
  <d2-container class="PrdProductionplanDetailWashinglabel">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-print" status="info" v-if="menuAction.allowAdd" @click="showPrintWashedLabelEvent">打印水洗唛</vxe-button>
          <vxe-button icon="fa fa-print" status="info" v-if="menuAction.allowAdd" @click="createWashingLabelHistoryEvent">生成洗唛信息</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent" class="changrow">
            <vxe-form-item field="dates">
              <template #default>
                <el-date-picker v-model="searchForm.dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="issueDates">
              <template #default>
                <el-date-picker v-model="searchForm.issueDates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="下单开始日期" end-placeholder="下单结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable style="width:130px">
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sorderType" :item-render="{}"><template #default>
                <el-select v-model="searchForm.sorderType" filterable placeholder="订单类型" size="mini" clearable style="width:110px">
                  <el-option v-for="item in SorderTypeStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientPersonID" :item-render="{}"><template #default>
                <el-select v-model.trim="searchForm.clientPersonID" filterable placeholder="顾客" size="mini" remote reserve-keyword :remote-method="remoteMethod5" clearable style="width:110px">
                  <el-option v-for="item in ClientPersonComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="productionPlanState">
              <template #default="{ data }">
                <el-select v-model="data.productionPlanState" filterable placeholder="订单状态" size="mini" clearable style="width:110px">
                  <el-option v-for="item in productionPlanStateComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isPrintWashingLabeled">
              <template #default="{ data }">
                <el-select v-model="data.isPrintWashingLabeled" size="mini" placeholder="已打印?" clearable style="width:85px">
                  <el-option v-for="item in isPrintWashingLabeleds" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" size="mini" clearable style="width:105px">
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <template>
      <vxe-table id='PrdProductionplandetailDetailTable' ref='master_table' height="auto" @cell-click='tableCellClick' :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}" :loading="tableLoading">
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <!-- <vxe-table-column type="radio" width="60"></vxe-table-column> -->
        <vxe-table-column field="productionPlanStateText" title="订单状态" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="isPrintWashingLabeled" :formatter='formatBool' title="标签打印" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="sorderTypeText" title="订单类型" width="100"> </vxe-table-column>
        <vxe-table-column field="sorderNum" title="订单号" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="clientName" title="客户名称" sortable width="100"> </vxe-table-column>
        <vxe-table-column field="serialNumber" title="流水号" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="customerNumber" title="客户订单号" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="itemName" title="面料号" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="groupName" title="类别" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="modelCode" title="版型编码" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="modelName" title="版型" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="sizeCode" title="规格" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="remark" title="备注" width="100" sortable> </vxe-table-column>
        <vxe-table-column field="createBy" title="创建人" sortable width="100"></vxe-table-column>
        <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
        <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
        <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
        <vxe-table-column title="操作" width="50" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
          <template v-slot="{ row }">
            <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button> -->
            <!-- <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
            </vxe-button> -->
            <vxe-button type="text" status="success" icon="fa fa-play-circle" :key="row.id" v-if="menuAction.allowEdit&&!row.isPrintWashingLabeled" @click="update(row)"></vxe-button>
          </template>
        </vxe-table-column>
      </vxe-table>
      <template slot="footer">
        <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
          <template v-slot:right>
            <mtm-footer-companyinfo v-if="footerCompanyInfo" />
          </template>
        </vxe-pager>
      </template>

      <vxe-modal v-model="showPrintWashedLabel" title='水洗唛' width="500" height="600" :position="{top:200}" resize destroy-on-close>
        <print-washed-label :selectRow="selectRow" @updateState="updateState" />
      </vxe-modal>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
// import PrintInfo from './components/printinfo.vue'
import PrintWashedLabel from './components/printwashedlabel'
export default {
  name: 'prd_printwashinglabel',
  mixins: [detailTableMixins],
  components: {
    PrintWashedLabel
  },
  data () {
    return {
      formData: {
        remark: '',
        isActive: true

      },
      searchForm: {
        productionPlanState: 8,
        isPrintWashingLabeled: false,
        dates: []
      },
      activeName: 'productPlanSchedule',
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      showPrintWashedLabel: false,
      api: {
        get: '/mes/prd_productionplandetail/get',
        add: '/mes/prd_productionplandetail/adds',
        edit: '/mes/prd_productionplandetail/updates',
        delete: '/mes/prd_productionplandetail/deletes',
        createWashingLabelHistory: '/mes/pRD_WashingLabelHistory/create',
        GroupComboStore: '/mtm/combo/groupComboStore',
        productionPlanStateComboStore: '/mes/combo/productionPlanStateComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        SorderTypeStore: '/mes/combo/SorderTypeStore',
        ClientPersonComboStoreByQuery: '/mtm/comboQuery/ClientPersonComboStoreByQuery'
      },
      isPrintWashingLabeleds: [
        { value: true, label: '是' },
        { value: false, label: '否' }
      ],
      GroupComboStore: [],
      SorderTypeStore: [],
      productionPlanStateComboStore: [],
      clientComboStoreByQuery: [],
      ClientPersonComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ productionPlanState: 8, isPlanning: true, isPrintWashingLabeled: false }).then(({ data }) => {

    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.productionPlanStateComboStore).then(result => {
        this.productionPlanStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SorderTypeStore).then(result => {
        this.SorderTypeStore = result
      })
      await this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    showPrintWashedLabelEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$message({ type: 'error', message: '一次只能打印一条数据' })
        return
      }
      if (checks.length === 0) {
        this.$message({ type: 'error', message: '请勾选数据' })
        return
      }
      this.selectRow = checks[0]
      var b = this.$utils.isEmpty(this.selectRow)
      if (b) {
        this.$message({ type: 'error', message: '请勾选数据' })
        return
      }
      this.showPrintWashedLabel = !this.showPrintWashedLabel
    },
    tableCellClick ({ row, rowIndex, column, columnIndex }) {
      if (column.title === '操作') { return }
      this.drawer = true
      this.selectRow = cloneDeep(row)
      this.$refs.master_table.setRadioRow(row)
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { text: query }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
    },
    async update (row) {
      row.isPrintWashingLabeled = true
      await this.$api.ActionRequest(this.api.edit, [row]).then(result => {
        this.loadData()
      })
    },
    async createWashingLabelHistoryEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length === 0) {
        this.$message({ type: 'error', message: '请勾选数据' })
        return
      }
      await this.$api.ActionRequest(this.api.createWashingLabelHistory, checks).then(result => {
        this.$XModal.confirm('生成水洗唛数据完成是否跳转?').then(type => {
          if (type === 'confirm') {
            this.$router.push({
              name: 'prd_washinglabelhistory',
              params: {
                refresh: true
              }
            })
          }
        })
      })
    },
    async updateState () {
      this.showPrintWashedLabel = false
      var row = cloneDeep(this.selectRow)
      await this.update(row)
    }
  }
}
</script>

<style lang="scss" >
.PrdProductionplanDetailWashinglabel {
  .el-tabs__content {
    height: calc(100vh - 212px);
  }
  .vxe-toolbar.size--mini.is--perfect {
    height: auto;
  }
  .is--perfect {
    // .vxe-toolbar.size--mini {
    //   height: auto;
    // }
    .vxe-tools--wrapper {
      width: 97%;
    }
  }

  .changrow {
  }
}
</style>
