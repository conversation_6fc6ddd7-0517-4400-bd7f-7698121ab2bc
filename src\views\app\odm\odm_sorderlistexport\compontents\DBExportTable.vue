<template>
  <d2-container>
    <vxe-table border resizable height="auto" show-overflow :data="tableData" :loading="tableLoading" ref="xTable1" :edit-config="{trigger: 'click', mode: 'cell'}">
      <!-- <vxe-table-column type="seq" title="No." width="60"></vxe-table-column> -->
      <vxe-table-column field="receiptNumber" title="Receipt number"></vxe-table-column>
      <vxe-table-column field="orderNumberJacket1" title="Order Number/Jacket 1"></vxe-table-column>
      <vxe-table-column field="orderNumberJacket2" title="Order Number/Jacket 2"></vxe-table-column>
      <vxe-table-column field="orderNumberTrousers1" title="Order Number/Trousers 1"></vxe-table-column>
      <vxe-table-column field="orderNumberTrousers2" title="Order Number/Trousers 2"></vxe-table-column>
      <vxe-table-column field="orderNumberTrousers3" title="Order Number/Trousers 3"></vxe-table-column>
      <vxe-table-column field="orderNumberWaistcoat1" title="Order Number/Waistcoat 1"></vxe-table-column>
      <vxe-table-column field="orderNumberWaistcoat2" title="Order Number/Waistcoat 2"></vxe-table-column>
      <vxe-table-column field="jyyOrderRef" title="JYY order ref." cell-type="string"></vxe-table-column>
      <vxe-table-column field="waybill" title="WAYBILL" cell-type="string"></vxe-table-column>
      <vxe-table-column field="category" title="Category" cell-type="string"></vxe-table-column>
      <vxe-table-column field="issueDate" title="Issue date" cell-type="string"></vxe-table-column>
      <vxe-table-column field="etd" title="ETD" cell-type="string"></vxe-table-column>
      <vxe-table-column field="shipon" title="Shipon" cell-type="string"></vxe-table-column>
      <vxe-table-column field="revisedETD" title="Revised ETD" cell-type="string"></vxe-table-column>
      <vxe-table-column field="exFactory" title="Ex-Factory" cell-type="string"></vxe-table-column>
      <vxe-table-column field="expressShipment" title="Express Shipment" cell-type="string"></vxe-table-column>
      <vxe-table-column field="destination" title="Destination" cell-type="string"></vxe-table-column>
      <vxe-table-column field="deliveryAddress" title="Delivery Address" cell-type="string"></vxe-table-column>
      <vxe-table-column field="shipToStore" title="Ship to Store" cell-type="string"></vxe-table-column>
      <vxe-table-column field="shipToLocationCode" title="Ship to Location Code" cell-type="string"></vxe-table-column>
      <vxe-table-column field="city" title="City" cell-type="string"></vxe-table-column>
      <vxe-table-column field="zipCode" title="Zip Code" cell-type="string"></vxe-table-column>
      <vxe-table-column field="jyyManufactureNumber" title="JYY Manufacture Number" cell-type="string"></vxe-table-column>
      <vxe-table-column field="jyySerialNumber" title="JYY Serial Number" cell-type="string"></vxe-table-column>
      <vxe-table-column field="fabricCode" title="Fabric Code" cell-type="string"></vxe-table-column>
      <vxe-table-column field="qtyDetail" title="QTY Detail" cell-type="string"></vxe-table-column>
      <vxe-table-column field="qty" title="QTY" cell-type="string"></vxe-table-column>
      <vxe-table-column field="unitPrice" title="UNIT Price" cell-type="string"></vxe-table-column>
      <vxe-table-column field="cmtPrice" title="CMT Price" cell-type="string"></vxe-table-column>
      <vxe-table-column field="fabricPrice" title="Fabric Price" cell-type="string"></vxe-table-column>
      <vxe-table-column field="remark" title="Remark" cell-type="string"></vxe-table-column>
      <vxe-table-column field="delayReason" title="Delay Reason" cell-type="string"></vxe-table-column>
      <vxe-table-column field="types" title="Types" cell-type="string"></vxe-table-column>
      <vxe-table-column field="patternJacket" title="Pattern/Jacket" cell-type="string"></vxe-table-column>
      <vxe-table-column field="patternTrousers" title="Pattern/Trousers" cell-type="string"></vxe-table-column>
      <vxe-table-column field="patternWaistcoat" title="Pattern/Waistcoat" cell-type="string"></vxe-table-column>
      <vxe-table-column field="patternCoat" title="Pattern/Coat" cell-type="string"></vxe-table-column>
      <vxe-table-column field="fabricCMTM" title="Fabric CMTM" cell-type="string"></vxe-table-column>
      <vxe-table-column field="receiptCMTM" title="Receipt CMTM" cell-type="string"></vxe-table-column>
      <vxe-table-column field="rate" title="Rate" cell-type="string"></vxe-table-column>
      <vxe-table-column field="usage" title="Usage" cell-type="string"></vxe-table-column>
      <vxe-table-column field="invoiceDate" title="InvoiceDate" cell-type="string"></vxe-table-column>
      <vxe-table-column field="invoiceNo" title="InvoiceNo" cell-type="string"></vxe-table-column>
      <!-- <vxe-table-column field="InvoiceDate" title="InvoiceDate" ></vxe-table-column> -->

    </vxe-table>
    <template slot="footer">
      <vxe-button status="warning" @click="exportDataEvent()">导出数据</vxe-button>
    </template>
  </d2-container>
</template>

<script>
export default {
  name: 'DBExportTable',
  props: {
    selectTable: {
      require: true,
      type: Array
    }
  },
  data () {
    return {
      tableLoading: false,
      api: {
        getdb: '/mtm/war_productwarehouse/GetDb'
      },
      tableData: []
    }
  },
  created () {
    var sorderIds = this.selectTable.map(item => { return item.id })
    this.getData(sorderIds)
  },
  methods: {
    async getData (sorderIds) {
      this.tableLoading = true
      await this.$api.ActionRequest(this.api.getdb, { sorderIDs: sorderIds }).then(result => {
        this.tableData = result
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    exportDataEvent () {
      this.$refs.xTable1.exportData({
        filename: '大宝出库单-' + this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss'),
        sheetName: 'Sheet1',
        isColgroup: false,
        isFooter: false,
        type: 'xlsx'
      })
    }
  }
}
</script>

<style>
</style>
