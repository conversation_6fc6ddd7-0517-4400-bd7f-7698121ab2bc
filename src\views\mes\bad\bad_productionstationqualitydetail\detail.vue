<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <!-- <vxe-form-item field="productionStationID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.productionStationID" filterable size="mini" remote reserve-keyword placeholder="工位" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in productionStationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item> -->
            <vxe-form-item field="qualityBaseID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.qualityBaseID" filterable size="mini" remote reserve-keyword placeholder="质检部位" :remote-method="remoteMethod2" clearable>
                  <el-option v-for="item in qualityBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="qualityID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.qualityID" filterable size="mini" remote reserve-keyword placeholder="质检分类" :remote-method="remoteMethod3" clearable>
                  <el-option v-for="item in qualityComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="qualityDetailID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.qualityDetailID" filterable size="mini" remote reserve-keyword placeholder="质检明细" :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in qualityDetailComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadProductionstationqualitydetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column title="工位" sortable width="100">
        <template v-slot="{ row }">
          {{row.productionStationCode}}: {{row.productionStationCodeName}}
        </template>
      </vxe-table-column>
      <vxe-table-column title="质检部位" sortable width="100">
        <template v-slot="{ row }">
          {{row.qualityBaseCode}}: {{row.qualityBaseCodeName}}
        </template>
      </vxe-table-column>
      <vxe-table-column title="质检分类" sortable width="100">
        <template v-slot="{ row }">
          {{row.qualityCode}}: {{row.qualityCodeName}}
        </template>
      </vxe-table-column>
      <vxe-table-column title="质检明细" sortable width="100">
        <template v-slot="{ row }">
          {{row.qualityDetailCode}}: {{row.qualityDetailCodeName}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close show-zoom :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="工位" field="productionStationID" span="12"><template #default>
            <el-select v-model="selectRow.productionStationID" filterable size="mini" remote reserve-keyword placeholder="请选择工位" :remote-method="remoteMethod1" disabled>
              <el-option v-for="item in productionStationComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="质检明细" field="qualityDetailID" span="12"><template #default>
            <el-select v-model="selectRow.qualityDetailID" filterable size="mini" remote reserve-keyword placeholder="请选择质检明细" :remote-method="remoteMethod4">
              <el-option v-for="item in qualityDetailComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'bad_productionstationqualitydetails',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        text: null,
        productionStationID: null,
        qualityDetailID: null,
        qualityBaseID: null,
        qualityID: null
      },
      formData: {
        productionStationID: null,
        qualityDetailID: null,
        remark: '',
        isActive: true
      },
      formRules: {
        productionStationID: [{ required: true, message: '请选择工位' }],
        qualityDetailID: [{ required: true, message: '请选择质检明细' }]
      },
      api: {
        get: '/mes/bad_productionstationqualitydetail/get',
        add: '/mes/bad_productionstationqualitydetail/adds',
        edit: '/mes/bad_productionstationqualitydetail/updates',
        delete: '/mes/bad_productionstationqualitydetail/deletes',
        productionStationComboStoreByQuery: '/mes/comboQuery/productionStationComboStoreByQuery',
        qualityDetailComboStoreByQuery: '/mes/comboQuery/qualityDetailComboStoreByQuery',
        qualityBaseComboStoreByQuery: '/mes/comboQuery/qualityBaseComboStoreByQuery',
        qualityComboStoreByQuery: '/mes/comboQuery/qualityComboStoreByQuery'
      },
      productionStationComboStoreByQuery: [],
      qualityDetailComboStoreByQuery: [],
      qualityBaseComboStoreByQuery: [],
      qualityComboStoreByQuery: []
    }
  },
  async created () {
    this.searchForm.productionStationID = this.form.id
    this.formData.productionStationID = this.form.id
    this.loadData({ productionStationID: this.form.id }).then(({ data }) => {
    })
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.productionStationComboStoreByQuery, { gid: this.searchForm.productionStationID }).then(result => {
        this.productionStationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityDetailComboStoreByQuery).then(result => {
        this.qualityDetailComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityBaseComboStoreByQuery).then(result => {
        this.qualityBaseComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityComboStoreByQuery).then(result => {
        this.qualityComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.productionStationComboStoreByQuery, { text: query }).then(result => {
        this.productionStationComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.qualityBaseComboStoreByQuery, { text: query }).then(result => {
        this.qualityBaseComboStoreByQuery = result
      })
    },
    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.qualityComboStoreByQuery, { text: query }).then(result => {
        this.qualityComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.qualityDetailComboStoreByQuery, { text: query }).then(result => {
        this.qualityDetailComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      this.$api.ActionRequest(this.api.productionStationComboStoreByQuery, { gid: row.productionStationID }).then(result => {
        this.productionStationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityDetailComboStoreByQuery, { gid: row.qualityDetailID }).then(result => {
        this.qualityDetailComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row) {
      this.$api.ActionRequest(this.api.productionStationComboStoreByQuery, { gid: row.productionStationID }).then(result => {
        this.productionStationComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityDetailComboStoreByQuery, { gid: row.qualityDetailID }).then(result => {
        this.qualityDetailComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.selectRow.id = null
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
