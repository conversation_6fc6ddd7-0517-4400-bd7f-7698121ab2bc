<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <span>基础信息</span>&nbsp;&nbsp;
      <span style="font-size: 16px;font-weight: 800;">{{activeModel.modelText}}</span>&nbsp;&nbsp;&nbsp;
      <span style="font-size: 16px;font-weight: 800; color:red;">[{{sorderStore.clientPersonText}}]</span>
      <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
    </div>
    <div class="text item">
      <vxe-form :data="activeModel" title-width="100" @submit="submitEvent">
        <vxe-form-item title="半成品试衣" field="halfFitting" :item-render="{}"> <template #default>
            <vxe-switch v-model="activeModel.halfFitting" open-label="是" close-label="否" size="mini" :disabled="EditState"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户尺码" field="customerSize" :item-render="{}"> <template v-slot>
            <vxe-input v-model="activeModel.customerSize" placeholder="客户尺码" clearable :disabled="EditState"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户订单号" field="customerNumber" :item-render="{}"> <template v-slot>
            <vxe-input v-model="activeModel.customerNumber" placeholder="客户订单号" clearable :disabled="EditState"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="长短款" field="designStyleID" :item-render="{}"> <template v-slot>
            <vxe-select v-model="activeModel.designStyleID" placeholder="长款/短款" clearable :disabled="EditState">
              <vxe-option value="2" label="长款"></vxe-option>
              <vxe-option value="1" label="短款"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <!-- <vxe-form-item field="frontBowOrBackUp" title="前弓体/后仰体" :item-render="{}">          <template>
            <vxe-select v-model="activeModel.frontBowOrBackUp" placeholder="前弓体/后仰体" clearable disabled>
              <vxe-option value="1" label="前弓体"></vxe-option>
              <vxe-option value="2" label="后仰体"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item> -->
        <vxe-form-item title="数量" field="qty" :item-render="{}"> <vxe-input v-model="activeModel.qty" type="number" placeholder="请输入数量" clearable :disabled="EditState"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="12" :item-render="{}"> <template #default>
            <vxe-textarea v-model="activeModel.remark" placeholder="备注" min="5 " :disabled="EditState"></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="success" :disabled="EditState">保存</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </div>
  </el-card>
</template>

<script>
import sorderEditState from '../sordereditstate'
import { mapState } from 'vuex'
export default {
  name: 'DetailModel',
  mixins: [sorderEditState],
  data () {
    return {
      api: {
        edit: '/mtm/oDM_SorderDetailModel/ModifyDetailModel'
      }
    }
  },
  props: {
    activeModel: {
      type: Object,
      required: true
    },
    sorderStore: {
      type: Object
    },
    psersonheight: {
      type: String
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  methods: {
    async submitEvent () {
      return await this.$api.ActionRequest(this.api.edit, [this.activeModel]).then(async res => {
        this.$notify({
          title: '成功',
          message: '保存成功',
          type: 'success'
        })
        return true
      })
    }
  }
}
</script>

<style>
</style>
