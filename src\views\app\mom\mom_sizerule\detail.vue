<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowEdit">保存
          </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
        </template>
      </vxe-toolbar>
      <!-- <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
  <template #default>
<vxe-button type="submit" status="success">查询</vxe-button>
              <vxe-button type="reset">重置</vxe-button>
  </template>
</vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar> -->
    </template>

    <vxe-table id='MomSizerule_detail_table' ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="b" title="B" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="minOperator" title="最小值" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="minValue" title="最小值" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="maxOperator" title="最大值" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="maxValue" title="最大值" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="value" title="值" sortable width="100px"> </vxe-table-column>
      <!-- <vxe-table-column field="modifyBy" title="修改人" sortable > </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable ></vxe-table-column>
      <vxe-table-column title="操作" width="100"  :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'MomSizeruleDetail',
  mixins: [detailTableMixins],

  data () {
    return {
      formData: {
        b: '',
        minOperator: '',
        minValue: '',
        maxOperator: '',
        maxValue: '',
        value: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'b', title: 'B', span: 12, itemRender: { name: '$input' } },
        { field: 'minOperator', title: '最小值', span: 12, itemRender: { name: '$input' } },
        { field: 'minValue', title: '最小值', span: 12, itemRender: { name: '$input' } },
        { field: 'maxOperator', title: '最大值', span: 12, itemRender: { name: '$input' } },
        { field: 'maxValue', title: '最大值', span: 12, itemRender: { name: '$input' } },
        { field: 'value', title: '值', span: 12, itemRender: { name: '$input' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch ' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_sizeruledetail/get',
        add: '/mtm/mom_sizeruledetail/adds',
        edit: '/mtm/mom_sizeruledetail/updates',
        delete: '/mtm/mom_sizeruledetail/deletes'

      }
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    async getCombStore () {

    }
  }
}
</script>

<style lang="scss" scoped>
</style>
