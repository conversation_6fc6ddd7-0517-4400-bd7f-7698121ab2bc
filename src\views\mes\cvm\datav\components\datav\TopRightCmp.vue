<template>
  <div class="top-right-cmp">
    <div class="chart-name">
      本月准时交付率
      <dv-decoration-3 style="width:200px;height:20px;" />
    </div>
    <dv-charts :option="option" />
  </div>
</template>

<script>
export default {
  name: 'TopRightCmp',
  data () {
    return {
      data: [],
      api: {
        get: '/mes/boardChart/GetWeekSorder'
      },
      option: {
        legend: {
          data: [
            {
              name: '交货数',
              color: '#00baff'
            },
            {
              name: '交货率',
              color: '#ff5ca9'
            }
          ],
          textStyle: {
            fill: '#fff'
          }
        },
        xAxis: {
          data: [1],
          axisLine: {
            style: {
              stroke: '#999'
            }
          },
          axisLabel: {
            style: {
              fill: '#999'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          data: 'value',
          splitLine: {
            show: false
          },
          axisLine: {
            style: {
              stroke: '#999'
            }
          },
          axisLabel: {
            style: {
              fill: '#999'
            }
          },
          axisTick: {
            show: false
          },
          min: 0,
          max: 8
        },
        series: [
          {
            name: '交货数',
            data: [1],
            type: 'bar',
            barStyle: {
              fill: 'rgba(0, 186, 255, 0.4)'
            }
          },
          {
            name: '交货率',
            data: [1],
            type: 'line',
            lineStyle: {
              stroke: '#ff5ca9'
            },
            linePoint: {
              radius: 4,
              style: {
                fill: '#ff5ca9',
                stroke: 'transparent'
              }
            }
          }

        ]
      }
    }
  },
  created () {
    this.get()
  },
  mounted () {
    const { get } = this
    get()
    setInterval(get, 1000 * 60 * 60)
  },
  methods: {
    async get () {
      await this.$api.ActionRequest(this.api.get).then(result => {
        this.data = result
        this.createData(result)
      })
    },
    createData (rows) {
      var xAxis = rows.map(item => { return item.text })
      var series1 = rows.map(item => { return item.count })
      var series2 = rows.map(item => { return item.orderDeliveryRate })
      this.option = {
        legend: {
          data: [
            {
              name: '交货数',
              color: '#00baff'
            },
            {
              name: '交货率',
              color: '#ff5ca9'
            }
          ],
          textStyle: {
            fill: '#fff'
          }
        },
        xAxis: {
          data: xAxis,
          axisLine: {
            style: {
              stroke: '#999'
            }
          },
          axisLabel: {
            style: {
              fill: '#999'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          data: 'value',
          splitLine: {
            show: false
          },
          axisLine: {
            style: {
              stroke: '#999'
            }
          },
          axisLabel: {
            style: {
              fill: '#999'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '交货数',
            data: series1,
            type: 'bar',
            barStyle: {
              fill: 'rgba(0, 186, 255, 0.4)'
            }
          },
          {
            name: '交货率',
            data: series2,
            type: 'line',
            lineStyle: {
              lineDash: [5, 5]
            },
            linePoint: {
              radius: 4,
              style: {
                fill: '#ff5ca9',
                stroke: 'transparent'
              }
            }
          }

        ]
      }
      // this.option.xAxis.data = xAxis
      // this.option.series[0].data = series1
      // this.option.series[1].data = series2
    }
  }

}
</script>

<style lang="scss">
.top-right-cmp {
  position: relative;
  padding: 0 50px;
  box-sizing: border-box;
  height: 20%;
  .chart-name {
    position: absolute;
    right: 70px;
    text-align: right;
    font-size: 20px;
    top: 10px;
  }
}
</style>
