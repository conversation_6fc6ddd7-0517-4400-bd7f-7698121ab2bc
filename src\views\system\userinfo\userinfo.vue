<template>
  <el-row>
    <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="8">
      <el-card class="box-card">
        <vxe-form ref="user" :data="user" size="mini" :title-width="85">
          <el-divider content-position="left"></el-divider>
          <vxe-form-item title="账号" field="username" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.username" :disabled="true" size="mini"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item :span="12"></vxe-form-item>
          <vxe-form-item title="姓名" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.name" size="mini"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item :span="12"></vxe-form-item>
          <vxe-form-item title="密码" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.password" show-password size="mini"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item :span="12"></vxe-form-item>
          <vxe-form-item title="生日" :span="12" :item-render="{}"> <template #default>
              <el-date-picker v-model="user.birthday" type="date" placeholder="选择日期" size="mini"></el-date-picker>
            </template>
          </vxe-form-item>
          <vxe-form-item title="性别" :span="12" :item-render="{}"> <template #default>
              <el-switch v-model="user.gender" active-color="#13ce66" inactive-color="#ff4949" active-text="男" inactive-text="女" size="mini"></el-switch>
            </template>
          </vxe-form-item>
          <vxe-form-item title="邮箱" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.email" size="mini"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="手机号" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.tel" size="mini"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="座机" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.mobile" size="mini"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="传真" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.fax" size="mini"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item :span="24"></vxe-form-item>
          <vxe-form-item title="部门" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.department" :disabled="true" size="mini"></el-input>
            </template>
          </vxe-form-item>

          <vxe-form-item title="最后登录时间" :span="12" :item-render="{}"> <template #default>
              <el-date-picker v-model="user.lastLoggingDate" type="datetime" :disabled="true" placeholder="选择日期" size="mini"></el-date-picker>
            </template>
          </vxe-form-item>
          <vxe-form-item title="职位" :span="12" :item-render="{}"> <template #default>
              <el-input v-model="user.position" :disabled="true" size="mini"></el-input>
            </template>
          </vxe-form-item>

          <vxe-form-item :span="24" :item-render="{}"> <template #default>
              <el-button type="primary" @click="onSubmit" size="mini">保存修改</el-button>
            </template>
          </vxe-form-item>

        </vxe-form>
      </el-card>
    </el-col>
    <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="8">

    </el-col>
  </el-row>
</template>
<script>
// import { mapState, mapActions } from 'vuex'
import { mapState } from 'vuex'
import { Loading } from 'element-ui'
export default {
  name: 'userinfo',
  computed: {
    ...mapState('d2admin/user', ['info'])
  },
  data () {
    return {
      user: {
        username: '',
        password: null,
        name: '',
        department: '',
        departmentID: '',
        gender: 1,
        birthday: '',
        email: '',
        mobile: '',
        tel: '',
        fax: '',
        clientID: '',
        lastLoggingDate: '',
        userType: 0,
        position: '',
        id: ''
      }
    }
  },
  methods: {
    onSubmit () {
      const loading = Loading.service({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading'
      })
      this.$api.SYS_USER_UPDATES([this.user]).then(data => {
        loading.close()
        this.$message({
          message: '保存成功！',
          type: 'success'
        })
      })
    }
  },
  created () {
    this.$api.SYS_USER_GET_BYID({ Id: this.info.userid }).then(data => {
      this.user = data
    })
  }
}
</script>

<style lang="scss">
.el-col {
  border-radius: 4px;
}
.bg-purple-dark {
  background: #99a9bf;
}
.bg-purple {
  background: #d3dce6;
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 4px;
  min-height: 36px;
}
</style>
