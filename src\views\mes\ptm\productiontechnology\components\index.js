
import { mapState } from 'vuex'
export default {
  name: 'defaultset',
  props: {
    productstationconfigData: {
      type: Object
    },
    ip: {
      type: String
    }
  },
  data () {
    return {
      size: 'medium' // medium, small, mini
    }
  },
  computed: {
    ...mapState('mes/ptm/productiontechnology/index', [
      'productstationconfig'
    ]),
    ...mapState('d2admin/user', [
      'info'
    ])
  }
}
