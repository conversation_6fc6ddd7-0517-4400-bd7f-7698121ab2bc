<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="configurationType" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.configurationType" filterable placeholder="Type" size="mini" reserve-keyword clearable>
                  <el-option v-for="item in SuitSupplyOptionConfigurationTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="optionId" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.optionId" filterable placeholder="Options" size="mini" remote reserve-keyword :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in SuitSupply_OptionStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default="{ data }">
                <vxe-input v-model.trim="data.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='OdmSuitsupplyoptionvalueMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="option" title="option" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="configurationTypeText" title="ConfigurationTypeText" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="code" title="code" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="name" title="Name" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="description" title="Description" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="layerImageName" title="LayerImageName" sortable width="100"> </vxe-table-column>
      <!-- <vxe-table-column field="code" title="编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sequenceNo" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="default" title="default" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="option" field="optionId" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.optionId" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="options" clearable :remote-method="remoteMethod2">
              <el-option v-for="item in SuitSupply_OptionStoreByQuery2" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="configurationType" field="configurationType" span="12" :item-render="{ name: '$select', options: SuitSupplyOptionConfigurationTypeComboStore,props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="code" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="name" field="name" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="layerImageName" field="layerImageName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sequenceNo" span="12" :item-render="{name: '$input', props: { type: 'float',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="description" field="description" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import { cloneDeep } from 'lodash'
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'odm_suitsupplyoptionvalue',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
      },
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/odm_suitsupplyoptionvalue/get',
        add: '/mtm/odm_suitsupplyoptionvalue/adds',
        edit: '/mtm/odm_suitsupplyoptionvalue/updates',
        delete: '/mtm/odm_suitsupplyoptionvalue/deletes',
        SuitSupplyOptionConfigurationTypeComboStore: '/mtm/combo/SuitSupplyOptionConfigurationTypeComboStore',
        SuitSupply_OptionStoreByQuery: '/mtm/comboQuery/SuitSupply_OptionStoreByQuery'
      },
      SuitSupplyOptionConfigurationTypeComboStore: [],
      SuitSupply_OptionStoreByQuery1: [],
      SuitSupply_OptionStoreByQuery2: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SuitSupplyOptionConfigurationTypeComboStore).then(result => {
        this.SuitSupplyOptionConfigurationTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.SuitSupply_OptionStoreByQuery).then(result => {
        this.SuitSupply_OptionStoreByQuery1 = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.SuitSupply_OptionStoreByQuery, { text: query }).then(result => {
        this.SuitSupply_OptionStoreByQuery1 = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.SuitSupply_OptionStoreByQuery, { text: query }).then(result => {
        this.SuitSupply_OptionStoreByQuery2 = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.SuitSupply_OptionStoreByQuery, { gid: row.optionId }).then(result => {
        this.SuitSupply_OptionStoreByQuery2 = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
