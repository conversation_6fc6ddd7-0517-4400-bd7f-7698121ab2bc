<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"><template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template></vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadworksecationMasterTable' ref='master_table' height="auto" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <!-- <vxe-table-column field="productionSeriesCode" title="产品线系列编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="productionSeriesCodeName" title="产品线系列名称" sortable width="100"> </vxe-table-column> -->
      <vxe-table-column field="code" title="编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column>
      <vxe-table-column title="品类" sortable width="100">
        <template v-slot="{ row }">
          {{getGroupName(row.groupIDs)}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="factoryTypeText" title="工厂属性" sortable width="100"></vxe-table-column>
      <vxe-table-column field="workSecationGrpupText" title="工艺属性" sortable width="100"></vxe-table-column>
      <vxe-table-column field="workSecationPlanGroupText" title="排产属性" sortable width="100"></vxe-table-column>
      <vxe-table-column field="dailyCapacity" title="最大产能" sortable width="100"></vxe-table-column>
      <vxe-table-column field="workSecationBaseCode" title="基础工段编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="workSecationBaseName" title="基础工段名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="manHour" title="生产周期/天" sortable width="80"></vxe-table-column>
      <vxe-table-column field="teamDayCount" title="小组合计工期" sortable width="100">
        <template v-slot="{ row }">
          <span v-if="row.teamDayCount>row.manHour" style="color:red">{{row.teamDayCount}}</span>
          <span v-else>{{row.teamDayCount}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="employeeCount" title="员工数" sortable width="100"></vxe-table-column>
      <vxe-table-column field="manHourWarning" title="生产周期预警%" sortable width="100"></vxe-table-column>
      <vxe-table-column field="tieUp" title="占压数" sortable width="100"></vxe-table-column>
      <vxe-table-column field="tieUpWarning" title="占压预警%" sortable width="100"></vxe-table-column>
      <vxe-table-column field="takeUp" title="当日排产/件" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :row-class-name="rowClassName" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <!-- <vxe-form-item title="基础工段" field="itemClassID" span="12">
          <template #default>
            <el-select v-model="selectRow.worksecationBaseID" filterable placeholder="基础工段" size="mini">
              <el-option v-for="item in worksecationBaseComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item> -->
        <vxe-form-item title="绑定品类" span="24" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.groupIDs" multiple placeholder="请选择" size="mini" style="width:100%">
              <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="工厂属性" span="12" field="factoryType" :item-render="{}">
          <template #default>
            <el-select v-model="selectRow.factoryType" placeholder="请选择" size="mini" style="width:100%">
              <el-option v-for="item in FactoryTypeStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="基础工段" field="worksecationBaseID" span="12" :item-render="{name: '$select', options: worksecationBaseComboStore,props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="工艺属性" field="workSecationGrpup" span="12" :item-render="{name: '$select', options: WorkSecationGroupComboStore,props:{clearable:true}}"></vxe-form-item>
        <vxe-form-item title="排产属性" field="workSecationPlanGroup" span="12" :item-render="{name: '$select', options: WorkSecationPlanGroupComboStore,props:{clearable:true}}"> </vxe-form-item>
        <vxe-form-item title="生产周期/天" field="manHour" span="12" :item-render="{}">
          <template #default>
            <vxe-input v-model="selectRow.manHour" type="float" :min="selectRow.teamDayCount"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="最大产能" field="dailyCapacity" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="生产周期预警%" field="manHourWarning" span="12" :item-render="{name: '$input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="占压数" field="tieUp" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="员工数" field="employeeCount" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="占压预警%" field="tieUpWarning" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <!-- <vxe-form-item title="特殊工段？" field="isSpecial" span="12" :item-render="{name: '$switch'}"></vxe-form-item> -->
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='50%'>
      <detail-table :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import { cloneDeep } from 'lodash'
import DetailTable from './detail'
export default {
  name: 'bad_worksecation',
  mixins: [masterTableMixins],
  components: {
    DetailTable
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        sort: 999,
        isActive: true,
        isSpecial: false,
        worksecationBaseID: null,
        productionSeriesID: null,
        manHour: null,
        manHourWarning: 100,
        tieUp: null,
        tieUpWarning: 100,
        groupIDs: [],
        employeeCount: null,
        factoryType: null,
        dailyCapacity: null,
        workSecationGrpup: null,
        workSecationPlanGroup: null
      },
      formRules: {
        productionSeriesID: [{ required: true, message: '请选择产品系列线' }],
        // workSecationGrpup: [{ required: true, message: '请选择工艺属性' }],
        factoryType: [{ required: true, message: '请选择产品系列线' }],
        // manHour: [{ required: true, message: '请输入生产周期' }],
        tieUp: [{ required: true, message: '请输入占压数' }],
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 12 个字符' }]
      },
      api: {
        get: '/mes/bad_worksecation/get',
        add: '/mes/bad_worksecation/adds',
        edit: '/mes/bad_worksecation/updates',
        delete: '/mes/bad_worksecation/deletes',
        worksecationBaseComboStore: '/mes/combo/worksecationBaseComboStore',
        WorkSecationGroupComboStore: '/mes/combo/WorkSecationGroupComboStore',
        WorkSecationPlanGroupComboStore: '/mes/combo/WorkSecationPlanGroupComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore',
        FactoryTypeStore: '/mes/combo/FactoryTypeStore'
      },
      GroupComboStore: [],
      FactoryTypeStore: [],
      worksecationBaseComboStore: [],
      WorkSecationGroupComboStore: [],
      WorkSecationPlanGroupComboStore: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.worksecationBaseComboStore).then(result => {
        this.worksecationBaseComboStore = result
      })
      await this.$api.ActionRequest(this.api.FactoryTypeStore).then(result => {
        this.FactoryTypeStore = result
      })
      await this.$api.ActionRequest(this.api.WorkSecationGroupComboStore).then(result => {
        this.WorkSecationGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.WorkSecationPlanGroupComboStore).then(result => {
        this.WorkSecationPlanGroupComboStore = result
      })
    },
    getGroupName (ids) {
      var name = ''
      ids.forEach((item) => {
        var dto = this.GroupComboStore.GetFirstElement('value', item)
        if (dto) {
          name += dto.label + ','
        }
      })
      return name
    }
    // remoteMethod1(query) {
    //   this.$api.ActionRequest(this.api.productionSeriesComboStoreByQuery, { text: query }).then(result => {
    //     this.productionSeriesComboStoreByQuery = result
    //   })
    // },
    // 编辑
    // async editEvent(row) {
    //   await this.$api.ActionRequest(this.api.productionSeriesComboStoreByQuery, { gid: row.productionSeriesID }).then(result => {
    //     this.productionSeriesComboStoreByQuery = result
    //     this.selectRow = cloneDeep(row)
    //     this.showEdit = true
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
