<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='OdmSordersuitsupplyMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="系统编号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sorderSuitSupplyStateText" title="状态" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sales_orders_id" title="订单编号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="types" title="订单类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="订单数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qty_details" title="数量明细" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groups" title="订单类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="issue_date" title="下单日期" sortable width="100"></vxe-table-column>
      <vxe-table-column field="etd_date" title="交货日期" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipping_contact" title="收货联系人" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipping_phone" title="收货人电话" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipping_address" title="收货地址" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipping_country" title="收货国家" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipping_city" title="收货城市" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipping_postal_code" title="收货邮编" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipping_state" title="收货所在州" sortable width="100"></vxe-table-column>
      <vxe-table-column field="receipt_number" title="小票号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="order_number_jacket" title="上衣订单编号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="order_number_trousers" title="裤子订单编号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="order_number_waistcoat" title="马夹订单编号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="fabric_code" title="面料货号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_jacket" title="上衣尺码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_jacket_len" title="上衣落差" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_jacket_pattern" title="上衣版型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_trousers" title="裤子尺码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_trousers_len" title="裤子落差" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_trousers_pattern" title="裤子版型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_waistcoat" title="马夹尺码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_waistcoat_len" title="马夹落差" sortable width="100"></vxe-table-column>
      <vxe-table-column field="base_size_waistcoat_pattern" title="马夹版型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="fee_fabric" title="材料金额" sortable width="100"></vxe-table-column>
      <vxe-table-column field="fee_cmt" title="加工金额" sortable width="100"></vxe-table-column>
      <vxe-table-column field="fee_ext" title="特殊工艺费" sortable width="100"></vxe-table-column>
      <vxe-table-column field="fee_unit" title="总价" sortable width="100"></vxe-table-column>
      <vxe-table-column field="rate" title="订单汇率" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button status="warning" @click="ShowBom(row)">原始Bom清单</vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <!-- <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button> -->
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="120" @submit="submitEvent">
        <vxe-form-item title="SuitSupply订单编号" field="sales_orders_id" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="订单类型" field="types" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="订单数量" field="qty" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="数量明细" field="qty_details" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="订单类别" field="groups" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="下单日期" field="issue_date" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="交货日期" field="etd_date" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="收货联系人" field="shipping_contact" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="收货人电话" field="shipping_phone" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="收货国家" field="shipping_country" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="收货城市" field="shipping_city" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="收货邮编" field="shipping_postal_code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="收货所在州" field="shipping_state" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="收货地址" field="shipping_address" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="小票号" field="receipt_number" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="面料货号" field="fabric_code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="上衣订单编号" field="order_number_jacket" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="裤子订单编号" field="order_number_trousers" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="马夹订单编号" field="order_number_waistcoat" span="8" :item-render="{name: 'input'}"></vxe-form-item>

        <vxe-form-item title="上衣尺码" field="base_size_jacket" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="上衣落差" field="base_size_jacket_len" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="上衣版型" field="base_size_jacket_pattern" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="裤子尺码" field="base_size_trousers" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="裤子落差" field="base_size_trousers_len" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="裤子版型" field="base_size_trousers_pattern" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="马夹尺码" field="base_size_waistcoat" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="马夹落差" field="base_size_waistcoat_len" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="马夹版型" field="base_size_waistcoat_pattern" span="8" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="材料金额" field="fee_fabric" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="加工金额" field="fee_cmt" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="特殊工艺费" field="fee_ext" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="总价" field="fee_unit" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="订单汇率" field="rate" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="showSorderSuitSupllyBom" :title="'原始物料清单'" width="50%" height="500" resize destroy-on-close>
      <sorder-suit-supply-bom :form="selectRow" v-if="showSorderSuitSupllyBom" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import SorderSuitSupplyBom from './sordersuitsupplybom.vue'
// import { cloneDeep } from 'lodash'
export default {
  name: 'odm_sordersuitsupply',
  mixins: [masterTableMixins],
  components: {
    SorderSuitSupplyBom
  },
  data () {
    return {
      searchForm: {
      },
      showSorderSuitSupllyBom: false,
      formData: {
        sales_orders_id: null,
        types: null,
        qty: null,
        qty_details: null,
        groups: null,
        issue_date: null,
        etd_date: null,
        shipping_contact: null,
        shipping_phone: null,
        shipping_address: null,
        shipping_country: null,
        shipping_city: null,
        shipping_postal_code: null,
        shipping_state: null,
        receipt_number: null,
        order_number_jacket: null,
        order_number_trousers: null,
        order_number_waistcoat: null,
        fabric_code: null,
        base_size_jacket: null,
        base_size_jacket_len: null,
        base_size_jacket_pattern: null,
        base_size_trousers: null,
        base_size_trousers_len: null,
        base_size_trousers_pattern: null,
        base_size_waistcoat: null,
        base_size_waistcoat_len: null,
        base_size_waistcoat_pattern: null,
        fee_fabric: 0,
        fee_cmt: 0,
        fee_ext: 0,
        fee_unit: 0,
        rate: 0,
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/odm_sordersuitsupply/get',
        add: '/mtm/odm_sordersuitsupply/adds',
        edit: '/mtm/odm_sordersuitsupply/updates',
        delete: '/mtm/odm_sordersuitsupply/deletes'
      }
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    },
    ShowBom (row) {
      this.selectRow = row
      this.showSorderSuitSupllyBom = true
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
