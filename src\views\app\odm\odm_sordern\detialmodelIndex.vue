<template>
  <d2-container class="odm_sorderdetailmodel">
    <div style="width:100%;height:10px"></div>
    <detail-model :activeModel="activeModel" :sorderStore='sorderStore' ref="sorderDetailModelRef" />
    <el-card class="box-card" id='id_sorderdetailbodyandsize'>
      <div slot="header" class="clearfix">
        <h3>规格特体<span style="padding-left: 30px;color:red;    font-size: 20px;">订单号{{ sorderStore.code }}</span></h3>
      </div>
      <el-row :gutter="10">
        <el-col :xs="20" :sm="20" :md="17" :lg="17" :xl="17">
          <sorder-detail-size :SorderDetailModel="activeModel" :sorderStore="sorderStore" :height='sorderStore.height' :sorderSizeCheck="msorderSizeCheck" :sorderdetailbodySaveEven="sorderdetailbodysave" :getBodysData="getBodysData" ref="sorderDetailSizeRef">
          </sorder-detail-size>
        </el-col>
        <el-col :xs="20" :sm="20" :md="7" :lg="7" :xl="7">
          <sorder-detail-body :SorderDetailModel="activeModel" ref="sorderDetailBodyRef" :sorderStore="sorderStore">
          </sorder-detail-body>
        </el-col>

      </el-row>
      <el-row :gutter="20">
        <el-col :span="4" :offset="20">
          <el-button type="success" size="mini" @click="sizeAndBodySaveEvent">保存</el-button>
        </el-col>
      </el-row>
    </el-card>
    <sorder-detail-elem :SorderDetailModel="activeModel" ref="sorderDetailElemRef" :sorderStore="sorderStore" :GetDetailSizeData="GetDetailSizeData" />

    <sorder-detail-image :SorderDetailModel="activeModel" :sorderStore="sorderStore" id='id_sorderdetailimage' />

    <div class="modelbtns">
      <el-row>
        <el-col :span="24">
          <el-button type="primary" size="mini" @click="prevMaster" v-preventReClick>返回上页</el-button>
        </el-col>
        <el-col :span="24">
          <el-button type="info" size="mini" @click="handleScroll('id_sorderdetailbodyandsize')">规格特体</el-button>
        </el-col>
        <el-col :span="24">
          <el-button type="success" size="mini" @click="handleScroll('id_sorderdetailelem')">款式明细</el-button>
        </el-col>
        <el-col :span="24">
          <el-button type="warning" size="mini" @click="handleScroll('id_sorderdetailelemimage')">款式图片</el-button>
        </el-col>
        <el-col :span="24">
          <el-button type="danger" size="mini" @click="handleScroll('id_sorderdetailimage')">顾客图片</el-button>
        </el-col>
        <el-col :span="24">
          <el-button type="success" size="mini" @click="saveAll()" v-preventReClick="1000">一键保存</el-button>
        </el-col>
      </el-row>
    </div>
  </d2-container>
</template>

<script>
import SorderDetailElem from './soderdetailelem'
// import SorderDetailSizeAndBody from './sorderdetailsizeAndBody'
import SorderDetailSize from './sorderdetailsize'
import SorderDetailBody from './sorderdetailbody'
import SorderDetailImage from './sorderdetailimage'
import DetailModel from './components/detailmodel'
export default {
  name: 'DetailModelIndex',
  components: {
    SorderDetailElem,
    // SorderDetailSizeAndBody,
    SorderDetailSize,
    SorderDetailBody,
    SorderDetailImage,
    DetailModel
  },
  props: {
    activeModel: {
      type: Object,
      required: true
    },
    sorderStore: {
      type: Object
    },
    psersonheight: {
      type: String
    }
  },

  data () {
    return {
      carousel: {
        autoplay: false,
        loop: false,
        indicatorPosition: 'none',
        arrow: 'never'
      },
      active: 'detailsizeandbody'

    }
  },

  created () { },
  methods: {
    async itemchange (val) {
      var b = false
      if (val === 'detailsizeandbody') {
        b = await this.$refs.sorderDetailElemRef.sorderDetailElemSave()
      }
      if (val === 'detailelem') {
        b = await this.$refs.sorderDetailSizeRef.sorderDetailSizeSave()
        b = await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
      }
      if (val === 'detailimage') {
        b = await this.$refs.sorderDetailElemRef.sorderDetailElemSave()
        b = await this.$refs.sorderDetailSizeRef.sorderDetailSizeSave()
        b = await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
      }
      if (!b) {
        return
      }
      switch (val) {
        case 'detailelem':
          this.$refs.carousel.setActiveItem('detailelem')
          break
        case 'detailsizeandbody':
          this.$refs.carousel.setActiveItem('detailsizeandbody')
          break
        case 'detailimage':
          this.$refs.carousel.setActiveItem('detailimage')
          break
        default:

          break
      }
    },
    msorderSizeCheck (data) {
      this.$refs.sorderDetailElemRef.sorderSizeCheck(data)
    },
    // 点击导航菜单，页面滚动到指定位置
    handleScroll (index) {
      // this.navgatorIndex = index;
      this.$nextTick(() => {
        var selected = document.getElementById(index)
        if (selected !== null) {
          // smooth平滑过渡  // start上边框与视窗顶部平齐。默认值
          selected.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      })
    },
    async sorderdetailbodysave () {
      return await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
    },
    async sizeAndBodySaveEvent () {
      const loading = this.$loading({
        lock: true,
        text: '数据保存中请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      var b = false
      b = await this.$refs.sorderDetailSizeRef.sorderDetailSizeSave()
      b = await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
      if (b) {
        this.$XModal.message({ message: '保存成功！', status: 'success' })
      }
      loading.close()
    },
    getBodysData () {
      return this.$refs.sorderDetailBodyRef.BodyListData.filter(item => { return item.bodyListID !== null })
    },

    GetDetailSizeData () {
      return this.$refs.sorderDetailSizeRef.DetailSizeData
    },
    // 全部保存
    async saveAll () {
      var b = await this.saveAllEvent()
      if (b) {
        this.$XModal.message({ message: '保存成功！', status: 'success' })
      }
    },
    async saveAllEvent () {
      const loading = this.$loading({
        lock: true,
        text: '数据保存中请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      var b4 = await this.$refs.sorderDetailModelRef.submitEvent()
      if (b4) {
        var b1 = await this.$refs.sorderDetailElemRef.sorderDetailElemSave()
        var b2 = await this.$refs.sorderDetailSizeRef.sorderDetailSizeSave()
        var b3 = await this.$refs.sorderDetailBodyRef.sorderDetailBodySave()
        loading.close()
        if (b1 && b2 && b3 && b4) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    },
    // 返回上一页
    async prevMaster () {
      var b = await this.saveAllEvent()
      if (b) {
        this.$emit('prevMaster', { sorder: this.sorderStore })
      }
    }
  }
}
</script>

<style  lang="scss" >
.odm_sorderdetailmodel {
  .modelbtns {
    position: fixed;
    bottom: 20%;
    right: 5%;
    z-index: 99999;
    width: 80px;
  }
  .el-carousel__container {
    height: 100% !important;
  }
  .carouselmain {
    // height: 90% !important;
    height: calc(100vh - 120px) !important;
  }
  .carousel {
    height: 100% !important;
    overflow-y: auto !important;
  }
}
</style>
