<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table ref='master_table' :loading="tableLoading" id="SymUserMasterTable" class="sortable-column-demo" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable show-overflow width="100"> </vxe-table-column>
      <vxe-table-column field="username" title="账号" sortable show-overflow width="100"> </vxe-table-column>
      <vxe-table-column field="name" title="姓名" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="department" title="部门" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="gender" title="性别" :formatter="formatSex" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="birthday" title="生日" show-overflow :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="email" title="邮箱" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="tel" title="手机号" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="mobile" title="座机" show-overflow width="100"> </vxe-table-column>
      <vxe-table-column field="fax" title="传真" show-overflow width="100"></vxe-table-column>
      <vxe-table-column field="userType" title="用户类型" show-overflow :formatter="val=>formatSelect(val,userTypeList)" width="100"> </vxe-table-column>
      <vxe-table-column field="position" title="职位" show-overflow width="100"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" show-overflow width="150"></vxe-table-column>
      <vxe-table-column field="viewTypeID" title="仅查看自己数据" :formatter="val=>formatBool(val)" width="100" show-overflow></vxe-table-column>
      <vxe-table-column field="lastLoggingDate" title="最后登录时间" :formatter="val=>formatDate(val)" show-overflow width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" show-overflow :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row,['username','password'])" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="850" showZoom resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="账号" field="username" span="12" :item-render="{}"> <vxe-input v-model="selectRow.username" placeholder="请输入账号" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="密码" field="password" span="12" :item-render="{}"> <vxe-input v-model="selectRow.password" placeholder="请输入密码" type="password" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="姓名" field="name" span="12" :item-render="{}"> <vxe-input v-model="selectRow.name" placeholder="请输入编码" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="生日" field="birthday" span="12" :item-render="{}"> <vxe-input v-model="selectRow.birthday" type="date" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="部门" field="departmentID" span="12" :item-render="{}"> <template #default="{ data }">
            <vxe-select v-model="data.departmentID" placeholder="部门" clearable>
              <vxe-option v-for="num in departmentCombStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="性别" field="gender" span="12" :item-render="{}"> <template #default>
            <!-- <vxe-select v-model="selectRow.gender" placeholder="性别" clearable>
            <vxe-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
          </vxe-select> -->
            <vxe-switch v-model="selectRow.gender" open-label="男" close-label="女"></vxe-switch>
          </template>
        </vxe-form-item>

        <vxe-form-item title="邮箱" field="email" span="12" :item-render="{}"> <vxe-input v-model="selectRow.email" placeholder="请输入编码" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="手机号" field="tel" span="12" :item-render="{}"> <vxe-input v-model="selectRow.tel" placeholder="请输入编码" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="座机" field="mobile" span="12" :item-render="{}"> <vxe-input v-model="selectRow.mobile" placeholder="请输入编码" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="用户类型" field="userType" span="12" :item-render="{}"> <template #default="{ data }">
            <vxe-select v-model="data.userType" placeholder="用户类型" clearable>
              <vxe-option v-for="num in userTypeList" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="职位" field="position" span="12" :item-render="{}"> <vxe-input v-model="selectRow.position" placeholder="请输入编码" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="启用" field="isActive" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isActive" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="仅查看自己数据" field="viewTypeID" span="12" :item-render="{}"> <template #default="{ data }">
            <vxe-switch v-model="data.viewTypeID" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="绑定客户" field="clientID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.clientID" filterable remote reserve-keyword placeholder="上级" size="mini" :remote-method="ClientComboMethod" clearable>
              <el-option v-for="(item,index) in ClientComboStore" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{}"> <template #default>
            <vxe-textarea v-model="selectRow.remark" placeholder="备注" show-word-count></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item span="24" align='center' :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='35%'>
      <detail-table :footerCompanyInfo="footerCompanyInfo" :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import DetailTable from './detail'
import { cloneDeep } from 'lodash'
export default {
  name: 'sym_user',
  mixins: [masterTableMixins],
  components: {
    DetailTable
  },
  data () {
    return {
      formData: {
        id: null,
        username: '',
        name: '',
        password: '',
        department: '',
        gender: true,
        birthday: '',
        email: '',
        tel: '',
        mobile: '',
        date3: '',
        userType: 1,
        position: '',
        isActive: true,
        departmentID: '',
        clientID: null,
        viewTypeID: false
        // roleIds:[],
      },
      formRules: {
        username: [{ required: true, message: '请输入名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
        password: [{ required: true, message: '请输入密码' }]
      },
      formItems: [
        { title: '基础信息', span: 24, titleAlign: 'left', titleWidth: 200, titlePrefix: { icon: 'fa fa-address-card-o' } },
        { field: 'username', title: '账号', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入账号' } } },
        { field: 'password', title: '密码', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入密码', type: 'password' } } },
        { field: 'name', title: '姓名', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        { field: 'departmentID', title: '部门', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'gender', title: '性别', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'birthday', title: '生日', span: 12, itemRender: { name: '$input', props: { type: 'date', placeholder: '请选择日期' } } },
        { field: 'email', title: 'Email', span: 12, itemRender: { name: '$input' } },
        { field: 'tel', title: '手机号', span: 12, itemRender: { name: '$input' } },
        { field: 'mobile', title: '座机', span: 12, itemRender: { name: '$input' } },
        { field: 'userType', title: '用户类型', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'position', title: '职位', span: 12, itemRender: { name: '$input' } },
        { field: 'viewTypeID', title: '仅查看自己数据', span: 12, itemRender: { name: '$select', options: [{ label: '是', value: true }, { label: '否', value: false }] } },
        { field: 'isActive', title: '启用？', span: 12, itemRender: { name: '$select', options: [{ label: '是', value: true }, { label: '否', value: false }] } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],

      sexList: [
        { label: '女', value: false },
        { label: '男', value: true }
      ],
      userTypeList: [
        { label: '系统账号', value: 0 },
        { label: '内部账户', value: 1 },
        { label: '外部账户', value: 2 }
      ],

      api: {
        get: '/mtm/sym_user/get',
        add: '/mtm/sym_user/adds',
        edit: '/mtm/sym_user/updates',
        delete: '/mtm/sym_user/deletes',
        departmentCombStore: '/mtm/combo/departmentComboStore',
        ClientComboStore: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      departmentCombStore: [],
      ClientComboStore: []

    }
  },

  async created () {
    await this.getCombStore()
    this.$utils.find(this.formItems, item => item.field === 'departmentID').itemRender.options = this.departmentCombStore
    this.$utils.find(this.formItems, item => item.field === 'gender').itemRender.options = this.sexList
    this.$utils.find(this.formItems, item => item.field === 'userType').itemRender.options = this.userTypeList
    // this.formItems[4].itemRender.options = this.departmentCombStore
    // this.formItems[5].itemRender.options = this.sexList
    // this.formItems[10].itemRender.options = this.userTypeList
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
        this.departmentCombStore = result
      })
      await this.$api.ActionRequest(this.api.ClientComboStore, { classID: 1, gid: this.formData.clientID }).then(result => {
        this.ClientComboStore = result
      })
    },
    ClientComboMethod (query) {
      this.$api.ActionRequest(this.api.ClientComboStore, { text: query }).then(result => {
        this.ClientComboStore = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ClientComboStore, { gid: row.clientID }).then(result => {
        this.ClientComboStore = result
      })
      this.selectRow = cloneDeep(row)
      this.showEdit = true
    },
    // 复制
    async copyRowEvent (row, attributenames = [], code = false, codeName = false) {
      await this.$api.ActionRequest(this.api.ClientComboStore, { gid: row.clientID }).then(result => {
        this.ClientComboStore = result
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (!code && this.$utils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (!codeName && this.$utils.has(this.selectRow, 'codeName')) {
        this.selectRow.codeName = null
      }
      if (attributenames.length > 1) {
        attributenames.forEach(name => {
          this.selectRow[name] = null
        })
      }
      this.showEdit = true
    }
  }
}
</script>

<style>
</style>
