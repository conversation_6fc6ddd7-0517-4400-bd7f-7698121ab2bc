<template>
  <d2-container class="chartview">
    <split-pane :min-percent='10' :default-percent='18' split="horizontal">
      <template slot="paneL">
        <chart-3/>
      </template>
      <template slot="paneR">
        <split-pane :min-percent='10' :default-percent='75' split="horizontal">
          <template slot="paneL">
            <el-row :gutter="20" style="height:100%">
              <el-col :span="8" style="height:100%;border: 1px solid;">
                <!-- <div class="grid-content bg-purple"></div> -->
                <split-pane :min-percent='10' :default-percent='50' split="horizontal">
                  <template slot="paneL">
                    <chart-2 />
                  </template>
                  <template slot="paneR">
                    <chart1 />
                  </template>
                </split-pane>

              </el-col>
              <el-col :span="8" style="height:100%;border: 1px solid;">
              </el-col>
              <el-col :span="8" style="height:100%;border: 1px solid;">
                小组占压
              </el-col>
            </el-row>
          </template>
          <template slot="paneR">
            <table-view />
          </template>
        </split-pane>
      </template>
    </split-pane>
  </d2-container>
</template>

<script>
import TableView from './componments/tableview'
import Chart1 from './componments/chart1'
import Chart2 from './componments/chart2'
import Chart3 from './componments/chart3'

export default {
  components: {
    TableView,
    Chart1,
    Chart2,
    Chart3
  }
}
</script>

<style lang="scss" scope>
.chartview {
  width: 100%;
  height: 100%;
  background-color: #1e1e2a;
  color: #fff;
  overflow: hidden;
  .splitter-pane {
    background-color: #1e1e2a;
    color: #fff;
  }
  .splitter-pane-resizer {
    opacity: 0.3 !important;
  }
  .d2-container-full {
    .d2-container-full__body:first-of-type {
      height: calc(100vh);
    }
  }
}
</style>
