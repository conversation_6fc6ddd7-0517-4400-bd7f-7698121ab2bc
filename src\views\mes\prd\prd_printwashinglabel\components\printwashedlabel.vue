<template>
  <d2-container>
    <div>

    </div>

    <div style="width:200px;" id="printwashedlabel" class="printwashedlabel">
      <table>
        <tr>
          <td colspan="2">品名：{{formdata.groupName}}</td>
        </tr>
        <tr>
          <td colspan="2">订单号：{{formdata.customerNumber}}</td>
        </tr>
        <tr>
          <td colspan="2">姓名：{{formdata.clientPersonName}}</td>
        </tr>
        <tr>
          <td>规格：{{formdata.sizeCode}}</td>
          <td>号型：{{formdata.customerSize}}</td>
        </tr>
        <tr>
          <td colspan="2">版型:{{formdata.modelCode}}</td>
        </tr>
        <tr>
          <td colspan="2">面料成分：{{formdata.itemComp}}</td>
        </tr>
        <template v-for="(item,index) in formdata.elems">
          <tr :key="index">
            <td colspan="2">{{item.label}}：{{item.value}}</td>
          </tr>
        </template>
        <tr>
          <td colspan="2">
            <div>
              <template v-for="(img,index) in masterData.washingLabelImages">
                <el-image :style="images[img-1].style" :src="images[img-1].src" fit="fit" :key="index"></el-image>
              </template>
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <div style="text-align: center;">
              <img id="barcode" style="width:90%" />
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <div style="text-align: center;">
              <strong> {{formdata.serialNumber}}</strong>
            </div>
          </td>
        </tr>
      </table>
    </div>
    <el-alert title="此效果仅展示数据,最终效果由打印机决定。" type="warning">
    </el-alert>
    <vxe-form :data="masterData" title-align="left" title-width="50">
      <vxe-form-item title="数量" field="qty" span="24" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
      <vxe-form-item title="品名" field="groupName" span="24" :item-render="{name: 'input'}"></vxe-form-item>
      <vxe-form-item title="流水号" field="serialNumber" span="24" :item-render="{name: 'input'}"> </vxe-form-item>
      <vxe-form-item title="图标" field="washingLabelImages" align="left" span="24" :item-render="{}"> <template v-slot>
          <vxe-checkbox-group v-model="masterData.washingLabelImages">
            <vxe-checkbox v-for="(item,index) in images" :key="item.id" :label="item.id">
              <el-image :style="item.style" :src="item.src" fit="fit" :key="index"></el-image>
            </vxe-checkbox>
          </vxe-checkbox-group>
        </template>
      </vxe-form-item>
    </vxe-form>
    <template slot="footer">
      <vxe-button status="info" content="打印" @click="print"></vxe-button>
    </template>
  </d2-container>
</template>

<script>
// import html2canvas from './html2canvas.min.js'
import JsBarcode from 'jsbarcode'
import image1 from './images/1.png'
import image2 from './images/2.png'
import image3 from './images/3.png'
import image4 from './images/4.png'
import image5 from './images/5.png'
export default {
  name: 'printwashedlabel',
  data () {
    return {
      api: {
        get: '/mes/pRD_ProductionPlanDetail/GetPrintWashingLabelInfo',
        print: '/mes/pRD_ProductionPlanDetail/printWashingLabel'
      },
      num: '202103220',
      imgUrl: null,
      images: [
        { src: image1, style: 'width:25px', id: 1 },
        { src: image2, style: 'width:25px', id: 2 },
        { src: image3, style: 'width:25px', id: 3 },
        { src: image4, style: 'width:25px', id: 4 },
        { src: image5, style: 'width:25px', id: 5 }
      ],
      formdata: {
        clientName: null,
        clientPersonName: null,
        elems: [],
        groupName: null,
        modelName: null,
        serialNumber: null,
        sizeCode: null,
        sorderNum: null,
        customerSize: null,
        sizes: []
      },
      masterData: {
        qty: 1,
        washingLabelImages: [1, 2, 3, 4, 5],
        id: this.selectRow.id,
        groupName: null,
        serialNumber: null
      }
    }
  },
  props: {
    selectRow: {
      type: Object,
      default: null
    }
  },
  mounted () {

  },
  created () {
    // console.clear()
    this.get(this.selectRow.id)
    // 不要在create时调用jsbarcode库，此时相关DOM还未加载。
  },
  methods: {
    async print () {
      await this.$api.ActionRequest(this.api.print, this.masterData).then(result => {
        // if (this.selectRow.isPrintWashingLabeled) {
        //   this.$XModal.message({ message: '已向打印机发送打印信息', status: 'success' })
        // } else {
        //   this.updatedetailstate()
        // }
        this.updatedetailstate()
      })
    },
    updatedetailstate () {
      this.$emit('updateState')
      // this.$XModal.confirm('已向打印机发送打印信息,是否修改当前订单打印状态').then(type => {
      //   if (type === 'confirm') {
      //     this.$emit('updateState')
      //   } else {
      //     this.$XModal.message({ message: '取消操作' })
      //   }
      // })
    },
    async get (id) {
      this.masterData.groupName = null
      this.masterData.serialNumber = null
      await this.$api.ActionRequest(this.api.get, { id: id }).then(result => {
        console.log(result)
        this.formdata = result
        this.masterData.groupName = this.formdata.groupName
        this.masterData.serialNumber = this.formdata.serialNumber
        this.createBarCode()
      })
    },
    createBarCode () {
      this.$nextTick(() => {
        // 生成条形码
        JsBarcode(
          '#barcode',
          this.formdata.serialNumber,
          {
            displayValue: false, // 是否在条形码下方显示文字
            // format: "pharmacode",  //条形码的格式
            // lineColor: "#0aa",  //线条颜色
            //  width: 2, //线宽
            height: 60 // 条码高度
            // margin:15,
            // fontSize:20
            // displayValue: false //是否显示文字信息
          }
        )
      })
    }
  }
}
</script>

<style lang="scss">
.printwashedlabel {
  font-size: 12px;
  padding: 10px;
  color: black;
  font-weight: 100 !important;
  //   font-style:italic;
  table {
    margin: 5px;
    display: inline-block;
    font-size: 8x;
    -webkit-transform-origin-x: 0;
    font-weight: 500;
    tr td:first-child {
      // padding-left: 10px;
    }
  }
  .el-image {
    padding-left: 8px;
  }
}
</style>
