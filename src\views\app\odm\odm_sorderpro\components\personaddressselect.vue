<template>
  <vxe-modal v-model="personAddress.Show" title="收货地址" width="1000" resize destroy-on-close>
    <vxe-grid border resizable height="530" :seq-config="{startIndex: (personAddress.tablePage.currentPage - 1) * personAddress.tablePage.pageSize}" :pager-config="personAddress.tablePage" :columns="personAddress.tableColumn" :data="personAddress.tableData" @page-change="handlePageChange" @cell-dblclick="personAddressTableCellClick"></vxe-grid>
  </vxe-modal>
</template>

<script>
export default {
  name: 'personAddressSelect',
  props: {
    sorderStore: {
      type: Object
    }
  },
  data () {
    return {
      personAddress: {
        Show: false,
        tableData: [],
        tableColumn: [
          { type: 'seq', width: 60 },
          { field: 'contact', title: '联系人' },
          { field: 'tel', title: '电话' },
          { field: 'address', title: '地址' },
          { field: 'contactDesc', title: '联系人描述' },
          { field: 'fax', title: '传真' },
          { field: 'mobile', title: '座机' },
          { field: 'email', title: '邮件' },
          { field: 'state', title: '国家' },
          { field: 'province', title: '省' },
          { field: 'city', title: '市' },
          { field: 'county', title: '县' },
          { field: 'street', title: '街道' },
          { field: 'port', title: '港口' },
          { field: 'transport', title: '运送方式' },
          { field: 'remark', title: '备注' }
        ],
        tablePage: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          align: 'left',
          pageSizes: [10, 20, 50, 100, 200, 500],
          layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
          perfect: true,
          id: null,
          maxResultCount: 10,
          skipCount: 0
        }
      }
    }
  },
  watch: {
    sorderStore: {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== null) {
          // this.sorderForm = Object.assign(this.sorderForm, newVal)
          this.personAddress.tablePage.id = newVal.clientID
          // this.clientPsersonSelectRow.clientID = newVal.clientID
        }
      }
    }
  },
  created () {
    this.personAddress.tablePage.id = this.sorderStore.clientID
  },
  methods: {
    handlePageChange ({ currentPage, pageSize }) {
      this.personAddress.tablePage.currentPage = currentPage
      this.personAddress.tablePage.pageSize = pageSize
      this.personAddress.tablePage.maxResultCount = pageSize
      this.personAddress.tablePage.skipCount = (currentPage - 1) * pageSize
      this.personAddressGet()
    },
    personAddressTableCellClick ({ row }) {
      this.sorderForm.contact = row.contact
      this.sorderForm.tel = row.tel
      this.sorderForm.address = row.address
      this.personAddress.Show = false
    }
  }
}
</script>

<style>
</style>
