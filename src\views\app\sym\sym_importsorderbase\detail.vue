<template>
  <d2-container class="importsorderbasedetail">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="editEvent" v-if="menuAction.allowEdit">保存</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
    </template>
    <template>
      <d2-container>
        <el-tabs v-model="activeName" type="border-card" style="height:99%">
          <el-tab-pane label="绑定版型系列" name="modelgroup">
            <sorder-model-group :form="form" />
          </el-tab-pane>
          <el-tab-pane label="基础信息" name="sorderdetail">
            <sorder-detail :form="form" />
          </el-tab-pane>
          <el-tab-pane label="款式明细" name="sordermodelelem">
            <sorder-model-elem :form="form" />
          </el-tab-pane>
          <el-tab-pane label="规格" name="sordermodelsize">
            <sorder-model-size :form="form" />
          </el-tab-pane>
          <el-tab-pane label="特体" name="sordermodelbody">
            <sorder-model-body :form="form" />
          </el-tab-pane>
        </el-tabs>
      </d2-container>
    </template>
  </d2-container>
</template>
<script>
import SorderDetail from './sorderdetail.vue'
import SorderModelElem from './sordermodelelem.vue'
import SorderModelBody from './sordermodelbody.vue'
import SorderModelSize from './sordermodelsize.vue'
import SorderModelGroup from './sordermodelgroup.vue'
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'DetailView',
  mixins: [detailTableMixins],
  components: {
    SorderDetail,
    SorderModelGroup,
    SorderModelElem,
    SorderModelBody,
    SorderModelSize
  },
  data () {
    return {
      activeName: 'sorderdetail'

    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {

    },
    goback () {
      this.$emit('nextpage', { pagename: 'master', data: {}, masterSeach: this.masterSeach, keepalive: true })
    }

  }
}
</script>
<style lang="scss" >
.importsorderbasedetail {
  .el-tabs__content {
    height: 92%;
  }
  .d2-container-full__body {
    overflow-x: hidden !important;
  }
  //处理vxe table 头和数据  上下不一致问题
  .vxe-cell {
    margin: 1px !important;
    // background-color: red;
  }
}
</style>
