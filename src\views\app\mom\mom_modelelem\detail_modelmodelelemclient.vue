<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">保存</vxe-button>
          <vxe-button status="primary" content="取消所有绑定" size="mini" @click="stateSet(0)"></vxe-button>
          <vxe-button size="mini" content="全部默认" status="primary" @click="stateSet(1)">
            <template v-slot:dropdowns>
              <vxe-button content="取消默认" @click="stateSet(0)"></vxe-button>
            </template>
          </vxe-button>
          <vxe-button size="mini" content="全部隐藏" status="primary" @click="stateSet(2)">
            <template v-slot:dropdowns>
              <vxe-button content="取消隐藏" @click="stateSet(0)"></vxe-button>
            </template>
          </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.modelID" filterable remote reserve-keyword placeholder="版型" size="mini" :remote-method="modelRemoteMethod" clearable>
                  <el-option v-for="(item,index) in modelComboStore" :key="index" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable remote reserve-keyword placeholder="客户" size="mini" :remote-method="ClientComboMethod" clearable>
                  <el-option v-for="(item,index) in ClientComboStore" :key="index" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id="MomModelelemDetailTable" ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="groupText" title="分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelText" title="版型名称" sortable width="250"></vxe-table-column>
      <vxe-table-column field="clientText" title="客户" sortable width="250"></vxe-table-column>
      <!-- <vxe-table-column field="modelElemCadName" title="CAD款式明细名称" sortable width="120"></vxe-table-column> -->
      <vxe-table-column field="state" title="选择" sortable width="150">
        <template v-slot="{row}">
          <el-select v-model.trim="row.state" size="mini">
            <el-option v-for="(item,index) in ModelModelElemClientStateEnumsComboStore" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <!-- <vxe-checkbox v-model="row.State"></vxe-checkbox> -->
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>

  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'MomModelModelElemClient', // 款式明细关联的版型
  mixins: [detailTableMixins],

  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codename', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch ' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mOM_ModelModelElemClient/get',
        edit: '/mtm/mOM_ModelModelElemClient/updates',
        ModelModelElemClientStateEnumsComboStore: '/mtm/combo/modelModelElemClientStateEnumsComboStore',
        ClientComboStore: '/mtm/comboQuery/clientComboStoreByQuery',
        modelComboStore: '/mtm/comboQuery/modelComboStoreByQuery'
      },
      ModelModelElemClientStateEnumsComboStore: [],
      modelComboStore: [],
      ClientComboStore: []
    }
  },
  watch: {
    'form.id': {
      deep: true,
      async handler (newVal, oldVal) {
        // console.log(`newVal:${newVal},oldVal:${oldVal}`)
        if (newVal !== oldVal) {
          await this.loadData({ modelElemID: newVal })
        }
      }
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ modelElemID: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ClientComboStore).then(result => {
        this.ClientComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelModelElemClientStateEnumsComboStore).then(result => {
        this.ModelModelElemClientStateEnumsComboStore = result
      })
      await this.$api.ActionRequest(this.api.modelComboStore).then(result => {
        this.modelComboStore = result
      })
    },
    modelRemoteMethod (query) {
      this.$api.ActionRequest(this.api.modelComboStore, { text: query }).then(result => {
        this.modelComboStore = result
      })
    },
    ClientComboMethod (query) {
      this.$api.ActionRequest(this.api.ClientComboStore, { text: query }).then(result => {
        this.ClientComboStore = result
      })
    },
    async insertEvent () {
      //   var list = this.tableData.filter(item => { return item.state !== 0 })
      //   if(list.lenght===0){
      //       return
      //   }
      await this.$api.ActionRequest(this.api.edit, this.tableData).then(result => {
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.loadData({ modelElemID: this.form.id })
      })
    },
    stateSet (state) {
      this.tableData.forEach(item => {
        item.state = state
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
