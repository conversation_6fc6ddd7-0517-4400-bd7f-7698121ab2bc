<template>
  <d2-container type='ghost' style="height:350px">
    <vxe-toolbar perfect>
      <template v-slot:tools>
        <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">

          <vxe-form-item field="itemID" :item-render="{}"> <template #default="{ data }">
              <el-select v-model="data.itemID" filterable remote reserve-keyword placeholder="面料" size="mini" :remote-method="remoteMethod1" clearable>
                <el-option v-for="item in ItemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
          </vxe-form-item>

          <vxe-form-item :item-render="{}"> <template #default>
              <vxe-button type="submit" status="success">查询</vxe-button>
            </template>
          </vxe-form-item>
        </vxe-form>
      </template>
    </vxe-toolbar>

    <vxe-table ref='master_table' id='MomModelelemMasterMasterTable' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="220" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="radio" title="请选择"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="clientCode" title="客户编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelCode" title="版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelName" title="版型名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemOriginalItemNo" title="原始货号" sortable width="100px"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']">
      </vxe-pager>
    </template>

  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
// import { cloneDeep } from 'lodash'
export default {
  name: 'selectOrderModel',
  mixins: [masterTableMixins],
  props: {
    model: {
      type: Object,
      default: null
    },
    getModel: {
      type: Function,
      default: null
    }
  },
  data () {
    return {
      api: {
        get: '/mtm/oDM_SorderDetailModel/get',
        GroupComboStore: '/mtm/combo/groupComboStore',

        ItemComboStoreByQuery: '/mtm/comboQuery/itemComboStoreByQuery'
      },
      footerCompanyInfo: false,
      ItemComboStoreByQuery: [],
      searchForm: {
        iD: this.model.soderDetailModelSource
      }
    }
  },
  async created () {
    await this.getCombStore()
    this.remoteMethod1()
    this.loadData().then(() => {
      if (this.model.soderDetailModelSource !== null) {
        // eslint-disable-next-line
        // var finddto = this.$utils.find(this.tableData, item => item.sorderDetailModelID = this.model.soderDetailModelSource)
        // if (finddto !== null) {
        //   this.selectRow = finddto
        //   this.$refs.master_table.setRadioRow(finddto)
        //   this.parentset(finddto)
        // }
      } else {
        // this.selectRow = cloneDeep(this.tableData[0])
        // this.$refs.master_table.setRadioRow(this.tableData[0])
        // this.parentset(this.selectRow)
      }
    })
  },
  methods: {
    async getCombStore () {
      //   await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
      //     this.GroupComboStore = result
      //   })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ItemComboStoreByQuery, { text: query }).then(result => {
        this.ItemComboStoreByQuery = result
      })
    },
    tableCellClick ({ row }) {
      this.$refs.master_table.setRadioRow(row)
      this.selectRow = cloneDeep(row)
      this.parentset(this.selectRow)
    },
    parentset (data) {
      //   this.$emit('getmodel',data);
      this.getModel(data)
      //    this.$parent.$parent.getmodel(data)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
