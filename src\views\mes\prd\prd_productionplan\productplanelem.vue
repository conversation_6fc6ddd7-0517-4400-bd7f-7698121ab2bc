<template>
  <d2-container>
    <template slot="header">
    </template>

    <vxe-table id='PRD_ProductPlanElemTable' ref='master_table' height="auto" :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="modelElemTypeName" title="工艺类型" width="150"> </vxe-table-column>
      <vxe-table-column field="modelElemListCode" title="款式编码" width="150"> </vxe-table-column>
      <vxe-table-column field="modelElemListName" title="款式名称" width="150"> </vxe-table-column>
      <vxe-table-column field="modelElemCode" title="工艺编码" width="150"> </vxe-table-column>
      <vxe-table-column field="modelElemName" title="工艺名称" width="150"> </vxe-table-column>
      <vxe-table-column field="itemName" title="物料名称" width="150"> </vxe-table-column>
      <vxe-table-column field="input" title="内容" width="150"> </vxe-table-column>
      <vxe-table-column field="qty" title="数量" width="100"> </vxe-table-column>
      <!-- <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column> -->
      <!-- <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :row-class-name="rowClassName" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="子编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="子名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'prd_productplanelem',
  mixins: [detailTableMixins],
  props: {
    productPlanDetail: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      formData: {
        remark: '',
        sort: 999,
        isActive: true
        // worksecationID: this.form.id
      },
      formRules: {
        // productionProcessesID: [{ required: true, message: '绑定生产工序' }],
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mes/pRD_ProductionPlanDetailElem/get',
        add: '/mes/pRD_ProductionPlanDetailElem/adds',
        edit: '/mes/pRD_ProductionPlanDetailElem/updates',
        delete: '/mes/pRD_ProductionPlanDetailElem/deletes'
      }
    }
  },
  watch: {
    productPlanDetail: {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== null) {
          this.loadData({ productionPlanDetailID: newVal.id }).then(({ data }) => {
          })
        }
      }
    }
  },
  async created () {
    await this.getCombStore()
    // this.loadData({ worksecationID: this.form.id }).then(({ data }) => {
    // })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.productionProcessesComboStoreByQuery).then(result => {
      //   this.productionProcessesComboStoreByQuery = result
      // })
    }
    // remoteMethod1 (query) {
    //   this.$api.ActionRequest(this.api.productionProcessesComboStoreByQuery, { text: query }).then(result => {
    //     this.productionProcessesComboStoreByQuery = result
    //   })
    // },
    // 编辑
    // async editEvent (row) {
    //   await this.$api.ActionRequest(this.api.productionProcessesComboStoreByQuery, { gid: row.productionSeriesID }).then(result => {
    //     this.productionProcessesComboStoreByQuery = result
    //     this.selectRow = cloneDeep(row)
    //     this.showEdit = true
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
