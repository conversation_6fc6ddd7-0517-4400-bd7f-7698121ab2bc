<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="qualityID">
              <template #default="{ data }">
                <el-select v-model="data.qualityID" filterable placeholder="质检分类" size="mini" remote reserve-keyword :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in qualityComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="productionTeamID">
              <template #default="{ data }">
                <el-select v-model="data.productionTeamID" filterable placeholder="生产小组" size="mini" remote reserve-keyword :remote-method="remoteMethod2" clearable>
                  <el-option v-for="item in productionTeamComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PtmQualitydetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="qualityCode" title="质检分类编码" sortable width="120"> </vxe-table-column>
      <vxe-table-column field="qualityName" title="质检分类名称" sortable width="120"> </vxe-table-column>
      <vxe-table-column field="productionTeamName" title="生产小组" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="code" title="编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="质检分类" field="qualityID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.qualityID" filterable placeholder="请选择" size="mini" remote reserve-keyword :remote-method="remoteMethod1">
              <el-option v-for="item in qualityComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="生产小组" field="productionTeamID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.productionTeamID" filterable placeholder="请选择" size="mini" remote reserve-keyword :remote-method="remoteMethod2" clearable>
              <el-option v-for="item in productionTeamComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'ptm_qualitydetail',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        sort: 999,
        qualityID: null,
        productionTeamID: null
      },
      formRules: {
        qualityID: [{ required: true, message: '请选择质检分类' }],
        // productionTeamID: [{ required: true, message: '请绑定生产小组' }],
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mes/ptm_qualitydetail/get',
        add: '/mes/ptm_qualitydetail/adds',
        edit: '/mes/ptm_qualitydetail/updates',
        delete: '/mes/ptm_qualitydetail/deletes',
        qualityComboStoreByQuery: '/mes/comboQuery/qualityComboStoreByQuery',
        productionTeamComboStoreByQuery: '/mes/comboQuery/ProductionTeamComboStoreByQuery'
      },
      qualityComboStoreByQuery: [],
      productionTeamComboStoreByQuery: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.qualityComboStoreByQuery).then(result => {
        this.qualityComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery).then(result => {
        this.productionTeamComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.qualityComboStoreByQuery, { text: query }).then(result => {
        this.qualityComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery, { text: query }).then(result => {
        this.productionTeamComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.productionTeamComboStoreByQuery, { gid: row.productionTeamID }).then(result => {
        this.productionTeamComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.qualityComboStoreByQuery, { gid: row.qualityBaseID }).then(result => {
        this.qualityComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
