<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadProductionlintMasterTable' ref='master_table' @cell-dblclick="cellDblClick" height="auto" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <!-- <vxe-table-column field="productionSeriesText" title="产品线" width="150"> </vxe-table-column>
      <vxe-table-column field="factoryText" title="工厂" width="150"> </vxe-table-column> -->
      <vxe-table-column field="workSecationName" title="工段" width="150"> </vxe-table-column>
      <vxe-table-column field="workSecationEndTime" title="工段截至期" :formatter="formatDate" width="130"> </vxe-table-column>
      <vxe-table-column field="productionTeamName" title="小组" width="130"> </vxe-table-column>
      <vxe-table-column field="productionTeamEndTime" title="小组截止期" :formatter="formatDate" width="130"> </vxe-table-column>
      <vxe-table-column field="productionStationName" title="工位" width="150"> </vxe-table-column>
      <vxe-table-column field="productionProcessesCode" title="工序编码" width="80"> </vxe-table-column>
      <vxe-table-column field="productionProcessesName" title="工序名称" width="150"> </vxe-table-column>
      <vxe-table-column field="stateText" title="状态" width="80"> </vxe-table-column>
      <vxe-table-column field="message" title="消息" width="150"> </vxe-table-column>
      <vxe-table-column field="sort" title="排序" width="150"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'bad_productionseries',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        remark: '',
        isActive: true,
        factoryID: this.form.id,
        workSecationID: null,
        sort: 999
      },
      formRules: {
        workSecationID: [{ required: true, message: '请选择工段' }]
      },
      api: {
        get: '/mes/pRD_ProductPlanSchedule/get'
        // add: '/mes/bad_factorydetail/adds',
        // edit: '/mes/bad_factorydetail/updates',
        // delete: '/mes/bad_factorydetail/deletes',
        // factoryComboStore: '/mes/combo/factoryComboStore',
        // GroupComboStore: '/mtm/combo/groupComboStore',
        // worksecationComboStoreByQuery: '/mes/comboQuery/worksecationComboStoreByQuery'
      },
      factoryComboStore: [],
      worksecationComboStoreByQuery: [],
      GroupComboStore: []
    }
  },
  async created () {
    this.loadData({ productionPlanWorkSecationID: this.form.id, productionPlanDetailID: this.form.productionPlanDetailID, workSecationID: this.form.workSecationID })
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.factoryComboStore).then(result => {
      //   this.factoryComboStore = result
      // })
      // await this.$api.ActionRequest(this.api.worksecationComboStoreByQuery).then(result => {
      //   this.worksecationComboStoreByQuery = result
      // })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.worksecationComboStoreByQuery, { text: query }).then(result => {
        this.worksecationComboStoreByQuery = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
