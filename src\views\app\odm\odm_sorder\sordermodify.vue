<template>
  <base-sorder ref="sorderbaseRef" :sordernum="sorder.num" :sorderData="SorderStore" v-loading="loading" element-loading-text="加载中请稍后" />
</template>

<script>
import { mapActions, mapState, mapMutations } from 'vuex'
import baseSorder from './index'

export default {
  name: 'odm_sordermodify',
  components: {
    baseSorder
  },
  data () {
    return {
      api: {
        get: '/mtm/odm_sorder/get'
      },
      SorderStore: {
        id: null,
        sorderDetailID: null,
        code: null,
        clientID: null,
        address: null,
        contact: null,
        tel: null,
        itemID: null,
        itemText: null,
        finalWidth: 0,
        finalTexture: null, // 纹理
        finalComposition: null, // 面料成分
        // finalFabricLabel: null, // 客供面料表
        finalFabric: null, // 客供面料号
        deliveryDate: null,
        clientPersonID: null,
        clientPersonText: null,
        height: null,
        HalfFitting: false,
        isUrgent: false,
        remark: null,
        statusID: 0,
        sorderDetailModels: [],
        sorderTypeID: 1,
        sorderPsersons: []
      },
      isActive: true,
      loading: false
    }
  },
  watch: {
    sorder: {
      deep: true,
      handler: async function (newVal, oldVal) {
        // console.log(`oldVal:${oldVal.id}---${oldVal.num}`)
        // console.log(`newVal:${newVal.id}------${newVal.num}`)
        // if (newVal.id !== null && oldVal.id === newVal.id && newVal.id !== undefined && !this.isActive) {
        //   this.isActive = false
        //   console.log('订单号变更')
        //   this.sorderload(newVal.id, newVal.num)
        //   this.$store.commit('d2admin/page/keepAliveClean')
        //   // this.handleCleanCacheAndRefreshCurrent()
        // } else {

        // }
      }
    }
  },
  computed: {
    ...mapState('app/odm/odm_sorder/index', [
      'sorder'
    ])
  },
  mounted () {

  },
  async created () {
    // if (this.sorder && this.sorder.id && this.sorder.id !== null) {
    //   console.log(`订单开始加载${this.sorder.id}--${this.sorder.num}`)
    //   // this.isActive = false
    //   //await this.sorderload(this.sorder.id, this.sorder.num)
    // } else {
    //   console.log(this.isActive)
    //   if (!this.isActive) {
    //     return
    //   }

    // }
  },
  methods: {
    ...mapActions('d2admin/page', [
      'close',
      'closeLeft',
      'closeRight',
      'closeOther',
      'closeAll',
      'openedSort'
    ]),
    ...mapMutations('d2admin/page', [
      'keepAliveRemove',
      'keepAliveClean'
    ]),
    ...mapActions('app/odm/odm_sorder/index', ['setSorder']),
    // 清空当前页缓存并刷新此页面
    async handleCleanCacheAndRefreshCurrent () {
      this.keepAliveRemove(this.$route.path)
      await this.$nextTick()
      this.$router.replace('/refresh')
    },
    // 关闭当前
    handleCloseCurrent () {
      this.close({
        tagName: this.$route.fullPath
      })
    },
    setActive () {
      //  console.log(this.$refs.sorderbaseRef.activeItem)
      var activeItem = this.$refs.sorderbaseRef.activeItem
      if (activeItem === 'detail') {
        this.$refs.sorderbaseRef.setActive()
      }
    },
    async sorderload (sorderid, num) {
      if (sorderid) {
        // const loading = this.$loading({
        //   lock: true,
        //   text: '订单数据加载中',
        //   spinner: 'el-icon-loading',
        //   background: 'rgba(0, 0, 0, 0.7)'
        // })
        this.loading = true
        await this.$api.ActionRequest(this.api.get, { Id: sorderid }).then(result => {
          this.setSorder({ id: sorderid, num: num })
          this.SorderStore = result
          this.setActive()
          // loading.close()
          this.loading = false
        })
      }
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (to.params.id && to.params.sorderid) {
        vm.$store.state.app['odm/odm_sorder/index'].sorder.num = to.params.sorderid
        vm.$store.state.app['odm/odm_sorder/index'].sorder.id = to.params.id
        vm.sorderload(to.params.id, to.params.sorderid)
      } else {
        if (vm.sorder.id && vm.sorder.num) {
          vm.sorderload(vm.sorder.id, vm.sorder.num)
        } else {
          vm.$notify({
            title: '警告',
            message: '页面数据刷新需要重新进入订单详情',
            type: 'error'
          })
          vm.handleCloseCurrent()
          vm.$router.push({
            name: 'odm_sorderlist'
          })
        }
      }
    })
  }

}
</script>

<style>
</style>
