<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd"> 新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">

            <vxe-form-item field="actionTypeID">
              <template #default="{ data }">
                <vxe-select v-model="data.actionTypeID" placeholder="选择权限类别" clearable>
                  <vxe-option v-for="num in actionTypeCombStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="actionGroupID">
              <template #default="{ data }">
                <vxe-select v-model="data.actionGroupID" placeholder="选择权限分组" clearable>
                  <vxe-option v-for="num in actionGroupCombStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>

    </template>

    <vxe-table ref='master_table' id='SymAction_master_table' :stripe="false" :row-class-name="rowClassName" :loading="tableLoading" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="code" title="权限编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="编码名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="actionTypeText" title="权限类别" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="actionGroupText" title="权限分组" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="url" title="链接" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="icon" title="显示图标" width="100px">
        <template v-slot="{ row }">
          <d2-icon :name="row.icon" />
        </template>
      </vxe-table-column>
      <vxe-table-column field="sequence" title="排序" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" show-overflow :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">

        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="链接地址" field="url" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="显示图标" field="icon" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="类型" field="actionTypeID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.actionTypeID" filterable placeholder="请选择类型" size="mini">
              <el-option v-for="item in actionTypeCombStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="分组" field="actionGroupID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.actionGroupID" filterable placeholder="请选择分组" size="mini" :clearable='true'>
              <el-option v-for="item in actionGroupCombStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="排序" field="sequence" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>

        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>

      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'sym_action',
  mixins: [masterTableMixins],

  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        url: '',
        actionTypeID: '',
        actionGroupID: '',
        sequence: 999,
        icon: ''
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        actionTypeID: [{ required: true, message: '请选择类型' }]

      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'url', title: '链接地址', span: 12, itemRender: { name: '$input' } },
        { field: 'icon', title: '显示图标', span: 12, itemRender: { name: '$input' } },
        { field: 'actionTypeID', title: '类别', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'actionGroupID', title: '分组', span: 12, itemRender: { name: '$select', options: [] }, props: { clearable: true } },
        { field: 'sequence', title: '排序', span: 12, itemRender: { name: '$input', props: { type: 'number', placeholder: '请输入顺序' } } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/sym_action/get',
        add: '/mtm/sym_action/adds',
        edit: '/mtm/sym_action/updates',
        delete: '/mtm/sym_action/deletes',
        actionTypeCombStore: '/mtm/combo/actionTypeComboStore',
        actionGroupCombStore: '/mtm/combo/actionGroupComboStore'
      },
      action: {
        get: true,
        add: true,
        edit: true,
        delete: true,
        print: true
      },
      actionTypeCombStore: [],
      actionGroupCombStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'actionTypeID').itemRender.options = this.actionTypeCombStore
    // this.$utils.find(this.formItems, item => item.field === 'actionGroupID').itemRender.options = this.actionGroupCombStore
    // this.formItems[4].itemRender.options = this.actionTypeCombStore
    // this.formItems[5].itemRender.options = this.actionGroupCombStore

    // await this.$store.dispatch('d2admin/page/isLoaded')
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.actionTypeCombStore).then(result => {
        this.actionTypeCombStore = result
      })
      await this.$api.ActionRequest(this.api.actionGroupCombStore).then(result => {
        this.actionGroupCombStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
