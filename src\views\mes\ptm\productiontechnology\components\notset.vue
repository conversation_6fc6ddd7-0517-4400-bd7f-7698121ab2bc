<template>
  <d2-container>
    <div class="notset">
      <h1>请联系管理员,设置工位数据！</h1>
      <h1>当前电脑IP:{{ip}}</h1>
      <h1>如果不显示当前电脑IP:请使用谷歌浏览打开,并按下列方法操作</h1>
      <h2>1:在chrome 浏览器地址栏中输入：chrome://flags/</h2>
      <h2>2:搜索 #enable-webrtc-hide-local-ips-with-mdns 该配置 并将属性改为 disabled</h2>
      <h2>3:谷歌内核130版本以上不生效,参考：安装插件WebRTC Network Limiter 点击插件图标选择第二项即可</h2>
      <i class="fa fa-meh-o "></i>
    </div>
  </d2-container>
</template>

<script>
import defaultset from './index'
export default {
  mixins: [defaultset]
}
</script>

<style lang="scss">
.notset {
  position: absolute;
  top: 35%;
  transform: translateY(-50%);
  width: 100%;
  text-align: center;
  color: #909399;
  i {
    font-size: 20em;
  }
}
</style>
