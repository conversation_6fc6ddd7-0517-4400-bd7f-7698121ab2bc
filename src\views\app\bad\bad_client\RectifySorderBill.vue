<template>
  <vxe-form :data="selectRow" title-align="right" title-width="100">
    <vxe-form-item title="当前客户支出" span="8" :item-render="{}"> <vxe-input v-model="selectRow.selfOutput" placeholder="请输入编码" clearable></vxe-input>
    </vxe-form-item>
    <vxe-form-item title="当前客户收入" span="8" :item-render="{}"> <vxe-input v-model="selectRow.selfInput" placeholder="请输入编码" clearable></vxe-input>
    </vxe-form-item>
    <vxe-form-item title="当前客户差值" span="8" :item-render="{}"> <vxe-input v-model="selectRow.selfDifferenceValue" placeholder="请输入编码" clearable></vxe-input>
    </vxe-form-item>
    <vxe-form-item title="总计支出" span="8" :item-render="{}"> <vxe-input v-model="selectRow.allOutput" placeholder="请输入简称" clearable></vxe-input>
    </vxe-form-item>
    <vxe-form-item title="总计收入" span="8" :item-render="{}"> <vxe-input v-model="selectRow.allInput" placeholder="请输入简称" clearable></vxe-input>
    </vxe-form-item>
    <vxe-form-item title="总计差值" span="8" :item-render="{}"> <vxe-input v-model="selectRow.allDifferenceValue" placeholder="请输入简称" clearable></vxe-input>
    </vxe-form-item>
  </vxe-form>
</template>

<script>
export default {
  // 账单统计
  name: 'RectifySorderBill',
  props: {
    client: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      selectRow: {
        selfOutput: null,
        selfInput: null,
        selfDifferenceValue: null,
        allOutput: null,
        allInput: null,
        allDifferenceValue: null
      },
      api: {
        get: '/mtm/odm_sorderbill/RectifySorderBill'
      }
    }
  },
  created () {
    this.get()
  },
  methods: {
    async get () {
      await this.$api.ActionRequest(this.api.get, { clientID: this.client.id }).then(result => {
        this.selectRow = result
        // console.log(result)
      })
    }
  }
}
</script>

<style>
</style>
