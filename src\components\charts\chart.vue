<template>
  <d2-container id="boardChart">
    <template slot="header">
      <slot name='chartheader' />
    </template>
    <component :is="`ve-${type}`" ref="boardChartRef" :data="data" :settings="settings" :width='chartWidth' :height='chartHeight' :title='title'>
    </component>

  </d2-container>
</template>

<script>
import elementResizeDetectorMark from 'element-resize-detector' // 检测容器变化
export default {
  name: 'boardChart',
  props: {
    data: {
      type: Object,
      default: null
    },
    settings: {
      type: Object,
      default: null
    },
    title: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      default: 'bar' // ['line', 'histogram', 'pie', 'bar']
    }

  },

  mounted () {
    const erd = elementResizeDetectorMark()
    // 创建实例带参
    // var erdUltraFast = elementResizeDetectorMaker({
    //   strategy: "scroll", //<- For ultra performance.
    //   callOnAdd: true,
    //   debug: true
    // });
    var dom = this.$el
    // var dom=document.getElementById("boardChart")
    var _this = this
    // 监听id为test的元素 大小变化
    erd.listenTo(dom, function (element) {
      _this.resizeChart(element.offsetWidth, element.offsetHeight)
    })
  },
  data () {
    return {
      chartWidth: '400px',
      chartHeight: '400px'
    }
  },
  methods: {
    resizeChart (width, height) {
      this.chartWidth = width - 35 + 'px'
      this.chartHeight = height - 50 + 'px'
      this.$refs.boardChartRef.resize()
    }
  }
}
</script>

<style>
</style>
