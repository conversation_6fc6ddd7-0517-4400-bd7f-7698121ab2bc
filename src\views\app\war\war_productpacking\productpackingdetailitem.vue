<template>
  <div class="productpackingdetail">
    <vxe-table border id='productpackingMasterTable' height="300px" ref='master_table' :data="tableData" :expand-config="{accordion: true,iconOpen: 'fa fa-minus-square', iconClose: 'fa fa-plus-square'}">
      <!-- <vxe-table-column type="checkbox" width="60"></vxe-table-column> -->
      <vxe-table-column type="seq" width="50"></vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" sortable width="150"> </vxe-table-column>
      <!-- <vxe-table-column field="number" title="序号" sortable width="100"> </vxe-table-column> -->
      <vxe-table-column field="clientPersonName" title="顾客" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="receiptNumber" title="小票号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="weight" title="重量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="customerNumber" title="客户订单号" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="size" title="马甲单号" sortable width="100"></vxe-table-column> -->
      <!-- <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="clientShopCode" title="店铺代码" width="100"></vxe-table-column>
      <!-- <vxe-table-column field="wight" title="重量" width="100"></vxe-table-column> -->
      <!-- <vxe-table-column title="操作" width="200" :fixed='tableOptFixed' align="center" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>

    <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
    </vxe-pager>
    <vxe-modal v-model="showEdit" :title=" '编辑&保存'" width="800" resize destroy-on-close :loading="submitLoading" :zIndex="9999">
      <vxe-form :data="selectRow" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="流水号" field="serialNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顾客" field="clientPersonName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="小票号" field="receiptNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="类别" field="groupText" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="重量" field="weight" span="12" :item-render="{name: '$input',   props: { type: 'number'}}"></vxe-form-item>
        <vxe-form-item title="客户订单号" field="customerNumber" span="12" :item-render="{name: '$input',   props: { disadble:true}}"></vxe-form-item>
        <vxe-form-item title="店铺代码" field="clientShopCode" span="12" :item-render="{name: '$input',   props: {disadble:true}}"></vxe-form-item>
        <!-- <vxe-form-item title="箱子体积" field="size" span="12" :item-render="{name: '$input',   props: { type: 'number'}}"></vxe-form-item>
        <vxe-form-item title="数量" field="qty" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item> -->
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </div>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'ProductPackingList', // 发货清单
  mixins: [detailTableMixins],
  props: {
    dataRow: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      api: {
        get: '/mtm/wAR_ProductPackingdetail/get',
        edit: '/mtm/wAR_ProductPackingdetail/updates',
        delete: '/mtm/wAR_ProductPackingdetail/deletes'

      },
      tableData: []

    }
  },

  components: {},
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.dataRow.id })
  },
  methods: {
    async getCombStore () {
      //   await this.$api.ActionRequest(this.api.productBoxTypeComboStore).then(result => {
      //     this.productBoxTypeComboStore = result
      //   })
    },
    reload () {
      this.loadData({ id: this.dataRow.id })
    }

  }

}
</script>

<style lang="scss" scoped>
.productpacking {
  .tab {
    // height: 80%;
  }
}
</style>
