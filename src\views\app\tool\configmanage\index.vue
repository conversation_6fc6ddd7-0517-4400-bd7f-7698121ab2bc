<template>
  <d2-container>
    <template v-if="info.userType!==0">
      <div class="notset">
        <h1>您没有访问此页面的权限</h1>
        <i class="fa fa-meh-o "></i>
      </div>
    </template>
    <template v-else>
      <el-divider content-position="left">后端设置</el-divider>
      <el-row>
        <el-col :span="8"><vxe-button status="warning" @click="InitTZTemplateEvent">生成/重置团装Excel模板配置</vxe-button></el-col>
        <el-col :span="8"> <vxe-button status="warning" @click="CadConfigEvent">Cad相关配置</vxe-button></el-col>

      </el-row>
      <el-divider content-position="left">MES</el-divider>
      <el-row>
        <vxe-button status="success" @click="MesPrintLabel=!MesPrintLabel">MES修改票头打印数量相关配置</vxe-button>
      </el-row>
    </template>

    <vxe-modal v-model="MesPrintLabel" title="MES修改票头打印数量相关配置" width="30%" resize destroy-on-close>
      <mes-print-label v-if="MesPrintLabel&&info.userType===0" :success="success" />
    </vxe-modal>
    <vxe-modal v-model="CadConfigShow" title="Cad相关配置" width="30%" resize destroy-on-close>
      <cad-config v-if="CadConfigShow&&info.userType===0" :success="success" />
    </vxe-modal>
  </d2-container>

</template>

<script>
import { mapState } from 'vuex'

import MesPrintLabel from './mescomponents/printlabel.vue'
import TzTemplate from './components/TZTemplate.js'
import CadConfig from './components/CadConfig.vue'
// import Test from './mescomponents/test.vue'
export default {
  name: 'ConfigManage',
  components: {
    MesPrintLabel,
    CadConfig
  },
  mixins: [TzTemplate],
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      MesPrintLabel: false,
      CadConfigShow: false
    }
  },
  methods: {
    success () {
      this.MesPrintLabel = false
      this.CadConfigShow = false
    },
    CadConfigEvent () {
      this.CadConfigShow = true
    }
  }
}
</script>

<style lang="scss">
.notset {
  position: absolute;
  top: 35%;
  transform: translateY(-50%);
  width: 100%;
  text-align: center;
  color: #909399;
  i {
    font-size: 20em;
  }
}
</style>
