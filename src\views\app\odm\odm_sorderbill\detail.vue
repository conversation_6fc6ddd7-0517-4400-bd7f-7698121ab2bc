<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd&&!(info.userType==2)">新增 </vxe-button>
          <vxe-button @click="exportSelectEvent" status="perfect" v-if="menuAction.allowPrint">导出选中</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="notZero" title="过滤价格0" :item-render="{}"> <template #default>
                <vxe-checkbox v-model="searchForm.notZero"></vxe-checkbox>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='OdmSorderbilldetailDetailTable' ref='master_table' height="auto" :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="isSystemCreated" title="系统生成?" sortable width="90px" :formatter='formatBool'></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="90px"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelElemCode" title="工艺编码" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="modelElemName" title="工艺名称" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="modelElemPrice" title="单价" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="modelElemQty" title="耗量" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="itemCode" title="面辅料编码" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="itemOriginalItemNo" title="原始货号" sortable width="90px"></vxe-table-column>
      <vxe-table-column field="itemPrice" title="单价" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="itemQty" title="耗量" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="other" title="其他" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="otherPrice" title="其他单价" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="otherQty" title="其他数量" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="count" title="共计" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="discount" title="折扣" sortable width="65px">
        <template v-slot="{ row }">
          <span>{{row.discount!=null?row.discount+'%':''}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="total" title="实际付款金额" sortable width="65px">
        <template v-slot="{ row }">
          <span style="color:red">{{row.total}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column title="操作" width="100" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit&&!row.isSystemCreated"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="栏目名称" field="other" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="单价" field="otherPrice" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="数量" field="otherQty" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="折扣" field="discount" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"></vxe-form-item>
        <!-- <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item> -->
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { mapState } from 'vuex'
export default {
  name: 'OdmSorderbillDetail',
  mixins: [detailTableMixins],
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      searchForm: {
        notZero: true
      },
      formData: {
        sorderbillID: this.form.id,
        isSystemCreated: false,
        other: null,
        otherPrice: null,
        otherQty: 1,
        discount: null,
        remark: '',
        isActive: true
      },
      formRules: {
        other: [{ required: true, message: '请输入栏目' }, { min: 2, max: 20, message: '长度在 2 到 30 个字符' }],
        otherPrice: [{ required: true, message: '请输入单价' }],
        otherQty: [{ required: true, message: '请输入数量' }]
      },
      api: {
        get: '/mtm/odm_sorderbilldetail/get',
        add: '/mtm/odm_sorderbilldetail/adds',
        edit: '/mtm/odm_sorderbilldetail/updates',
        delete: '/mtm/odm_sorderbilldetail/deletes'
      }
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
