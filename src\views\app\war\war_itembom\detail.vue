<template>
  <d2-container class="WarItembomDetail">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增 </vxe-button>
          <vxe-button icon="vxe-icon--download" status="success" @click="exportDataEvent">导出</vxe-button>
          <vxe-button status="warning" @click="updateAll">重新生成</vxe-button>
          <vxe-button status="success" @click="updateItemBom()" v-if="menuAction.allowEdit&&(form.state===1||form.state===3)">备货</vxe-button>
        </template>
        <!-- <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template> -->
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}">
              <template #default="{data}">
                <vxe-switch v-model="data.sorderBomShow" size="mini" open-label="显示全部" close-label="只显示必要"></vxe-switch>
              </template>
            </vxe-form-item>
            <vxe-form-item field="notZero" title="过滤耗量为0" :item-render="{}">
              <template #default="{data}">
                <vxe-checkbox v-model="data.notZero"></vxe-checkbox>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItembomdetailDetailTable' ref='master_table' height="auto" :row-class-name="({ row, rowIndex })=>rowClassName({ row, rowIndex ,other:'itemStockHas'})" :data="tableData" :custom-config="{storage: true}" :span-method="mergeRowMethod">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <!-- <vxe-table-column field="cadRuleText" title="衣片组" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="cadLayoutText" title="排料图" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelElemCode" title="工艺编码" sortable width="70px"></vxe-table-column> -->
      <vxe-table-column field="groupName" title="分类" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelElemListName" title="款式名称" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelElemName" title="工艺名称" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="itemText" title="货号" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="itemGroupText" title="物料类别" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="clientName" title="所属客户" sortable width="120px"></vxe-table-column>
      <vxe-table-column field="businessGroupText" title="物料业务属性" sortable width="120px"></vxe-table-column>
      <vxe-table-column field="itemID" title="合并耗量" sortable width="95px" align='center'>
        <template v-slot="{ row,column }">
          <span class="rowSpan">{{rowSpan(row,column)}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="qty" title="耗量/件" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelQty" title="件/数" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="countQty" title="合计耗量" sortable width="95px">
        <template v-slot="{ row }">
          <span>{{ row.countQty }}</span>
          <span v-if="row.itemMLText!==null" style="color:red;font-weight: 500;">({{ row.itemMLText }}cm)</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="inventoryQty" title="有效库存" sortable width="95px" :class-name='inventoryQtyClass'>
        <!-- <template v-slot="{ row }">
          <span v-if="row.inventoryQty<0" style="background-color: red;color: white;">{{row.inventoryQty}}</span>
          <span v-else>{{row.inventoryQty}}</span>
        </template> -->
      </vxe-table-column>
      <vxe-table-column field="itemStockPositionText" title="库位" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="itemBatchCode" title="海关合同号" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="itemBatchEoriNo" title="手册号" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="itemBatchQty" title="手册号库存" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column title="操作" width="100" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit" :disabled="form.state!==1&&form.state!==3"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete" :disabled="form.state!==1&&form.state!==3"> </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="工艺信息" field="modelElemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable remote reserve-keyword placeholder="工艺信息" :remote-method="remoteMethod2" size="mini">
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="物料信息" field="itemStockID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.itemStockID" filterable placeholder="面料" size="mini" @change="(val)=>itemStockChange(val,selectRow)" remote reserve-keyword :remote-method="remoteMethod">
              <el-option v-for="item in ItemStockComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.clientName==null?"":item.clientName+ '/'}}{{item.businessGroupText}}</span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="合同号" field="itemBatchID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemBatchID" filterable placeholder="请选择" size="mini" remote reserve-keyword default-first-option plremoteaceholder="合同号" :remote-method="remoteMethod4" style="width:100%">
              <el-option v-for="item in ItemBatchComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.text }}</span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="耗量" field="qty" span="12" :item-render="{name: '$input',   props: { type: 'float'}}"> </vxe-form-item>
        <!-- <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item> -->
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'WarItembomDetail',
  mixins: [detailTableMixins],
  data () {
    return {
      formData: {
        itemBomID: this.form.id,
        itemID: null,
        qty: null,
        modelElemID: '',
        remark: '',
        isActive: true,
        itemBatchID: null
      },
      searchForm: {
        sorderBomShow: false,
        notZero: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        itemID: [{ required: true, message: '请输入物料信息' }]
      },
      api: {
        get: '/mtm/war_itembomdetail/get',
        add: '/mtm/war_itembomdetail/adds',
        edit: '/mtm/war_itembomdetail/updates',
        delete: '/mtm/war_itembomdetail/deletes',
        updateItemBom: '/mtm/war_itembom/updates',
        // ItemStockComboStoreByQuery: '/mtm/comboQuery/itemComboStoreByQuery',
        ItemStockComboStoreByQuery: '/mtm/comboQuery/ItemStockComboStoreByQuery',
        updateall: '/mtm/wAR_ItemBom/createSorderBom',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ItemBatchComboStoreByQuery: '/mtm/comboQuery/ItemBatchComboStoreByQuery'
      },
      ItemBatchComboStoreByQuery: [],
      ItemStockComboStoreByQuery: [],
      ModelElemComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery).then(result => {
        this.ItemStockComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    exportDataEvent () {
      var filename = '【' + this.form.code + '】Bom清单' + this.timeFormate(new Date())
      this.$refs.master_table.exportData({ type: 'csv', filename: filename })
    },
    timeFormate (timeStamp) {
      const year = new Date(timeStamp).getFullYear()
      const month = new Date(timeStamp).getMonth() + 1 < 10 ? '0' + (new Date(timeStamp).getMonth() + 1) : new Date(timeStamp).getMonth() + 1
      const date = new Date(timeStamp).getDate() < 10 ? '0' + new Date(timeStamp).getDate() : new Date(timeStamp).getDate()
      const hh = new Date(timeStamp).getHours() < 10 ? '0' + new Date(timeStamp).getHours() : new Date(timeStamp).getHours()
      const mm = new Date(timeStamp).getMinutes() < 10 ? '0' + new Date(timeStamp).getMinutes() : new Date(timeStamp).getMinutes()
      const ss = new Date(timeStamp).getSeconds() < 10 ? '0' + new Date(timeStamp).getSeconds() : new Date(timeStamp).getSeconds()
      return (year + '年' + month + '月' + date + '日' + hh + ':' + mm + ':' + ss)
    },
    itemStockChange (val, selectRow) {
      this.ItemBatchComboStoreByQuery = []
      selectRow.itemBatchID = null
      if (val === null || val === '') {
        selectRow.itemID = null
      } else {
        var dto = this.ItemStockComboStoreByQuery.GetFirstElement('id', val)
        if (dto) {
          selectRow.itemID = dto.itemID
        }
      }
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery, { text: query }).then(result => {
        this.ItemStockComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      if (this.selectRow.itemID === null) {
        this.$message({ message: '请先选择物料', type: 'error' })
        return
      }
      this.$api.ActionRequest(this.api.ItemBatchComboStoreByQuery, { text: query, xid: this.selectRow.itemID }).then(result => {
        this.ItemBatchComboStoreByQuery = result
      })
    },
    updateAll () {
      this.$XModal.confirm('已有数据会全部情况,确认重新生成吗?').then(type => {
        if (type === 'confirm') {
          this.$api.ActionRequest(this.api.updateall, { sorderID: this.form.sorderID }).then(res => {
            this.$message({ message: '生成成功', type: 'success' })
            this.loadData()
          })
        }
      })
    },
    inventoryQtyClass ({ row }) {
      if (row.inventoryQty < 0) {
        return 'inventoryQtyWarning'
      }
    },
    async updateItemBom (confirm = 0) {
      var data = cloneDeep(this.form)
      data.state = 5
      data.confirm = confirm
      await this.$api.ActionRequest(this.api.updateItemBom, [data]).then(result => {
        if (result) {
          this.$message({ message: '操作成功', type: 'success' })
          this.$emit('finish')
        } else {
          this.confirm()
        }
      })
    },
    confirm () {
      this.$confirm('物料清单中有库存数小于0的,是否强制出库?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.updateItemBom(1)
      }).catch(() => {

      })
    },
    // 编辑
    async editEvent (row) {
      this.$api.ActionRequest(this.api.ItemBatchComboStoreByQuery, { gid: row.itemBatchID }).then(result => {
        this.ItemBatchComboStoreByQuery = result
      })
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery, { gid: row.itemStockID }).then(result => {
        this.ItemStockComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 通用行合并函数（将相同多列数据合并为一行）
    mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
      const fields = ['itemID', 'groupID']
      const cellValue = row[column.property]
      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    rowSpan (row, column) {
      // console.log(row.itemText + ':' + row.groupID + ':' + row.itemID)
      var countLength = this.$utils.filter(this.tableData, item => item.groupID === row.groupID && item.itemID === row.itemID)
      if (countLength.length > 1) {
        return this.$utils.sum(countLength, 'qty') // 99
      } else {
        return row.qty
      }
    }
  }
}
</script>

<style lang="scss">
.WarItembomDetail {
  .inventoryQtyWarning {
    color: white;
    background-color: red;
  }
  .rowSpan {
    font-weight: bold;
    font-size: 15px;
    color: red;
  }
}
</style>
