<template>
  <d2-container class="chartview">
    <split-pane :min-percent='10' :default-percent='80' split="horizontal">
      <template slot="paneL">
        <split-pane :min-percent='10' :default-percent='50' split="vertical">
          <template slot="paneL">
            <chart-3 />
          </template>
          <template slot="paneR">
            <chart-4 />
          </template>
        </split-pane>

      </template>
      <template slot="paneR">
        <table-view />
      </template>
    </split-pane>
  </d2-container>
</template>

<script>
import TableView from './componments/tableview'
// import Chart1 from './componments/chart1'
import Chart4 from './componments/chart4'
// import Productionplanfinishindicator from '@/views/mes/prd/prd_productionindicator/componments/productionplanfinishindicator.vue'
import Chart3 from './componments/chart3'

export default {
  components: {
    TableView,
    Chart3,
    Chart4
  }
}
</script>

<style lang="scss" scope>
.chartview {
  width: 100%;
  height: 100%;
  // background-color: #1e1e2a;
  // color: #fff;
  overflow: hidden;
  .splitter-pane {
    // background-color: #1e1e2a;
    // color: #fff;
  }
  .splitter-pane-resizer {
    opacity: 0.3 !important;
  }
  .d2-container-full {
    .d2-container-full__body:first-of-type {
      height: calc(100vh);
    }
  }
}
</style>
