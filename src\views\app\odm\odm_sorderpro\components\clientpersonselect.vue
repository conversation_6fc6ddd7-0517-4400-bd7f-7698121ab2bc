<template>
  <vxe-pulldown ref="personDown" size="mini" :transfer="true">
    <template v-slot>
      <vxe-input v-model="clientPersonText" suffix-icon="fa fa-search" placeholder="点击搜索顾客" @keyup="personkeyupEvent" @focus="personFocusEvent" @suffix-click="personSuffixClick" clearable></vxe-input>
    </template>
    <template v-slot:dropdown>
      <div class="dropdownperson">
        <vxe-grid Id="sorderperson" keep-source highlight-hover-row auto-resize height="300" width="800" min-height="300px;" ref="personGrid" :loading="personLoading" :pager-config="personTablePage" :data="PsersontableData" :columns="PsersontableColumn" @cell-click="PsersonCellClickEvent" @page-change="PsersonPageChange" :edit-config="{trigger: 'manual', mode: 'row', showStatus: true, icon: 'fa fa-pencil'}" :custom-config="{storage: false}">
          <!-- <template v-slot:toolbar_buttons>
            <vxe-button @click="clientPersonShow=!clientPersonShow">新增</vxe-button>
          </template> -->
          <template v-slot:sex_default="{ row }">
            <template>
              {{row.gender?"男":"女"}}
            </template>
          </template>
          <template v-slot:operate="{ row }">
            <template v-if="$refs.personGrid.isActiveByRow(row)">
              <vxe-button icon="fa fa-save" status="primary" title="保存" circle @click="savePsersonRowEvent(row)"></vxe-button>
            </template>
            <template v-else>
              <vxe-button icon="fa fa-edit" title="编辑" circle @click="editPsersonRowEvent(row)"></vxe-button>
            </template>
            <vxe-button icon="fa fa-trash" title="删除" circle @click="removePsersonRowEvent(row)"></vxe-button>
            <vxe-button icon="fa fa-copy" title="复制" circle @click="copyRowEvent(row)"></vxe-button>
          </template>
        </vxe-grid>
      </div>
    </template>

  </vxe-pulldown>
</template>

<script>
// import { isEmpty } from 'lodash'
export default {
  name: 'clientpersoninsert',
  props: {
    detailrow: {
      type: Object
    },
    sorderStore: {
      type: Object
    }

  },
  data () {
    return {
      clientPersonText: this.detailrow.clientPersonText,
      api: {
        sorderPserson: '/mtm/odm_sorder/GetSorderPsersons',
        clientPsersonAdd: '/mtm/bAD_ClientPerson/adds',
        clientPsersonEdit: '/mtm/bAD_ClientPerson/updates',
        clientPsersonDelete: '/mtm/bAD_ClientPerson/deletes'
      },
      // tableToolbar: {
      //   custom: true,
      //   slots: {
      //     buttons: 'toolbar_buttons'
      //   }
      // },
      PsersontableColumn: [
        { field: 'code', title: '顾客编码', width: 120, editRender: { name: 'input' } },
        { field: 'tel', title: '手机号', width: 120, editRender: { name: '$input', props: { type: 'number', min: 0, max: 500 } } },
        { field: 'codeName', title: '顾客名称', width: 120, editRender: { name: 'input' } },
        { field: 'height', title: '身高', width: 150, editRender: { name: '$input', props: { type: 'number', min: 0, max: 500 } } },
        {
          field: 'gender',
          title: '性别',
          width: 100,
          editRender: { name: '$switch', props: { onLabel: '男', offLabel: '女' } },
          slots: {
            default: 'sex_default'
          }
        },
        { field: 'weight', title: '体重', width: 100, editRender: { name: '$input', props: { type: 'number', min: 1, max: 500 } } },
        { title: '操作', width: 150, slots: { default: 'operate' } }
      ],
      personValidRules: {
        code: [
          { required: true, message: '顾客编码必须填写' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }
        ],
        codeName: [
          { required: true, message: '顾客名称必须填写' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }
        ],
        height: [
          { required: true, message: '身高必须填写' }
        ]
        // weight: [
        //   { required: true, message: '体重必须填写' }
        // ]
        // tel: [
        //   { required: true, message: '手机号必须填写' },
        //   { message: '请填写正确11位手机号', type: 'string', pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/ }
        // ]
      },

      PsersontableData: [],
      personTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      }

    }
  },
  methods: {
    personSuffixClick () {
      this.$refs.personDown.togglePanel()
    },
    PsersonCellClickEvent ({ row, column }) {
      if (column.title !== '操作') {
        // this.sorderForm.clientPersonID = row.id

        // this.sorderForm.height = row.height + ''
        this.$emit('setClientPerson', { detailrow: this.detailrow, person: row })
        this.clientPersonText = row.label
        this.$refs.personDown.hidePanel()
      }
    },
    async personFocusEvent () {
      if (this.sorderStore.clientID === null) {
        this.$notify({
          message: '请先选择客户',
          type: 'error'
        })
        return
      }
      if (this.PsersontableData.length === 0) {
        await this.getPsersonbyQuery()
      }
      this.$refs.personDown.showPanel()
    },
    async personkeyupEvent (query) {
      if (!query) return
      const { value } = query || ''
      this.personTablePage.currentPage = 1
      if (this.sorderStore.clientID === null || this.sorderStore.clientID === '') {
        this.$notify({
          message: '请先选择客户',
          type: 'error'
        })
        return
      };
      await this.getPsersonbyQuery(value)
    },
    async getPsersonbyQuery (query = null, personId = null) {
      this.personLoading = true
      await this.$api.ActionRequest(this.api.sorderPserson, { ClientID: this.sorderStore.clientID, isActive: true, Id: personId, Text: query, MaxResultCount: this.personTablePage.pageSize, SkipCount: (this.personTablePage.currentPage - 1) * this.personTablePage.pageSize }).then(result => {
        this.personTablePage.total = result.totalCount
        this.PsersontableData = result.items
        this.personLoading = false
      })
    },
    async PsersonPageChange ({ currentPage, pageSize }) {
      this.personTablePage.pageSize = pageSize
      this.personTablePage.currentPage = currentPage
      await this.getPsersonbyQuery()
    }

  }
}
</script>

<style>
</style>
