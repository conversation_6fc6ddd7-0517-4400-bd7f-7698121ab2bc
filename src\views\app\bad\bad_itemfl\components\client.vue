<template>
  <div>
    <el-select v-model="clientID" :remote-method="remoteMethod" @change="clientChange" size="mini" remote filterable placeholder="客户" clearable>
      <el-option v-for="item in options" :key="item.value+item.label" :label="item.label" :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'client',
  data () {
    return {
      options: [],
      clientID: null
    }
  },
  props: {
    api: {
      type: Object,
      requited: true
    },
    modelElem: {
      type: Object,
      requited: true
    },
    clientSet: {
      type: Function,
      requited: true
    }
  },
  watch: {
    'modelElem.clientID': {
      handler: function (newval, oldval) {
        if (newval === null) {
          this.clientID = null
        } else {
          this.clientID = newval
          this.get('', newval)
        }
      }
    }
  },
  async created () {
    await this.get('', this.modelElem.clientID)
    this.clientID = this.modelElem.clientID
  },
  methods: {
    async get (query, id = null) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, gid: id, isNotG: true }).then(result => {
        this.options = result
      })
    },
    async remoteMethod (query) {
      await this.get(query, null)
    },
    clientChange (val) {
      this.clientSet(this.modelElem, val)
    }

  }
}
</script>

<style>
</style>
