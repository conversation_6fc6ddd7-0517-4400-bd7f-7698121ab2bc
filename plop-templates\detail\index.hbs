{{#if template}}
<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增
          </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            {{!-- <vxe-form-item field="suitSupply_OrderState"
              :item-render="{name: '$select', options: SuitSupply_OrderStateComboStore,props:{placeholder:'状态',clearable:true}}" />
            --}}
            {{!-- <vxe-form-item field="modelElemListID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式"
                  :remote-method="remoteMethod" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item> --}}
            <vxe-form-item field="text"
              :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>


    <vxe-table id='{{ properCase name }}DetailTable' ref='master_table' height="auto" :row-class-name="rowClassName"
      :data="tableData" :custom-config="{storage: true}" loading="tableLoading">
      {{!-- <vxe-table-column type="seq" width="60px"></vxe-table-column> --}}
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="code" title="编码" width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" width="100"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column title="操作" width="100" :fixed='tableOptFixed' align="center"
        v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage"
        :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount"
        :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" show-zoom resize
      destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}" />
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}" />
        <vxe-form-item title="顺序" field="sort" span="12"
          :item-render="{name: '$input', props: { type: 'number',clearable:true}}" />

        {{!-- <vxe-form-item field="typeProductID"
          :item-render="{name: '$select', options: ReportFormConfigTypeProductGroupComboStore,props:{clearable:true,placeholder:'商品描述'}}"></vxe-form-item>
        --}}
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}" />
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}" />
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>
{{/if}}

{{#if script}}

<script>
  import detailTableMixins from '@/mixins/detail_table_mixins/index'
  export default {
    name: '{{ properCase mastername }}Detail',
    mixins: [detailTableMixins],

    data() {
      return {
        searchForm: {
        },
        formData: {
          code: '',
          codeName: '',
          remark: '',
          isActive: true
        },
        formRules: {
          code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
          codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        },
        api: {
          get: '/mtm/{{name}}/get',
          add: '/mtm/{{name}}/adds',
          edit: '/mtm/{{name}}/updates',
          delete: '/mtm/{{name}}/deletes',
          //ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
        },
        //ModelElemListComboStoreByQuery
      }
    },
    async created() {
      await this.getCombStore()
      this.loadData({ id: this.form.id }).then(({ data }) => {
      })
      // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
    },
    methods: {
      async getCombStore() {
        // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
        //   this.departmentCombStore = result
        // })
      },
      //remoteMethod(query) {
      //this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
      //this.ModelElemListComboStoreByQuery = result
      //})
    }
  }
  }
</script>
{{/if}}

{{#if style}}
<style lang="scss" scoped>

</style>
{{/if}}