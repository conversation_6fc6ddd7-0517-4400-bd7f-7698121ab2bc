<template>
  <vxe-form :data="formData" :rules="formRules" title-align="right" title-width="100" v-loading="loading">
    <vxe-form-item title="客户订单号" field="number" span="24" :item-render="{}">
      <template #default="{data}">
        <el-input ref="numberInputref" v-model.trim="data.number" @keyup.enter.native="numberEvent()"></el-input>
      </template>
    </vxe-form-item>
    <vxe-form-item title="RFID" field="rfid" span="24" :item-render="{}">
      <template #default="{data}">
        <el-input ref="rfidInputref" v-model.trim="data.rfid" @keyup.enter.native="RfidEvent()"></el-input>
      </template>
    </vxe-form-item>
    <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
        <vxe-button @click="submitEvent()" status="primary">保存</vxe-button>
      </template>
    </vxe-form-item>
    <audio :src="successsrc" controls="controls" ref="sucessaudio" style="display: none;"></audio>
    <audio :src="errorsrc" controls="controls" ref="erroraudio" style="display: none;"></audio>
  </vxe-form>
</template>

<script>
import sucessaudio from '@/assets/voice/success.mp3'
import erroraudio from '@/assets/voice/error.mp3'
export default {
  name: 'Rfid',
  props: {
    loadData: {
      type: Function,
      default: null

    }
  },
  data () {
    return {
      loading: false,
      successsrc: sucessaudio,
      errorsrc: erroraudio,
      formData: {
        number: null,
        rfid: null
      },
      formRules: {
        number: [{ required: true, message: '请输入客户订单号' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        rfid: [{ required: true, message: '请输入RFID' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }]
      },
      api: {
        edit: '/mes/prd_productionplandetail/AddpendRfid'
      }
    }
  },
  methods: {
    submitEvent () {
      if (this.$utils.isEmpty(this.formData.number)) {
        this.$XModal.message({ message: '客户订单号不能为空', status: 'error' })
        this.numberFocus()
        return
      }
      if (this.$utils.isEmpty(this.formData.rfid)) {
        this.$XModal.message({ message: 'RFID不能为空', status: 'error' })
        this.rfidFocus()
        return
      }
      return new Promise((resolve, reject) => {
        this.loading = true
        this.$api.ActionRequest(this.api.edit, [this.formData]).then(result => {
          this.voiceplay(true)
          this.formData.number = null
          this.formData.rfid = null
          this.$XModal.message({ message: '绑定成功', status: 'success' })
          this.loading = false
          // 表格刷新
          if (this.loadData) {
            this.loadData()
            this.numberFocus()
          }
        }).catch(res => {
          this.numberFocus()
          this.voiceplay(false)
          this.loading = false
        })
      })
    },
    numberFocus () {
      var input = this.$refs.numberInputref
      if (input) {
        input.focus()
        input.select()
      }
    },
    rfidFocus () {
      var input = this.$refs.rfidInputref
      if (input) {
        input.focus()
        input.select()
      }
    },
    // 流水号输入
    numberEvent () {
      this.rfidFocus()
    },
    // rfid输入
    RfidEvent () {
      this.submitEvent().then(res => {
        this.numberFocus()
      })
    },
    voiceplay (b) {
      if (b) {
        this.$refs.sucessaudio.play()
      } else {
        this.$refs.erroraudio.play()
      }
    }
  }
}
</script>

<style>
</style>
