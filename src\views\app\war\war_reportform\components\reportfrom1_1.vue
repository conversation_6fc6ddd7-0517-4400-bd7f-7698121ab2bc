<template>
  <d2-container class="portrait">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <!-- <vxe-button status="warning" @click="printEvent('reportfrom')" icon="vxe-icon--print">打印</vxe-button> -->
          <!-- <vxe-button status="warning" @click="get() " icon="vxe-icon--print">打印</vxe-button> -->
          <vxe-button status="warning" @click="print" icon="vxe-icon--print">打印</vxe-button>
          <vxe-button :content="tablename" status="warning"  disabled></vxe-button>
        </template>
      </vxe-toolbar>

    </template>
    <div id="reportfrom" class="reportfrom">
      <div class="root">
        <pdf ref="pdf" :src="pdfurl"></pdf>
      </div>
    </div>
  </d2-container>
</template>

<script>
import reportfrommix from './reportfrom'
import pdf from 'vue-pdf'
// import ReportFormHead from './reportfromhead.vue'
export default {
  mixins: [reportfrommix],
  components: {
    pdf
    // ReportFormHead
  },

  data () {
    return {
      apiurl: '/fs/print/reportfrom1_1/pdf'
    }
  },
  created () { },
  methods: {

  }
}
</script>

<style  lang="scss" >

</style>
