<template>
  <el-card shadow="always" style="min-height:600px;" class="sorderdetailbody">
    <vxe-form v-loading="EditState" element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)">
      <vxe-form-item v-for="(bodylist,index) in BodyListData" :key="index" :title="bodylist.bodyListCode+':'+bodylist.bodyListName" title-width="120" :class="(index+1)===BodyListData.length?'lastbodylist':''" :item-render="{}">
        <template v-slot>
          <vxe-select v-model="bodylist.bodyID" placeholder="请选择" clearable @clear="bodylist.value=0" @change="({value})=>bodylistchange({bodylist,value})">
            <vxe-option v-for="item in bodylist.bodys" :key="item.id" :value="item.id" :label="item.code+':'+item.codeName">
              <div v-if="item.imagePath!==null&&item.imagePath!==''">
                <el-popover placement="right-end" :title="item.code+':'+item.codeName" width="400" trigger="hover">
                  <el-image style="width: 400px; height: 400px" :src="item.imagePath" fit="scale-down"></el-image>
                  <div class="table-td table-td1" slot="reference">
                    <span>{{ item.code }}:{{ item.codeName}}</span><i v-if="item.imagePath!==null&&item.imagePath!==''" style="color:#606266" class="el-icon-picture-outline"></i>
                  </div>
                </el-popover>
              </div>
              <div v-else>

                <div class="table-td table-td1">
                  <span>{{ item.code }}:{{ item.codeName }}</span>
                </div>
              </div>
            </vxe-option>
          </vxe-select>
          <el-popover ref="popover" placement="right-end" trigger="focus">
            <div slot style="color:red">
              <span>系统最大值:{{bodylist.max}} 系统最小值{{bodylist.min}}</span><br>
              <span>人工最大值:{{bodylist.allowMax}} 人工最小值{{bodylist.allowMin}}</span>
            </div>
            <el-input-number slot="reference" style="width:100px" v-model="bodylist.value" :precision="2" :step="0.1" :max="bodylist.allowMax" :min="bodylist.allowMin" size="mini" :disabled="bodylist.bodyID===null"></el-input-number>
          </el-popover>
        </template>
      </vxe-form-item>
    </vxe-form>
  </el-card>
</template>

<script>

import sorderEditState from './sordereditstate'
import { mapState } from 'vuex'
import { cloneDeep } from 'lodash'
export default {
  name: 'SorderDetailBody',
  mixins: [sorderEditState],
  props: {
    SorderDetailModel: {
      type: Object,
      requited: true
    },
    sorderStore: {
      type: Object,
      default: null
    }

  },
  watch: {
    SorderDetailModel: {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal.modelId !== null) {
          // console.log(`特体---版型ID${newVal}`)
          // this.get(newVal.modelId)
        }
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      timer: '',
      BodyListData: [],
      api: {
        get: '/mtm/oDM_SorderDetailBody/get',
        sorderDetailBodyEdit: '/mtm/oDM_SorderDetailBody/modify'
      }
    }
  },

  created () {
    // if (this.SorderDetailModel.modelID) {
    //   this.get(this.SorderDetailModel.modelID)
    // }
  },
  methods: {
    clientPersonSizeChange () {
      this.get(this.SorderDetailModel.modelId)
    },
    get (modelid) {
      // this.EditState = true
      this.BodyListData = []
      this.$api.ActionRequest(this.api.get, { modelId: modelid, sorderDetailModelID: this.SorderDetailModel.sorderDetailModelId }).then(result => {
        this.BodyListData = result
        // this.EditState = false
      })
    },
    bodylistchange ({ value, bodylist }) {
      if (value === null) {
        bodylist.value = null
        bodylist.bodyID = null
      } else {
        var dto = bodylist.bodys.GetFirstElement('id', value)
        if (dto != null) {
          bodylist.allowMax = dto.allowMax
          bodylist.allowMin = dto.allowMin
          bodylist.max = dto.max
          bodylist.min = dto.min
          if (bodylist.allowMax <= bodylist.value || bodylist.value === 0) {
            bodylist.value = bodylist.max
          }
          if (bodylist.allowMin >= bodylist.value || bodylist.value === 0) {
            bodylist.value = bodylist.min
          }
        }
      }
    },
    change () {
      this.sorderDetailBodySave().then(res => {
        if (res) {
          this.get(this.SorderDetailModel.modelId)
        }
      })
    },
    clear () {
      // var b = this.sorderDetailBodySave();
      // if (b) {
      //   this.get(this.SorderDetailModel.modelId)
      // }
    },
    async sorderDetailBodySave () {
      return new Promise((resolve, reject) => {
        if (this.EditState) {
          return resolve(this.EditState)
        }
        var list = cloneDeep(this.BodyListData)
        list = list.filter(item => {
          if (item.bodyListID !== null) {
            delete item.bodys
            return item
          }
        })
        console.log('特体保存')
        this.$api.ActionRequest(this.api.sorderDetailBodyEdit, list).then(async res => {
          return resolve(true)
        }).catch(() => {
          return resolve(false)
        })
      })
    }
  }
}
</script>

<style>
.sorderdetailbody {
  margin-bottom: 200px;
}
</style>
