<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="goto">跳转订单列表</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='SymWorkbenchMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="code" title="编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="workbenchTypeText" title="类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="workbenchGroupText" title="分类" sortable width="100"></vxe-table-column>
      <vxe-table-column field="receiverObjectText" title="接受对象" sortable width="100"></vxe-table-column>
      <vxe-table-column field="url" title="跳转链接" sortable width="100"></vxe-table-column>
      <vxe-table-column field="parameters" title="参数" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="跳转测试" width="100" align="center">
        <template v-slot="{ row }">
          <vxe-button status="warning" @click="goto(row)" v-if="menuAction.allowEdit">测试</vxe-button>
        </template>
      </vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="分类" field="workbenchGroup" span="12" :item-render="{name: '$select', options: WorkbenchGroupComboStore}"></vxe-form-item>
        <vxe-form-item title="类型" field="workbenchType" span="12" :item-render="{name: '$select', options: WorkbenchTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="接收对象" field="receiverObject" span="12" :item-render="{name: '$select', options: ReceiverObjectComboStore}"></vxe-form-item>
        <vxe-form-item title="跳转链接" field="url" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="参数" field="parameters" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',},props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" show-close size='30%' :modal="false" :append-to-body="false">
      <workbench-receiver-detail :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import WorkbenchReceiverDetail from './detail.vue'
export default {
  name: 'sym_workbench',
  mixins: [masterTableMixins],
  components: {
    WorkbenchReceiverDetail
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        workbenchType: 1,
        workbenchGroup: null,
        receiverObject: 1,
        remark: '',
        isActive: true,
        parameters: null,
        url: null
      },
      drawer: false,
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 35, message: '长度在 2 到 35 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 35, message: '长度在 2 到 35 个字符' }],
        workbenchType: [{ required: true, message: '请选择类型' }],
        workbenchGroup: [{ required: true, message: '请选择分类' }],
        receiverObject: [{ required: true, message: '请选择接受对象' }],
        parameters: [{ required: true, message: '请输入参数' }]
      },
      api: {
        get: '/mtm/sym_workbench/get',
        add: '/mtm/sym_workbench/adds',
        edit: '/mtm/sym_workbench/updates',
        delete: '/mtm/sym_workbench/deletes',
        WorkbenchGroupComboStore: '/mtm/combo/WorkbenchGroupComboStore',
        WorkbenchTypeComboStore: '/mtm/combo/WorkbenchTypeComboStore',
        ReceiverObjectComboStore: '/mtm/combo/ReceiverObjectComboStore'
      },
      WorkbenchGroupComboStore: [],
      WorkbenchTypeComboStore: [],
      ReceiverObjectComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.WorkbenchGroupComboStore).then(result => {
        this.WorkbenchGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.WorkbenchTypeComboStore).then(result => {
        this.WorkbenchTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ReceiverObjectComboStore).then(result => {
        this.ReceiverObjectComboStore = result
      })
    },
    goto (row) {
      // var params = {
      //   name: 'odm_sorderlist',
      //   params: {
      //     stateIDs: [40],
      //     refresh: true
      //   }
      // }
      var params = JSON.parse(row.parameters)
      // console.log(params)
      if (params === null) {
        return
      }
      this.$message({
        message: '正在跳转请稍后.....',
        type: 'success'
      })
      var p = {
        name: row.url,
        params: JSON.parse(row.parameters)
      }
      setTimeout(() => {
        this.$router.push(p)
      }, 500)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
