<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelElemListID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod1" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelelemvariant_master_table' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="modelElemListText" title="款式" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemText" title="款式明细" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElem1Text" title="款式明细1" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="variant" title="变体" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="value" title="值" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式明细" field="modelElemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod2" clearable>
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式明细1" field="modelElemID1" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemID1" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod3" clearable>
              <el-option v-for="item in ModelElemComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="变体" field="variant" span="12" :item-render="{}"> <vxe-input v-model="selectRow.variant" placeholder="请输入变体" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="值" field="remark" span="12" :item-render="{}"> <vxe-input v-model="selectRow.value" placeholder="请输入值" clearable type="number"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{}"> <template #default>
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="selectRow.remark" clearable>
            </el-input>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modelelemvariant', // 款式明细->变体
  mixins: [masterTableMixins],
  components: {
  },
  data() {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        variant: '',
        value: '',
        modelElemID: null,
        modelElemID1: null
      },
      formRules: {
        modelElemID: [{ required: true, message: '请选择款式明细' }],
        variant: [{ required: true, message: '变体' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modelelemvariant/get',
        add: '/mtm/mom_modelelemvariant/adds',
        edit: '/mtm/mom_modelelemvariant/updates',
        delete: '/mtm/mom_modelelemvariant/deletes',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery'
      },
      footerCompanyInfo: false,
      ModelElemListComboStoreByQuery: [],
      ModelElemComboStoreByQuery: [],
      ModelElemComboStoreByQuery1: []
    }
  },
  async created() {
    await this.getCombStore()
    this.remoteMethod1()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore() {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    },
    remoteMethod1(query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    remoteMethod2(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    remoteMethod3(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
      })
    },
    // 编辑
    async editEvent(row) {
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent(row, code = false, codeName = false) {
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery1 = result
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (!code && this.$utils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (!codeName && this.$utils.has(this.selectRow, 'codeName')) {
        this.selectRow.codeName = null
      }
      this.showEdit = true
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
