import Vue from 'vue'
// import XEUtils from 'xe-utils'
import VXETable from 'vxe-table'
import 'vxe-table/lib/index.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import ExcelJS from 'exceljs'
// 方式1：NPM 安装，注入 ExcelJS 对象
VXETable.use(VXETablePluginExportXLSX, {
  ExcelJS
})

// 完整导入 UI 组件库
// import { VxeFormGroup } from 'vxe-pc-ui'
// import 'vxe-pc-ui/lib/style.css'
// 全局尺寸
// medium, small, mini
VXETable.setConfig({
  size: 'mini',
  table: {
    exportConfig: {
      types: ['csv', 'html', 'xml', 'txt']
      //     isPrint: true,
      //     modes: ['current', 'selected']
    },
    showHeader: true, // 是否显示表头
    keepSource: false,
    // 表格动画效果开关（关闭后视觉效果更快）
    animat: false,
    // 可以设置为 true 来避免初始化渲染时的闪动
    cloak: true,
    //   delayHover: 250,//当表格发生拖动、滚动...等行为时，至少多少毫秒之后才允许触发 hover 事件
    showOverflow: true,
    showHeaderOverflow: true,
    showFooterOverflow: true,
    size: 'mini',
    resizable: true,
    autoResize: true,
    // 是否带有斑马纹
    stripe: false,
    border: 'full',
    highlightHoverRow: true,
    highlightCurrentRow: true,
    highlightHoverColumn: true,
    highlightCurrentColumn: true,
    // rowClassName:'row-isActive-false',
    columnKey: true,
    align: 'left',
    // customConfig: {
    //   storage: true,
    //   showStatus: true
    // }
    //   round: false, //是否为圆角边框
    //   radioConfig: {
    //     trigger: 'default'
    //   },
    checkboxConfig: {
      trigger: 'default',
      highlight: true
    }
    //   sortConfig: {
    //     remote: false,
    //     trigger: 'default',
    //     orders: ['asc', 'desc', null],
    //     sortMethod: null
    //   },
    //   filterConfig: {
    //     remote: false,
    //     filterMethod: null
    //   },
    //   expandConfig: {
    //     trigger: 'default'
    //   },
    //   treeConfig: {
    //     children: 'children',
    //     hasChild: 'hasChild',
    //     indent: 20
    //   },
    //   tooltipConfig: {
    //     theme: 'dark',
    //     enterable: false
    //   },
    //   contextMenu: {
    //     visibleMethod () {}
    //   },
    //   rowId: '_XID', // 行数据的唯一主键字段名
    //   editConfig: {
    //     mode: 'cell',
    //     showAsterisk: true
    //   },
    //   importConfig: {
    //     modes: ['insert', 'covering']
    //   },

    //   scrollX: {
    //     gt: 60
    //   },
    //   scrollY: {
    //     gt: 100
    //   }
  },
  pager: {
    size: 'mini',
    autoHidden: false,
    perfect: false,
    pageSize: 50,
    pagerCount: 7,
    pageSizes: [10, 20, 50, 100, 200, 500, 1000],
    layouts: ['PrevJump', 'PrevPage', 'Jump', 'PageCount', 'NextPage', 'NextJump', 'Sizes', 'Total']
  },
  grid: {
    size: 'nimi',
    align: 'left'
  },
  modal: {
    showZoom: true,
    dblclickZoom: true
    // size: null,
    // minWidth: 340,
    // minHeight: 200,
    // lockView: true,
    // mask: true,
    // duration: 3000,
    // marginSize: 0,
    // dblclickZoom: true,
    // showTitleOverflow: true
    // storage: false
  },
  // 全局 zIndex 起始值，如果项目的的 z-index 样式值过大时就需要跟随设置更大，避免被遮挡
  // zIndex: 100,
  // 版本号，对于某些带数据缓存的功能有用到，上升版本号可以用于重置数据
  version: 1
})
// VxeUI.setConfig({
//   // size: null, // 全局尺寸
//   // zIndex: 999, // 全局 zIndex 起始值，如果项目的的 z-index 样式值过大时就需要跟随设置更大，避免被遮挡；新版本可以使用 dom-zindex 共享配置
//   // version: 1, // 版本号，对于某些带数据缓存的功能有用到，上升版本号可以用于重置数据

//   // alert: {},
//   // anchor: {},
//   // anchorLink: {},
//   // breadcrumb: {
//   //   separator: '/'
//   // },
//   // breadcrumbItem: {},
//   // button: {
//   //   trigger: 'hover'
//   // },
//   // buttonGroup: {},
//   // card: {
//   //   border: true,
//   //   padding: true
//   // },
//   // checkbox: {},
//   // checkboxGroup: {},
//   // col: {},
//   // colgroup: {},
//   // collapse: {},
//   // collapsePane: {},
//   // column: {},
//   // datePicker: {
//   //   // size: null,
//   //   // transfer: false
//   //   // parseFormat: 'yyyy-MM-dd HH:mm:ss.SSS',
//   //   // labelFormat: '',
//   //   // valueFormat: '',
//   //   startDate: new Date(1900, 0, 1),
//   //   endDate: new Date(2100, 0, 1),
//   //   startDay: 1,
//   //   selectDay: 1
//   // },
//   // drawer: {
//   //   // size: null,
//   //   position: 'right',
//   //   showHeader: true,
//   //   lockView: true,
//   //   mask: true,
//   //   showTitleOverflow: true,
//   //   showClose: true,
//   //   padding: true
//   // },
//   // form: {
//   //   // preventSubmit: false,
//   //   // size: null,
//   //   // colon: false,
//   //   validConfig: {
//   //     showMessage: true,
//   //     autoPos: true
//   //   },
//   //   tooltipConfig: {
//   //     enterable: true
//   //   },
//   //   titleAsterisk: true,
//   //   titleOverflow: false
//   // },
//   // formDesign: {
//   //   height: 400,
//   //   showPc: true
//   // },
//   // formGather: {},
//   // formItem: {},
//   // formView: {},
//   // grid: {},
//   // icon: {},
//   // image: {},
//   // imagePreview: {},
//   // input: {
//   //   // size: null,
//   //   // transfer: false
//   //   // parseFormat: 'yyyy-MM-dd HH:mm:ss.SSS',
//   //   // labelFormat: '',
//   //   // valueFormat: '',
//   //   startDate: new Date(1900, 0, 1),
//   //   endDate: new Date(2100, 0, 1),
//   //   startDay: 1,
//   //   selectDay: 1,
//   //   digits: 2,
//   //   controls: true
//   // },
//   // layoutAside: {},
//   // layoutBody: {},
//   // layoutContainer: {},
//   // layoutFooter: {},
//   // layoutHeader: {},
//   // link: {
//   //   underline: true
//   // },
//   // listDesign: {
//   //   height: 400,
//   //   showPc: true
//   // },
//   // list: {
//   //   // size: null,
//   //   scrollY: {
//   //     enabled: true,
//   //     gt: 100
//   //     // oSize: 0
//   //   }
//   // },
//   // loading: {},
//   // modal: {
//   //   // size: null,
//   //   top: 16,
//   //   showHeader: true,
//   //   minWidth: 340,
//   //   minHeight: 140,
//   //   lockView: true,
//   //   mask: true,
//   //   duration: 3000,
//   //   marginSize: 0,
//   //   dblclickZoom: true,
//   //   showTitleOverflow: true,
//   //   showClose: true,
//   //   padding: true,
//   //   draggable: true,
//   //   showConfirmButton: null,
//   //   // storage: false,
//   //   storageKey: 'VXE_MODAL_POSITION'
//   // },
//   // numberInput: {
//   //   // size: null,
//   //   // transfer: false
//   //   digits: 2,
//   //   controls: true
//   // },
//   // optgroup: {},
//   // option: {},
//   // pager: {
//   //   pageSizePlacement: 'top'
//   //   // size: null,
//   //   // autoHidden: false,
//   //   // perfect: true,
//   //   // pageSize: 10,
//   //   // pagerCount: 7,
//   //   // pageSizes: [10, 15, 20, 50, 100],
//   //   // layouts: ['PrevJump', 'PrevPage', 'Jump', 'PageCount', 'NextPage', 'NextJump', 'Sizes', 'Total']
//   // },
//   // print: {},
//   // passwordInput: {},
//   // printPageBreak: {},
//   // pulldown: {},
//   // radio: {
//   //   strict: true
//   // },
//   // radioButton: {
//   //   strict: true
//   // },
//   // radioGroup: {
//   //   strict: true
//   // },
//   // row: {},
//   // select: {
//   //   multiCharOverflow: 8
//   // },
//   // switch: {},
//   // tabPane: {},
//   // table: {},
//   // tabs: {},
//   // textarea: {},
//   // toolbar: {},
//   // tip: {},
//   // tooltip: {
//   //   // size: null,
//   //   trigger: 'hover',
//   //   theme: 'dark',
//   //   enterDelay: 500,
//   //   leaveDelay: 300
//   // },
//   // tree: {
//   //   indent: 20,
//   //   radioConfig: {
//   //     strict: true
//   //   }
//   // },
//   // treeSelect: {},
//   // upload: {
//   //   mode: 'all',
//   //   imageTypes: ['jpg', 'jpeg', 'png', 'gif'],
//   //   showList: true
//   // }
// })
// 核心插件
VXETable.formats.mixin({
  // // 格式化性别
  // formatSex({ cellValue }) {
  //   return cellValue ? (cellValue === true ? '男' : '女') : ''
  // },
  // // 格式化下拉选项
  // formatSelect({ cellValue }, list) {
  //   const item = list.find(item => item.value === cellValue)
  //   return item ? item.label : ''
  // },
  // // 格式化日期，默认 yyyy-MM-dd HH:mm:ss
  // formatDate({ cellValue }, format) {
  //   return XEUtils.toDateString(cellValue, format || 'yyyy-MM-dd HH:mm:ss')
  // }
  //   // 格式金额，默认2位数
  //   formatAmount ({ cellValue }, digits) {
  //     return XEUtils.commafy(cellValue, { digits: digits || 2 })
  //   },
  //   // 格式化银行卡，默认每4位隔开
  //   formatBankcard ({ cellValue }) {
  //     return XEUtils.commafy(cellValue, { spaceNumber: 4, separator: ' ' })
  //   },
  //   // 四舍五入,默认两位数
  //   formatFixedNumber ({ cellValue }, digits) {
  //     return XEUtils.toNumber(cellValue).toFixed(digits || 2)
  //   },
  //   // 截取小数,默认两位数
  //   formatCutNumber ({ cellValue }, digits) {
  //     return XEUtils.toFixedString(cellValue, digits || 2)
  //   },
  //   // 转换 moment 类型为字符串
  //   toMomentString ({ cellValue }, format) {
  //     return cellValue ? cellValue.format(format) : ''
  //   }
})
// Vue.use(VxeFormGroup)
Vue.use(VXETable)

// 给 vue 实例挂载全局窗口对象，属性名称随意定义，例如：$XModal
Vue.prototype.$MTMmodal = VXETable.modal
Vue.prototype.$XModal = VXETable.modal
Vue.prototype.$XPrint = VXETable.print
