<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button @click="exportSelectEvent" v-if="menuAction.allowPrint">导出选中</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="detailType" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.productWarehouseDetailType" placeholder="类别" clearable>
                  <vxe-option v-for="item in ProductWarehouseDetailTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item> <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarProductwarehousedetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="shipmentNumber" title="发货单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientShopCode" title="店铺" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientPersonName" title="顾客" sortable width="100"></vxe-table-column>
      <vxe-table-column field="productWarehouseDetailTypeText" title="状态" sortable width="100"></vxe-table-column>
      <vxe-table-column field="despatchFinished" title="是否发货" :formatter="formatBool" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="logisticsCompany" title="快递公司" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipmentsNumber" title="发货单号" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" title="录入数据" width="800" :top='8' resize destroy-on-close :loading="submitLoading">
      <el-tabs v-model="activeName">
        <el-tab-pane label="扫描枪录入" name="first">
          <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100">
            <vxe-form-item title="流水号" field="serialNumber" span="24" :item-render="{}">
              <template #default>
                <el-input ref="numinputref" v-model.trim="selectRow.serialNumber" @keyup.enter.native="submitEvent(false)"></el-input>
              </template>
            </vxe-form-item>
            <vxe-form-item title="类型" field="productWarehouseDetailType" span="12" :item-render="{name: '$select', options: ProductWarehouseDetailTypeComboStore}"></vxe-form-item>
            <vxe-form-item title="扫描计数" field="count" span="12" :item-render="{}"> <template #default>
                <span><strong>{{selectRow.count}}</strong></span>
              </template>
            </vxe-form-item>
          </vxe-form>
        </el-tab-pane>
        <el-tab-pane label="手动录入" name="second">
          <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
            <vxe-form-item title="流水号" field="serialNumber" span="12" :item-render="{name: 'input'}"></vxe-form-item>
            <vxe-form-item title="类型" field="productWarehouseDetailType" span="12" :item-render="{name: '$select', options: ProductWarehouseDetailTypeComboStore}"></vxe-form-item>
            <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
            <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
                <vxe-button type="submit" status="primary">保存</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </el-tab-pane>
      </el-tabs>
    </vxe-modal>
    <audio :src="successsrc" controls="controls" ref="sucessaudio" style="display: none;"></audio>
    <audio :src="errorsrc" controls="controls" ref="erroraudio" style="display: none;"></audio>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import sucessaudio from '@/assets/voice/success.mp3'
import erroraudio from '@/assets/voice/error.mp3'
import { isEmpty } from 'lodash'
export default {
  name: 'war_productwarehousedetail',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      successsrc: sucessaudio,
      errorsrc: erroraudio,
      formData: {
        productWarehouseID: null,
        serialNumber: null,
        remark: '',
        isActive: true,
        productWarehouseDetailType: 1,
        count: 0
      },
      activeName: 'first',
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mtm/war_productwarehousedetail/get',
        add: '/mtm/war_productwarehousedetail/adds',
        delete: '/mtm/war_productwarehousedetail/deletes',
        ProductWarehouseDetailTypeComboStore: '/mtm/combo/ProductWarehouseDetailTypeComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'

      },
      clientComboStoreByQuery: [],
      ProductWarehouseDetailTypeComboStore: []
    }
  },
  mounted () {
    this.timer = setInterval(() => {
      var numinput = this.$refs.numinputref
      if (numinput) {
        numinput.focus()
      }
    }, 1000)
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ProductWarehouseDetailTypeComboStore).then(result => {
        this.ProductWarehouseDetailTypeComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    // 提交
    submitEvent (close = true) {
      const loading = this.$loading({
        lock: true,
        text: '请稍后.....',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (isEmpty(this.selectRow.id) || this.selectRow.id === undefined || this.selectRow.id === null || this.selectRow.id === '00000000-0000-0000-0000-000000000000') {
        this.$api.ActionRequest(this.api.add, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '新增成功', status: 'success' })
          loading.close()
          this.loadData()
          if (close) {
            this.showEdit = false
          } else {
            this.voiceplay(true)
            this.selectRow.count += 1
            this.selectRow.serialNumber = null
          }
        }).catch(() => {
          if (!close) {
            this.voiceplay(false)
          }
          loading.close()
          this.selectRow.serialNumber = null
        })
      }
    },
    voiceplay (b) {
      if (b) {
        this.$refs.sucessaudio.play()
      } else {
        this.$refs.erroraudio.play()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
