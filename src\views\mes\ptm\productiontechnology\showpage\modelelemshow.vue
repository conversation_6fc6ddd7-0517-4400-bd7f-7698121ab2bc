<template>
  <d2-container class="modelelemshow">
    <template slot="header">
      <div style="position: absolute;    display: flex;">
        <h2 style="float: left;"> 工艺显示:</h2>
        <h2>{{ productstationconfigData.code }}:{{ productstationconfigData.codeName }}</h2>
      </div>
      <div style="position: absolute;   display: flex;right:20px">
        <slot name="scanqty" />
      </div>
      <div class="sordernum">
        <slot name="numinput" />
      </div>
      <show-header :form="form" />
    </template>
    <template>
      <d2-container>
        <split-pane :min-percent='10' :default-percent='35' split="vertical">
          <template slot="paneL">
            <model-elem :form="form" ref="modelelem" />
          </template>
          <template slot="paneR">
            <template>
              <div>
                <sorder-size v-if="sorderSizeSet()" :form="form" ref="sordersize" />
                <template v-if="form !== null && form.sorderType === 0">
                  <!-- 返修数据 -->
                  <sorder-repair-part-price :form="form" />
                  <sorder-repair-image :form="form" />
                </template>
                <template v-else>
                  <template v-if="form !== null && form.sorderType === 30">
                    <!-- 研发单图片 -->
                    <research-development-image :form="form" :productstationconfigData="productstationconfigData" />
                  </template>
                  <template v-else-if="form !== null && form.isResearchDevelopmentSource">
                    <research-development-image-1 :form="form" :productstationconfigData="productstationconfigData" />
                  </template>
                  <template v-else>
                    <model-elem-image :form="form" :productstationconfigData="productstationconfigData" ref="modelelemimage" />
                  </template>
                </template>

              </div>
            </template>
          </template>
        </split-pane>
      </d2-container>
    </template>

  </d2-container>
</template>

<script>
import defaultset from '../components/index'
import showHeader from '../components/showheader'
import ModelElem from '../components/modelelem'
import ModelElemImage from '../components/modelelemimage'
import SorderSize from '../components/sordersize'
import SorderRepairImage from '../components/sorderrepairimage.vue'
import SorderRepairPartPrice from '../components/sorderrepairpartprice.vue'
import ResearchDevelopmentImage from '../components/ResearchDevelopmentImage.vue'
import ResearchDevelopmentImage1 from '../components/ResearchDevelopmentImage1.vue'
export default {
  name: 'ModelElemShow', // 工艺显示
  mixins: [defaultset],
  components: {
    showHeader,
    ModelElem,
    ModelElemImage,
    SorderSize,
    SorderRepairImage,
    SorderRepairPartPrice,
    ResearchDevelopmentImage,
    ResearchDevelopmentImage1
  },
  props: {
    form: {
      type: Object
    }
  },
  data () {
    return {
      number: ''
    }
  },
  computed: {

  },
  created () {
    console.log(this.form)
  },
  methods: {
    sorderSizeSet () {
      var b = this.productstationconfigData.isShowSorderSize
      if (b === null || b === undefined) {
        return true
      } else {
        return b
      }
    }
  }

}
</script>

<style lang='scss'>
.modelelemshow {
  .sordernum {
    // position: absolute;
    // top: 0px;
    line-height: 50px;
    margin: 0 auto;
    text-align: center;
    width: 300px;
  }

  //   /deep/.numrule input::-webkit-outer-spin-button,
  //   /deep/.numrule input::-webkit-inner-spin-button {
  //     -webkit-appearance: none !important;
  //   }
  //   /deep/.numrule input[type="number"] {
  //     -moz-appearance: textfield;
  //   }
  .d2-container-full {
    .d2-container-full__body:first-of-type {
      // height: calc(100vh - 590px);
    }
  }
}
</style>
