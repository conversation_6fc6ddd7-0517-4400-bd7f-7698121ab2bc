
import pdf from 'vue-pdf'
export default {
  props: {
    form: {
      type: Object,
      required: true
    },
    type: {
      default: 1
    },
    tablename: {
      type: String
    }
  },
  errorCaptured (msg, vm, trace) {
    return false
  },
  components: {
    pdf
  },
  data () {
    return {
      printStyle: '@page { size: portrait;}',
      mtmpai: process.env.VUE_APP_API,
      apiurl: null,
      pageNum: 1,
      pageTotalNum: 1,
      pageRotate: 0,
      // 加载进度
      loadedRatio: 0,
      curPageNum: 0
    }
  },
  created () {
    console.log(this.apiurl)
  },
  computed: {
    pdfurl () {
      var api = this.mtmpai.replace('/api/', '')
      var url = api + this.apiurl + '?id=' + this.form.id + '&.pdf'

      return url
    }
  },
  methods: {
    print () {
      window.open(this.pdfurl)
    }
  }
}
