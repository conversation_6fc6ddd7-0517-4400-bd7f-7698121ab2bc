<template>
  <d2-container class="sorderlist">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent('odm_sordern')" v-if="menuAction.allowAdd" content="快速下单">
            <!-- <template #dropdowns>
              <vxe-button type="text" status="success" content="快速下单(图)" @click="insertEvent('odm_sordern')"></vxe-button>
            </template> -->
          </vxe-button>
          <!-- <el-button>打印</el-button> -->
          <el-button type="primary" size="mini" @click="printEvent">预览</el-button>
          &nbsp;&nbsp;
          <vxe-button type="primary" size="mini" @click="printPlusEvent" content="批量预览">
            <template #dropdowns>
              <vxe-button status="primary" size="mini" @click="printPlusEvent({DoubleFaced:true})">(双面)批量预览</vxe-button>
            </template>
          </vxe-button>
          <!-- <el-button type="success" size="mini" @click="editEvent('odm_sordermodify')">编辑</el-button> -->
          <!-- <el-button type="success" size="mini" @click="editEvent('odm_sorderpromodify')" v-if="menuAction.allowEdit">编辑(团装)</el-button> -->
          &nbsp;
          <vxe-button status="success" size="mini" @click="editEvent('odm_sordernmodify')" v-if="menuAction.allowEdit" content="编辑">
            <!-- <template #dropdowns>
              <vxe-button type="text" status="success" content="编辑(图)" @click="editEvent('odm_sordernmodify')"></vxe-button>
            </template> -->
          </vxe-button>
          &nbsp;
          <!-- <el-button type="info" size="mini" v-if="info.userType!==2">驳回</el-button> -->
          <el-button type="danger" size="mini" @click="deletesEvent" v-if="menuAction.allowDelete">删除</el-button>
          <el-button type="warning" size="mini" v-if="menuAction.allowAdd" @click="deepCloneEvent">订单复制</el-button>
          <!-- <vxe-button status="perfect" @click="CadMake" v-if="menuAction.allowAdd">CAD制版</vxe-button>-->
          <!-- <vxe-button  status="warning" @click="getSorderCadlayout()" v-if="menuAction.allowAdd"></vxe-button> -->
          <el-button type="warning" size="mini" @click="createCad">获取精准耗量</el-button>
          <el-button type="warning" size="mini" @click="getSorderCadlayout()" v-if="menuAction.allowAdd&&info.userType!==2">智能制版</el-button>
          <el-button type="warning" size="mini" @click="getBomItem()" v-if="menuAction.allowAdd&&info.userType!==2">Bom清单</el-button>
          <el-button type="warning" size="mini" @click="salesBillShowEvent()">账单明细</el-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom class="mtmtoolbar">
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="dates" :item-render="{}"> <template #default>
                <!-- <el-date-picker size="mini" v-model="searchForm.dates" type="daterange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker> -->
                <el-date-picker v-model="searchForm.dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini" style="width:280px">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="stateIDs" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.stateIDs" placeholder="节点" multiple collapse-tags clearable size="mini" style="width:190px">
                  <el-option v-for="item in SorderStatusComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <template v-if="!(info.userType==2)">
              <vxe-form-item field="clientID" :item-render="{}"> <template #default="{ data }">
                  <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                    <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>
            </template>
            <vxe-form-item field="personID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.personID" filterable placeholder="顾客" size="mini" remote reserve-keyword :remote-method="remoteMethod5" clearable style="width:110px">
                  <el-option v-for="item in ClientPersonComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="createID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.createID" filterable placeholder="创单人" size="mini" remote reserve-keyword :remote-method="remoteMethod6" clearable style="width:110px">
                  <el-option v-for="item in UserComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model.trim="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable style="width:150px">
                  <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable style="width:110px">
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <el-button type="success" size="mini" @click="searchEvent()" v-preventReClick="1000">查询</el-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomSewbase_master_table' ref='master_table' @cell-click='tableCellClick' :checkbox-config="{highlight:false}" :custom-config="{storage: true}" @cell-dblclick="cellDblClick" :stripe="false" :highlight-hover-row="false" :highlight-current-row="false" :highlight-current-column="false" :row-class-name="rowClassName" :loading="tableLoading" :height="TableHeight" :data="tableData">
      <!-- <vxe-table-column type="expand" width="35" class-name="expandclass">
        <template v-slot:content="{ row, rowIndex }">
          <detail-list :sorder='row' :rowIndex='rowIndex' />
        </template>
      </vxe-table-column> -->
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column title="操作" width="50" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <!-- <vxe-button type="text" icon="fa fa-file-pdf-o" v-if="menuAction.allowEdit" @click="printEvent(row)"></vxe-button> -->
          <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)"></vxe-button> -->
          <!-- <vxe-button type="text" icon="el-icon-s-flag" ></vxe-button> -->
          <vxe-button type="text" icon="el-icon-location-information" @click="showOrderState(row)"></vxe-button>
          <!-- <el-button  size="mini">进度查看</el-button> -->
        </template>
      </vxe-table-column>
      <vxe-table-column field="statusText" title="订单状态" width="100"></vxe-table-column>
      <vxe-table-column field="isManualOrder" title="是否手工单?" width="100">
        <template v-slot="{ row }">
          <span v-if="row.isManualOrder" style="color:red;background-color: white;">手工单</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="code" title="订单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientText" title="客户" width="100" sortable></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="tel" title="联系电话" width="100" sortable></vxe-table-column>
      <vxe-table-column field="address" title="地址" width="100" sortable></vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="业务类型" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="currencyText" title="货币" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="exchangeRate" title="汇率" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="detailInfo" title="明细" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="clientPerson" title="顾客" show-overflo width="100" sortable> </vxe-table-column>
      <vxe-table-column field="clientShop" title="店铺" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="itemText" title="面料" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="itemConsumption" title="面料耗量" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="itemConsumptionL" title="里布耗量" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="layoutMethodText" title="排料方式" show-overflo width="100" sortable></vxe-table-column>
      <vxe-table-column field="finalTextureText" title="纹理" show-overflo width="100" sortable></vxe-table-column>
      <!-- <vxe-table-column field="halfFitting" title="半成品试衣" :formatter="formatBool" show-overflo width="100"></vxe-table-column> -->

      <vxe-table-column field="createBy" title="创单人" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="isUrgent" title="是否加急" width="100" sortable>
        <template v-slot="{ row }">
          <span v-if="row.isUrgent" style="color:red">加急</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="deliveryDate" title="期望交期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>

      <vxe-table-column field="remark" title="备注" width="100"></vxe-table-column>
      <vxe-table-column field="checkOn" title="客服审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="technicianCheckDate" title="技术审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="checkBy" title="审核人" width="100"></vxe-table-column>
      <vxe-table-column field="sorderFromText" title="订单来源" width="100"></vxe-table-column>
      <vxe-table-column field="shipmentText" title="发货状态" width="100"></vxe-table-column>
      <vxe-table-column field="mesProductionPlanStateText" title="MES状态" width="100"></vxe-table-column>
      <vxe-table-column field="productionTeamName" title="MES最新进度" width="100">
        <template v-slot="{ row }">
          <span v-if="row.mesSchedulesDto===null||row.mesSchedulesDto.length<=0">{{row.mEsLastFinishedProductionSchedule}}</span>
          <el-popover v-else placement="top-start" title="生产进度" width="800" trigger="hover">
            <vxe-table align="center" :data="row.mesSchedulesDto">
              <vxe-table-column field="groupName" title="类别" width="80px"></vxe-table-column>
              <vxe-table-column field="serialNumber" title="流水号" width="120px"></vxe-table-column>
              <vxe-table-column field="workSecationCodeName" title="工段" width="120px"></vxe-table-column>
              <vxe-table-column field="productionTeamName" title="班组" width="120px"></vxe-table-column>
              <vxe-table-column field="productionStationName" title="工位" width="120px"></vxe-table-column>
              <vxe-table-column field="stateText" title="状态" width="80px"></vxe-table-column>
              <vxe-table-column field="modifyOn" title="时间" :formatter="formatDate" width="135px"></vxe-table-column>
            </vxe-table>
            <span slot="reference">{{row.productionTeamName}}</span>
          </el-popover>

        </template>
      </vxe-table-column>
      <vxe-table-column field="mesAsyncState" title="MES同步状态" :formatter="formatBool" width="100"></vxe-table-column>
      <vxe-table-column field="modelsText" title="版型" width="150"></vxe-table-column>
    </vxe-table>
    <template slot="footer">

      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:left>
          <span style="color:red;font-weight: 900;">已选择 {{selectCheckRows}}</span>
        </template>
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <!-- 智能制版 -->
    <vxe-modal v-model="cadLayoutShow" v-if="cadLayoutShow" :title="'CAD:'+selectRow.code" width="60%" height="70%" show-zoom resize remember destroy-on-close>
      <cad-lay-out :form="selectRow" :sorderNumber="selectRow.code" ref="cadlayout" />
    </vxe-modal>
    <vxe-modal v-model="cadTextShow" title="CAD脚本" width="50%" height="60%" show-zoom resize remember destroy-on-close>
      <vxe-form ref="xForm" class="my-form2" title-align="right" title-width="100" :data="Cad">
        <vxe-form-item title="名称" field="name" span="24" :item-render="{}"> <template>
            <vxe-input v-model="Cad.sorderNum"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="服务器地址" field="nickname" span="24" :item-render="{}"> <template>
            <vxe-input v-model="Cad.server"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="文件名称" field="age" span="24" :item-render="{}"> <template v-slot>
            <vxe-input v-model="Cad.fileName" type="integer"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="CAD脚本" field="address" span="24" :item-render="{}"> <template v-slot>
            <!-- <vxe-textarea v-model="Cad.cadText" :rows="20"></vxe-textarea> -->
            <el-input type="textarea" :rows="25" placeholder="请输入内容" v-model="Cad.cadText">
            </el-input>
          </template>
        </vxe-form-item>

      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="bomTableShow" :title="'Bom清单'+selectRow.code" width="65%" height="60%" show-zoom resize remember destroy-on-close>
      <bom-item :sorder="selectRow" />
    </vxe-modal>
    <vxe-modal v-model="salesBillShow" :title="selectRow.code+'账单明细'" width="80%" height="60%" show-zoom resize remember destroy-on-close>
      <sorder-sales-bill-detail :sorder="selectRow" />
    </vxe-modal>
    <el-drawer :visible.sync="sorderStateDrawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <sorder-log :sorder="selectRow" v-if="sorderStateDrawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
// import Vue from 'vue'
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import pluginExport from '@d2-projects/vue-table-export'
// import detailList from './listdetail'
import SorderLog from './sorderlog'
import BomItem from '../components/bomitem'
import SorderSalesBillDetail from '@/components/mtm/sordersalesbilldetail/index'
import CadLayOut from '../components/cadlayout.vue'
import { mapState } from 'vuex'
import { toString } from 'lodash'
// import preventReClick from '@/plugin/button/plugins'
// import {groupBy } from "utils"
export default {

  name: 'odm_sorderlist',
  mixins: [masterTableMixins],
  components: {
    // detailList
    SorderLog,
    BomItem,
    SorderSalesBillDetail,
    CadLayOut
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        sequence: 9999
      },
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/odm_sorder/list',
        createCad: '/mtm/oDM_SorderCadLayout/addSorderCadLaout',
        getCadStr: '/mtm/oDM_SorderCadLayout/GetCadStr',
        delete: '/mtm/odm_sorder/deletes',
        print: '/mtm/oDM_Sorder/sorderPrint',
        sorderCadlayout: '/mtm/oDM_SorderCadLayout/get',
        sendCad: '/mtm/oDM_SorderCadLayout/sendcad',
        deepClone: '/mtm/oDM_Sorder/deepClone',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ClientPersonComboStoreByQuery: '/mtm/comboQuery/ClientPersonComboStoreByQuery',
        UserComboStoreByQuery: '/mtm/comboQuery/UserComboStoreByQuery',
        SorderStatusComboStore: '/mtm/combo/sorderStatusComboStore',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        productionPlanState: '/mes/PRD_ProductionPlan/GetProductionPlanStateToMTM'
      },
      detailForm: {
        sorderId: null

      },
      UserComboStoreByQuery: [],
      SorderStatusComboStore: [],
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      ClientPersonComboStoreByQuery: [],
      sorderStateDrawer: false,
      footerCompanyInfo: false,
      cadLayoutShow: false,
      cadTextShow: false,
      bomTableShow: false,
      Cad: {
        cadText: null,
        sorderNum: '',
        server: '',
        fileName: ''
      },
      searchForm: {
        stateIDs: [],
        sorderTypes: [1, 2, 0, 10],
        createID: null
      },
      salesBillShow: false,
      isAutoLoding: false

    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ]),
    selectCheckRows () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      return rows.length
    }
    //   ...mapState('d2admin/page', [
    //     'state'
    //   ])
  },
  async created () {
    await this.getCombStore()
    this.setSorderState()
    this.loadData()
  },
  methods: {
    setSorderState () {
      if (this.info.userType === 2) {

      }
      // var kefu = [0, 1, 20, 21, 22, 32]
      // var jishu = [21, 30, 31, 32]
      var admin = [1, 20, 21, 22, 30, 31, 32, 40]
      if (this.info.userRoles.ElementExist('code', 'SuperAdmin')) {
        this.searchForm.stateIDs = this.$utils.union(this.searchForm.stateIDs, admin)
      }
      // if (this.info.userRoles.ElementExist('code', 'Customer')) {
      //   this.searchForm.stateIDs = this.$utils.union(this.searchForm.stateIDs, kefu)
      // }
      // if (this.info.userRoles.ElementExist('code', 'PatternMaker')) {
      //   this.searchForm.stateIDs = this.$utils.union(this.searchForm.stateIDs, jishu)
      // }
    },
    // // 数据加载
    // async loadData () {
    //   this.tableLoading = true
    //   this.$api.ActionRequest(this.api.get, this.searchForm).then(async result => {
    //     this.tableData = result.items
    //     this.tableData = await this.getProductionPlanState(result.items)
    //     this.searchForm.totalCount = result.totalCount
    //     this.tableLoading = false
    //   }).catch(() => {
    //     this.tableLoading = false
    //   })
    //   // this.tableLoading = false
    // },
    // 批量删除
    deletesEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length === 0) {
        this.$XModal.message({ message: '请勾选要删除的数据', status: 'error' })
        return
      }
      var codes = toString(rows.map(item => { return item.code }))
      this.$XModal.confirm(`您确定要删除${rows.length}个订单:[${codes}]的数据吗?删除后不可恢复!`).then(type => {
        if (type === 'confirm') {
          this.$api.ActionRequest(this.api.delete, rows).then(result => {
            this.$XModal.message({ message: '批量删除成功', status: 'success' })
            this.loadData()
          })
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    getBomItem () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能查看一个订单', status: 'error' })
        return
      }
      this.selectRow = checks[0]
      this.bomTableShow = !this.bomTableShow
    },
    salesBillShowEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能查看一个订单', status: 'error' })
        return
      }
      this.selectRow = checks[0]
      this.salesBillShow = true
    },
    async getProductionPlanState (data) {
      var sorderIDs = data.map((item) => {
        return item.id
      })
      if (sorderIDs.length <= 0) {
        return data
      }
      var list = await this.$api.ActionRequest(this.api.productionPlanState, { sorderId: sorderIDs }).then(async ({ totalCount, items }) => {
        return items
      })
      list.forEach(item => {
        var dto = this.$utils.find(data, it => it.id === item.sorderId)
        if (dto !== null) {
          dto.mEsLastFinishedProductionSchedule = item.lastFinishedProductionSchedule
          dto.mesProductionPlanStateText = item.productionPlanStateText
          dto.mesSchedulesDto = item.schedulesDto
        }
      })
      return data
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { isNotG: true }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SorderStatusComboStore).then(result => {
        this.SorderStatusComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
    },
    insertEvent (name) {
      this.$router.push({ name: name })
    },
    CadMake () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var ids = checks.map(item => { return item.id })
      this.$api.ActionRequest(this.api.sendCad, { sorderIDs: ids }).then(res => {
        console.log(res)
        // var groupList = this.$utils.groupBy(res, "type")
        // this.$utils.objectEach(groupList, (item, key) => {
        //   // console.log(key)
        //   // console.log(item)
        //   this.tableExport(item, key)
        // })
      })
    },
    async getCad () {
      await this.$api.ActionRequest(this.api.sorderCadlayout, { sorderID: this.selectRow.id }).then(async res => {
        this.cadLayoutList = res
      })
    },
    async createCad () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能查看一个订单', status: 'error' })
        return
      }
      await this.$api.ActionRequest(this.api.createCad, { sorderID: checks[0].id, skipManual: true }).then(async res => {
        if (this.info.userType === 2) {
          this.$message({ type: 'success', message: '获取成功,请等待一分钟左右后刷新列表,如果未获取到请联系客服人员,请勿多次点击!', duration: 5000 })
        } else {
          this.$message({ type: 'success', message: '排料成功,请等待CAD返回耗量' })
        }

        await this.getCad()
      })
    },
    async DownloadETCad () {
      await this.$api.ActionRequest(this.api.getCadStr, { SorderID: this.selectRow.id }).then(res => {
        this.createJson(res)
      })
    },
    createJson (res) {
      // var data = res
      var data = JSON.stringify(res)
      // encodeURIComponent解决中文乱码
      const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(data)
      // 通过创建a标签实现
      const link = document.createElement('a')
      link.href = uri
      // 对下载的文件命名
      link.download = 'ETCAD格式数据.json'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    handleClose () {
      this.sorderStateDrawer = false
    },
    showOrderState (row) {
      this.selectRow = row
      this.sorderStateDrawer = true
    },

    sendCadService () {
      if (this.selectRow == null) {
        return
      }
      this.$api.ActionRequest(this.api.sendCad, { sorderIDs: [this.selectRow.id] }).then(res => {
        console.log(res)
      })
    },
    // 导出
    async tableExport (list, name) {
      // console.log(list)
      var colums = []
      var data = []
      await list.forEach(async item => {
        var obj = this.$utils.clone(item)
        delete obj.fabric
        delete obj.number
        delete obj.select
        delete obj.url
        delete obj.id
        for (let index = 0; index < item.number.length; index++) {
          const element = item.number[index]
          this.$utils.set(obj, element.name, element.val)
        }
        for (let index = 0; index < item.select.length; index++) {
          const element = item.select[index]
          this.$utils.set(obj, element.name, element.val)
        }
        data.push(obj)
      })
      this.$utils.objectEach(data[0], (item, key) => {
        var columnsObj = {}
        this.$utils.set(columnsObj, 'label', key)
        this.$utils.set(columnsObj, 'prop', key)
        colums.push(columnsObj)
      })
      console.log(colums)
      this.$export.excel({
        columns: colums,
        data: data,
        title: name + 'CAD排料图'
        // merges: ['A1', 'BG']
      })
        .then(() => {
          this.$message(name + '导出表格成功')
        })
    },
    getSorderCadlayout () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能查看一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.selectRow = checks[0]
      this.cadLayoutShow = true
    },
    removeEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能删除一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.$XModal.confirm('您确定要删除该数据?').then(type => {
        if (type === 'confirm') {
          if (checks[0].id != null) {
            this.$api.ActionRequest(this.api.delete, checks).then(res => {
              this.$XModal.message({ message: '删除成功！', status: 'success' })
              this.loadData()
            })
          }
        }
      })
    },
    tableCellClick ({ column, row }) {
      if (column && column.type === 'checkbox') {
        return
      } else {
        this.$refs.master_table.clearCheckboxRow()
        this.$refs.master_table.toggleCheckboxRow(row)
      }
      this.selectRow = row
    },
    cellDblClick ({ row }) {
      this.gosorder(row.id, row.code, 'odm_sordernmodify')
    },
    editEvent (name) {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能编辑一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      this.gosorder(checks[0].id, checks[0].code, name)
    },

    gosorder (id, orderid, name) {
      this.$router.push({
        name: name,
        params: {
          id: id, sorderid: orderid
        }
      })
    },
    printEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能打印一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var url = this.mtmpai.replace('/api/', '')
      window.open(`${url}/fs/print/sorder/pdf?num=${checks[0].id}`, '_blank')
    },
    printPlusEvent ({ DoubleFaced = false }) {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var ids = checks.map(row => { return row.id })
      var url = this.mtmpai.replace('/api/', '')
      var api = url + '/fs/print/sorderplus/pdf'
      console.log(api)
      this.openPostWindow1(api, ids, '_blank', DoubleFaced)
    },
    openPostWindow1 (url, datas, targetType, DoubleFaced = false) {
      var tempForm = document.createElement('form')
      tempForm.setAttribute('id', 'tempForm1')
      tempForm.setAttribute('style', 'display:none')
      tempForm.setAttribute('target', targetType)
      tempForm.setAttribute('method', 'post')
      tempForm.setAttribute('action', url)
      datas.forEach(item => {
        var a = document.createElement('input')
        a.setAttribute('type', 'hidden')
        a.setAttribute('name', 'iDs')
        a.setAttribute('value', item)
        tempForm.append(a)
      })
      if (DoubleFaced) {
        var a = document.createElement('input')
        a.setAttribute('type', 'hidden')
        a.setAttribute('name', 'DoubleFaced')
        a.setAttribute('value', true)
        tempForm.append(a)
      }
      document.body.append(tempForm)
      tempForm.submit()
    },
    rowClassName ({ row, rowIndex }) {
      var stateClass = ''
      switch (row.statusID) {
        case 0: // 待定 ClientUndetermined
          stateClass = 'sorderstate-client'
          break
        case 1:// 已确认Confirmed
          stateClass = 'sorderstate-confirmed' //  background-color: #0598e1;
          break

        case 20:// 客服锁定CLock
          stateClass = 'sorderstate-clock'
          break
        case 21:// 客服审核完成 //CChecked
          stateClass = 'sorderstate-cchecked'
          break
        case 22:// 客服驳回 CReject
          stateClass = 'sorderstate-customer'
          break
        case 30:// 技术锁定 //MLock
          stateClass = 'sorderstate-mlock'
          break
        case 31:// 技术审核完成 MChecked
          stateClass = 'sorderstate-MChecked'
          break
        case 32:// 技术驳回 MReject
          stateClass = 'sorderstate-technology'
          break
        case 40:// 计划下单Planed
          stateClass = 'sorderstate-planed'
          break
        case 41:// 计划驳回 PReject
          stateClass = 'sorderstate-preject'
          break
        case 50: // 完成 Finished
          stateClass = 'sorderstate-finished'
          break
        default:
          stateClass = ''
          break
      }
      return stateClass
    },
    // 订单复制
    deepCloneEvent () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能编辑一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var sorderdto = checks[0]
      const loading = this.$loading({
        lock: true,
        text: `正在复制订单${sorderdto.code},请稍后`,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.$api.ActionRequest(this.api.deepClone, sorderdto).then(res => {
        loading.text = `订单复制成功,新订单号为：${res.sorderNumber},正在准备跳转到编辑页！`
        var _this = this
        setTimeout(function () {
          loading.close()
          _this.gosorder(res.sorderId, res.sorderNumber, 'odm_sordernmodify')
        }, 2000)
      }).catch(() => {
        loading.close()
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { text: query }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
    },
    remoteMethod6 (query) {
      this.$api.ActionRequest(this.api.UserComboStoreByQuery, { text: query }).then(result => {
        this.UserComboStoreByQuery = result
      })
    }

  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (to.params.stateIDs) {
        vm.searchForm.stateIDs = to.params.stateIDs
        to.params.stateIDs = null
      }
      if (to.params.text) {
        vm.searchForm.text = to.params.text
        to.params.text = null
      }
      if (to.params.refresh) {
        vm.loadData().then(res => {
          // console.log(to.params)
          // vm.master_table.clearCheckboxRow()
          var aa = vm.tableData.GetFirstElement('id', to.params.sorderid)
          if (aa) {
            // console.log(aa)
            vm.tableCellClick({ row: aa })
          }
        })
      }
    })
  }
}
</script>

<style lang="scss" >
.sorderlist {
  // .mtmtoolbar {
  //   overflow-x: auto;
  //   overflow-y: hidden;
  // }
  // .el-drawer {
  //   overflow-y: auto !important;
  // }
  .expandclass {
    background-color: #e6f7ff;
  }
  .sorderstate-client {
    background-color: #909399;
    color: cornsilk;
  }
  .sorderstate-confirmed {
    background-color: #0598e1;
    color: cornsilk;
  }
  //客服驳回
  .sorderstate-customer {
    background-color: #0da468;
    color: cornsilk;
  }
  //客服锁定CLock
  .sorderstate-clock {
    background-color: #0d97a4;
    color: cornsilk;
  }
  //客服审核完成 //CChecked
  .sorderstate-cchecked {
    background-color: #0da410;
    color: cornsilk;
  }
  //技术 技术驳回
  .sorderstate-technology {
    background-color: #e6a23c;
    color: cornsilk;
  }
  //技术 技术锁定
  .sorderstate-mlock {
    background-color: #a68934;
    color: cornsilk;
  }
  //技术 技术审核完成
  .sorderstate-MChecked {
    background-color: #a47e0d;
    color: cornsilk;
  }
  //计划下单Planed
  .sorderstate-planed {
    background-color: #ea6157;
    color: cornsilk;
  }
  //计划驳回 PReject
  .sorderstate-preject {
    background-color: #de3327;
    color: cornsilk;
  }
  //完成 Finished
  .sorderstate-finished {
    background-color: #0d6aa4;
    color: cornsilk;
  }
}
</style>
