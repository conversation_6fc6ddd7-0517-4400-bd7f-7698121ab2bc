<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">保存</vxe-button>
          <vxe-button status="perfect" @click="checkall('selected')" v-if="menuAction.allowAdd">全选选择</vxe-button>
          <vxe-button status="perfect" @click="checkall('notselected')" v-if="menuAction.allowAdd">全选取消</vxe-button>
          <vxe-button status="primary" v-if="menuAction.allowAdd" @click="deepCloneShow=!deepCloneShow">快速复制</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id="MomModelelemDetailTable" ref='master_table' keep-source :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :edit-rules="validRules" :edit-config="{trigger: 'click', selected: true, mode: 'cell',showStatus: true, icon: 'fa fa-pencil'}" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="cadRuleCode" title="编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="cadRuleName" title="名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="code" title="CAD款式明细编码" sortable width="120" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"></vxe-table-column>
      <vxe-table-column field="codeName" title="CAD款式明细名称" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="selected" title="选择" sortable width="100" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-checkbox v-model="row.selected"></vxe-checkbox>
        </template>
        <template #default="{ row }">
          <vxe-checkbox v-model="row.selected"></vxe-checkbox>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="deepCloneShow" title="快速复制" width="1200" height="800" resize destroy-on-close>
      <model-elem v-if="deepCloneShow" :getModelElem="getModelElem" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import modelElem from './components/modelelem'
export default {
  name: 'MomModelelemDetail', // 款式明细关联的版型
  mixins: [detailTableMixins],
  components: {
    modelElem
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      validRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 1, max: 25, message: '长度在 1 到 25 个字符' }]
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codename', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch ' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      deepCloneShow: false,
      api: {
        get: '/mtm/mom_modelelemcad/getByModelElemID',
        edit: '/mtm/mom_modelelemcad/updatesByModelElemID',
        deepClone: '/mtm/mom_modelelemcad/DeepCloneByModelElemID'
      }
    }
  },
  watch: {
    'form.id': {
      deep: true,
      async handler (newVal, oldVal) {
        // console.log(`newVal:${newVal},oldVal:${oldVal}`)
        if (newVal !== oldVal) {
          await this.loadData(this.form)
        }
      }
    }
  },
  created () {
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    checkall (type) {
      this.tableData.forEach(element => {
        switch (type) {
          case 'selected':
            element.selected = true
            break
          case 'default':
            element.default = !element.default
            break
          case 'notselected':
            element.selected = false
            break
        }
      })
    },
    async validEvent () {
      const $table = this.$refs.master_table
      const errMap = await $table.validate().catch(errMap => errMap)
      if (errMap) {
        this.$XModal.message({ status: 'error', content: '校验不通过！' })
        return false
      } else {
        // this.$XModal.message({ status: 'success', content: '校验成功！' })
        return true
      }
    },
    async insertEvent () {
      const $table = this.$refs.master_table
      const insertRecords = $table.getInsertRecords()
      const updateRecords = $table.getUpdateRecords()
      var arr = this.$utils.union(insertRecords, updateRecords)
      if (arr.length <= 0) {
        this.$XModal.message({ status: 'error', content: '没有操作任何数据！' })
        return
      }
      var b = await this.validEvent()
      if (!b) {
        return
      }
      await this.$api.ActionRequest(this.api.edit, arr).then(result => {
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.loadData({ id: this.form.id })
      })
    },
    getModelElem (modelElem) {
      this.$api.ActionRequest(this.api.deepClone, { OldModelElemID: modelElem.id, NewModelElemID: this.form.id }).then(result => {
        this.$XModal.message({ message: '复制成功', status: 'success' })
        this.loadData({ id: this.form.id }).then(({ data }) => {
        })
        this.deepCloneShow = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
