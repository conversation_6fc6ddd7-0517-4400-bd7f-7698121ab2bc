<template>
  <d2-container>
    <vxe-table border resizable height="auto" show-overflow :data="tableData" :loading="tableLoading" ref='master_table' :edit-config="{trigger: 'click', mode: 'cell'}">
      <vxe-table-column type="seq" width="60" />
      <vxe-table-column field="sorderClientName" title="订单所属" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="groupName" title="分类" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelElemListName" title="款式名称" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelElemName" title="工艺名称" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="itemText" title="货号" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="itemGroupText" title="物料类别" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="clientName" title="所属客户" sortable width="120px"></vxe-table-column>
      <vxe-table-column field="businessGroupText" title="物料业务属性" sortable width="120px"></vxe-table-column>
      <vxe-table-column field="qty" title="耗量/件" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelQty" title="件/数" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="countQty" title="合计耗量" sortable width="95px">
        <template v-slot="{ row }">
          <span>{{ row.countQty }}</span>
          <span v-if="row.itemMLText!==null" style="color:red;font-weight: 500;">({{ row.itemMLText }}cm)</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="inventoryQty" title="有效库存" sortable width="95px">
      </vxe-table-column>
      <vxe-table-column field="itemStockPositionText" title="库位" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="itemBatchCode" title="海关合同号" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="itemBatchEoriNo" title="手册号" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="itemBatchQty" title="手册号库存" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>

    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-sizes="[10,50,100,500,1000,3000,5000]" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <vxe-button status="warning" @click="exportDataEvent()">导出数据</vxe-button>
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'ExportTable',
  mixins: [detailTableMixins],
  props: {
    selectTable: {
      require: true,
      type: Array
    }
  },
  data () {
    return {
      searchForm: {
        pageSize: 1000,
        maxResultCount: 1000
      },
      tableLoading: false,
      api: {
        get: '/mtm/war_itembomdetail/get'
      },
      tableData: []
    }
  },
  created () {
    var itemBomIds = this.selectTable.map(item => { return item.id })
    this.loadData({ itemBomIds: itemBomIds }).then(({ data }) => {
    })
  },
  methods: {
    async getData (itemBomIds) {
      this.tableLoading = true
      await this.$api.ActionRequest(this.api.get, { itemBomIds: itemBomIds }).then(result => {
        this.tableData = result
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    exportDataEvent () {
      this.$refs.master_table.exportData({
        filename: '物料清单-' + this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss'),
        sheetName: 'Sheet1',
        isColgroup: false,
        isFooter: false,
        type: 'xlsx'
      })
    }
  }
}
</script>

<style>
</style>
