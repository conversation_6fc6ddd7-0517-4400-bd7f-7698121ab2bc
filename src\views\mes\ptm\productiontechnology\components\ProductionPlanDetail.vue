<template>
  <d2-container style="height: 89%">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:tools>
          <!-- <vxe-button type="text" icon="vxe-icon--question" class="tool-btn" @click="documentBoxShowEvent"></vxe-button> -->
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="reload()">
          </vxe-button>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='PrdProductionplandetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="productionPlanStateText" title="订单状态" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="sorderNum" title="订单号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="productionPlanDetailStateText" title="状态" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="itemName" title="面料号" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="groupName" title="类别" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="modelName" title="版型" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="sizeCode" title="规格" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'prd_productionplandetail',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      api: {
        get: '/mes/pTM_ProductionTechnology/GetScan'

      },
      footerCompanyInfo: false

    }
  },
  async created () {
    this.reload()
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
    },
    reload () {
      this.loadData({ productionStationID: this.form.id, isToday: true })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
