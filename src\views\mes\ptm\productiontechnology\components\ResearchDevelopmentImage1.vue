<template>
  <el-image :src="fileurl" fit='scale-down' class="ResearchDevelopmentImage">
    <div slot="placeholder" class="image-slot">
      加载中<span class="dot">...</span>
    </div>
  </el-image>
</template>

<script>
export default {
  name: 'ResearchDevelopmentImage',
  props: {
    form: {
      type: Object,
      requited: true
    }
  },
  data () {
    return {
      src: null,
      mtmapi: process.env.VUE_APP_API,
      api: {
        fileurl: '/fs/getfile'
      }
    }
  },
  computed: {
    fileurl () {
      if (this.form == null || this.form.fileManagementID == null) {
        return null
      }
      var api = this.mtmapi.replace('/api/', '')
      var url = api + this.api.fileurl + '?FileID=' + this.form.fileManagementID
      return url
    }
  }
}
</script>

<style>
.ResearchDevelopmentImage{
    width: 100%;
}
</style>
