<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="load()">
          </vxe-button>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarProductwarehouseMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">

      <vxe-table-column field="createOnNumber" title="货件信息货件处理时间戳" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="productNumber" title="货件信息货件标识" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="trackingNumber" title="货件信息主追踪编号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="countryCode" title="发至国家地区" sortable width="100"></vxe-table-column>
      <vxe-table-column field="expressageType" title="货件信息服务类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="weight" title="货件信息实际重量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="no" title="货件信息扩充区域标记" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="货件信息包裹数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qVN1" title="货件信息QVN标签制作通知5选项" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qVN2" title="货件信息QVN运输途中通知5选项" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qVN3" title="货件信息QVN运输通知5选项" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qVN4" title="货件信息QVN递送通知5选项" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qVN5" title="货件信息QVN例外通知5选项" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipToName" title="发至公司或姓名" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipToAddress" title="发至地址行1" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="shipToCity" title="发至市镇" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipToPostalCode" title="发至邮政编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipToPhone" title="发至电话" sortable width="100"></vxe-table-column>
      <vxe-table-column field="oteherName" title="第三方（收件人）公司或姓名" sortable width="100"></vxe-table-column>
      <vxe-table-column field="dutyNo" title="发自税号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipFromUpsAccount" title="发自UPS帐号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipFromName" title="发自公司或姓名" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipFromContactName" title="发自经手人" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipFromAddress" title="发自地址行1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipFromCity" title="发自市镇" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipFromPostalCode" title="发自邮政编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="shipFromPhone" title="发自电话" sortable width="100"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <vxe-button status="warning" @click="downLoad" v-if="menuAction.allowPrint">下载</vxe-button>
        </template>
      </vxe-pager>
    </template>

  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'

export default {
  name: 'war_productwarehouse',
  mixins: [detailTableMixins],
  props: {
    IDs: {
      type: Array,
      required: true
    }
  },
  components: {

  },
  data () {
    return {
      api: {
        get: '/mtm/wAR_Shipment/ShipmentUpsInfoSum'

      }

    }
  },

  async created () {
    await this.getCombStore()
    this.loadData({ IDs: this.IDs })
  },
  methods: {
    async getCombStore () { },
    async load () {
      this.loadData({ IDs: this.IDs })
    },
    downLoad () {
      const xTable = this.$refs[this.tableRef]
      xTable.exportData({
        filename: 'UPS汇总---' + this.$utils.toDateString(new Date(), 'yyyy-MM-dd'),
        data: this.tableData
      })
    }
  }

}
</script>

<style lang="scss" scoped>
</style>
