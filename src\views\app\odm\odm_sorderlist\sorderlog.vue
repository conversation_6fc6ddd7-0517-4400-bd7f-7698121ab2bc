<template>
  <div class="sorderlog">
    <el-timeline>
      <el-timeline-item v-for="(item, index) in dataList" :key="index" :timestamp="item.time+'  操作人：'+item.userName">
        <template v-if="item.statusID1!==null">
          <el-button :class="orderState(item.statusID1)" size="mini">{{item.status1Text}}</el-button>
          <span><i class="el-icon-back"></i></span>
        </template>
        <el-button :class="orderState(item.statusID)" size="mini">{{item.statusText}}</el-button>
        <p v-if="item.remark!==null&&item.remark!==''">{{item.remark}}</p>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
export default {
  name: 'sorderlog',
  data () {
    return {
      activities: [],
      api: {
        get: '/mtm/oDM_SorderLog/get'
      },
      dataList: []
    }
  },
  watch: {
    sorder: {
      handler: function (newVal, olVal) {
        if (newVal !== null) {
          // this.get(newVal)
        }
      }
    }
  },
  props: {
    sorder: {
      required: true,
      type: Object
    }
  },
  created () {
    // console.log(this.sorder)
    if (this.sorder.id) {
      this.get(this.sorder)
    }
  },
  methods: {
    orderState (state) {
      var str = 'orderinfo'
      switch (state) {
        case 0:
          str = 'orderinfo'
          break
        case 1:
          str = 'orderprimary'
          break
        case 20:
        case 21:
        case 22:
          str = 'ordersuccess'
          break
        case 30:
        case 31:
        case 32:
          str = 'orderwarning'
          break
        case 40:
        case 41:
        case 50:
          str = 'orderdanger'
          break
        default:
          break
      }
      return str
    },
    get (order) {
      console.log(order.id)
      this.$api.ActionRequest(this.api.get, { sorderID: order.id }).then(res => {
        this.dataList = res
      })
    }
  }
}
</script>

<style lang="scss">
.sorderlog {
  margin-left: 20%;

  .el-drawer__body {
    overflow-y: auto !important;
  }
  .orderinfo {
    color: white;
    background-color: #909399;
  }
  .orderprimary {
    color: white;
    background-color: #409eff;
  }
  .ordersuccess {
    color: white;
    background-color: #67c23a;
  }
  .orderwarning {
    color: white;
    background-color: #e6a23c;
  }
  .orderdanger {
    color: white;
    background-color: #f56c6c;
  }
}
</style>
