<template>
  <d2-container class="qualityset">
    <div>
      <div class="qualitysetchild">
        <el-button style="width:100px;" type="success" :disabled="form==null||form.productionTeamID==undefined" @click="qulityCheck">合格</el-button>
        <p></p>
        <p></p>
        <el-button style="width:100px;" type="danger" @click="showQuality=!showQuality" :disabled="form==null||form.productionTeamID==undefined">不合格</el-button>
      </div>
    </div>

    <vxe-modal v-model="showQuality" title="不合格原因" width="80%" height="800" resize destroy-on-close mask-closable class="showQuality">
      <d2-container>
        <split-pane :min-percent='10' :default-percent='20' split="horizontal">
          <template slot="paneL">
            <el-radio-group v-model="qualityBaseId" size="medium" @change="qualityBaseChange">
              <el-radio-button v-for="(qualityBase,index) in qualityBaseStore" :key="index" :label="qualityBase.id">{{qualityBase.codeName}}</el-radio-button>
            </el-radio-group>
          </template>
          <template slot="paneR">
            <template>
              <split-pane :min-percent='10' :default-percent='20' split="vertical">
                <template slot="paneL">
                  <el-radio-group v-model="qualityId" size="medium" @change="qualityChange">
                    <el-radio-button v-for="(quality,index) in qualityStore" :key="index" :label="quality.id">{{quality.codeName}}</el-radio-button>
                  </el-radio-group>
                </template>
                <template slot="paneR">
                  <el-radio-group v-model="qualityDetailId" size="medium">
                    <el-radio-button v-for="(qualityDetail,index) in qualityDetailStore" :key="index" :label="qualityDetail.id">{{qualityDetail.codeName}}</el-radio-button>
                  </el-radio-group>
                </template>
              </split-pane>
            </template>
          </template>
        </split-pane>
        <template slot="footer">
          <vxe-button style="float: right;" status="success" @click="qulityCheck1">确定</vxe-button>
        </template>
      </d2-container>
    </vxe-modal>
  </d2-container>
</template>

<script>
export default {
  name: 'qualityset',
  props: {
    form: {
      type: Object
    },
    productstationconfig: {
      type: Object
    }
  },
  data () {
    return {
      showQuality: false,
      api: {
        qualitybaseget: '/mes/PTM_ProductionTechnology/GetQualityBase',
        updatePlanScheduleQulity: '/mes/PTM_ProductionTechnology/UpdatePlanScheduleQulity',
        qualityget: '/mes/ptm_quality/get',
        qualitydetailget: '/mes/ptm_qualitydetail/get'
      },
      qualityBaseId: null,
      qualityBaseStore: [],
      qualityId: null,
      qualityStore: [],
      qualityDetailStore: [],
      qualityDetailId: null
    }
  },
  watch: {
    qualityBaseId: {
      handler (newval, oldval) {
      }
    },
    showQuality: {
      async handler (newval, oldval) {
        if (newval) {
          await this.get1()
        }
      }
    }
  },
  async created () {

  },
  methods: {
    async get1 () {
      this.qualityBaseId = null
      await this.$api.ActionRequest(this.api.qualitybaseget, { productionTeamID: this.form.productionTeamID, productionStationID: this.form.productionStationID }).then(async result => {
        if (result.length !== 0) {
          this.qualityBaseStore = result
          this.qualityBaseId = result[0].id
          await this.get2(this.qualityBaseId)
        } else {
          this.qualityStore = []
          this.qualityDetailStore = []
        }
      })
    },
    async get2 (id) {
      this.qualityId = null
      var _this = this
      await this.$api.ActionRequest(this.api.qualityget, { qualityBaseID: id, productionStationID: this.form.productionStationID }).then(async result => {
        _this.qualityStore = result.items
        if (result.items.length > 0) {
          _this.qualityId = result.items[0].id
          await _this.get3(_this.qualityId)
        }
      })
    },
    async get3 (id) {
      this.qualityDetailId = null
      this.qualityDetailStore = []
      var _this = this
      await this.$api.ActionRequest(this.api.qualitydetailget, { qualityID: id, productionStationID: this.form.productionStationID }).then(result => {
        _this.qualityDetailStore = result.items
        if (result.items.length > 0) {
          _this.qualityDetailId = result.items[0].id
        }
      })
    },
    async qualityBaseChange (label) {
      await this.get2(label)
    },
    async qualityChange (label) {
      await this.get3(label)
    },
    async qulityCheck () {
      var b = await this.updatePlanScheduleQulity(true, null)
      if (b) {
        this.$XModal.message({ message: '检验完成', status: 'success' })
      }
    },
    async qulityCheck1 () {
      if (this.qualityDetailId === null) {
        this.$XModal.message({ message: '请选择不合格原因', status: 'error' })
        return
      }
      var b = await this.updatePlanScheduleQulity(false, this.qualityDetailId)
      if (b) {
        this.$XModal.message({ message: '完成', status: 'success' })
        this.showQuality = false
      }
    },
    async updatePlanScheduleQulity (ischeck, qualityDetailID = null) {
      return await this.$api.ActionRequest(this.api.updatePlanScheduleQulity, {
        productionPlanDetailID: this.form.productionPlanDetailID,
        productionTeamID: this.form.productionTeamID,
        qualityDetailID: qualityDetailID,
        IsCheck: ischeck,
        productionStationID: this.form.productionStationID
      }).then(result => {
        return true
      })
    }
  }
}
</script>

<style lang="scss">
.qualityset {
  display: table;
  width: 100%;
  height: 100%;
  .qualitysetchild {
    text-align: right;
    display: table-cell;
    vertical-align: middle;
  }
  .d2-container-full__body {
    // height: calc(100vh - 100px);
    // height: 700px !important;
    background-color: #fff !important;
  }
  .showQuality {
    .container-component {
      height: 95% !important;
    }
  }
}
</style>
