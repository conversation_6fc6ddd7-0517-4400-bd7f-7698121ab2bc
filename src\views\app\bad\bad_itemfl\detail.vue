<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button icon="fa fa-save" status="perfect" @click="saveEvent" v-if="menuAction.allowEdit"> 保存</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowEdit">保存 -->
          <!-- </vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:buttons>
          <vxe-form ref="xForm" :data="form">
            <vxe-form-item title="编码" field="code" :item-render="{name: '$input',props:{disabled:true}}"></vxe-form-item>
            <vxe-form-item title="原始货号" field="originalItemNo" :item-render="{name: '$input',props:{disabled:true}}"></vxe-form-item>
          </vxe-form>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table keep-source id='BadItemElemItem_detail_table' ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :edit-config="{trigger: 'click', selected: true, mode: 'cell',showStatus: true, icon: 'fa fa-pencil'}" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="业务类型" sortable width="150px" :edit-render="{name: '$select', options: SorderTypeComboStore}">
        <template v-slot="{ row }">
          <el-select v-model="row.sorderType" placeholder="请选择" size="mini">
            <el-option v-for="item in SorderTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template>
      </vxe-table-column>
      <vxe-table-column field="modelElemListText" title="款式" sortable width="150px"> </vxe-table-column>
      <vxe-table-column field="modelElemText" title="款式明细" sortable width="150px">
        <template v-slot="{ row }">
          <model-elem-select :modelElem="row" :api="api" :modelElemSet="modelElemSet" />
        </template>
      </vxe-table-column>
      <vxe-table-column field="item1Text" title="货号" sortable width="150px">
        <template v-slot="{ row }">
          <item-select :modelElem="row" :api="api" :itemSet="itemSet" />
        </template>
      </vxe-table-column>
      <vxe-table-column field="clientText" title="客户" sortable width="150px">
        <template v-slot="{ row }">
          <client-select :modelElem="row" :api="api" :clientSet="clientSet" />
        </template>
      </vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="isActive" title="活动" width="120px">
        <template v-slot="{ row }">
          <el-switch v-model="row.isActive" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </template>
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="100px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式明细" field="modelElemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="remoteMethod1">
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="业务类型" field="sorderType" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.sorderType" filterable placeholder="请选择" size="mini">
              <el-option v-for="item in SorderTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="货号" field="itemID1" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemID1" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式" :remote-method="query=>remoteMethod2(query,selectRow)">
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户" field="clientID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
              <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <!-- <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item> -->
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
import ModelElemSelect from './components/modelelem'
import ItemSelect from './components/item'
import ClientSelect from './components/client'
export default {
  name: 'ItemElemItemFLDetail',
  mixins: [detailTableMixins],
  components: {
    ModelElemSelect, ItemSelect, ClientSelect
  },
  props: {
    form: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      formData: {
        remark: '',
        isActive: true,
        itemID1: null,
        itemID: null,
        sorderType: 1,
        modelElemID: null
      },
      formRules: {
        itemID1: [{ required: true, message: '货号必填' }],
        modelElemID: [{ required: true, message: '款式明细必选' }]
      },

      api: {
        get: '/mtm/bAD_ItemElemItem/getfl',
        add: '/mtm/bAD_ItemElemItem/updates',
        edit: '/mtm/bAD_ItemElemItem/updates',
        delete: '/mtm/bAD_ItemElemItem/deletes',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        SorderTypeComboStore: '/mtm/combo/SorderTypeComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      ModelElemComboStoreByQuery: [],
      clientComboStoreByQuery: [],
      ItemComboStore: [],
      SorderTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.formData.itemID = this.form.id
    this.loadData({ itemID: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.SorderTypeComboStore).then(result => {
        this.SorderTypeComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    remoteMethod2 (query, selectRow) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query, modelElemID: selectRow.modelElemID }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    modelElemSet (row, newVal) {
      var elem = this.tableData.GetFirstElement('modelElemID', row.modelElemID)
      if (elem) {
        elem.modelElemID = newVal
      }
    },
    itemSet (row, newVal) {
      var elem = this.tableData.GetFirstElement('modelElemID', row.modelElemID)
      if (elem) {
        elem.itemID1 = newVal
        elem.isActive = true
      }
    },
    clientSet (row, newVal) {
      var elem = this.tableData.GetFirstElement('modelElemID', row.modelElemID)
      if (elem) {
        elem.clientID = newVal
        elem.isActive = true
      }
    },
    // 编辑
    editEvent (row) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { gid: row.clientID }).then(result => {
        this.clientComboStoreByQuery = result
      })
      this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID1 }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    saveEvent () {
      var data = this.tableData.filter(item => { return item.modelElemID !== null })
      if (data.length === 0) {
        return
      }
      this.$api.ActionRequest(this.api.edit, data).then(result => {
        this.$XModal.message({ message: '保存成功', status: 'success' })
        this.loadData()
      })
    }

  }
}
</script>

<style>
</style>
