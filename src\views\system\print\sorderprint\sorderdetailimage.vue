<template>
        <table class="table table-bordered table-striped">
                        <tr>
                          <td colspan="12">
                            <div class="main-picture center-block">
                              <template v-if="item.GroupID!=6">
                                <div v-for="(item1,index1) in 4" v-bind:Key="'b'+index1" class="main-list">
                                  <div v-for="(item2,index2) in 3" :key="'c'+index2" class="item">
                                    <img v-for="(elem,elemindex) in ElemFilter(arrIntImgRule1[index1][index2], item.DetialModelImages)" :key="elemindex" :src="elem.Image" v-bind:ImageSeq="elem.ImageSeq" />
                                  </div>
                                </div>
                              </template>
                              <template v-else>
                                <div v-for="(item1,index1) in 3" v-bind:Key="'b'+index1" class="main-list-pants">
                                  <div v-for="(item2,index2) in 4" :key="'c'+index2" class="item">
                                    <template v-if="arrIntImgRule2[index1,index2]!=-1">
                                      <img v-for="(elem,elemindex) in ElemFilter(arrIntImgRule2[index1][index2], item.DetialModelImages)" :key="elemindex" :src="elem.Image" v-bind:ImageSeq="elem.ImageSeq" />
                                    </template>
                                  </div>
                                </div>
                              </template>
                            </div>
                          </td>
                        </tr>

                      </table>
</template>

<script>
export default {

}
</script>

<style>

</style>
