<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table ref='master_table' id="SymRoleMasterTable" :loading="tableLoading" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" @cell-click='tableCellClick' :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="code" title="角色编码" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="编码名称" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable show-overflow width="100px"></vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable show-overflow width="100px"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <vxe-button status="success" @click="addEvent()">确定</vxe-button>
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'SelectUser',
  mixins: [masterTableMixins],
  components: {
  },
  props: {
    form: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },

      api: {
        get: '/mtm/sym_role/get',
        add: '/mtm/SYM_WorkbenchReceiverDetail/adds'

      },
      footerCompanyInfo: false
    }
  },
  created () {
  },
  methods: {
    async addEvent () {
      var data = this.$refs.master_table.getCheckboxRecords()
      if (data.length <= 0) {
        this.$notify.error({
          title: '错误',
          message: '请勾选要添加的信息'
        })
        return
      }
      var rows = data.map(item => { return { roleID: item.id, workbenchID: this.form.id, isActive: true } })
      await this.$api.ActionRequest(this.api.add, rows).then(result => {
        this.$notify.success({
          title: '成功',
          message: '添加成功'
        })
        this.loadData()
        this.$emit('reload')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
