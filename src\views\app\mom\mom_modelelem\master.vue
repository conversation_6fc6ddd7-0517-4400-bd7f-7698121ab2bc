<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="genderID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.genderID" placeholder="性别" clearable>
                  <vxe-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemBaseID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelElemBaseID" filterable remote reserve-keyword placeholder="款式部位" size="mini" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in ModelElemBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>

            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table ref='master_table' id='MomModelelemMasterMasterTable' :loading="tableLoading" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="genderText" title="性别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemTypeText" title="款式类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemBaseText" title="款式部位" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemListText" title="款式" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="code" title="款式明细编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="款式明细名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="itemCode" title="默认货号编码" width="100px" sortable></vxe-table-column>
      <vxe-table-column field="itemName" title="默认货号名称" width="100px" sortable></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="默认原始货号" width="100px" sortable></vxe-table-column>
      <!-- <vxe-table-column field="Price" title="单价" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="qty" title="数量" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="count" title="关联版型数量" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sort" title="排序" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column title="操作" width="100"   :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </vxe-modal>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <detail-table :footerCompanyInfo="footerCompanyInfo" :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import DetailTable from './detail'
// import { cloneDeep } from 'lodash'
export default {
  name: 'MomModelelemMaster',
  mixins: [masterTableMixins],
  components: {
    DetailTable
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modelelem/get',
        add: '/mtm/mom_modelelem/adds',
        edit: '/mtm/mom_modelelem/updates',
        delete: '/mtm/mom_modelelem/deletes',
        GroupComboStore: '/mtm/combo/groupComboStore',
        ModelElemBaseComboStoreByQuery: '/mtm/comboQuery/ModelElemBaseComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      footerCompanyInfo: false,
      GroupComboStore: [],
      ModelElemBaseComboStoreByQuery: [],
      ModelElemListComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    this.remoteMethod1()
    this.remoteMethod2()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery, { gid: this.searchForm.modelElemBaseID }).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: this.searchForm.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    }
    // cellDblClick ({ row }) {
    //   this.$emit('nextpage', {
    //     pagename: 'detail',
    //     data: cloneDeep(row),
    //     keepalive: true,
    //     action: 'edit',
    //     masterSeach: this.searchForm
    //   })
    // }

  }
}
</script>

<style lang="scss" scoped>
</style>
