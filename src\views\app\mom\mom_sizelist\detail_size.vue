<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" @click="insertEvent()" v-if="menuAction.allowEdit" size="mini">新增</vxe-button>
          <vxe-button status="success" @click="savedata()" v-if="menuAction.allowEdit" size="mini">保存</vxe-button>
          <vxe-button v-if="menuAction.allowEdit" @click="tableImportEvent" status="warning">
            <d2-icon name="upload" />导入
          </vxe-button>
          <vxe-button status="primary" @click="tableExport" size="mini">
            <d2-icon name="download" />
            导出 Excel
          </vxe-button>
          <!-- <vxe-button status="primary" @click="exportEvent" size="mini">
            <d2-icon name="download" />
            导出 Excel
          </vxe-button> -->
          <vxe-button v-if="menuAction.allowEdit" @click="updateSizeElemShow=!updateSizeElemShow" status="warning">更新型</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="sizeElemaID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.sizeElemaID" filterable remote reserve-keyword placeholder="身高" :remote-method="SizeElemAMethod" clearable size="mini" style="width:90px">
                  <el-option v-for="item in SizeElemAComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sizeElembID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.sizeElembID" filterable remote reserve-keyword placeholder="胸围" :remote-method="SizeElemBMethod" clearable size="mini" style="width:90px">
                  <el-option v-for="item in SizeElemBComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="sizeElemcID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.sizeElemcID" filterable remote reserve-keyword placeholder="体型" :remote-method="SizeElemCMethod" clearable size="mini" style="width:90px">
                  <el-option v-for="item in SizeElemCComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="sizeElemdID" :item-render="{}"><template #default>
                <el-select v-model="searchForm.sizeElemdID" filterable remote reserve-keyword placeholder="臀围" :remote-method="SizeElemDMethod" clearable size="mini" style="width:90px">
                  <el-option v-for="item in SizeElemDComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>

            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelbase_master_table' :export-config="tableExports" keep-source ref='master_table' :edit-rules="formRules" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :edit-config="{trigger: 'click', selected: true, mode: 'cell',showStatus: true, icon: 'fa fa-pencil'}" :custom-config="{storage: true,}" :mouse-config="{selected: true}" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="code" title="规格编码" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="规格名称" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="cadSizeCode" title="CAD尺码" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="cadModelCode" title="CAD版型号" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="height" title="身高" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}" width="100px"></vxe-table-column>
      <!-- 上衣 -->
      <template v-if="form.groupID!=GroupConfig.xiku">
        <vxe-table-column field="collar" title="衬衣领围" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="frontLength" title="前衣长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="backMiddleLength" title="后中长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"> </vxe-table-column>
        <vxe-table-column field="bust" title="胸围" sortable width="150" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfBust" title="半胸围" sortable width="150" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="middleWaist" title="中腰" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="jacketHipline" title="上衣臀围" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfJacketHipline" title="半臀围" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="hem" title="下摆" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfHem" title="半下摆" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="shoulderWidth" title="肩宽" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="smallShoulderWidth" title="小肩宽" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="sleeveLength" title="袖长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="sleeveWidth" title="袖肥" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfSleeveWidth" title="半袖肥" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="cuff" title="袖口" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfCuff" title="半袖口" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="backWidth" title="背宽" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfBackWidth" title="半背宽" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="rightinSleeveLength" title="右内袖长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="leftinSleeveLength" title="左内袖长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="rightOutSleeveLength" title="右外袖长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="leftOutSleeveLength" title="左外袖长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfMiddleWaist" title="半腰围(上衣)" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>

      </template>
      <!-- 裤子 -->
      <template v-else>
        <vxe-table-column field="trouserLong" title="毛裤长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="netTrouserLong" title="净裤长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="waist" title="腰围" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfWaist" title="半腰围(裤子)" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="yaosheng" title="腰绳" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="waist1" title="腰围（拉前）" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="waist2" title="腰围（拉后）" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="hipline" title="臀围" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfHipline" title="半臀围(裤子)" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="crossCrotch" title="腿围/横档" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfCrossCrotch" title="半横档" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="smallCrossCrotch" title="腿围（裆下5CM）" sortable :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="kneeGirth" title="膝围" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfKneeGirth" title="半膝围" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="throughCrotch" title="通裆" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <!-- <vxe-table-column field="halfCrossCrotch" title="1/2通裆" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column> -->
        <vxe-table-column field="standCrotch" title="立裆" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="frontWaveLength" title="前浪长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="backWaveLength" title="后浪长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="trouserBottom" title="脚口" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="halfTrouserBottom" title="半脚口" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="zipperLength" title="拉链长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="lNetInnerTrouserLong" title="左内裤长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="rNetInnerTrouserLong" title="右内裤长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="leftOutTrouserLong" title="左外裤长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="rightOutTrouserLong" title="右外裤长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="half44CrossCrotch" title="裆下44CM" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="half15CrossCrotch" title="裆下15CM" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
        <vxe-table-column field="skirtlength" title="裙长" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number'}}"></vxe-table-column>
      </template>
      <vxe-table-column field="sizeElemaCode" title="身高A型" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sizeElembCode" title="胸围B型" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sizeElemcCode" title="体型C型" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sizeElemdCode" title="臀围D型" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sizeElemaID" title="身高" sortable width="100px" :visible="false"></vxe-table-column>
      <vxe-table-column field="sizeElembID" title="胸围" sortable width="100px" :visible="false"></vxe-table-column>
      <vxe-table-column field="sizeElemcID" title="体型" sortable width="100px" :visible="false"></vxe-table-column>
      <vxe-table-column field="sizeElemdID" title="臀围" sortable width="100px" :visible="false"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"></vxe-table-column>
      <!-- <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable  ></vxe-table-column> -->
      <!-- <vxe-table-column field="createBy" title="创建人" sortable show-overflow></vxe-table-column> -->
      <!-- <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable  ></vxe-table-column> -->
      <!-- <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable  > </vxe-table-column> -->
      <!-- <vxe-table-column field="modifyBy" title="修改人" sortable  ></vxe-table-column> -->
      <vxe-table-column title="操作" width="100px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <!-- <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button> -->
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form>
    </vxe-modal>
    <vxe-modal v-model="importDataShow" title="规格导入" width="800" resize>
      <template v-slot>
        <vxe-toolbar>
          <template v-slot:buttons>
            <el-row>
              <el-upload :before-upload="tableImport" action="default" size="mini">
                <el-button type="success" size="mini">
                  <d2-icon name="upload" />
                  选择要导入的 .xlsx 表格
                </el-button>
              </el-upload>
            </el-row>
          </template>
        </vxe-toolbar>
        <vxe-grid border ref="xGrid1" height="400" width="50%" :columns="GridSizeDatatableColumn" :data="GridSizeData" :edit-config="{trigger: 'click', mode: 'cell'}"></vxe-grid>
        <el-row>
          <vxe-button style="float: right;right: 10px;" status="success" @click="insertSizeData" v-if="menuAction.allowEdit" size="mini">提交</vxe-button>
        </el-row>
      </template>

    </vxe-modal>
    <vxe-modal v-model="updateSizeElemShow" title="更新型" width="50%" resize>
      <update-size-elem v-if="updateSizeElemShow" :rows="$refs.master_table.getCheckboxRecords()" :form="form" :updateSuccess="updateSuccess" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import Vue from 'vue'
import pluginExport from '@d2-projects/vue-table-export'
import pluginImport from '@d2-projects/vue-table-import'
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import UpdateSizeElem from './updatesizeelem.vue'
import { unionWith, cloneDeep } from 'lodash'
import XEUtils from 'xe-utils'
import config from '@/config.js'
Vue.use(pluginImport)
Vue.use(pluginExport)
export default {
  name: 'MomSizeListDetailSize', // 规格单
  mixins: [detailTableMixins, config],
  components: {
    UpdateSizeElem
  },
  props: {
    form: {
      required: true,
      type: Object
    }

  },
  data () {
    return {
      updateSizeElemShow: false,
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        modelTypeID: null
      },
      tableExports: {
        // 默认选中类型
        type: 'xlsx',
        // 自定义类型
        types: ['xlsx', 'csv', 'html', 'xml', 'txt']
      },
      importDataShow: false,
      groupid: null,
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'modelTypeID', title: '版型归类', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],

      api: {
        get: '/mtm/mOM_Size/get',
        add: '/mtm/mOM_Size/add',
        edit: '/mtm/mOM_Size/updates',
        delete: '/mtm/mOM_Size/deletes',
        ModelTypeComboStore: '/mtm/combo/modelTypeComboStore',
        SizeElemAComboStoreByQuery: '/mtm/comboQuery/SizeElemAComboStoreByQuery',
        SizeElemBComboStoreByQuery: '/mtm/comboQuery/SizeElemBComboStoreByQuery',
        SizeElemCComboStoreByQuery: '/mtm/comboQuery/SizeElemCComboStoreByQuery',
        SizeElemDComboStoreByQuery: '/mtm/comboQuery/SizeElemDComboStoreByQuery'
      },
      footerCompanyInfo: false,
      ModelTypeComboStore: [],
      SizeElemAComboStoreByQuery: [],
      SizeElemBComboStoreByQuery: [],
      SizeElemCComboStoreByQuery: [],
      SizeElemDComboStoreByQuery: [],
      GridSizeDatatableColumn: [
        { type: 'seq', width: 50 },
        { field: 'id', title: 'ID', width: '100', visible: false },
        { field: 'code', title: '规格编码', width: '100', editRender: { name: 'input' } },
        { field: 'codeName', title: '规格名称', width: '100', editRender: { name: 'input' } },
        { field: 'cadSizeCode', title: 'CAD尺码', width: '100', editRender: { name: 'input' } },
        { field: 'cadModelCode', title: 'CAD版型号', width: '100', editRender: { name: 'input' } },
        { field: 'height', title: '身高', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'collar', title: '衬衣领围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'frontLength', title: '前衣长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'backMiddleLength', title: '后中长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'bust', title: '胸围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'middleWaist', title: '中腰', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'jacketHipline', title: '上衣臀围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'hem', title: '下摆', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'shoulderWidth', title: '肩宽', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'sleeveLength', title: '袖长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'sleeveWidth', title: '袖肥', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfSleeveWidth', title: '半袖肥', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'cuff', title: '袖口', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfCuff', title: '半袖口', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'backWidth', title: '背宽', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'trouserLong', title: '毛裤长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'netTrouserLong', title: '净裤长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'waist', title: '腰围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'yaosheng', title: '腰绳', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'waist1', title: '腰围（拉前）', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'waist2', title: '腰围（拉后）', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'hipline', title: '臀围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'crossCrotch', title: '腿围/横档', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfCrossCrotch', title: '半横档', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'smallCrossCrotch', title: '腿围（裆下5CM）', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'kneeGirth', title: '膝围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfKneeGirth', title: '半膝围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'throughCrotch', title: '通裆', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        // { field: 'halfTroughCrotch', title: '1/2通裆', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'standCrotch', title: '立裆', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'frontWaveLength', title: '前浪长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'backWaveLength', title: '后浪长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'trouserBottom', title: '脚口', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfTrouserBottom', title: '半脚口', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'zipperLength', title: '拉链长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },

        { field: 'halfJacketHipline', title: '半臀围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'smallShoulderWidth', title: '小肩宽', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfBackWidth', title: '半背宽', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'rightinSleeveLength', title: '右内袖长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'leftinSleeveLength', title: '左内袖长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'rightOutSleeveLength', title: '右外袖长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'leftOutSleeveLength', title: '左外袖长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfBust', title: '半胸围', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfHem', title: '半下摆', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfMiddleWaist', title: '半腰围(上衣)', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfWaist', title: '半腰围(裤子)', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'lNetInnerTrouserLong', title: '左内裤长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'rNetInnerTrouserLong', title: '右内裤长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'leftOutTrouserLong', title: '左外裤长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'rightOutTrouserLong', title: '右外裤长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'half44CrossCrotch', title: '裆下44CM', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'half15CrossCrotch', title: '裆下15CM', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'halfHipline', title: '半臀围(裤子)', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'skirtlength', title: '裙长', width: '100', editRender: { name: '$input', props: { type: 'float' } } },
        { field: 'remark', title: '备注', width: '100', editRender: { name: 'input' } }
      ],
      GridSizeData: []
    }
  },
  async created () {
    this.loadData({ sizeListID: this.form.id }).then(({ data }) => {
      this.tableData = data
    })
    await this.getCombStore()
    await this.SizeElemAMethod()
    await this.SizeElemBMethod()
    await this.SizeElemCMethod()
    await this.SizeElemDMethod()
    this.$utils.find(this.formItems, item => item.field === 'modelTypeID').itemRender.options = this.ModelTypeComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelTypeComboStore).then(result => {
        this.ModelTypeComboStore = result
      })
    },
    // 导入
    tableImportEvent () {
      this.importDataShow = !this.importDataShow
      this.GridSizeData = []
    },
    async SizeElemAMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemAComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemAComboStoreByQuery = result
      })
    },
    async SizeElemBMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemBComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemBComboStoreByQuery = result
      })
    },
    async SizeElemCMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemCComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemCComboStoreByQuery = result
      })
    },
    async SizeElemDMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemDComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemDComboStoreByQuery = result
      })
    },
    updateSuccess () {
      this.updateSizeElemShow = false
      this.loadData({ sizeListID: this.form.id }).then(({ data }) => {
        this.tableData = data
      })
    },
    tableImport (file) {
      this.$import.xlsx(file)
        .then(({ header, results }) => {
          this.GridSizeData = []
          results.map(e => {
            var size = { sizeListID: this.form.id, isActive: true, id: e.ID }
            Object.keys(e).forEach(key => {
              var field = XEUtils.find(this.GridSizeDatatableColumn, item => item.title === key)
              if (field && field !== null) {
                XEUtils.set(size, field.field, e[key])
              }
            })
            this.GridSizeData.push(size)
          })
        })
      return false
    },

    // 导出
    tableExport () {
      var sizeColumns = this.GridSizeDatatableColumn.filter(item => {
        if (item.title && item.field) {
          return item
        }
      })
      // console.log(sizeColumns)
      sizeColumns = sizeColumns.map(item => {
        return { label: item.title, prop: item.field }
      })
      var _sizeColumns = sizeColumns.filter(item => {
        if (item.label) {
          return item
        }
      })
      var data = cloneDeep(this.tableData)
      data.forEach(item => {
        delete item._XID
      })
      this.$export.excel({
        columns: _sizeColumns,
        data: data,
        title: '规格单-' + new Date().getTime(),
        merges: []
        // merges: ['A1', 'BG']
      })
        .then(() => {
          this.$message('导出表格成功')
        })
    },

    // exportEvent() {
    //   // const toBuffer = (wbout) => {
    //   //   const buf = new ArrayBuffer(wbout.length)
    //   //   const view = new Uint8Array(buf)
    //   //   for (let index = 0; index !== wbout.length; ++index) view[index] = wbout.charCodeAt(index) & 0xFF
    //   //   return buf
    //   // }
    //   // // 转换数据
    //   // const table = this.$refs.master_table.$el.querySelector('.body--wrapper>.vxe-table--body')
    //   // const book = XLSX.utils.book_new()
    //   // const sheet = XLSX.utils.table_to_sheet(table)
    //   // XLSX.utils.book_append_sheet(book, sheet)
    //   // const wbout = XLSX.write(book, { bookType: 'xlsx', bookSST: false, type: 'binary' })
    //   // const blob = new Blob([toBuffer(wbout)], { type: 'application/octet-stream' })
    //   // // 保存导出
    //   // this.$XSaveFile({ filename: '数据导出', type: 'xlsx', content: blob })
    //   this.$refs.master_table.exportData({
    //     filename: '导出',
    //     sheetName: 'Sheet1',
    //     type: 'xlsx'
    //   })
    // },
    async insertEvent (row) {
      const record = {
        sizeListID: this.form.id,
        height: 0,
        sharpFrontLength: 0,
        frontLength: 0,
        backMiddleLength: 0,
        bust: 0,
        middleWaist: 0,
        shoulderWidth: 0,
        smallShoulderWidth: 0,
        sleeveLength: 0,
        collar: 0,
        backWidth: 0,
        halfBackWidth: 0,
        sleeveWidth: 0,
        halfSleeveWidth: 0,
        cuff: 0,
        halfCuff: 0,
        jacketHipline: 0,
        hem: 0,
        trouserLong: 0,
        netTrouserLong: 0,
        innerTrouserLong: 0,
        netInnerTrouserLong: 0,
        waist: 0,
        yaosheng: 0,
        waist1: 0,
        waist2: 0,
        hipline: 0,
        smallCrossCrotch: 0,
        halfSmallCrossCrotch: 0,
        crossCrotch: 0,
        halfCrossCrotch: 0,
        kneeGirth: 0,
        halfKneeGirth: 0,
        frontCrotch: 0,
        frontWaveLength: 0,
        backWaveLength: 0,
        trouserBottom: 0,
        halfTrouserBottom: 0,
        throughCrotch: 0,
        standCrotch: 0,
        zipperLength: 0,
        halfJacketHipline: 0,
        rightinSleeveLength: 0,
        leftinSleeveLength: 0,
        halfBust: 0,
        halfHem: 0,
        halfMiddleWaist: 0,
        halfWaist: 0,
        lNetInnerTrouserLong: 0,
        rNetInnerTrouserLong: 0,
        half44CrossCrotch: 0,
        half15CrossCrotch: 0,
        halfHipline: 0,
        leftOutSleeveLength: 0,
        rightOutSleeveLength: 0
      }
      var xtable = this.$refs[this.tableRef]
      const { row: newRow } = await xtable.insertAt(record, row)
      await xtable.setActiveCell(newRow, 'code')
    },
    async removeEvent (row) {
      var xtable = this.$refs[this.tableRef]
      xtable.remove(row)
      if (row.id !== undefined) {
        await this.$api.ActionRequest(this.api.delete, [row]).then(res => {
          this.loadData({ sizeListID: this.form.id })
        })
      }
    },
    getTableData () {
      var xtable = this.$refs[this.tableRef]
      var insertData = xtable.getInsertRecords()
      var updateData = xtable.getUpdateRecords()
      var data = unionWith(insertData, updateData)
      return data
    },
    async savedata () {
      var b = await this.fullValidEvent()
      if (b) {
        var data = this.getTableData()
        this.$api.ActionRequest(this.api.edit, data).then(result => {
          this.$XModal.message({ message: '保存成功', status: 'success' })
          this.loadData({ sizeListID: this.form.id }).then(({ data }) => {
            this.tableData = data
          })
        })
      }
    },
    insertSizeData () {
      this.$api.ActionRequest(this.api.edit, this.GridSizeData).then(result => {
        this.$XModal.message({ message: '保存成功', status: 'success' })
        this.loadData({ sizeListID: this.form.id }).then(({ data }) => {
          this.tableData = data
          this.importDataShow = !this.importDataShow
          this.GridSizeData = []
        })
      })
    },
    async fullValidEvent () {
      var xtable = this.$refs.master_table
      const errMap = await xtable.fullValidate().catch(errMap => errMap)
      if (errMap) {
        const msgList = []
        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            const { rowIndex, column, rules } = params
            rules.forEach(rule => {
              msgList.push(`第 ${rowIndex} 行 ${column.title} 校验错误：${rule.message}`)
            })
          })
        })
        this.$XModal.message({
          status: 'error',
          message: () => {
            return [
              <div class="red" style="max-height: 400px;overflow: auto;">
                {
                  msgList.map(msg => <div>{msg}</div>)
                }
              </div>
            ]
          }
        })
        return false
      } else {
        return true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
