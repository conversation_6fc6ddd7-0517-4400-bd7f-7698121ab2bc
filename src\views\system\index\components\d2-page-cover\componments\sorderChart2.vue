<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <!-- <strong>下单数</strong> -->
      <div style="float: right; padding: 3px 0" type="text">
        <el-radio-group v-model="day" size="mini" @change="change">
          <el-radio-button label="7">7天</el-radio-button>
          <el-radio-button label="30">30天</el-radio-button>
          <el-radio-button label="180">6个月</el-radio-button>
          <el-radio-button label="365">1年</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div>
      <div ref="main" style="width: 800px;height:400px;"></div>
    </div>
  </el-card>
</template>

<script>
// 订单
import commonjs from './common.js'
export default {
  mixins: [commonjs],
  data () {
    return {
      timer: '',
      api: {
        get: 'mtm/indexBoard/SorderByDate'
      },
      day: 7,
      option: {
        title: {
          text: '下单数'
          // subtext: 'Living Expenses in Shenzhen'
        },
        legend: {
          data: ['总数', '正式订单', '待定订单']
        }
      }
    }
  },
  async created () {
    await this.get()
  },
  methods: {
    setOptions (data) {
      var xAxis = []
      var series1 = []
      var series2 = []
      var series3 = []
      data.forEach(a => {
        xAxis.push(a.createOn)
        series1.push(a.count)
        series2.push(a.state1)
        series3.push(a.state2)
      })
      // 填入数据
      this.myChart.setOption({
        xAxis: {
          data: xAxis || []
        },
        series: [
          {
            label: {
              show: true,
              position: 'inside'
            },
            name: '总数',
            type: 'bar',
            data: series1
          },
          {
            label: {
              show: true,
              position: 'inside'
            },
            name: '待定订单',
            type: 'bar',
            data: series2
          },
          {
            name: '正式订单',
            label: {
              show: true,
              position: 'inside'
            },
            type: 'bar',
            data: series3
          }
        ]
      })
    }

  }

}
</script>

<style>
</style>
