#!/bin/sh

# 设置默认值
VUE_APP_USER_API=${VUE_APP_USER_API:-"https://api-sso-test.kngintl.com/api/"}
VUE_APP_API=${VUE_APP_API:-"https://api-mtm-test.kngintl.com/api/"}
VUE_APP_MESAPI=${VUE_APP_MESAPI:-"https://api-mes-test.kngintl.com/api/"}
VUE_APP_TITLE=${VUE_APP_TITLE:-"C2M智尚工场"}

echo "=== 启动脚本开始执行 ==="
echo "当前环境变量："
echo "VUE_APP_USER_API: ${VUE_APP_USER_API}"
echo "VUE_APP_API: ${VUE_APP_API}"
echo "VUE_APP_MESAPI: ${VUE_APP_MESAPI}"
echo "VUE_APP_TITLE: ${VUE_APP_TITLE}"

# 检查index.html文件是否存在
if [ ! -f "/usr/share/nginx/html/index.html" ]; then
    echo "❌ 错误: index.html 文件不存在"
    exit 1
fi

echo "检查替换前的index.html内容："
grep -n "{{VUE_APP" /usr/share/nginx/html/index.html || echo "未找到占位符"

# 方案1: 尝试替换index.html中的占位符
echo "开始替换占位符..."
sed -i "s|{{VUE_APP_USER_API}}|${VUE_APP_USER_API}|g" /usr/share/nginx/html/index.html
sed -i "s|{{VUE_APP_API}}|${VUE_APP_API}|g" /usr/share/nginx/html/index.html
sed -i "s|{{VUE_APP_MESAPI}}|${VUE_APP_MESAPI}|g" /usr/share/nginx/html/index.html
sed -i "s|{{VUE_APP_TITLE}}|${VUE_APP_TITLE}|g" /usr/share/nginx/html/index.html

# 方案2: 生成独立的配置文件（主要方案）
echo "生成配置文件 config.js..."
cat > /usr/share/nginx/html/config.js << EOF
// 运行时配置文件 - 强制覆盖所有配置
(function() {
  // 强制设置配置
  window.VUE_APP_USER_API = '${VUE_APP_USER_API}';
  window.VUE_APP_API = '${VUE_APP_API}';
  window.VUE_APP_MESAPI = '${VUE_APP_MESAPI}';
  window.VUE_APP_TITLE = '${VUE_APP_TITLE}';

  // 同时设置到process.env（如果存在）
  if (typeof process !== 'undefined' && process.env) {
    process.env.VUE_APP_USER_API = '${VUE_APP_USER_API}';
    process.env.VUE_APP_API = '${VUE_APP_API}';
    process.env.VUE_APP_MESAPI = '${VUE_APP_MESAPI}';
    process.env.VUE_APP_TITLE = '${VUE_APP_TITLE}';
  }

  // 创建全局配置对象
  window.RUNTIME_CONFIG = {
    VUE_APP_USER_API: '${VUE_APP_USER_API}',
    VUE_APP_API: '${VUE_APP_API}',
    VUE_APP_MESAPI: '${VUE_APP_MESAPI}',
    VUE_APP_TITLE: '${VUE_APP_TITLE}'
  };

  console.log('🔧 运行时配置已强制加载:', window.RUNTIME_CONFIG);
  console.log('🔧 window.VUE_APP_API =', window.VUE_APP_API);

  // 标记配置已加载
  window.CONFIG_LOADED = true;
})();
EOF

echo "检查生成的配置文件："
cat /usr/share/nginx/html/config.js

echo "检查替换后的index.html内容："
grep -n "window.VUE_APP\|config.js" /usr/share/nginx/html/index.html || echo "未找到相关内容"

echo "=== 配置替换完成，启动nginx ==="

# 启动nginx
nginx -g "daemon off;"
