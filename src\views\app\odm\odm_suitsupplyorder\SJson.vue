<template>
  <d2-container>
    <pre v-html="formattedJSON" style="height:300px"></pre>
  </d2-container>
</template>
<script>
export default {
  props: {
    json: Object
  },
  mounted () {
    this.formattedJSON = syntaxHighlight(this.json)
  },
  data () {
    return {
      formattedJSON: ''
    }
  }
}
/* eslint-disable */
function syntaxHighlight (json) {
  if (typeof json !== 'string') {
    json = JSON.stringify(json, undefined, 2)
  }
  json = json
    .replace(/&/g, '&')
    .replace(/</g, '<')
    .replace(/>/g, '>')
  return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
    function (match) {
      var cls = 'number'
      if (/^"/.test(match)) {
        if (/:$/.test(match)) {
          cls = 'key'
        } else {
          cls = 'string'
        }
      } else if (/true|false/.test(match)) {
        cls = 'boolean'
      } else if (/null/.test(match)) {
        cls = 'null'
      }
      return '<span class="' + cls + '">' + match + '</span>'
    }
  )
}
/* eslint-enable */
</script>
<style scoped>
pre {
  outline: 1px solid #ccc;
  padding: 5px;
  margin: 5px;
  overflow: auto;
}

.string {
  color: green;
}

.number {
  color: darkorange;
}

.boolean {
  color: blue;
}

.null {
  color: magenta;
}

.key {
  color: red;
}
</style>
