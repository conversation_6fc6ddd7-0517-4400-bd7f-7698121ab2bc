import { Message } from 'element-ui'
export default ({ service, request, faker, tools }) => ({
  /**
     * @description 登录
     * @param {Object} data 登录携带的信息
     */
  ActionRequest(apiurl, data, successmsg = false, type = 'post', baseurl = window.VUE_APP_API || process.env.VUE_APP_API) {
    var urlarr = apiurl.split('/')
    if (urlarr.length > 0) {
      var sp = urlarr[1]
      if (sp === 'mes') {
        baseurl = window.VUE_APP_MESAPI || process.env.VUE_APP_MESAPI
      }
    }
    switch (type) {
      case 'post':
        var res = request({
          url: apiurl,
          method: 'post',
          data: data || {},
          baseurl: baseurl
        })
        if (successmsg) {
          Message({
            message: '成功',
            type: 'success'
          })
        }
        return res
      case 'get':
        return request({
          url: apiurl,
          method: 'get',
          params: data,
          baseurl: baseurl
        })
      default:
        return request({
          url: apiurl,
          method: type,
          data: data || {}
        })
    }
  },
  ActionRequestOther(apiurl, data, successmsg = false, type = 'post', baseurl = window.VUE_APP_API || process.env.VUE_APP_API) {
    switch (type) {
      case 'post':
        var res = request({
          url: apiurl,
          method: 'post',
          data: data || {},
          baseurl: baseurl
        })
        if (successmsg) {
          Message({
            message: '成功',
            type: 'success'
          })
        }
        return res
      case 'get':
        return request({
          url: apiurl,
          method: 'get',
          params: data,
          baseurl: baseurl
        })
      default:
        return request({
          url: apiurl,
          method: type,
          data: data || {}
        })
    }
  },
  ActionFileRequest(apiurl, data, successmsg = false, type = 'post', baseurl = window.VUE_APP_API || process.env.VUE_APP_API) {
    const form = new FormData()
    const keys = Object.keys(data)
    keys.forEach(key => {
      form.append(key, data[key])
    })
    switch (type) {
      case 'post':
        var res = request({
          url: apiurl,
          method: 'post',
          data: form || data,
          baseurl: baseurl,
          ContentType: 'multipart/form-data'
        })
        if (successmsg) {
          Message({
            message: '成功',
            type: 'success'
          })
        }
        return res
      default:
        return request({
          url: apiurl,
          method: type,
          data: form || data,
          ContentType: 'multipart/form-data'
        })
    }
  },
  ActionExcelRequest(apiurl, data, successmsg = false, type = 'post', baseurl = window.VUE_APP_API || process.env.VUE_APP_API) {
    switch (type) {
      case 'post':
        var res = request({
          url: apiurl,
          method: 'post',
          header: { 'Content-Type': 'application/xls' },
          responseType: 'blob', // 返回格式，默认json，可选arraybuffer、blob、document、json、text、stream
          data: data || {},
          baseurl: baseurl
          // ContentType: 'multipart/form-data'
        })
        if (successmsg) {
          Message({
            message: '成功',
            type: 'success'
          })
        }
        return res
      default:
        return request({
          url: apiurl,
          method: type,
          data: data,
          ContentType: 'multipart/form-data'
        })
    }
  },
  ActionAndTokenRequest(apiurl, data, token = '', successmsg = false, type = 'post', baseurl = window.VUE_APP_API || process.env.VUE_APP_API) {
    switch (type) {
      case 'post':
        var res = request({
          url: apiurl,
          method: 'post',
          Authorization: token,
          responseType: 'blob', // 返回格式，默认json，可选arraybuffer、blob、document、json、text、stream
          data: data || {},
          baseurl: baseurl
          // ContentType: 'multipart/form-data'
        })
        if (successmsg) {
          Message({
            message: '成功',
            type: 'success'
          })
        }
        return res
      default:
        return request({
          url: apiurl,
          method: type,
          data: data,
          ContentType: 'multipart/form-data'
        })
    }
  }
})
