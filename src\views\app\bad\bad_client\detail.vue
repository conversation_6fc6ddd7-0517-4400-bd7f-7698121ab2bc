<template>
  <d2-container class="clientPersonDetail">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="editEvent" v-if="menuAction.allowEdit">保存</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>

    </template>
    <template>
      <d2-container>
        <split-pane :min-percent='15' :default-percent='20' split="vertical">
          <template slot="paneL">
            <!-- <vxe-form :data="form" :items="formItems" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent"></vxe-form> -->

            <vxe-form :data="form" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
              <vxe-form-item title="编码名称" field="codeName" span="24">
                <vxe-input v-model="form.codeName" placeholder="请输入编码" clearable></vxe-input>
              </vxe-form-item>
              <vxe-form-item title="客户分类" field="clientGroup" span="24">
                <template #default="{ data }">
                  <vxe-select v-model="data.clientGroup" placeholder="客户分类" clearable>
                    <vxe-option v-for="num in ClientGroupComboStore" :key="num.label" :value="num.value" :label="num.label"></vxe-option>
                  </vxe-select>
                </template>
              </vxe-form-item>
              <vxe-form-item title="客户等级" field="clientGrade" span="24">
                <template #default="{ data }">
                  <vxe-select v-model="data.clientGrade" placeholder="客户等级" clearable>
                    <vxe-option v-for="num in ClientGradeComboStore" :key="num.label" :value="num.value" :label="num.label"></vxe-option>
                  </vxe-select>
                </template>
              </vxe-form-item>
              <vxe-form-item title="简称" field="shortName" span="24">
                <vxe-input v-model="form.shortName" placeholder="请输入编码" clearable></vxe-input>
              </vxe-form-item>
              <vxe-form-item title="联系人" field="contact" span="24">
                <vxe-input v-model="form.contact" placeholder="请输入编码" clearable></vxe-input>
              </vxe-form-item>
              <vxe-form-item title="手机号" field="tel" span="24">
                <vxe-input v-model="form.tel" placeholder="请输入编码" clearable></vxe-input>
              </vxe-form-item>
              <vxe-form-item title="支付模式" field="accountPaymentGroup" span="24">
                <template #default="{ data }">
                  <el-select v-model="data.accountPaymentGroup" filterable remote reserve-keyword placeholder="支付模式" size="mini" clearable>
                    <el-option v-for="item in AccountPaymentGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item> <!-- <vxe-form-item title="余额" field="accountBalance" span="24">
                <vxe-input v-model="form.accountBalance" placeholder="余额" clearable type="float">
                  <template #suffix>
                    ￥
                  </template>
                </vxe-input>
              </vxe-form-item> -->
              <vxe-form-item title="额度" field="accountLimit" span="24">
                <vxe-input v-model="form.accountLimit" placeholder="额度" clearable type="float">
                  <template #suffix>
                    ￥
                  </template>
                </vxe-input>
              </vxe-form-item>
              <vxe-form-item title="专属折扣" field="discount" span="24">
                <vxe-input v-model="form.discount" placeholder="专属折扣" clearable type="float">
                  <template #suffix>
                    %
                  </template>
                </vxe-input>
              </vxe-form-item>
              <vxe-form-item title="面料耗量折扣" field="itemDiscount" span="24">
                <vxe-input v-model="form.itemDiscount" placeholder="面料耗量折扣" clearable type="float">
                  <template #suffix>
                    %
                  </template>
                </vxe-input>
              </vxe-form-item>
              <vxe-form-item title="传真" field="fax" span="24">
                <vxe-input v-model="form.fax" placeholder="请输入编码" clearable></vxe-input>
              </vxe-form-item>
              <vxe-form-item title="电话号码" field="mobile" span="24">
                <vxe-input v-model="form.mobile" placeholder="请输入编码" clearable></vxe-input>
              </vxe-form-item>
              <vxe-form-item title="邮箱" field="email" span="24">
                <vxe-input v-model="form.email" placeholder="请输入编码" clearable></vxe-input>
              </vxe-form-item>
              <vxe-form-item title="地址" field="address" span="24">
                <vxe-input v-model="form.address" placeholder="请输入编码" clearable></vxe-input>
              </vxe-form-item>
              <vxe-form-item title="继承总店" field="inheritedParentModel" span="12">
                <template>
                  <vxe-switch v-model="form.inheritedParentModel" open-label="是" close-label="否"></vxe-switch>
                </template>
              </vxe-form-item>
              <vxe-form-item title="继承总店账单" field="inheritedParentBill" span="12">
                <template>
                  <vxe-switch v-model="form.inheritedParentBill" open-label="是" close-label="否"></vxe-switch>
                </template>
              </vxe-form-item>
              <vxe-form-item title="活动" field="isActive" span="24">
                <template>
                  <vxe-switch v-model="form.isActive" open-label="是" close-label="否"></vxe-switch>
                </template>
              </vxe-form-item>

              <vxe-form-item title="备注" field="remark" span="24">
                <template>
                  <vxe-textarea v-model="form.remark" placeholder="备注" show-word-count></vxe-textarea>
                </template>
              </vxe-form-item>
              <vxe-form-item title="总公司" field="parentClientID" span="24">
                <template #default="{ data }">
                  <el-select v-model="data.parentClientID" filterable remote reserve-keyword placeholder="父级客户" size="mini" :remote-method="ClientComboMethod" clearable>
                    <el-option v-for="(item,index) in ClientComboStore" :key="index" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>
            </vxe-form>
          </template>
          <template slot="paneR">
            <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" style="height:99%">
              <el-tab-pane label="顾客" name="clientperson">
                <client-person :form="form" ref='ClientPerson' />
              </el-tab-pane>
              <el-tab-pane label="店铺/分公司" name="clientshop">
                <client-shop :form="form" ref='ClientShop' />
              </el-tab-pane>
              <el-tab-pane label="地址" name="clientaddress">
                <client-address :form="form" ref="ClientAddress" />
              </el-tab-pane>
              <el-tab-pane label="关联版型系列" name="clientmodelclass">
                <client-model-class :form="form" ref="ClientModelClass" />
              </el-tab-pane>
              <el-tab-pane label="关联版型" name="clientmodel">
                <client-model :form="form" ref="ClientModel" />
              </el-tab-pane>
              <el-tab-pane label="绑定款式明细和物料" name="clientmodelelem">
                <client-model-elem :form="form" ref="ClientModelElem" />
              </el-tab-pane>
              <el-tab-pane label="工艺价格维护" name="modelelempricebyclient">
                <model-elem-price-by-client :form="form" ref="modelelempricebyclient" />
              </el-tab-pane>
              <el-tab-pane label="客户指定面料" name="clientitem">
                <client-item :form="form" ref="clientitem" />
              </el-tab-pane>
              <el-tab-pane label="客户账单" name="sorderbill">
                <sorder-bill :form="form" ref="sorderbill" />
              </el-tab-pane>
            </el-tabs>
          </template>
        </split-pane>
      </d2-container>
    </template>
  </d2-container>
</template>

<script>
import actionMixins from '@/mixins/action_mixins/index'
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import ClientPerson from './clientperson'
import ClientShop from './clientshop'
import ClientAddress from './clientaddress'
import ClientModel from './clientmodel'
import ClientModelElem from './clientmodelelem'
import ClientModelClass from './clientmodelclass'
import ModelElemPriceByClient from './modelelempricebyclient.vue'
import ClientItem from './clientitem.vue'
import SorderBill from './sorderbill.vue'
import { mapActions } from 'vuex'
// import Qs from 'qs'
export default {
  name: 'BadClientDetail',
  mixins: [actionMixins, detailTableMixins],
  components: {
    ClientPerson,
    ClientAddress,
    ClientModel,
    ClientModelElem,
    ClientModelClass,
    ModelElemPriceByClient,
    ClientItem,
    SorderBill,
    ClientShop
  },
  props: {
    form: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      activeName: 'clientperson',
      formRules: {
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 3, max: 12, message: '长度在 3 到 20 个字符' }],
        shortName: [{ required: true, message: '请输入简称' }, { min: 2, max: 12, message: '长度在 2 到 20 个字符' }],
        clientGroup: [{ required: true, message: '请选择客户分类' }],
        clientGrade: [{ required: true, message: '请选择客户等级' }],
        contact: [{ required: true, message: '请输入联系人' }],
        tel: [{ required: true, message: '请输入手机号' }],
        address: [{ required: true, message: '请输入地址' }]
      },

      api: {
        edit: '/mtm/bad_client/updates',
        ClientComboStore: '/mtm/comboQuery/clientComboStoreByQuery',
        ClientGroupComboStore: '/mtm/combo/clientGroupComboStore',
        ClientGradeComboStore: '/mtm/combo/clientGradeComboStore',
        AccountPaymentGroupComboStore: '/mtm/combo/AccountPaymentGroupComboStore'
      },
      AccountPaymentGroupComboStore: [],
      ClientGroupComboStore: [],
      ClientGradeComboStore: [],
      ClientComboStore: []

    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'clientGroup').itemRender.options = this.ClientGroupComboStore
    // this.$utils.find(this.formItems, item => item.field === 'clientGrade').itemRender.options = this.ClientGradeComboStore
  },
  methods: {
    ...mapActions('app/bad/bad_client', ['loadBadClient']),
    // goback () {
    //   this.$emit('nextpage', { pagename: 'master', data: {}, keepalive: false })
    // },
    ClientComboMethod (query) {
      this.$api.ActionRequest(this.api.ClientComboStore, { text: query }).then(result => {
        this.ClientComboStore = result
      })
    },
    submitEvent () { },
    handleClick () { },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ClientComboStore, { gid: this.form.parentClientID }).then(result => {
        this.ClientComboStore = result
      })
      await this.$api.ActionRequest(this.api.ClientGroupComboStore, { gid: this.form.parentClientID }).then(result => {
        this.ClientGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.ClientGradeComboStore, { gid: this.form.parentClientID }).then(result => {
        this.ClientGradeComboStore = result
      })
      await this.$api.ActionRequest(this.api.AccountPaymentGroupComboStore, { gid: this.form.parentClientID }).then(result => {
        this.AccountPaymentGroupComboStore = result
      })
    },
    async editEvent () {
      var personData = this.$refs.ClientPerson.getTableData()
      var a1 = await this.$refs.ClientPerson.fullValidEvent()
      // var shopData = this.$refs.ClientShop.getTableData()
      // var a2 = await this.$refs.ClientShop.fullValidEvent()
      var addressData = this.$refs.ClientAddress.getTableData()
      var a3 = await this.$refs.ClientAddress.fullValidEvent()
      var clientModel = this.$refs.ClientModel.tableData
      if (!a1 || !a3) {
        return
      }
      this.form.clientPerson = personData
      // this.form.clientShop = shopData
      this.form.clientAddress = addressData
      this.form.clientModel = clientModel
      delete this.form.accountBalance
      await this.$api.ActionRequest(this.api.edit, [this.form]).then(result => {
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.$refs.ClientPerson.loadData({ id: this.form.id })
        this.$refs.ClientShop.loadData({ id: this.form.id })
        this.$refs.ClientAddress.loadData({ id: this.form.id })
        this.$refs.ClientModel.loadData({ id: this.form.id })
      })
    }
  }
}
</script>

<style  lang="scss" >
.clientPersonDetail {
  .el-tabs__content {
    height: 90%;
  }
  .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }
  .d2-container-full__body {
    overflow-x: hidden !important;
  }
  .splitter-paneL {
    overflow-y: auto;
  }
}
</style>
