<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>

        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="cloneForm" @reset="resetEvent">
            <vxe-form-item title="替换项"></vxe-form-item>
            <vxe-form-item field="modelBaseID">
              <template #default="{ data }">
                <el-select v-model="data.modelBaseID" filterable remote reserve-keyword placeholder="基础版型" :remote-method="ModelBaseComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemID1">
              <template #default="{ data }">
                <el-select v-model="data.modelElemID1" filterable remote reserve-keyword placeholder="款式明细" :remote-method="ModelElemComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>

          </vxe-form>
          <vxe-button @click="cloneplushEvent">批量复制</vxe-button>

        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelBaseID">
              <template #default="{ data }">
                <el-select v-model="data.modelBaseID" filterable remote reserve-keyword placeholder="基础版型" :remote-method="ModelBaseComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">

                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemID1">
              <template #default="{ data }">
                <el-select v-model="data.modelElemID1" filterable remote reserve-keyword placeholder="款式明细" :remote-method="ModelElemComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="positionID">
              <template #default="{ data }">
                <el-select v-model="data.positionID" filterable placeholder="位置" clearable size="mini">
                  <el-option v-for="item in PositionComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelbaseimageMasterTable' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="mix" title="合成图" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelBaseCode" title="基础版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelBaseName" title="基础版型名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemCode1" title="款式明细编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemName1" title="款式明细名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelImage" title="图片" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="imageSeq" title="图片顺序" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="positionID" title="图片位置" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="imageUrl" title="图片" sortable>
        <template v-slot="{ row }">
          <el-popover placement="right-end" width="200" trigger="hover">
            <el-image :src="row.imageUrl" fit="fill">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.imageUrl" fit="fill" slot="reference">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>

        </template>
      </vxe-table-column>

      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
  </d2-container>
</template>
<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'clonepush',
  mixins: [masterTableMixins],
  data () {
    return {
      cloneForm: {
        modelBaseID: null,
        modelElemID1: null
      },
      api: {
        get: '/mtm/mom_modelbaseimage/get',
        clone: '/mtm/mom_modelbaseimage/clones',
        ModelBaseComboStoreByQuery: '/mtm/comboQuery/ModelBaseComboStoreByQuery',
        ModelImageComboStoreByQuery: '/mtm/comboQuery/ModelImageComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        PositionComboStore: '/mtm/combo/PositionComboStore'

      },
      ModelImageComboStoreByQuery: [],
      ModelElemComboStoreByQuery: [],
      ModelBaseComboStoreByQuery: [],
      PositionComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.ModelBaseComboStoreByQueryMethod()
    this.ModelElemComboStoreByQueryMethod()
    this.ModelImageComboStoreByQueryMethod()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.PositionComboStore).then(result => {
        this.PositionComboStore = result
      })
    },
    ModelBaseComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelBaseComboStoreByQuery, { text: query }).then(result => {
        this.ModelBaseComboStoreByQuery = result
      })
    },
    ModelElemComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    ModelImageComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
    },
    cloneplushEvent () {
      const selectRecords = this.$refs.master_table.getCheckboxRecords()
      if (selectRecords.length === 0) {
        this.$XModal.message({ message: '请先勾选下方要替换的数据', status: 'error' })
        return
      }
      if (this.cloneForm.modelBaseID === null && this.cloneForm.modelElemID1 === null) {
        this.$XModal.message({ message: '请选择替换项中要替换的数据', status: 'error' })
      }
      selectRecords.forEach(element => {
        if (this.cloneForm.modelBaseID !== null) {
          element.modelBaseID = this.cloneForm.modelBaseID
        }
        if (this.cloneForm.modelElemID1 !== null) {
          element.modelElemID1 = this.cloneForm.modelElemID1
        }
      })
      this.$api.ActionRequest(this.api.clone, selectRecords).then(result => {
        this.$XModal.message({ message: '复制成功', status: 'success' })
        this.loadData()
      })
    }
  }
}
</script>
