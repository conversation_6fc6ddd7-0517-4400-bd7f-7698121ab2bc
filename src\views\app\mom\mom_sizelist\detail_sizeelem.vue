<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button status="perfect" @click="$refs.master_table.setAllTreeExpand(true)">展开</vxe-button>
          <vxe-button status="perfect" @click="$refs.master_table.clearTreeExpand()">折叠</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="saveEvent" v-if="menuAction.allowAdd">保存</vxe-button>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id='MomModelbase_master_table' ref='master_table' @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" :tree-config="{children: 'children', iconOpen: 'fa fa-minus-square-o', iconClose: 'fa fa-plus-square-o'}" class="MomSizeListDetailSizeElem" :stripe="false">
      <vxe-table-column type="seq" width="60px" tree-node></vxe-table-column>
      <vxe-table-column field="text" title="款式" width="100px"></vxe-table-column>
      <vxe-table-column field="selected" title="选中" width="100px">
        <template v-slot="{row}">
          <vxe-checkbox v-if="row.text!='身高'&&row.text!='胸围/腰围'&&row.text!='体型'&&row.text!='臀围'" v-model="row.selected"></vxe-checkbox>
        </template>
      </vxe-table-column>
      <vxe-table-column field="sequence" title="顺序" width="100px"></vxe-table-column>
    </vxe-table>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'MomSizeListDetailSizeElem', // 规格组成
  mixins: [detailTableMixins],
  components: {
  },
  props: {
    form: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        modelTypeID: null
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'modelTypeID', title: '版型归类', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mOM_SizeList/getSizeElem',
        // add: '/mtm/mom_modelbase/adds',
        edit: '/mtm/mOM_SizeList/UpdatesSizeElem'
        // delete: '/mtm/mom_modelbase/deletes',
        // ModelTypeComboStore: '/mtm/combo/modelTypeComboStore'
      },
      footerCompanyInfo: false,
      ModelTypeComboStore: []
    }
  },
  async created () {
    this.loadData({ id: this.form.id }).then(({ data }) => {
      this.tableData = data
    })
    await this.getCombStore()
    this.$utils.find(this.formItems, item => item.field === 'modelTypeID').itemRender.options = this.ModelTypeComboStore
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.ModelTypeComboStore).then(result => {
      //   this.ModelTypeComboStore = result
      // })
    },
    saveEvent () {
      this.$api.ActionRequest(this.api.edit, this.tableData).then(result => {
        this.$XModal.message({ message: '保存成功', status: 'success' })
        this.loadData({ id: this.form.id }).then(({ data }) => {
          this.tableData = data
        })
      })
    }
  }
}
</script>

<style lang="scss">
.MomSizeListDetailSizeElem {
  .d2-container-full__body {
    overflow: hidden !important;
  }
}
</style>
