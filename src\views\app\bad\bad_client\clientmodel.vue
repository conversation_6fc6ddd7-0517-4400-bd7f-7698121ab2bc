<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-save" status="perfect" @click="saveEvent" v-if="menuAction.allowAdd">保存</vxe-button> -->
          <vxe-button status="perfect" @click="selectAll(true)" v-if="menuAction.allowAdd">全选</vxe-button>
          <vxe-button status="perfect" @click="selectAll(false)" v-if="menuAction.allowAdd">取消全选</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="saveEvent" v-if="menuAction.allowAdd">保存</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="classID">
              <template #default="{ data }">
                <el-select v-model="data.classID" filterable placeholder="版型系列" size="mini" clearable>
                  <el-option v-for="item in ModelGroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="品类" size="mini" clearable style="width: 100px;">
                  <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isSelected">
              <template #default="{ data }">
                <el-select v-model="data.isSelected" filterable placeholder="是否关联" size="mini" clearable style="width: 100px;">
                  <el-option v-for="item in boolList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="BadClientModelDetailModel" keep-source ref="clientModelxTable" :row-class-name="rowClassName" height="auto" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="groupName" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelCode" title="版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelName" title="版型名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="selected" title="关联" sortable width="100px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.selected"></vxe-checkbox>
        </template>
      </vxe-table-column>
      <vxe-table-column field="isFavorite" title="收藏" sortable width="100px">
        <template v-slot="{row}">
          <vxe-checkbox v-model="row.isFavorite"></vxe-checkbox>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>
<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
export default {
  name: 'BadClientModel',
  mixins: [detailMixins],
  props: {

  },
  data () {
    return {
      tableRef: 'clientModelxTable',

      api: {
        get: '/mtm/bad_clientmodel/get',
        edit: '/mtm/bad_clientmodel/updates',
        ModelGroupComboStore: '/mtm/combo/ModelGroupComboStore',
        GroupComboStore: '/mtm/combo/groupComboStore'
        // edit: '/mtm/mom_modelmodelelem/UpdatesByModel',
      },
      ModelGroupComboStore: [],
      GroupComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelGroupComboStore).then(result => {
        this.ModelGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    async saveEvent () {
      const updateRecords = this.$refs.clientModelxTable.getUpdateRecords()
      if (updateRecords.length === 0) {
        return
      }
      await this.$api.ActionRequest(this.api.edit, updateRecords).then(result => {
        this.tableLoading = false
        this.$notify({
          message: '保存成功',
          type: 'success'
        })
        this.loadData({ ClientID: this.form.id })
      })
    },
    selectAll (b) {
      this.tableData.forEach(item => {
        item.selected = b
      })
    }
  }
}
</script>

<style>
</style>
