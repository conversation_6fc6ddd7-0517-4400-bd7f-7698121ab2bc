<template>
  <d2-container class="momplusmodel">
    <vxe-table id='MomModel_master_table' ref='master_table' :highlight-current-row='false' :highlight-hover-row='false' :highlight-current-column='false' :height="TableHeight" :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}" :edit-config="{trigger: 'click', mode: 'cell',showStatus: true, icon: 'fa fa-pencil-square-o'}" keep-source>
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="modelElemTypeText" title="类别" sortable></vxe-table-column>
      <vxe-table-column field="modelElemBaseCode" title="部位编码" sortable></vxe-table-column>
      <vxe-table-column field="modelElemBaseName" title="部位名称" sortable></vxe-table-column>
      <vxe-table-column field="modelElemListCode" title="款式编码" sortable></vxe-table-column>
      <vxe-table-column field="modelElemListName" title="款式名称" sortable></vxe-table-column>
      <vxe-table-column field="modelElemCode" title="款式明细编码" sortable></vxe-table-column>
      <vxe-table-column field="modelElemName" title="款式明细名称" sortable></vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable></vxe-table-column>
      <vxe-table-column field="itemName" title="物料名称" sortable></vxe-table-column>
      <vxe-table-column field="itemOriginalItemNo" title="物料原始货号" sortable></vxe-table-column>
      <vxe-table-column field="itemWidth" title="物料宽度" sortable></vxe-table-column>
      <vxe-table-column field="qty" title="数量" sortable></vxe-table-column>
      <vxe-table-column field="input" title="输入内容" sortable></vxe-table-column>
    </vxe-table>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'MomPlusModel',
  mixins: [detailTableMixins],
  components: {
  },
  watch: {
    'form.id': {
      deep: true,
      async handler (newVal, oldVal) {
        // console.log(`newVal:${newVal},oldVal:${oldVal}`)
        if (newVal !== oldVal) {
          await this.loadData(this.form.id)
        }
      }
    }
  },
  data () {
    return {
      api: {
        get: '/mtm/mOM_ModelModelElem/GetPlusModelElem'

      },
      tableData: []
    }
  },
  async created () {
    this.loadData(this.form.id)
  },
  methods: {
    async loadData (id) {
      await this.$api.ActionRequest(this.api.get, { modelID: id }).then(result => {
        this.tableData = result
      })
    },
    rowClassName ({ row, rowIndex }) {
      var style = ''
      switch (row.modelElemTypeText) {
        case '款式':
          style = 'elemtype1'
          break
        case '工艺':
          style = 'elemtype2'
          break
        case '辅料':
          style = 'elemtype3'
          break
      }
      return style
    }

  }
}
</script>

<style lang="scss" >
.momplusmodel {
  .vxe-cell {
    margin: 1px;
    // background-color: red;
  }
  .elemtype1 {
    background-color: #5bae11;
  }
  .elemtype2 {
    background-color: #bf971f;
  }
  .elemtype3 {
    background-color: #0598e1;
  }
}
</style>
