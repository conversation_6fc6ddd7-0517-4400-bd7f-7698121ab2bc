<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="plusShow=!plusShow" v-if="menuAction.allowAdd">批量添加</vxe-button>
          <vxe-button status="danger" @click="deletesEvent" v-if="menuAction.allowDelete">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID" :item-render={}><template #default>
                <vxe-select v-model="searchForm.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template></vxe-form-item>
            <vxe-form-item field="modelElemBaseID" :item-render={}>
              <template #default>
                <el-select v-model="searchForm.modelElemBaseID" filterable remote reserve-keyword placeholder="款式部位" size="mini" :remote-method="remoteMethod5" clearable>
                  <el-option v-for="item in ModelElemBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID" :item-render={}><template #default>
                <el-select v-model="searchForm.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod4" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template></vxe-form-item>
            <vxe-form-item field="modelElemTypeID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemTypeID" placeholder="款式类别" size="mini" clearable>
                  <el-option v-for="item in modelElemTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render={}><template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template></vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadProductionProcessesDetailDetailTable' ref='master_table' height="auto" :row-class-name="rowClassName" :loading="tableLoading" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column type="checkbox" width="60px"></vxe-table-column>
      <!-- <vxe-table-column field="productionProcessesCode" title="生产工序编码" width="150"> </vxe-table-column>
      <vxe-table-column field="productionProcessesCodeName" title="生产工序名称" width="150"> </vxe-table-column> -->
      <vxe-table-column field="groupText" title="分类" width="150"> </vxe-table-column>
      <vxe-table-column title="部位" width="150">
        <template v-slot="{ row }">
          {{row.modelElemBaseCode}}{{row.modelElemBaseName===null?"":":"+row.modelElemBaseName}}
        </template>
      </vxe-table-column>
      <vxe-table-column title="款式" width="150">
        <template v-slot="{ row }">
          {{row.modelElemListCode}}{{row.modelElemListName===null?"":":"+row.modelElemListName}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="modelElemTypeName" title="款式类别" width="100"> </vxe-table-column>
      <vxe-table-column title="款式明细" width="150">
        <template v-slot="{ row }">
          {{row.modelElemCode}}{{row.modelElemCodeName===null?"":":"+row.modelElemCodeName}}
        </template>
      </vxe-table-column>
      <vxe-table-column title="款式明细1" width="150">
        <template v-slot="{ row }">
          {{row.modelElem1Code}}{{row.modelElem1CodeName===null?"":":"+row.modelElem1CodeName}}
        </template>
      </vxe-table-column>
      <vxe-table-column title="款式明细2" width="150">
        <template v-slot="{ row }">
          {{row.modelElem2Code}}{{row.modelElem2CodeName===null?"":":"+row.modelElem2CodeName}}
        </template>
      </vxe-table-column>

      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isLook" title="仅查看" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"> </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">

        <vxe-form-item title="款式明细" field="modelElemID" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.modelElemID" filterable placeholder="款式明细" size="mini" remote reserve-keyword :remote-method="remoteMethod1">
              <el-option v-for="item in BasicTechnologyComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="款式明细1" field="modelElem1ID" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.modelElem1ID" filterable placeholder="款式明细" size="mini" remote reserve-keyword :remote-method="remoteMethod2">
              <el-option v-for="item in BasicTechnologyComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="款式明细2" field="modelElem2ID" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.modelElem2ID" filterable placeholder="款式明细" size="mini" remote reserve-keyword :remote-method="remoteMethod3">
              <el-option v-for="item in BasicTechnologyComboStoreByQuery2" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <!-- <vxe-form-item title="类型" field="itemClassID" span="12" :item-render="{}"><template #default>
          <el-select v-model="selectRow.itemClassID" filterable placeholder="请选择类型" size="mini">
            <el-option v-for="item in ItemClassComboStore" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template></vxe-form-item> -->
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="仅查看" field="isLook" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"> </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>

    <vxe-modal v-model="plusShow" title="批量增加" width="950" resize destroy-on-close>
      <vxe-form ref="xForm" :data="basictechnology.searchForm" @submit="basictechnologyloadData()" @reset="resetEvent">
        <vxe-form-item field="genderID">
          <template #default>
            <vxe-select v-model="basictechnology.searchForm.genderID" placeholder="性别" clearable>
              <vxe-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item field="groupID" :item-render={}>
          <template #default>
            <el-select v-model="basictechnology.searchForm.groupID" placeholder="类别" clearable size="mini" style="width:110px">
              <el-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item field="modelElemBaseID" :item-render={}>
          <template #default>
            <el-select v-model="basictechnology.searchForm.modelElemBaseID" filterable remote reserve-keyword placeholder="款式部位" size="mini" :remote-method="remoteMethod5" clearable>
              <el-option v-for="item in ModelElemBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item field="modelElemListID" :item-render={}>
          <template #default>
            <el-select v-model="basictechnology.searchForm.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod4" size="mini" clearable>
              <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item field="text" :item-render={}>
          <template #default>
            <vxe-input v-model="basictechnology.searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item>
          <template #default>
            <vxe-button type="submit" status="success">查询</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
            <vxe-button status="warning" @click="addPlush()">批量添加</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
      <vxe-table ref='basictechnology_table' id="basictechnology_table" height="500" :data="basictechnologyTableData" :custom-config="{storage: true}">
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <vxe-table-column field="genderID" title="性别" :formatter='formatSex' sortable width="100"> </vxe-table-column>
        <vxe-table-column field="groupText" title="类别" sortable width="100"> </vxe-table-column>
        <vxe-table-column field="modelElemBaseCode" title="部位" sortable width="150">
          <template v-slot="{ row }">
            {{row.modelElemBaseCode}}{{row.modelElemBaseName===null?"":":"+row.modelElemBaseName}}
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column field="modelElemBaseName" title="部位名称" sortable width="100"></vxe-table-column> -->
        <vxe-table-column field="modelElemTypeName" title="工艺类别" sortable width="100"></vxe-table-column>
        <vxe-table-column field="modelELemListCode" title="款式编码" sortable width="150">
          <template v-slot="{ row }">
            {{row.modelELemListCode}}{{row.modelElemListName===null?"":":"+row.modelElemListName}}
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column field="modelElemListName" title="款式名称" sortable width="100"></vxe-table-column> -->
        <vxe-table-column field="modelElemCode" title="款式明细编码" sortable width="100">
          <template v-slot="{ row }">
            {{row.modelElemCode}}{{row.modelElemName===null?"":":"+row.modelElemName}}
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column field="modelElemName" title="款式明细名称" sortable width="100"></vxe-table-column> -->
      </vxe-table>
      <vxe-pager align="left" size="mini" :current-page.sync="basictechnology.searchForm.currentPage" :page-size.sync="basictechnology.searchForm.pageSize" :total="basictechnology.searchForm.totalResult" @page-change="elemPageChange" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'BadProductionProcessesDetail',
  mixins: [detailTableMixins],

  data () {
    return {
      formData: {
        remark: '',
        sort: 999,
        isActive: true,
        productionProcessesID: this.form.id,
        modelElemID: null,
        modelElem1ID: null,
        modelElem2ID: null,
        isLook: false

      },
      formRules: {
        modelElemID: [{ required: true, message: '绑定款式明细' }]
        // code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      api: {
        get: '/mes/bAD_ProductionProcessesDetail/get',
        add: '/mes/bAD_ProductionProcessesDetail/adds',
        edit: '/mes/bAD_ProductionProcessesDetail/updates',
        delete: '/mes/bAD_ProductionProcessesDetail/deletes',
        // productionProcessesComboStoreByQuery: '/mes/comboQuery/ProductionProcessesComboStoreByQuery',
        BasicTechnologyComboStoreByQuery: '/mes/comboQuery/BasicTechnologyComboStoreByQuery',
        basictechnologyget: '/mes/bad_basictechnology/get',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        GroupComboStore: '/mtm/combo/groupComboStore',
        ModelElemBaseComboStoreByQuery: '/mtm/comboQuery/ModelElemBaseComboStoreByQuery',
        modelElemTypeComboStore: '/mtm/combo/modelElemTypeComboStore'
      },
      plusShow: false,
      basictechnology: {
        searchForm: {
          genderID: true,
          text: null,
          modelElemListID: null,
          skipCount: 0,
          maxResultCount: 100,
          currentPage: 1,
          pageSize: 100,
          totalResult: 0,
          modelElemBaseID: null,
          groupID: []
        }
      },
      BasicTechnologyComboStoreByQuery: [],
      BasicTechnologyComboStoreByQuery1: [],
      BasicTechnologyComboStoreByQuery2: [],
      ModelElemListComboStoreByQuery: [],
      modelElemTypeComboStore: [],
      basictechnologyTableData: [],
      ModelElemBaseComboStoreByQuery: [],
      GroupComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ productionProcessesID: this.form.id }).then(({ data }) => {
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.BasicTechnologyComboStoreByQuery).then(result => {
        this.BasicTechnologyComboStoreByQuery = result
        this.BasicTechnologyComboStoreByQuery1 = result
        this.BasicTechnologyComboStoreByQuery2 = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.modelElemTypeComboStore).then(result => {
        this.modelElemTypeComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.BasicTechnologyComboStoreByQuery, { text: query }).then(result => {
        this.BasicTechnologyComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.BasicTechnologyComboStoreByQuery, { text: query }).then(result => {
        this.BasicTechnologyComboStoreByQuery1 = result
      })
    },
    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.BasicTechnologyComboStoreByQuery, { text: query }).then(result => {
        this.BasicTechnologyComboStoreByQuery2 = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
    },
    async addPlush () {
      const selectRecords = this.$refs.basictechnology_table.getCheckboxRecords()
      var list = selectRecords.map(item => {
        return {
          productionProcessesID: this.form.id,
          isActive: true,
          remark: '',
          sort: 999,
          modelElemID: item.id
        }
      })

      await this.$api.ActionRequest(this.api.add, list).then(result => {
        this.$XModal.message({ message: '新增成功', status: 'success' })
        this.plusShow = false
        this.loadData({ id: this.form.id })
      })
    },
    async basictechnologyloadData () {
      await this.$api.ActionRequest(this.api.basictechnologyget, this.basictechnology.searchForm).then(result => {
        this.basictechnologyTableData = result.items
        this.basictechnology.searchForm.totalResult = result.totalCount
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    async elemPageChange (page) {
      this.basictechnology.searchForm.maxResultCount = page.pageSize
      this.basictechnology.searchForm.skipCount = (page.pageSize) * (page.currentPage - 1)
      await this.basictechnologyloadData()
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.BasicTechnologyComboStoreByQuery, { gid: row.modelElem2ID }).then(result => {
        this.BasicTechnologyComboStoreByQuery1 = result
      })
      await this.$api.ActionRequest(this.api.BasicTechnologyComboStoreByQuery, { gid: row.modelElem1ID }).then(result => {
        this.BasicTechnologyComboStoreByQuery2 = result
      })
      await this.$api.ActionRequest(this.api.BasicTechnologyComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.BasicTechnologyComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
