<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="addPlus=!addPlus" v-if="menuAction.allowAdd">批量增加</vxe-button>
          <vxe-button status="danger" @click="deletesEvent" v-if="menuAction.allowDelete">删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelElemTypeID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemTypeID" placeholder="款式类别" size="mini" clearable>
                  <el-option v-for="item in modelElemTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID">
              <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
          <!-- <vxe-button type="submit" status="success" @click="loadData()">查询</vxe-button>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button> -->
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModel_master_table' ref='master_table' @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" :checkbox-config="{checkRowKeys: defaultSelecteRows, highlight: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="id" title="ID" :visible="false"></vxe-table-column>
      <vxe-table-column field="default" :formatter='formatBool' title="默认值" sortable>
        <template v-slot="{ row }">
          <span v-if="row.default" style="color:red;background-color: white;">是</span>
          <span v-else>否</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="modelElemTypeText" title="款式类别" sortable></vxe-table-column>
      <vxe-table-column field="modelElemListText" title="款式" sortable></vxe-table-column>
      <vxe-table-column field="modelElemListCadSeq" title="CAD顺序" sortable></vxe-table-column>
      <vxe-table-column field="modelElemText" title="款式明细" sortable>
        <template v-slot="{ row }">
          {{row.modelElemCode}}{{row.modelElemCodeName===null?"":':'+row.modelElemCodeName}}
        </template>
      </vxe-table-column>
      <template v-if="form.businessSubType===0">
        <vxe-table-column field="itemCode" title="默认货号编码" sortable></vxe-table-column>
        <vxe-table-column field="itemName" title="默认货号名称" sortable></vxe-table-column>
        <vxe-table-column field="originalItemNo" title="默认原始货号" sortable></vxe-table-column>
      </template>
      <template v-if="form.businessSubType===2">
        <vxe-table-column field="nItemCode" title="物料编码" sortable></vxe-table-column>
        <vxe-table-column field="nItemName" title="物料名称" sortable></vxe-table-column>
        <vxe-table-column field="nOriginalItemNo" title="原始货号" sortable></vxe-table-column>
        <vxe-table-column field="qty" title="数量" sortable></vxe-table-column>
        <!-- <vxe-table-column field="itemWidth" title="门幅" sortable></vxe-table-column> -->
        <vxe-table-column field="input" title="内容" sortable></vxe-table-column>
      </template>
      <vxe-table-column title="操作" width="100px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式明细" field="modelElemID" span="20">
          <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod3" clearable size="small">
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="默认？" field="default" span="12">
          <template #default>
            <vxe-switch v-model="selectRow.default"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="addPlus" title="批量增加" width="850" resize show-zoom destroy-on-close>
      <vxe-form ref="xForm" :data="modelElem.searchForm" @submit="ModelElemloadData()" @reset="resetEvent">
        <vxe-form-item field="genderID">
          <template #default="{ data }">
            <vxe-select v-model="data.genderID" placeholder="性别" clearable>
              <vxe-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item field="modelElemListID">
          <template #default="{ data }">
            <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod4" size="mini" clearable>
              <el-option v-for="item in ModelElemListComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item field="text">
          <vxe-input v-model="modelElem.searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
        </vxe-form-item>

        <vxe-form-item>
          <template #default>
            <vxe-button type="submit" status="success">查询</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
            <vxe-button status="warning" @click="addPlush()">保存勾选</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
      <vxe-table ref='modelElem_table' id="modelElem_table" height="500" :data="modelElemRableData" :custom-config="{storage: true}">
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="genderText" title="性别" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemTypeText" title="款式类别" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemBaseText" title="款式部位" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemListText" title="款式" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="code" title="款式明细编码" sortable width="120px"></vxe-table-column>
        <vxe-table-column field="codeName" title="款式明细名称" sortable width="120px"></vxe-table-column>
      </vxe-table>
      <vxe-pager align="left" size="mini" :current-page.sync="modelElem.searchForm.currentPage" :page-size.sync="modelElem.searchForm.pageSize" :total="modelElem.searchForm.totalResult" @page-change="elemPageChange" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'MomModelElemDetail',
  mixins: [detailTableMixins],
  components: {
  },
  watch: {
    'form.id': {
      deep: true,
      async handler(newVal, oldVal) {
        // console.log(`newVal:${newVal},oldVal:${oldVal}`)
        if (newVal !== oldVal) {
          await this.loadData(this.form)
        }
      }
    }
  },
  data() {
    return {
      formData: {
        isActive: true,
        modelID: null,
        modelElemID: null,
        default: false,
        selected: true,
        itemID: null,
        qty: null,
        input: null
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 3, max: 12, message: '长度在 3 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
        businessSubType: [{ required: true, message: '请选择业务类型' }],
        modelBaseID: [{ required: true, message: '请选择基础版型' }]
      },
      formItems: [
        { field: 'code', title: '版型编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '款式描述', span: 12, itemRender: { name: '$input' } },
        { field: 'businessSubType', title: '业务类型', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'modelBaseID', title: '基础版型', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'groupID', title: '类别', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'genderID', title: '性别', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'sewBaseID', title: '缝份列表', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'sizeListID', title: '规格单', span: 12, itemRender: { name: '$select', option: [] } },
        { field: 'shortName', title: '简称', span: 12, itemRender: { name: '$input' } },
        { field: 'issueDate', title: '日期', span: 12, itemRender: { name: '$input', props: { type: 'date', placeholder: '请选择日期' } } },
        { field: 'minEase', title: '最小加放量', span: 12, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'maxEase', title: '最大加放量', span: 12, itemRender: { name: '$input', props: { type: 'number' } } },
        { field: 'prefix', title: '排料图前缀', span: 12, itemRender: { name: '$input' } },
        // { field: 'IsClientShow', title: '客户端显示?', span: 12, itemRender: { name: '$input' } },
        { field: 'isRuleSize', title: '支持算法', span: 12, itemRender: { name: '$switch' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mOM_ModelModelElem/getByModelID',
        add: '/mtm/mOM_ModelModelElem/adds',
        edit: '/mtm/mOM_ModelModelElem/updates',
        delete: '/mtm/mOM_ModelModelElem/deletes',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        modelElemTypeComboStore: '/mtm/combo/modelElemTypeComboStore',
        modelElemGet: '/mtm/mom_modelelem/get'
      },
      footerCompanyInfo: false,
      ModelElemListComboStoreByQuery: [],
      ModelElemListComboStoreByQuery1: [],
      ModelElemComboStoreByQuery: [],
      modelElemTypeComboStore: [],
      addPlus: false,
      modelElem: {
        searchForm: {
          genderID: true,
          text: null,
          modelElemListID: null,
          skipCount: 0,
          maxResultCount: 100,
          currentPage: 1,
          pageSize: 100,
          totalResult: 0
        }
      },
      modelElemRableData: []
    }
  },
  async created() {
    this.formData.modelID = this.form.id
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    this.remoteMethod2()
    this.remoteMethod3()
  },
  methods: {
    async getCombStore() {
      await this.$api.ActionRequest(this.api.modelElemTypeComboStore).then(result => {
        this.modelElemTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery1 = result
      })
    },
    remoteMethod2(query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    remoteMethod3(query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },

    remoteMethod4(query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery1 = result
      })
    },
    async editEvent(row) {
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    async ModelElemloadData() {
      await this.$api.ActionRequest(this.api.modelElemGet, this.modelElem.searchForm).then(result => {
        this.modelElemRableData = result.items
        this.modelElem.searchForm.totalResult = result.totalCount
      })
    },
    async elemPageChange(page) {
      this.modelElem.searchForm.maxResultCount = page.pageSize
      this.modelElem.searchForm.skipCount = (page.pageSize) * (page.currentPage - 1)
      await this.ModelElemloadData()
    },
    async addPlush() {
      const selectRecords = this.$refs.modelElem_table.getCheckboxRecords()
      var list = selectRecords.map(item => {
        return { selected: true, modelID: this.form.id, isActive: true, default: false, modelElemID: item.id }
      })

      await this.$api.ActionRequest(this.api.add, list).then(result => {
        this.$XModal.message({ message: '新增成功', status: 'success' })
        this.addPlus = false
        this.loadData({ id: this.form.id })
      })
    }

  }
}
</script>

<style lang="scss" >
.vxe-cell {
  margin: 1px;
  // background-color: red;
}
</style>
