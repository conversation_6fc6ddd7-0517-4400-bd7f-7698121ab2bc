<template>
  <d2-container class="modelbaseimage">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button icon="fa fa-files-o" status="perfect" @click="clonePlusShow=!clonePlusShow" v-if="menuAction.allowAdd">批量复制</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelBaseID">
              <template #default="{ data }">
                <el-select v-model="data.modelBaseID" filterable remote reserve-keyword placeholder="基础款式" :remote-method="ModelBaseComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">

                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemID1">
              <template #default="{ data }">
                <el-select v-model="data.modelElemID1" filterable remote reserve-keyword placeholder="款式明细" :remote-method="ModelElemComboStoreByQueryMethod" clearable size="mini">
                  <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="positionID">
              <template #default="{ data }">
                <el-select v-model="data.positionID" filterable placeholder="位置" clearable size="mini">
                  <el-option v-for="item in PositionComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomModelbaseimageMasterTable' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="mix" title="合成图" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelBaseCode" title="基础版型编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelBaseName" title="基础版型名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemCode1" title="款式明细编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelElemName1" title="款式明细名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelImage" title="图片" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="imageSeq" title="图片顺序" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="positionID" title="图片位置" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="imageUrl" title="图片" sortable>
        <template v-slot="{ row }">
          <el-popover placement="right-end" width="800" trigger="hover">
            <el-image :src="row.imageUrl" fit="fill">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.imageUrl" fit="scale-down" slot="reference" style="height:36px;">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>

        </template>
      </vxe-table-column>

      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="基础版型" field="modelBaseID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelBaseID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="ModelBaseComboStoreByQueryMethod" clearable>
              <el-option v-for="item in ModelBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式明细" field="modelElemID1" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelElemID1" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="ModelElemComboStoreByQueryMethod" clearable>
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="图片" field="modelImageID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.modelImageID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="ModelImageComboStoreByQueryMethod" clearable @change="selectchange">
              <el-option v-for="item in ModelImageComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <el-tooltip class="item" effect="dark" :content=" item.label" placement="top-start">
                  <span style="float: left ;overflow:hidden;width:200px; text-overflow: ellipsis;    white-space: nowrap;   word-break:keep-all;" class="modelimageselect">{{ item.label }}</span>
                </el-tooltip>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  <el-image :src="item.text" fit="fill" style="width: 100px; height: 100px">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片顺序" field="imageSeq" span="12">
          <vxe-input v-model="selectRow.imageSeq" placeholder="图片顺序" type="number" clearable>
          </vxe-input>
        </vxe-form-item>
        <vxe-form-item title="图片" span="24">
          <template #default>
            <el-image style="width: 200px; height: 200px" :src="imageurl" fit="fill">
            </el-image>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="clonePlusShow" :title="'批量复制'" width="1000" height='600' resize destroy-on-close>
      <clone-plush v-if="clonePlusShow" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep, find } from 'lodash'
import ClonePlush from './components/cloneplus'
export default {
  name: 'mom_modelbaseimage',
  mixins: [masterTableMixins],
  components: {
    ClonePlush
  },
  watch: {
    'selectRow.modelImageID': function (val, old) {
      if (val === null || val === '') {
        this.imageurl = null
      }
    }
  },
  data () {
    return {
      formData: {
        mix: true,
        imageSeq: null,
        modelBaseID: null,
        modelElemID1: null,
        modelImageID: null,
        positionID: null
      },
      imageurl: null,
      formRules: {
        modelImageID: [{ required: true, message: '请输入编码' }],
        modelBaseID: [{ required: true, message: '请输入编码名称' }]
      },
      formItems: [
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modelbaseimage/get',
        add: '/mtm/mom_modelbaseimage/adds',
        edit: '/mtm/mom_modelbaseimage/updates',
        delete: '/mtm/mom_modelbaseimage/deletes',
        ModelBaseComboStoreByQuery: '/mtm/comboQuery/ModelBaseComboStoreByQuery',
        ModelImageComboStoreByQuery: '/mtm/comboQuery/ModelImageComboStoreByQuery',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        PositionComboStore: '/mtm/combo/PositionComboStore'

      },
      clonePlusShow: false,
      footerCompanyInfo: false,
      ModelImageComboStoreByQuery: [],
      ModelElemComboStoreByQuery: [],
      ModelBaseComboStoreByQuery: [],
      PositionComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    this.ModelBaseComboStoreByQueryMethod()
    this.ModelElemComboStoreByQueryMethod()
    this.ModelImageComboStoreByQueryMethod()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    selectchange (row) {
      var itme = find(this.ModelImageComboStoreByQuery, function (i) { return i.value === row })
      if (itme) {
        this.imageurl = itme.text
      } else { this.imageurl = null }
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.PositionComboStore).then(result => {
        this.PositionComboStore = result
      })
    },
    ModelBaseComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelBaseComboStoreByQuery, { text: query }).then(result => {
        this.ModelBaseComboStoreByQuery = result
      })
    },
    ModelElemComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    ModelImageComboStoreByQueryMethod (query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelBaseComboStoreByQuery, { gid: row.modelBaseID }).then(result => {
        this.ModelBaseComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.imageurl = row.imageUrl
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row, code = false, codeName = false) {
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID1 }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelBaseComboStoreByQuery, { gid: row.modelBaseID }).then(result => {
        this.ModelBaseComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.imageurl = row.imageUrl
        this.showEdit = true
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }

      this.showEdit = true
    }
  }
}
</script>

<style lang="scss">
</style>
