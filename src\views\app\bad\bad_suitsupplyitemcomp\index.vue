<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadSuitsupplyitemcompMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="itemName" title="物料名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value1" title="成分1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value2" title="成分2" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value3" title="成分3" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value4" title="成分4" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="物料名称" field="itemID" span="24" :item-render="{}"> <template #default>
            <el-select v-model.trim="selectRow.itemID" filterable placeholder="物料名称" size="mini" remote reserve-keyword :remote-method="remoteMethod">
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.code+'['+item.originalItemNo+']'" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="成分1" field="value1" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="成分2" field="value2" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="成分3" field="value3" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="成分4" field="value4" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', props: { type: 'number',clearable:true}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'bad_suitsupplyitemcomp',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        itemID: '',
        sort: '',
        remark: '',
        isActive: true
      },
      formRules: {
        itemID: [{ required: true, message: '请选择物料信息' }]
      },
      api: {
        get: '/mtm/bad_suitsupplyitemcomp/get',
        add: '/mtm/bad_suitsupplyitemcomp/adds',
        edit: '/mtm/bad_suitsupplyitemcomp/updates',
        delete: '/mtm/bad_suitsupplyitemcomp/deletes',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery'
      },
      ItemComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
      })
      this.selectRow = cloneDeep(row)
      this.showEdit = true
    },
    async copyRowEvent (row) {
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
      })
      this.selectRow = cloneDeep(row)
      this.selectRow.id = null
      this.showEdit = true
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
