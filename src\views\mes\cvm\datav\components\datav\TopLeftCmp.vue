<template>
  <div class="top-left-cmp">
    <div class="dc-left">
      <dv-border-box-5>
        <div class="main-value"><span>{{sorderInfo.monthOrderCount}}</span>件</div>
        <div class="compare-value"><span>延期率</span>{{sorderInfo.delayTheOrderRate}}%</div>
        <div class="compare-value"><span>计划</span>{{sorderInfo.monthOrderPlanDelivery}}</div>
      </dv-border-box-5>
      <div class="dc-text">
        已交付订单数
        <dv-decoration-3 style="width:200px;height:20px;" />
      </div>
    </div>
    <div class="dc-right">
      <div class="dc-text">
        当月订单数
        <dv-decoration-3 style="width:200px;height:20px;" />
      </div>
      <dv-border-box-5 :reverse="true">
        <div class="main-value"><span>{{sorderInfo.monthOrderDelivery}}</span></div>
        <div class="compare-value"><span>延期数</span>{{sorderInfo.delayTheOrder}}</div>
        <div class="compare-value"><span>交货率</span>{{sorderInfo.monthOrderDeliveryRate}}%</div>
      </dv-border-box-5>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TopLeftCmp',
  data () {
    return {

      sorderInfo: {
        monthOrderCount: 0,
        monthOrderPlanDelivery: 0,
        monthOrderDelivery: 0,
        monthOrderDeliveryRate: 0,
        delayTheOrder: 0,
        delayTheOrderRate: 0
      },
      api: {
        get: '/mes/boardChart/GetThisMonthSorder'
      }
    }
  },
  mounted () {
    const { get } = this
    get()
    setInterval(get, 1000 * 60)
  },
  created () {
    this.get()
  },
  methods: {
    async get () {
      await this.$api.ActionRequest(this.api.get).then(async result => {
        this.sorderInfo = result
      })
    }
  }
}
</script>

<style lang="scss">
.top-left-cmp {
  display: flex;

  .dc-left,
  .dc-right {
    width: 50%;
  }

  .dv-border-box-5 {
    height: 60%;
  }

  .dc-text {
    display: flex;
    flex-direction: column;
    height: 40%;
    font-size: 20px;
    padding: 20px;
    box-sizing: border-box;
  }

  .dc-left .dc-text {
    align-items: flex-end;
    justify-content: center;
  }

  .dc-right .dc-text {
    justify-content: flex-start;
    padding-top: 20px;
  }

  .dc-left .dv-border-box-5 {
    padding: 30px;
    box-sizing: border-box;
  }

  .dc-right .dv-border-box-5 {
    padding: 40px;
    padding-left: 75px;
    box-sizing: border-box;
  }

  .main-value {
    font-weight: bold;
    font-size: 30px;

    span {
      font-size: 50px;
      color: #00c0ff;
      margin-right: 15px;
    }
  }

  .compare-value {
    height: 35px;
    line-height: 35px;
    font-size: 18px;

    span {
      margin-right: 30px;
    }
  }
}
</style>
