<template>
  <div class="home">
    <div class="header">
      <div class="companyname"> <span style="color:#9F0303;font-size: 30px;font-weight:600;">金鸳鸯</span>
        <span style="margin-left:30px;font-size: 22px;color:#817D7D">全球高端西服个性化定制平台</span>
      </div>
      <div class="headerother">
        <el-row>
          <el-col :span="8">
            <el-link href="/login">用户登录</el-link>
          </el-col>
          <el-col :span="8">
            <el-link href="#http://www.cnjyy.cn/">公司官网</el-link>
          </el-col>
          <el-col :span="8">
            <el-link>联系我们</el-link>
          </el-col>
        </el-row>

      </div>
    </div>
    <div class="middle">
      <div class="middle_item">
        <el-carousel indicator-position="outside" style="width:100%;height:70%;overflow: hidden;">
          <el-carousel-item style="height:100%;overflow: hidden;">
            <div class="bgimg bgimg1"></div>
          </el-carousel-item>
          <el-carousel-item style="height:100%">
            <div class="bgimg bgimg2"></div>
          </el-carousel-item>
          <el-carousel-item style="height:100%">
            <div class="bgimg bgimg3"></div>
          </el-carousel-item>
          <el-carousel-item style="height:100%">
            <div class="bgimg bgimg4"></div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="middle_item">
        <div class="design">
          <div class="designsvg">
            <el-link href="/login">
              <d2-icon-svg style="width:180px;height:180px" name="pencil-ruler" />
            </el-link>
          </div>
          <div class="designlabel">
            <el-link href="/login">
              <p><strong style="font-size: 35px;">自主设计</strong> </p>
              <p>自主研发设计 从这里开始</p>
            </el-link>
          </div>

        </div>
        <div class="mall">
          <div class="mallsvg">
            <d2-icon-svg style="width:250px;height:250px" name="suits-shop" />
          </div>
          <div class="designlabel">
            <p> <strong style="font-size: 35px;">商城</strong></p>
          </div>
        </div>
      </div>

    </div>
    <div class="footer">
      <div class="footerone">
        <div class="footeronediv">
          <p><span class='footerh3'>邮箱</span></p>
          <p><EMAIL></p>
        </div>
        <div class="footeronediv">
          <p><span class='footerh3'>服务电话</span></p>
          <p>12345678901</p>
        </div>
        <div class="footeronediv">
          <p> <span class='footerh3'>地址</span></p>
          <p>浙江温州龙湾区兴国路7号</p>
        </div>
      </div>
      <el-row>
        <el-col>
          <div class="footerCompany">
            <span>©2020 温州金鸳鸯服装有限公司</span><span style="color:red">&</span> <span>{{company}}联合出品</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoginIndex',
  data () {
    return {
      images: [],
      company: process.env.VUE_APP_Development_Company,
      versions: process.env.VUE_APP_Development_Versions
    }
  },
  methods: {
    gologin () {
      this.$router.push({ path: '/login' })
    }
  }
}
</script>

<style lang='scss'>
.home {
  .el-carousel__container {
    height: 100%;
  }
  background: #fff;
  margin: 0 auto;
  text-align: center;
  overflow: hidden;
  * {
    margin: 0;
    padding: 0;
    text-align: center;
  }
  .header {
    width: 100%;
    height: 100px;
    line-height: 100px;
    display: flex;
    align-items: center;
    .companyname {
      flex-grow: 1;
    }
    .headerother {
      flex-grow: 0.5;
    }
  }
  .middle {
    width: 100%;
    position: absolute;
    top: 100px;
    bottom: 140px;
    background: url("./images/bg.jpg") no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    // .middle_item1 {
    //   display: flex;
    //   flex-direction: column;
    // }
    .middle_item {
      display: flex;
      flex-direction: column;
      .design {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        .designsvg {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-grow: 1;
        }
        .designlabel {
          flex-grow: 0.2;
        }
      }
      .mall {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        .mallsvg {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-grow: 1;
        }
        .designlabel {
          flex-grow: 0.2;
        }
      }
    }
    .middle_item:nth-of-type(1) {
      flex-grow: 1;
      height: 80%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .middle_item:nth-of-type(2) {
      flex-grow: 0.3;
      height: 80%;
    }
  }
  .bgimg {
    width: 100%;
    height: 100%;

    // background-size: cover;
    // background-attachment: fixed;
    // position: fixed;
    // top: 0;
    // left: 0;
    // right: 0;
    // bottom: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }
  .bgimg1 {
    background-image: url("./images/ubg1.jpg");
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    z-index: -10;
    zoom: 1;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    background-position: center 0;
  }
  .bgimg2 {
    background-image: url("./images/ubg2.jpg");
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    z-index: -10;
    zoom: 1;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    background-position: center 0;
  }
  .bgimg3 {
    background-image: url("./images/ubg3.jpg");
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    z-index: -10;
    zoom: 1;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    background-position: center 0;
  }
  .bgimg4 {
    background-image: url("./images/ubg4.jpg");
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    z-index: -10;
    zoom: 1;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    background-position: center 0;
  }
  .footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
  }
  .footerone {
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    .footeronediv {
      //   align-items: center;
      flex-grow: 1;
      margin: 15px;
    }
  }
  .footerh3 {
    font-size: 20px;
    font-weight: 600;
  }
  .footerCompany {
    padding: 10px;
    text-align: center;
    background-color: #333333;
    color: #ffffff;
  }
}
</style>
