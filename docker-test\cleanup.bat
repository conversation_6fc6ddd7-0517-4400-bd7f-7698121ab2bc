@echo off
setlocal enabledelayedexpansion
echo Docker Test Environment Cleanup Tool

echo Stopping all test containers...
for %%i in (test-config frontend-debug app-dev app-test app-prod config-test multi-env-test) do (
    docker stop %%i >nul 2>&1
    if %errorlevel% equ 0 (
        echo OK: Stopped %%i
    )
)

echo.
echo Removing all test containers...
for %%i in (test-config frontend-debug app-dev app-test app-prod config-test multi-env-test) do (
    docker rm %%i >nul 2>&1
    if %errorlevel% equ 0 (
        echo OK: Removed %%i
    )
)

echo.
echo Removing test images...
for %%i in (config-test:latest frontend-debug:latest multi-env-test:latest) do (
    docker rmi %%i >nul 2>&1
    if %errorlevel% equ 0 (
        echo OK: Removed image %%i
    )
)

echo.
echo Cleaning unused Docker resources...
docker system prune -f >nul 2>&1

echo.
echo Cleanup completed!
echo.
echo Current Docker status:
docker ps -a | findstr "test\|config\|app-\|frontend-debug\|multi-env"
if %errorlevel% neq 0 (
    echo No remaining test containers found
)

echo.
pause
