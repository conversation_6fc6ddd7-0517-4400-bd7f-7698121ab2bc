import { cloneDeep, isEmpty, toInteger } from 'lodash'
import XEUtils from 'xe-utils'
// import tableSortColumnMixins from '../table_sort_column_mixins/index'
import actionMixins from '../action_mixins/index'
import { mapMutations, mapActions, mapState } from 'vuex'
const AppPageSize = toInteger(process.env.VUE_APP_PageSize) || 50
export default {
  // mixins: [tableSortColumnMixins, actionMixins],
  mixins: [actionMixins],
  props: {
    masterSeach: { type: Object, default: null },
    toolbarShow: {
      type: Boolean,
      default: true
    },
    showfooterCompanyInfo: {
      type: Boolean,
      default: true
    },
    operationColumnShow: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      TableHeight: 'auto',
      searchForm: {
        currentPage: 1,
        pageSize: AppPageSize,
        totalCount: 0,
        text: '',
        skipCount: 0,
        // genderID: true,
        maxResultCount: AppPageSize
      },
      tableData: null,
      selectRow: {},
      submitLoading: false,
      showEdit: false,

      drawer: false,
      sexList: [
        { label: '女', value: false },
        { label: '男', value: true }
      ],
      boolList: [
        { label: '是', value: true },
        { label: '否', value: false }

      ],
      tableRef: 'master_table',
      tableLoading: false,
      defaultSelecteRows: [],
      tableOptFixed: 'right', // left
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setHours(0)
              start.setMinutes(0)
              start.setSeconds(0)
              end.setHours(23)
              end.setMinutes(59)
              end.setSeconds(59)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一周',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }, {
            text: '最近一个月',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }, {
            text: '最近三个月',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }]
      }
    }
  },
  watch: {
    'searchForm.currentPage': function (val) {
      // this.searchForm.skipCount = (val-1) * this.searchForm.pageSize
    }
  },

  created () {
    this.tableOptFixed = this.vxetableconfig.tableOptFixed
  },
  computed: {
    ...mapState('d2admin/vxetable', ['vxetableconfig'])
  },
  methods: {
    ...mapMutations('d2admin/page', [
      'keepAliveRemove',
      'keepAliveClean'
    ]),
    ...mapActions('d2admin/page', [
      'close',
      'closeLeft',
      'closeRight',
      'closeOther',
      'closeAll'
    ]),
    async searchEvent () {
      this.searchForm.currentPage = 1
      this.searchForm.skipCount = 0
      await this.loadData()
    },
    // 格式化性别
    formatSex ({ cellValue }) {
      return cellValue ? (cellValue === true ? '男' : '女') : '女'
    },
    //
    formatBool ({ cellValue }) {
      if (cellValue === null) {
        return null
      }
      var b = (cellValue === true ? '是' : '否')
      return b
    },
    formatBool1 ({ cellValue }) {
      if (cellValue === null) {
        return null
      }
      var b = (cellValue === true ? '是' : '')
      return b
    },
    reloadData (data) {
      const xTable = this.$refs[this.tableRef]
      xTable.reloadData(data)
    },
    // 格式化下拉选项
    formatSelect ({ cellValue }, list) {
      if (!this.$utils.isArray(list)) {
        return ''
      }

      const item = list.find(item => item.value === cellValue)
      return item ? item.label : ''
    },
    formatDate ({ cellValue }, format) {
      if (cellValue === null) {
        return null
      }
      return XEUtils.toDateString(this.formatLongDate(cellValue), format || 'yyyy-MM-dd HH:mm:ss')
    },
    formatLongDate (date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },

    tableCellClick ({ row, rowIndex, column, columnIndex }) {
      if (column.title === '操作') {
        this.checkBoxChecked(row)
        this.drawer = false
        return
      }
      if (column.title === '操作' || column.type === 'checkbox') {
        this.drawer = false
        return
      } else {
        this.checkBoxChecked(row)
        this.drawer = true
      }
      this.selectRow = cloneDeep(row)
    },
    checkBoxChecked (row) {
      if (this.$refs[this.tableRef]) {
        this.$refs[this.tableRef].clearCheckboxRow()
        this.$refs[this.tableRef].toggleCheckboxRow(row)
      }
    },
    handleClose () {
      this.drawer = false
    },

    // 分页发生改变时会触发该事件
    pageChange ({ currentPage, pageSize }) {
      this.searchForm.maxResultCount = pageSize
      this.searchForm.skipCount = (currentPage - 1) * pageSize
      this.searchForm.currentPage = currentPage
      const xTable = this.$refs[this.tableRef]
      // xTable.refreshScroll()
      // xTable.clearScroll()
      xTable.scrollTo(0, 0)
      // xTable.scrollToRow(this.tableData[0], null)
      this.loadData()
    },
    // 新增
    insertEvent () {
      this.selectRow = cloneDeep(this.formData)
      this.showEdit = true
    },
    // 编辑
    editEvent (row, callback) {
      this.selectRow = cloneDeep(row)
      if (callback) {
        callback(row)
      } else {
        this.showEdit = true
      }
    },
    deepCopyEvent (row) {
      this.tableLoading = true
      this.$api.ActionRequest(this.api.deepclone, { id: row.id }).then(result => {
        this.$XModal.message({ message: '复制成功', status: 'success' })
        this.tableLoading = false
        this.loadData()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 删除
    removeEvent (row) {
      this.$XModal.confirm({ content: '您确定要删除该数据?', transfer: true, zIndex: 9999 }).then(type => {
        if (type === 'confirm') {
          this.tableLoading = true
          this.$api.ActionRequest(this.api.delete, [row]).then(result => {
            this.tableLoading = false
            this.$XModal.message({ message: '删除成功', status: 'success' })
            this.loadData()
          }).catch(() => {
            this.tableLoading = false
          })
        }
      })
    },

    // 复制
    // attributenames 要清除的属性明细 code 编码 codename 编码名称
    copyRowEvent (row, attributenames = [], callback) {
      this.selectRow = cloneDeep(row)
      if (XEUtils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (XEUtils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (XEUtils.has(this.selectRow, 'createBy')) {
        this.selectRow.createBy = null
      }
      if (XEUtils.has(this.selectRow, 'createID')) {
        this.selectRow.createID = null
      }
      if (XEUtils.has(this.selectRow, 'createOn')) {
        this.selectRow.createOn = null
      }
      if (XEUtils.has(this.selectRow, 'modifyBy')) {
        this.selectRow.modifyBy = null
      }
      if (XEUtils.has(this.selectRow, 'modifyID')) {
        this.selectRow.modifyID = null
      }
      if (XEUtils.has(this.selectRow, 'modifyOn')) {
        this.selectRow.modifyOn = null
      }
      // if (!code && XEUtils.has(this.selectRow, 'code')) {
      //   this.selectRow.code = null
      // }
      // if (!codeName && XEUtils.has(this.selectRow, 'codeName')) {
      //   this.selectRow.codeName = null
      // }
      if (attributenames.length > 1) {
        attributenames.forEach(name => {
          this.selectRow[name] = null
        })
      }
      if (callback) {
        callback(row)
      } else {
        this.showEdit = true
      }
    },
    // 批量删除
    deletesEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length === 0) {
        this.$XModal.message({ message: '请勾选要删除的数据', status: 'error' })
        return
      }
      this.$XModal.confirm('您确定要批量删除数据?删除后不可恢复').then(type => {
        if (type === 'confirm') {
          this.$api.ActionRequest(this.api.delete, rows).then(result => {
            this.$XModal.message({ message: '批量删除成功', status: 'success' })
            this.loadData()
          })
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    rowClassName ({ row, rowIndex, other }) {
      if (XEUtils.has(row, 'isActive') && !row.isActive) {
        return 'row-isActive-false'
      }
      if (other !== null && other !== undefined) {
        if (XEUtils.has(row, other) && !row[other]) {
          return 'row-isActive-false'
        }
      }
    },
    // 提交
    submitEvent () {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (isEmpty(this.selectRow.id) || this.selectRow.id === undefined || this.selectRow.id === null || this.selectRow.id === '00000000-0000-0000-0000-000000000000') {
        this.$api.ActionRequest(this.api.add, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '新增成功', status: 'success' })
          this.loadData()
          this.showEdit = false
          loading.close()
        }).catch(() => {
          loading.close()
        })
      } else {
        this.$api.ActionRequest(this.api.edit, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '保存成功', status: 'success' })
          this.loadData()
          this.showEdit = false
          loading.close()
        }).catch(() => {
          loading.close()
        })
      }
    },
    resetEvent () {
      this.loadData()
    },
    cellDblClick ({ row }) {
      this.drawer = true
      this.selectRow = cloneDeep(row)
      this.$emit('nextpage', {
        pagename: 'detail',
        data: cloneDeep(row),
        keepalive: false,
        action: 'edit',
        masterSeach: this.searchForm
      })
    },
    exportSelectEvent ({ name = null }) {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要导出的数据', status: 'error' })
        return
      }
      var tabConfig = {
        // filename: '文件导出-' + this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss'),
        sheetName: 'Sheet1',
        isColgroup: false,
        isFooter: false,
        type: 'xlsx',
        data: list
      }
      if (name !== null && name !== undefined) {
        tabConfig.filename = name + '_' + this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss')
      }
      xTable.exportData(tabConfig)
    },
    // 清空当前页缓存并刷新此页面
    async handleCleanCacheAndRefreshCurrent () {
      this.keepAliveRemove(this.$route.name)
      await this.$nextTick()
      this.$router.replace('/refresh')
    }
  }
}
