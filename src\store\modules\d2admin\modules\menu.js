// 设置文件
import setting from '@/setting.js'
import api from '@/api'
import util from '@/libs/util'
import { isEmpty, concat, cloneDeep } from 'lodash'
import router from '@/router'
import setMenus from '@/menu/menus.js'
import { menuAside } from '@/menu'
export default {
  namespaced: true,
  state: {
    // 顶栏菜单
    header: [],
    // 侧栏菜单
    aside: [],
    // 侧边栏收缩
    asideCollapse: setting.menu.asideCollapse,
    // 侧边栏折叠动画
    asideTransition: setting.menu.asideTransition,
    selectMenu: null
  },
  actions: {
    /**
     * 设置侧边栏展开或者收缩
     * @param {Object} context
     * @param {Boolean} collapse is collapse
     */
    async asideCollapseSet ({ state, dispatch }, collapse) {
      // store 赋值
      state.asideCollapse = collapse
      // 持久化
      await dispatch('d2admin/db/set', {
        dbName: 'sys',
        path: 'menu.asideCollapse',
        value: state.asideCollapse,
        user: true
      }, { root: true })
    },
    /**
     * 切换侧边栏展开和收缩
     * @param {Object} context
     */
    async asideCollapseToggle ({ state, dispatch }) {
      // store 赋值
      state.asideCollapse = !state.asideCollapse
      // 持久化
      await dispatch('d2admin/db/set', {
        dbName: 'sys',
        path: 'menu.asideCollapse',
        value: state.asideCollapse,
        user: true
      }, { root: true })
    },
    /**
     * 设置侧边栏折叠动画
     * @param {Object} context
     * @param {Boolean} transition is transition
     */
    async asideTransitionSet ({ state, dispatch }, transition) {
      // store 赋值
      state.asideTransition = transition
      // 持久化
      await dispatch('d2admin/db/set', {
        dbName: 'sys',
        path: 'menu.asideTransition',
        value: state.asideTransition,
        user: true
      }, { root: true })
    },
    /**
     * 切换侧边栏折叠动画
     * @param {Object} context
     */
    async asideTransitionToggle ({ state, dispatch }) {
      // store 赋值
      state.asideTransition = !state.asideTransition
      // 持久化
      await dispatch('d2admin/db/set', {
        dbName: 'sys',
        path: 'menu.asideTransition',
        value: state.asideTransition,
        user: true
      }, { root: true })
    },
    /**
     * 持久化数据加载侧边栏设置
     * @param {Object} context
     */
    async asideLoad ({ state, dispatch }) {
      // store 赋值
      const menu = await dispatch('d2admin/db/get', {
        dbName: 'sys',
        path: 'menu',
        defaultValue: setting.menu,
        user: true
      }, { root: true })
      state.asideCollapse = menu.asideCollapse !== undefined ? menu.asideCollapse : setting.menu.asideCollapse
      state.asideTransition = menu.asideTransition !== undefined ? menu.asideTransition : setting.menu.asideTransition
    },
    async loadmenus ({ state, dispatch }) {
      // console.log(router)
      // debugger;
      const uuid = util.cookies.get('uuid')
      const token = util.cookies.get('token')
      // this.store.state.d2admin;
      // console.log(this.state.d2admin.page.current)
      // console.log(router.app.$route.path)
      if (isEmpty(uuid) || isEmpty(token)) {
        router.push({ name: 'login' })
        return
      }
      await dispatch('getservermenus')
    },
    // 获取远程服务器菜单
    async getservermenus ({ state, dispatch }) {
      await api.SYS_USER_MENUS('/sso/menu/getMeun', null, false, 'get').then(res => {
        var meuns = setMenus.removeEmptyChildren(res)
        state.aside = cloneDeep(concat(menuAside, meuns))
        state.header = cloneDeep(concat(menuAside, meuns))
        dispatch('setMenus', state.aside)
      })
    },
    // 储存菜单
    async setMenus ({ state, dispatch }, menus) {
      await dispatch('d2admin/db/set', {
        dbName: 'sys',
        path: 'menu.menus',
        value: menus,
        user: true
      }, { root: true })
    },
    // 根据路由地址获取当前节点信息
    async getMenu ({ state, dispatch }, path) {
      if (!path || path === '') {
        return
      }
      var menus = cloneDeep(state.aside)
      if (menus.length === 1) {
        menus = await dispatch('getMenus')
      }
      await dispatch('selectMenu', { menus, path })
      return state.selectMenu
    },
    async selectMenu ({ state, dispatch }, { menus, path }) {
      for (let index = 0; index < menus.length; index++) {
        const element = menus[index]
        if (element.path === path) {
          state.selectMenu = element
        } else {
          if (element.children) {
            await dispatch('selectMenu', { menus: element.children, path })
          }
        }
      }
    },
    // 获取所有储存的菜单信息
    async getMenus ({ state, dispatch }) {
      const menus = await dispatch('d2admin/db/get', {
        dbName: 'sys',
        path: 'menu.menus',
        user: true
      }, { root: true })
      return menus
    }

  },
  mutations: {
    /**
     * @description 设置顶栏菜单
     * @param {Object} state state
     * @param {Array} menu menu setting
     */
    headerSet (state, menu) {
      // store 赋值
      state.header = menu
    },
    /**
     * @description 设置侧边栏菜单
     * @param {Object} state state
     * @param {Array} menu menu setting
     */
    asideSet (state, menu) {
      // store 赋值
      state.aside = menu
    }
  }
}
