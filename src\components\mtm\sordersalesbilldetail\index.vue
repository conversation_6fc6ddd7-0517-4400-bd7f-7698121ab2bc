<template>
  <d2-container class="mom_modelelemlist_master">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button status="success" @click="createEvent">生成账单</vxe-button>
          <vxe-button icon="vxe-icon--download" status="success" @click="exportDataEvent">导出</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button status="success" @click="get()">查询</vxe-button>
          <!-- <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="get()"></vxe-button> -->
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table ref='master_table' id="bomitem" show-footer :custom-config="{storage: true}" height="auto" :data="tableData" :merge-footer-items="mergeFooterItems" :footer-method="footerMethod">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="isSystemCreated" title="系统生成?" sortable width="90px" :formatter='formatBool'></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="90px"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="95px"></vxe-table-column>
      <vxe-table-column field="modelElemCode" title="工艺编码" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="modelElemName" title="工艺名称" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="modelElemPrice" title="单价" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="modelElemQty" title="数量/耗量" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="itemCode" title="面辅料编码" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="itemOriginalItemNo" title="原始货号" sortable width="90px"></vxe-table-column>
      <vxe-table-column field="itemPrice" title="单价" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="itemQty" title="数量/耗量" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="other" title="其他" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="otherPrice" title="其他单价" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="otherQty" title="其他数量" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="sorderDetailQty" title="版型数量" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="count" title="合计" sortable width="65px"></vxe-table-column>
      <vxe-table-column field="discount" title="折扣" sortable width="65px">
        <template v-slot="{ row }">
          <span>{{row.discount!=null?row.discount+'%':''}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="total" title="实际付款金额" sortable width="65px">
        <template v-slot="{ row }">
          <span style="color:red">{{row.total}}</span>
        </template>
      </vxe-table-column>
    </vxe-table>
  </d2-container>
</template>

<script>
export default {
  name: 'SorderSalesBillDetails', // 订单销售账单明细
  props: {
    sorder: {
      required: false,
      type: Object,
      default: null
    },
    client: {
      required: false,
      type: Object,
      default: null
    }
  },
  watch: {
    sorder: {
      handler: function (newVal, olVal) {
        if (newVal !== null) {
          this.searchFormSet({ sorderID: newVal.id })
        }
      }
    }
  },

  data () {
    return {
      mergeFooterItems: [{ row: 0, col: 1, rowspan: 1, colspan: 16 }],
      searchForm: {
        sorderIDs: null,
        sorderID: this.sorder?.id || null,
        sorderDetailModelID: null,
        clientID: this.client?.id || null
      },
      tableData: [],
      api: {
        get: '/mtm/odm_sorderbilldetail/get',
        createbill: '/mtm/odm_sorderbill/create'
      }
    }
  },
  created () {
    if (this.sorder.id) {
      this.get()
    }
  },
  methods: {
    searchFormSet ({ sorderID, sorderIDs, sorderDetailModelID, clientID }) {
      this.searchForm.sorderID = sorderID
      this.searchForm.sorderIDs = sorderIDs
      this.searchForm.sorderDetailModelID = sorderDetailModelID
      this.searchForm.clientID = clientID
      this.get()
    },
    formatBool ({ cellValue }) {
      if (cellValue === null) {
        return null
      }
      var b = (cellValue === true ? '是' : '否')
      return b
    },
    get () {
      this.$api.ActionRequest(this.api.get, this.searchForm).then(res => {
        this.tableData = res.items
        this.$refs.master_table.refreshColumn()
        this.$refs.master_table.updateData()
      })
    },
    exportDataEvent () {
      var filename = '【' + this.sorder.code + '】Bom清单' + this.timeFormate(new Date())
      this.$refs.master_table.exportData({ type: 'csv', filename: filename })
    },
    timeFormate (timeStamp) {
      const year = new Date(timeStamp).getFullYear()
      const month = new Date(timeStamp).getMonth() + 1 < 10 ? '0' + (new Date(timeStamp).getMonth() + 1) : new Date(timeStamp).getMonth() + 1
      const date = new Date(timeStamp).getDate() < 10 ? '0' + new Date(timeStamp).getDate() : new Date(timeStamp).getDate()
      const hh = new Date(timeStamp).getHours() < 10 ? '0' + new Date(timeStamp).getHours() : new Date(timeStamp).getHours()
      const mm = new Date(timeStamp).getMinutes() < 10 ? '0' + new Date(timeStamp).getMinutes() : new Date(timeStamp).getMinutes()
      const ss = new Date(timeStamp).getSeconds() < 10 ? '0' + new Date(timeStamp).getSeconds() : new Date(timeStamp).getSeconds()
      return (year + '年' + month + '月' + date + '日' + hh + ':' + mm + ':' + ss)
    },
    footerMethod ({ columns, data }) {
      const footerData = [
        columns.map((column, _columnIndex) => {
          if (_columnIndex === 0) {
            return '共计'
          }
          if (['total'].includes(column.property)) {
            return this.sumNum(data, 'total')
          }
          return null
        })
      ]
      return footerData
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count += this.$utils.toNumber(item[field])
      })
      return this.$utils.round(count, 2)
    },
    createEvent () {
      this.$api.ActionRequest(this.api.createbill, this.searchForm).then(res => {
        this.$message({
          type: 'success',
          message: '生成成功'
        })
        this.get()
      })
    }
  }

}
</script>

<style>
</style>
