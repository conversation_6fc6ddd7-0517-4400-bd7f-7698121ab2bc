<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <vxe-button status="warning" @click="deepCopyEvent()" v-if="menuAction.allowEdit" size="mini">深度复制</vxe-button>
          <vxe-button status="success" @click="updateCreate()" v-if="menuAction.allowEdit" size="mini">更新规格单</vxe-button>
          <vxe-button icon="fa fa-trash-o" status="danger" v-if="menuAction.allowDelete" @click="deletesEvent">批量删除</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="submit" status="success" @click="loadData()">查询</vxe-button>
          <!-- <vxe-button type="text" icon="vxe-icon--question" class="tool-btn" @click="documentBoxShowEvent"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button> -->
        </template>
      </vxe-toolbar>

    </template>

    <vxe-table id='MomModelbase_master_table' ref='master_table' @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="40"></vxe-table-column>
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="sizeColumn" title="规格字段" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="algorithmTypeText" title="算法类型" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="sizeElema" title="身高" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="stepa" title="身高档差" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="sizeElemb" title="胸围" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="stepb" title="胸围档差" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="sizeElemc" title="体型" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="stepc" title="体型档差" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="sizeElemd" title="臀围" sortable width="70px"></vxe-table-column>
      <vxe-table-column field="stepd" title="臀围档差" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="value" title="值" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="minValue" title="最小值" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="maxValue" title="最大值" sortable width="80px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="规格字段" field="sizeColumnID" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.sizeColumnID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="sizeColumnMethod" clearable size="small">
              <el-option v-for="item in sizeColumnComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="算法类型" field="algorithmType" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.algorithmType" size="small">
              <el-option v-for="item in AlgorithmTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="身高" field="sizeElemaID" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.sizeElemaID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="SizeElemAMethod" clearable size="mini">
              <el-option v-for="item in SizeElemAComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="身高档差" field="stepa" span="12" :item-render="{}"><template #default>
            <vxe-input v-model="selectRow.stepa" placeholder="请输入值" clearable type="number"></vxe-input>
          </template></vxe-form-item>
        <vxe-form-item title="胸围" field="sizeElembID" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.sizeElembID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="SizeElemBMethod" clearable size="mini">
              <el-option v-for="item in SizeElemBComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="胸围档差" field="stepb" span="11" :item-render="{}"><template #default>
            <vxe-input v-model="selectRow.stepb" placeholder="请输入值" clearable type="number"></vxe-input>
          </template></vxe-form-item>
        <vxe-form-item title="体型" field="sizeElemcID" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.sizeElemcID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="SizeElemCMethod" clearable size="mini">
              <el-option v-for="item in SizeElemCComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="体型档差" field="stepc" span="12" :item-render="{}"><template #default>
            <vxe-input v-model="selectRow.stepc" placeholder="请输入值" clearable type="number"></vxe-input>
          </template></vxe-form-item>

        <vxe-form-item title="臀围" field="sizeElemdID" span="12" :item-render="{}"><template #default>
            <el-select v-model="selectRow.sizeElemdID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="SizeElemDMethod" clearable size="small">
              <el-option v-for="item in SizeElemDComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template></vxe-form-item>
        <vxe-form-item title="臀围档差" field="stepd" span="12" :item-render="{}"><template #default>
            <vxe-input v-model="selectRow.stepd" placeholder="请输入值" clearable type="number"></vxe-input>
          </template></vxe-form-item>
        <vxe-form-item title="值" field="value" span="24" :item-render="{}"><template #default>
            <vxe-input v-model="selectRow.value" placeholder="请输入值" clearable type="number"></vxe-input>
          </template></vxe-form-item>
        <vxe-form-item title="最小值" field="minValue" span="12" :item-render="{}"><template #default>
            <vxe-input v-model="selectRow.minValue" placeholder="请输入值" clearable type="number"></vxe-input>
          </template></vxe-form-item>
        <vxe-form-item title="最大值" field="maxValue" span="12" :item-render="{}"><template #default>
            <vxe-input v-model="selectRow.maxValue" placeholder="请输入值" clearable type="number"></vxe-input>
          </template></vxe-form-item>

        <vxe-form-item title="备注" field="remark" span="24" :item-render="{}"><template #default>
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="selectRow.remark" clearable>
            </el-input>
          </template></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"><template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template></vxe-form-item>
      </vxe-form>
    </vxe-modal>

    <vxe-modal v-model="showSizeList" :title="'深度复制'" width="80%" height="60%" resize destroy-on-close :showFooter='true'>
      <size-list ref="sizeList" :showOpc="false" />
      <template slot='footer'>
        <vxe-button status="warning" v-if="menuAction.allowAdd" @click="deepcopy">复制选中</vxe-button>
      </template>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import SizeList from './master.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'MomSizeListDetailSizeListBase',
  mixins: [detailTableMixins],
  components: {
    SizeList
  },
  props: {
    form: {
      required: true,
      type: Object
    }
  },
  data () {
    return {
      formData: {
        remark: '',
        isActive: true,
        sizeColumnID: null,
        sizeListID: null,
        sizeElemaID: null,
        sizeElembID: null,
        sizeElemcID: null,
        sizeElemdID: null,
        stepa: 0,
        stepb: 0,
        stepc: 0,
        stepd: 0,
        value: 0,
        minValue: 0,
        maxValue: 0,
        algorithmType: null
      },
      showSizeList: false,
      formRules: {
        sizeColumnID: [{ required: true, message: '请选择规格字段' }],
        algorithmType: [{ required: true, message: '请选择算法类型' }]
      },
      formItems: [
        // { field: 'modelTypeID', title: '版型归类', span: 12, itemRender: { name: '$select', options: [] } },
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mOM_SizeListBase/get',
        add: '/mtm/mOM_SizeListBase/adds',
        edit: '/mtm/mOM_SizeListBase/updates',
        delete: '/mtm/mOM_SizeListBase/deletes',
        copy: '/mtm/mOM_SizeListBase/copy',
        updateCreate: '/mtm/mom_sizelist/CreateUpdateSizes',
        sizeColumnComboStoreByQuery: '/mtm/comboQuery/sizeColumnComboStoreByQuery',
        SizeElemAComboStoreByQuery: '/mtm/comboQuery/SizeElemAComboStoreByQuery',
        SizeElemBComboStoreByQuery: '/mtm/comboQuery/SizeElemBComboStoreByQuery',
        SizeElemCComboStoreByQuery: '/mtm/comboQuery/SizeElemCComboStoreByQuery',
        SizeElemDComboStoreByQuery: '/mtm/comboQuery/SizeElemDComboStoreByQuery',
        AlgorithmTypeComboStore: '/mtm/combo/AlgorithmTypeComboStore'
      },
      footerCompanyInfo: false,
      sizeColumnComboStoreByQuery: [],
      SizeElemAComboStoreByQuery: [],
      SizeElemBComboStoreByQuery: [],
      SizeElemCComboStoreByQuery: [],
      SizeElemDComboStoreByQuery: [],
      AlgorithmTypeComboStore: []
      // ModelTypeComboStore: []
    }
  },
  async created () {
    this.loadData({ id: this.form.id }).then(({ data }) => {
      this.tableData = data
    })
    this.formData.sizeListID = this.form.id
    await this.sizeColumnMethod()
    await this.SizeElemAMethod()
    await this.SizeElemBMethod()
    await this.SizeElemCMethod()
    await this.SizeElemDMethod()
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.AlgorithmTypeComboStore).then(result => {
        this.AlgorithmTypeComboStore = result
      })
    },
    async sizeColumnMethod (query) {
      await this.$api.ActionRequest(this.api.sizeColumnComboStoreByQuery, { text: query }).then(result => {
        this.sizeColumnComboStoreByQuery = result
      })
    },
    async SizeElemAMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemAComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemAComboStoreByQuery = result
      })
    },
    async SizeElemBMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemBComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemBComboStoreByQuery = result
      })
    },
    async SizeElemCMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemCComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemCComboStoreByQuery = result
      })
    },
    async SizeElemDMethod (query) {
      await this.$api.ActionRequest(this.api.SizeElemDComboStoreByQuery, { text: query }).then(result => {
        this.SizeElemDComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.SizeElemAComboStoreByQuery, { gid: row.sizeElemaID }).then(result => {
        this.SizeElemAComboStoreByQuery = result
      })

      await this.$api.ActionRequest(this.api.SizeElemBComboStoreByQuery, { gid: row.sizeElembID }).then(result => {
        this.SizeElemBComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SizeElemCComboStoreByQuery, { gid: row.sizeElemcID }).then(result => {
        this.SizeElemCComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SizeElemDComboStoreByQuery, { gid: row.sizeElemdID }).then(result => {
        this.SizeElemDComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.sizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
        this.sizeColumnComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    async updateCreate () {
      const loading = this.$loading({
        lock: true,
        text: '更新中请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      await this.$api.ActionRequest(this.api.updateCreate, this.form).then(result => {
        loading.close()
        this.$XModal.message({ message: '更新成功', status: 'success' })
      }).catch(() => {
        loading.close()
      })
    },
    deepCopyEvent () {
      this.showSizeList = true
    },
    async deepcopy () {
      var sizeList = this.$refs.sizeList
      var data = sizeList.$refs.master_table.getCheckboxRecords()
      if (data.length === 0) {
        this.$XModal.message({ message: '请勾选要复制的数据', status: 'error' })
        return
      }
      if (data.length > 1) {
        this.$XModal.message({ message: '一次只能复制一个规格单的数据', status: 'error' })
        return
      }
      console.log(data)
      await this.$api.ActionRequest(this.api.copy, { originalID: data[0].id, targetID: this.form.id }).then(result => {
        this.showSizeList = false
        this.$XModal.message({ message: '复制成功', status: 'success' })
        this.loadData({ id: this.form.id })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
