<template>
  <d2-container>
    <div style="text-align:center">
      <!-- 此处action需为有效的任意接口——当前为官网范例接口 -->
      <el-upload drag :limit="20" :multiple="true" action="" ref="upload" accept=".json" :auto-upload="false" :file-list="fileListData" :on-change="onChange" :on-success="onSuccess" :on-remove="onRemove" :on-exceed="handleExceed" :on-preview="handlePreview">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          上传json文件，且一次性最多只能上传 20 个json文件
          <el-alert title="注意：SuitSupply订单的Json文件与系统中的Json文件不一样,请根据下方开关选择要导入的方式,请勿修改任何ID相关的东西,否则上传会失败！！" type="error" :closable="false" effect="dark"> </el-alert>
        </div>
      </el-upload>
    </div>
    <span slot="footer" style="float: right;">
      <!-- <el-button @click="dialogImport = false" size="mini">取 消</el-button> -->
      <vxe-switch v-model="isSuitSupply" open-label="SuitSupply_Json" close-label="MTM_Json"></vxe-switch>
      <vxe-button @click="importConfirm" size="mini" type="primary" round>确 定</vxe-button>
    </span>
    <el-dialog :visible.sync="dialogPreviewJSON" title="JSON文件预览" v-if="dialogPreviewJSON" width="80%" height="50%" style="height" append-to-body>
      <d2-container>
        <s-json :json="jsonData"></s-json>
      </d2-container>
    </el-dialog>
  </d2-container>
</template>

<script>
import SJson from './SJson.vue'
export default {
  name: 'UpLoadJson',
  components: {
    SJson
  },
  props: {
    upLoadSuccess: { type: Function }
  },
  data () {
    return {
      jsonData: null,
      dialogPreviewJSON: false,
      fileListData: [],
      stockData: null,
      isSuitSupply: false,
      uploadData: [],
      api: {
        import: '/mtm/oDM_SorderSuitSupplyOrder/importOrder',
        mtmupdate: '/mtm/oDM_SorderSuitSupplyOrder/updates' // http://localhost:21021/api/mtm/oDM_SorderSuitSupplyOrder/updates
      }
    }
  },
  created () {

  },
  methods: {
    // 上传文件超出文件数量限制/文件格式不符合要求时
    handleExceed (files, fileList) {
      this.$message.warning('每次最多导入10个json文件！')
    },

    // 文件上传成功
    onSuccess (res, file, fileList) {
      const reader = new FileReader()
      reader.readAsText(file.raw)
      reader.onload = (e) => {
        console.log('上传成功')
      }
    },
    // 文件上传
    onChange (file) {
      this.fileListData.push(file)
      // const reader = new FileReader()
      // reader.readAsText(file.raw)
      // reader.onload = (e) => {
      //   console.log("上传")
      //   this.fileList.push(file)
      //   // this.uploadData = this.jsonPar(e.target.result)
      // }
    },
    getData () {
      this.fileListData.forEach(file => {
        const reader = new FileReader()
        reader.readAsText(file.raw)
        reader.onload = (e) => {
          var jsondata = this.jsonPar(e.target.result)
          if (this.$utils.isArray(jsondata)) {
            this.uploadData.push(...jsondata)
          } else {
            this.uploadData.push(jsondata)
          }

          // this.uploadData = this.jsonPar(e.target.result)
        }
      })
    },
    jsonPar (data) {
      try {
        return JSON.parse(data)
      } catch (error) {
        console.error(error)
        this.$message.error('json文件读入失败,可能Json文件格式有误！')
      }
    },
    // 移除文件
    onRemove (file, fileList) {
      this.fileListData = fileList
    },
    // 导入确认
    async importConfirm () {
      this.getData()

      this.$confirm('导入后原数据会被覆盖，确定导入吗?', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // 使用目标数据变量接收上传后的文件数据
        this.dialogImport = false
        await this.importorder1()
      })
    },
    // json预览
    handlePreview (file) {
      const reader = new FileReader()
      reader.readAsText(file.raw)
      reader.onload = (e) => {
        this.jsonData = this.jsonPar(e.target.result)
        this.dialogPreviewJSON = true
      }
    },
    async importorder1 () {
      if (this.uploadData === null) {
        this.$message({ type: 'error', message: '导入数据为空,请重试!!!' })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '导入中请稍后！！！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      var url = this.api.import
      if (!this.isSuitSupply) {
        url = this.api.mtmupdate
      }
      await this.$api.ActionRequest(url, this.uploadData).then(result => {
        this.$message({ type: 'success', message: '导入成功!' })
        this.upLoadSuccess()
        loading.close()
      }).catch(() => {
        loading.close()
      })
    }
  }
}
</script>

<style>
</style>
