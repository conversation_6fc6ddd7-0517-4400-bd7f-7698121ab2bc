<template>
  <div class="sorderdetailimages">
    <div class="text main-container" v-if="detailelemimage">
      <div class="main-picture center-block">
        <template v-if="isGroupUp(SorderDetailModel.groupID)">
          <div v-for="(item1,index1) in 4" v-bind:Key="'b'+index1" class="main-list shangyi">
            <div v-for="(item2,index2) in 3" :key="'c'+index2" class="item" :title="'位置 ' + (arrIntImgRule1[index1][index2])">
              <template v-for="(elem,elemindex) in ElemFilter(index1,index2,DetailElemImages)">
                <img v-if="DetailModelElem(elem,index1,index2)" :key="elemindex" :src="elem.imagePath" v-bind:imageSeq="elem.imageSeq" v-bind:tag-modelElemID='elem.modelElemID' v-bind:tag-modelElemID1='elem.modelElemID1' />
              </template>
            </div>
          </div>
        </template>
        <template v-else>
          <div v-for="(item1,index1) in 3" v-bind:Key="'b'+index1" class="main-list-pants xiayi">
            <div v-for="(item2,index2) in 4" :key="'c'+index2" class="item" :title="'位置 ' + (arrIntImgRule2[index1][index2])">
              <template v-if="showPostion(arrIntImgRule2,index1,index2)">
                <template v-for="(elem,elemindex) in ElemFilter(index1,index2,DetailElemImages)">
                  <img v-if="DetailModelElem(elem,index1,index2)" :key="elemindex" :src="elem.imagePath" v-bind:imageSeq="elem.imageSeq" v-bind:tag-modelElemID='elem.modelElemID' v-bind:tag-modelElemID1='elem.modelElemID1' />
                </template>
              </template>
            </div>
          </div>
        </template>

      </div>

    </div>
    <div class="itemimage" v-if="itemimage">
      <template v-for="(elem,elemindex) in itemImageFilter">
        <div class="item" :key="elemindex">
          <img style="width: 300px;height: 150px;display: inherit;" :key="elemindex" :src="elem.itemImageUrl" v-bind:tag-modelElemID='elem.modelElemID' v-bind:tag-modelElemID1='elem.modelElemID1' />
        </div>
      </template>
    </div>
    <el-row>
      <vxe-switch v-model="detailelemimage" open-label="展示图" close-label="展示图" class="fuliaoactive"></vxe-switch>
      <vxe-switch v-model="itemimage" open-label="商标图" close-label="商标图" class="fuliaoactive"></vxe-switch>
    </el-row>
  </div>
</template>

<script>
import config from '@/config.js'
export default {
  name: 'SorderDetailModelImages',
  mixins: [config],
  props: {
    SorderDetailModel: {
      type: Object,
      requited: true
    },
    SorderDetailElemData: {
      type: Array,
      requited: true
    }
  },
  watch: {
    'SorderDetailModel.modelID': {
      deep: true,
      handler: function (newVal, oldVal) {
        this.get(newVal)
      }
    }
  },
  computed: {
    itemImageFilter: function () {
      var list = []
      var images = this.SorderDetailElemData.filter(item => { return item.itemImageUrl !== null })
      if (images.length > 0) {
        list = images.map(item => { return { modelElemID: item.modelElemID, modelElemID1: item.modelElemID, itemImageUrl: item.itemImageUrl } })
      }
      return list
    }
  },
  data () {
    return {
      itemimage: true,
      detailelemimage: true,
      DetailElemImages: [],
      api: { get: '/mtm/oDM_SorderDetailModel/getModelElemImages' },
      arrIntImgRule1: [[1, 5, 6], [2, 4, 8], [10, 11, -1], [3, 9, 7]],
      arrIntImgRule2: [[1, 2, -1, -1], [3, 4, 5, 6], [7, 8, 9, 10]],
      arrIntImgRule3: [[12, 13, 14], [15, 18, 19]]
    }
  },
  created () {
    if (this.SorderDetailModel.modelID) {
      this.get(this.SorderDetailModel.modelID)
    }
  },
  methods: {
    get (modelId) {
      this.$api.ActionRequest(this.api.get, { Id: modelId }).then(result => {
        this.DetailElemImages = result
      })
    },
    showPostion (positions, index1, index2) {
      var index = positions[index1][index2]
      console.log(index)
      if (index !== (-1)) {
        return true
      } else {
        return false
      }
    },
    ElemFilter (index1, index2, data) {
      if (data.length === 0) return
      var index = 0
      if (this.isGroupUp(this.SorderDetailModel.groupID)) {
        index = this.arrIntImgRule1[index1][index2] // 上衣
      } else {
        index = this.arrIntImgRule2[index1][index2] // 裤子
      }
      // var index = this.arrIntImgRule3[index1][index2] //商标
      var array = []
      var elem = data.GetFirstElement('positionID', index)
      if (elem) {
        array = elem.list
      }
      // console.log(array)
      return array
    },
    // itemImageFilter(data) {
    //   if (data.length === 0) return
    // },
    ElemFilter1 (index1, index2, data) {
      if (data.length === 0) return
      var index = this.arrIntImgRule3[index1][index2]// 商标

      var array = []
      var elem = data.GetFirstElement('positionID', index)
      if (elem) {
        array = elem.list
      }
      // console.log(array)
      return array
    },
    DetailModelElem (item, index1, index2) {
      if (item.fromType === 'Type') {
        return true
      }
      if (item.fromType === 'Base') {
        if (item.modelElemID1 !== null) {
          return this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID1)
        }
        return true
      }
      if (item.fromType === 'ModelElem') {
        return false
      } else {
        if (item.modelElemID === null) {
          return false
        }
        var b = false
        if (item.modelElemID1 !== null) {
          b = this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID) && this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID1)
        } else {
          b = this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID)
        }
        return b
      }
    },
    compare (prop) {
      return function (obj1, obj2) {
        var val1 = obj1[prop]
        var val2 = obj2[prop]
        if (val1 < val2) {
          return -1
        } else if (val1 > val2) {
          return 1
        } else {
          return 0
        }
      }
    }
  }
}
</script>

<style lang="scss">
.sorderdetailimages {
  display: block;
  // min-height: 800px;
  width: 100%;
  .detialelems {
    min-width: 200px;
  }
  .itemimage {
    min-height: 300px;
    // text-align: center;
    .item {
      display: inline-block;
      // border: 1px solid red;
      width: 300px;
      height: 150px;
      img {
        position: absolute;
        display: block;
      }
      // display: block;
      // width: 100%;
      // height: 50%;
    }
  }
  // .item {
  //   border: 1px solid red;
  // }
  .text {
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
  }
  .main-picture .main-list .item:hover,
  .main-picture .main-list-pants .item:hover {
    position: relative;
    border: 1px solid red;
    z-index: 999;
  }

  .main-picture-split {
    width: 65em;
  }
  .main-picture img {
    vertical-align: middle;
  }

  .main-picture {
    max-width: 100%;
    /* height: 800px; */
    min-height: 800px;
    display: inline-block;
  }
  /* 上衣 */
  .main-picture .main-list,
  .main-picture .main-list-pants {
    display: inline-block;
    padding: 2px;
  }

  .main-picture .main-list .item,
  .main-picture .main-list-pants .item {
    float: left;
  }

  .main-picture .main-list .item img,
  .main-picture .main-list-pants .item img {
    position: absolute;
  }

  .main-picture .main-list:first-child {
    width: 240px;
  }

  .main-picture .main-list:nth-child(2) {
    width: 135px;
  }

  .main-picture .main-list:nth-child(3) {
    width: 135px;
  }

  .main-picture .main-list:nth-child(4) {
    width: 495px;
  }

  .main-picture .main-list:first-child .item:first-child,
  .main-picture .main-list:first-child .item:first-child img {
    height: 361.82px;
    width: 240px;
    display: inherit;
  }

  .main-picture .main-list:first-child .item:nth-child(2),
  .main-picture .main-list:first-child .item:nth-child(2) img,
  .main-picture .main-list:first-child .item:nth-child(3),
  .main-picture .main-list:first-child .item:nth-child(3) img {
    height: 78.44px;
    width: 185px;
    display: inherit;
  }

  .main-picture .main-list:nth-child(2) .item:first-child,
  .main-picture .main-list:nth-child(2) .item:first-child img {
    height: 218.42px;
    width: 135px;
    display: inherit;
  }

  .main-picture .main-list:nth-child(2) .item:nth-child(2),
  .main-picture .main-list:nth-child(2) .item:nth-child(2) img {
    height: 206.41px;
    width: 128px;
    display: inherit;
  }

  .main-picture .main-list:nth-child(2) .item:nth-child(3),
  .main-picture .main-list:nth-child(2) .item:nth-child(3) img {
    height: 151.78px;
    width: 135px;
    display: inherit;
  }

  .main-picture .main-list:nth-child(3) .item:first-child,
  .main-picture .main-list:nth-child(3) .item:first-child img {
    height: 254.98px;
    width: 135px;
    display: inherit;
  }

  .main-picture .main-list:nth-child(3) .item:nth-child(2),
  .main-picture .main-list:nth-child(3) .item:nth-child(2) img {
    height: 254.98px;
    width: 135px;
    display: inherit;
  }

  .main-picture .main-list:nth-child(4) .item:first-child,
  .main-picture .main-list:nth-child(4) .item:first-child img {
    height: 408.33px;
    width: 490px;
    display: inherit;
  }

  .main-picture .main-list:nth-child(4) .item:nth-child(2),
  .main-picture .main-list:nth-child(4) .item:nth-child(2) img {
    height: 75.16px;
    width: 200px;
    display: inherit;
  }

  .main-picture .main-list:nth-child(4) .item:nth-child(3),
  .main-picture .main-list:nth-child(4) .item:nth-child(3) img {
    height: 78.52px;
    width: 195px;
    display: inherit;
  }

  .main-picture .main-list-pants:first-child {
    width: 600px;
    height: 800px;
  }
  /* 下装 */
  .main-picture .main-list-pants:nth-child(2) {
    width: 200px;
    height: 800px;
  }
  .main-picture .main-list-pants:nth-child(3) {
    width: 200px;
    height: 800px;
  }
  /* .main-picture .main-list-pants:nth-child(3)  .item:first-child img{
            height: 33%;
            display: inherit;
         }
         .main-picture .main-list-pants:nth-child(3)  .item:nth-child(2) img{
            height: 33%;
            display: inherit;
         }
        .main-picture .main-list-pants:nth-child(3)  .item:nth-child(3) img{
           height: 33%;
           display: inherit;
        } */
  .main-picture .main-list-pants:first-child .item:first-child,
  .main-picture .main-list-pants:first-child .item:first-child img,
  .main-picture .main-list-pants:first-child .item:nth-child(2),
  .main-picture .main-list-pants:first-child .item:nth-child(2) img {
    height: 288.02px;
    width: 604px;
    display: inherit;
  }

  .main-picture .main-list-pants:nth-child(2) .item,
  .main-picture .main-list-pants:nth-child(2) .item img {
    height: 160px;
    width: 200px;
    display: inherit;
  }
  .main-picture .main-list-pants:first-child .item:first-child,
  .main-picture .main-list-pants:first-child .item:first-child img,
  .main-picture .main-list-pants:first-child .item:nth-child(3),
  .main-picture .main-list-pants:first-child .item:nth-child(2) img {
    height: 288.02px;
    width: 604px;
    display: inherit;
  }

  .main-picture .main-list-pants:nth-child(3) .item,
  .main-picture .main-list-pants:nth-child(3) .item img {
    height: 160px;
    width: 200px;
    display: inherit;
  }
}
</style>
