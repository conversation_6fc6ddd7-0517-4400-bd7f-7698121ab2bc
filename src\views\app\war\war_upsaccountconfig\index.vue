<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="upsAccountConfigType">
              <template #default="{ data }">
                <vxe-select v-model="data.upsAccountConfigType" placeholder="类型" clearable>
                  <vxe-option v-for="item in UpsAccountConfigTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarUpsaccountconfigMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="upsAccountConfigTypeText" title="类型" sortable width="100"></vxe-table-column>
      <vxe-table-column field="name" title="公司名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="country" title="国家简称" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="toCountry" title="国家" sortable width="100"></vxe-table-column>

      <vxe-table-column field="stateProvince" title="省州区简称" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="stateProvinceCode" title="省州区简称" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="city" title="城市" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="cityCode" title="城市简称" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="postalCode" title="邮编" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="contactName" title="联系人" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="phone" title="电话" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="email" title="邮箱" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="isShipper" title="默认发货人" sortable width="100" :formatter='formatBool'> </vxe-table-column>
      <vxe-table-column field="isDefaultInvestor" title="默认运费扣除方" sortable width="100" :formatter='formatBool'> </vxe-table-column>
      <vxe-table-column field="address" title="收货地址" sortable width="100"></vxe-table-column>
      <vxe-table-column field="address1" title="收货地址1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="registeredAddressEntity" title="注册地址" sortable width="100"></vxe-table-column>
      <vxe-table-column field="vat" title="AVT" sortable width="100"></vxe-table-column>
      <vxe-table-column field="upsAccount" title="账号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="类型" field="upsAccountConfigType" span="24" :item-render="{name: '$select', options: UpsAccountConfigTypeComboStore}"></vxe-form-item>
        <vxe-form-item title="公司名称" field="name" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="国家" field="toCountry" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="国家简称" field="country" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="省/周/区" field="stateProvince" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="省/周/区简称" field="stateProvinceCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="城市" field="city" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="城市编码" field="cityCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="联系人" field="contactName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="电话" field="phone" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="邮箱" field="email" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="邮编" field="postalCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>

        <vxe-form-item title="地址" field="address" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="地址1" field="address1" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="注册地址" field="registeredAddressEntity" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="ups账号" field="upsAccount" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="VAT" field="vat" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',},props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="是否发货人" field="isShipper" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="默认运费扣除方?" field="isDefaultInvestor" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'war_upsaccountconfig',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        name: '',
        country: null,
        toCountry: null,
        stateProvince: null,
        stateProvinceCode: null,
        city: null,
        cityCode: null,
        address: null,
        address1: null,
        postalCode: null,
        registeredAddressEntity: null,
        contactName: null,
        phone: null,
        email: null,
        upsAccount: null,
        remark: '',
        isActive: true,
        isShipper: false,
        isDefaultInvestor: false,
        upsAccountConfigType: null
      },
      formRules: {
        upsAccountConfigType: [{ required: true, message: '请选择类型' }],
        country: [{ required: true, message: '请输入国家编码' }],
        toCountry: [{ required: true, message: '请输入国家名称' }],
        name: [{ required: true, message: '请输入公司名称' }],
        stateProvince: [{ required: true, message: '请输入省/周/区' }],
        stateProvinceCode: [{ required: true, message: '请输入省/周/区简称' }],
        city: [{ required: true, message: '请输入城市' }],
        phone: [{ required: true, message: '请输入联系电话' }],
        upsAccount: [{ required: true, message: '请输入ups账号' }],
        cityCode: [{ required: true, message: '请输入城市简称' }],
        postalCode: [{ required: true, message: '请输入邮编' }],
        address: [{ required: true, message: '请输入收货地址' }, { min: 2, max: 34, message: '长度在 2 到 34 个字符,地址过长可以填写在地址1中' }],
        registeredAddressEntity: [{ required: true, message: '请输入注册地址' }]
      },
      api: {
        get: '/mtm/war_upsaccountconfig/get',
        add: '/mtm/war_upsaccountconfig/adds',
        edit: '/mtm/war_upsaccountconfig/updates',
        delete: '/mtm/war_upsaccountconfig/deletes',
        UpsAccountConfigTypeComboStore: '/mtm/combo/UpsAccountConfigTypeComboStore'
      },
      UpsAccountConfigTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.UpsAccountConfigTypeComboStore).then(result => {
        this.UpsAccountConfigTypeComboStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
