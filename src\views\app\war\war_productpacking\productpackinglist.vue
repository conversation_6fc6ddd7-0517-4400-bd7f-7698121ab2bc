<template>
  <d2-container class="productpacking">

    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button> -->
          <vxe-button status="warning" @click="createEvent()">系统生成装箱明细</vxe-button>
          <vxe-button status="success" @click="save()">保存修改</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="load">
          </vxe-button>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table border id='WarItemstockdetailMasterTable' height="auto" ref='master_table' :data="tableData" :print-config="{}" keep-source :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, icon: 'fa fa-pencil-square-o'}" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}" :edit-rules="validRules">
      <!-- <vxe-table-column type="checkbox" width="60"></vxe-table-column> -->
      <vxe-table-column type="seq" width="50"></vxe-table-column>
      <vxe-table-column field="productBoxType" title="箱子类型" sortable width="150" :edit-render="{name: '$select', options: productBoxTypeComboStore}"> </vxe-table-column>
      <vxe-table-column field="number" title="序号" sortable width="100" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}"> </vxe-table-column>
      <vxe-table-column field="boxNumber" title="箱号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="clientPersonName" title="顾客" sortable width="100"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sorderNumber" title="订单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="serialNumber" title="流水号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="({cellValue})=>formatDate(cellValue)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="({cellValue})=>formatDate(cellValue)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="qty" title="数量" width="100"></vxe-table-column>
    </vxe-table>
    <vxe-modal v-model="packinglist" title="装箱清单" height="70%" width="70%" resize destroy-on-close>
      <product-packing-list v-if="packinglist" :form="form" />
    </vxe-modal>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import ProductPackingList from './productpackinglist.vue'
export default {
  name: 'ShipmentsList', // 发货清单
  mixins: [detailTableMixins],
  components: { ProductPackingList },
  props: {
  },
  data () {
    return {
      packinglist: false,
      api: {
        get: '/mtm/WAR_ShipmentDetail/get',
        edit: '/mtm/WAR_ShipmentDetail/updates',
        createpacking: '/mtm/wAR_ProductPacking/createPacking',
        productBoxTypeComboStore: '/mtm/combo/productBoxTypeComboStore',
        createPackingList: '/mtm/wAR_ProductPacking/createPackingList'
      },
      formData: {
        logisticsCompany: null,
        trackingNumber: null,
        remark: null,
        detailList: ''
      },
      tableData: [],
      validRules: {
        number: [
          { required: true, message: '序号必须填写' }
        ],
        productBoxType: [
          { required: true, message: '箱子类型必须填写' }
        ]
      },
      productBoxTypeComboStore: []
    }
  },

  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id })
  },
  methods: {
    load () {
      this.loadData({ id: this.form.id })
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.productBoxTypeComboStore).then(result => {
        this.productBoxTypeComboStore = result
      })
    },
    printEvent () {
      this.$refs.xTable.print()
    },

    formatDate (cellValue) {
      if (cellValue === null) {
        return null
      }
      return this.$utils.toDateString(this.formatLongDate(cellValue), 'yyyy-MM-dd HH:mm:ss')
    },
    formatLongDate (date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    async createEvent () {
      this.$XModal.confirm('已生成的装箱单会被覆盖,请确认！').then(async type => {
        if (type === 'confirm') {
          const loading = this.$loading({
            lock: true,
            text: '生成中.....请稍后！！',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          await this.$api.ActionRequest(this.api.createpacking, { shipmentID: this.form.id }).then(async result => {
            this.$notify.success({
              title: '成功',
              message: '成功'
            })
            loading.close()
            this.$emit('reload')
            await this.loadData()
          }).catch(() => {
            loading.close()
          })
        }
      })
    },
    async save () {
      var b = await this.fullValidEvent()
      if (!b) {
        return
      }
      var xtable = this.$refs[this.tableRef]
      var insertData = xtable.getInsertRecords()
      var updateData = xtable.getUpdateRecords()
      if (insertData.length > 0) {
        await this.$api.ActionRequest(this.api.add, insertData).then(async res => {
          this.$notify.success({
            title: '成功',
            message: '成功'
          })
          this.loadData({ id: this.form.id })
          await this.$api.ActionRequest(this.api.createPackingList, { shipmentID: this.form.id })
        })
      }
      if (updateData.length > 0) {
        await this.$api.ActionRequest(this.api.edit, updateData).then(async res => {
          this.$notify.success({
            title: '成功',
            message: '成功'
          })
          this.loadData({ id: this.form.id })

          await this.$api.ActionRequest(this.api.createPackingList, { shipmentID: this.form.id }).then(() => {
            this.$emit('reload')
          })
        })
      }
    },
    async fullValidEvent () {
      var xtable = this.$refs[this.tableRef]
      const errMap = await xtable.fullValidate().catch(errMap => errMap)
      if (errMap) {
        const msgList = []
        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            const { rowIndex, column, rules } = params
            rules.forEach(rule => {
              msgList.push(`第 ${rowIndex} 行 ${column.title} 校验错误：${rule.message}`)
            })
          })
        })
        this.$XModal.message({
          status: 'error',
          message: () => {
            return [
              <div class="red" style="max-height: 400px;overflow: auto;">
                {
                  msgList.map(msg => <div>{msg}</div>)
                }
              </div>
            ]
          }
        })
        return false
      } else {
        // this.$XModal.message({ status: 'success', message: '校验成功！' })
        return true
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.productpacking {
  .tab {
    // height: 80%;
  }
}
</style>
