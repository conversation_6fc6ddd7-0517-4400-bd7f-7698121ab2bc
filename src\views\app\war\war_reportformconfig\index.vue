<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="reportFormType" :item-render="{name: '$select', options: ReportFormTypeComboStore,props:{clearable:true,placeholder:'单据分类'}}">

            </vxe-form-item>
            <vxe-form-item field="reportFormConfigType">
              <template #default="{ data }">
                <vxe-select v-model="data.reportFormConfigType" placeholder="类型" clearable>
                  <vxe-option v-for="item in ReportFormConfigTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarReportformconfigMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="reportFormTypeText" title="单据分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="reportFormConfigTypeText" title="类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="isDefault" title="是否默认" :formatter='formatBool' sortable width="100"> </vxe-table-column>
      <vxe-table-column field="code" title="编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value" title="绑定值" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value1" title="绑定值1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value2" title="绑定值2" sortable width="100"></vxe-table-column>
      <vxe-table-column field="value3" title="绑定值3" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="单据分类" field="reportFormType" span="12" :item-render="{name: '$select', options: ReportFormTypeComboStore,props: { clearable: true}}"></vxe-form-item>
        <vxe-form-item title="绑定类型" field="reportFormConfigType" span="12" :item-render="{name: '$select', options: ReportFormConfigTypeComboStore,}"></vxe-form-item>
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="绑定值" field="value" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="绑定值1" field="value1" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="绑定值2" field="value2" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="绑定值3" field="value3" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="是否默认" field="isDefault" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',},props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'war_reportformconfig',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
        reportFormType: null
      },
      formData: {
        reportFormConfigType: null,
        isDefault: false,
        reportFormType: null,
        value: null,
        value1: null,
        value2: null,
        value3: null,
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        sort: 1
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 1, max: 50, message: '长度在 1 到 50 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 1, max: 50, message: '长度在 1 到 50 个字符' }],
        reportFormConfigType: [{ required: true, message: '请选择绑定类型' }]
      },
      api: {
        get: '/mtm/war_reportformconfig/get',
        add: '/mtm/war_reportformconfig/adds',
        edit: '/mtm/war_reportformconfig/updates',
        delete: '/mtm/war_reportformconfig/deletes',
        ReportFormTypeComboStore: '/mtm/combo/ReportFormTypeComboStore',
        ReportFormConfigTypeComboStore: '/mtm/combo/ReportFormConfigTypeComboStore'
      },
      ReportFormTypeComboStore: [],
      ReportFormConfigTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ReportFormTypeComboStore).then(result => {
        this.ReportFormTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.ReportFormConfigTypeComboStore).then(result => {
        this.ReportFormConfigTypeComboStore = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
