<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <!-- <vxe-button status="success" @click="pickUpEvent" v-if="menuAction.allowEdit">提货</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="shipmentState" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.shipmentState" placeholder="状态" clearable>
                  <vxe-option v-for="item in ShipmentStateComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="shipmentType" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.shipmentType" placeholder="类型" clearable>
                  <vxe-option v-for="item in ShipmentTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="clientID" size="mini" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarProductwarehouseMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="shipmentTypeText" title="发货类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="shipmentsNumber" title="发货单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="shipmentStateText" title="状态" sortable width="100"></vxe-table-column>
      <vxe-table-column field="logisticsCompany" title="快递公司" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" sortable width="100"></vxe-table-column>
      <vxe-table-column field="tel" title="电话" sortable width="100"></vxe-table-column>
      <vxe-table-column field="address" title="地址" sortable width="100"></vxe-table-column>

      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="180" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button status="success" @click="editEvent(row)" v-if="menuAction.allowEdit&&row.shipmentState===0">发货</vxe-button>
          <!-- <vxe-button status="success" @click="shipmentsEvent(row)" v-if="menuAction.allowEdit&&row.shipmentState===0">发货</vxe-button> -->
          <vxe-button status="warning" @click="shipmentsListShowEvent(row)" v-if="menuAction.allowEdit">清单</vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">

        <vxe-form-item title="联系人" field="contact" span="24" :item-render="{}"> <template v-slot>
            <el-input placeholder="请输入联系人" v-model="selectRow.contact" size="mini">
              <el-button slot="append" icon="el-icon-search" @click="personAddressShowEvent"></el-button>
            </el-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="电话" field="tel" span="12" :item-render="{}"> <template v-slot>
            <vxe-input v-model="selectRow.tel" placeholder="请输入地址" size="mini">></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="收货地址" field="address" span="12" :item-render="{}"> <template v-slot>
            <vxe-input v-model="selectRow.address" placeholder="请输入地址" size="mini"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="物流公司" field="logisticsCompany" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="快递单号" field="trackingNumber" span="24" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="shipmentsListShow" :title="'['+selectRow.shipmentsNumber+']发货清单'" width="60%" height="50%" resize destroy-on-close>
      <shipments-list :dataRow="selectRow" />
    </vxe-modal>
    <vxe-modal v-model="personAddress.Show" title="收货地址" width="70%" resize destroy-on-close>
      <vxe-grid border resizable height="530" :seq-config="{startIndex: (personAddress.tablePage.currentPage - 1) * personAddress.tablePage.pageSize}" :pager-config="personAddress.tablePage" :columns="personAddress.tableColumn" :data="personAddress.tableData" @page-change="handlePageChange" @cell-dblclick="personAddressTableCellClick"></vxe-grid>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
// import Shipments from './shipments.vue'
import ShipmentsList from './shipmentslist.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_shipments',
  mixins: [masterTableMixins],
  components: {
    // Shipments,
    ShipmentsList
  },
  data () {
    return {
      personAddress: {
        Show: false,
        tableData: [],
        tableColumn: [
          { type: 'seq', width: 60 },
          { field: 'contact', title: '联系人' },
          { field: 'tel', title: '电话' },
          { field: 'address', title: '地址' },
          { field: 'contactDesc', title: '联系人描述' },
          { field: 'fax', title: '传真' },
          { field: 'mobile', title: '座机' },
          { field: 'email', title: '邮件' },
          { field: 'state', title: '国家' },
          { field: 'province', title: '省' },
          { field: 'city', title: '市' },
          { field: 'county', title: '县' },
          { field: 'street', title: '街道' },
          { field: 'port', title: '港口' },
          { field: 'transport', title: '运送方式' },
          { field: 'remark', title: '备注' }
        ],
        tablePage: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          align: 'left',
          pageSizes: [10, 20, 50, 100, 200, 500],
          layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
          perfect: true,
          id: null,
          maxResultCount: 10,
          skipCount: 0
        }
      },
      shipmentsListShow: false,
      outputStorageFinised: '7',
      outputStorageFinisedRatio: 0,
      formData: {
        logisticsCompany: '',
        trackingNumber: '',
        remark: '',
        isActive: true
      },
      formRules: {
        logisticsCompany: [{ required: true, message: '请输入物流公司名称' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        trackingNumber: [{ required: true, message: '请输入快递单号' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }]
      },
      api: {
        get: '/mtm/wAR_Shipment/get',
        add: '/mtm/wAR_Shipment/adds',
        edit: '/mtm/wAR_Shipment/updates',
        delete: '/mtm/wAR_Shipment/deletes',
        PersonAddressGet: '/mtm/bAD_ClientAddress/get',
        // ProductStateComboStore: '/mtm/combo/ProductStateComboStore',
        OutputStorageFinisedRatio: '/mtm/war_productwarehouse/OutputStorageFinisedRatio',
        ShipmentStateComboStore: '/mtm/combo/ShipmentStateComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ShipmentTypeComboStore: '/mtm/combo/ShipmentTypeComboStore'
      },
      ShipmentStateComboStore: [],
      ShipmentTypeComboStore: [],
      clientComboStoreByQuery: []
      // ProductStateComboStore: []
    }
  },
  mounted () {
    this.timer = setInterval(this.getOutputStorageFinisedRatio, 1000 * 60 * 10)// 毫秒*秒*分钟
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  async created () {
    await this.getCombStore()
    this.getOutputStorageFinisedRatio()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ShipmentStateComboStore).then(result => {
        this.ShipmentStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ShipmentTypeComboStore).then(result => {
        this.ShipmentTypeComboStore = result
      })
    },
    // 出库完成率
    async getOutputStorageFinisedRatio () {
      await this.$api.ActionRequest(this.api.OutputStorageFinisedRatio, { day: this.outputStorageFinised }).then(result => {
        this.outputStorageFinisedRatio = result.outputStorageFinisedRatio
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },

    shipmentsListShowEvent (row) {
      this.selectRow = row
      this.shipmentsListShow = true
    },
    async pickUpEvent () {
      var data = this.$refs.master_table.getCheckboxRecords()
      if (data.length <= 0) {
        this.$notify.error({
          title: '错误',
          message: '请勾选要提货的明细订单'
        })
        return
      }
      var index = this.$utils.findIndexOf(data, (item) => item.productState === 2)
      if (index < 0) {
        this.$notify.error({
          title: '错误',
          message: '只有入库完成的才能安排提货'
        })
        return
      }
      var rows = cloneDeep(data)
      await rows.forEach(async item => {
        item.productState = 9
      })
      await this.$api.ActionRequest(this.api.edit, rows).then(result => {
        this.$notify.success({
          title: '成功',
          message: '提货成功'
        })
        this.loadData()
        this.$emit('reload')
      })
    },
    personAddressShowEvent () {
      if (this.selectRow.clientID === null || this.selectRow.clientID === '') {
        this.$message({ type: 'error', message: '没有绑定客户,无法查询地址' })
        return
      }
      this.personAddress.Show = true
      this.personAddressGet()
    },
    personAddressGet () {
      this.personAddress.tablePage.id = this.selectRow.clientID
      this.$api.ActionRequest(this.api.PersonAddressGet, this.personAddress.tablePage).then(result => {
        this.personAddress.tableData = result.items
        this.personAddress.total = result.totalCount
      })
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.personAddress.tablePage.currentPage = currentPage
      this.personAddress.tablePage.pageSize = pageSize
      this.personAddress.tablePage.maxResultCount = pageSize
      this.personAddress.tablePage.skipCount = (currentPage - 1) * pageSize
      this.personAddressGet()
    },
    personAddressTableCellClick ({ row }) {
      this.selectRow.contact = row.contact
      this.selectRow.tel = row.tel
      this.selectRow.address = row.address
      this.personAddress.Show = false
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
