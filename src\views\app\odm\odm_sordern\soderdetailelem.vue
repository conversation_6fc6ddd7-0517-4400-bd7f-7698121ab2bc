<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h3>款式明细<span style="padding-left: 30px;color:red;    font-size: 20px;">订单号{{ sorderStore.code }}</span></h3>
    </div>
    <div class="sorderdeatilelem" v-loading="EditState" element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)">
      <div v-loading="SorderDetailElemDataLoading" element-loading-text="款式明细加载中" element-loading-spinner="el-icon-loading">
        <el-card class="box-card">
          <!-- 图片 -->
          <sorder-detail-elem-image id="id_sorderdetailelemimage" :SorderDetailModel="SorderDetailModel" :SorderDetailElemData="SorderDetailElemData" :sorderStore="sorderStore" @ModelElemChange="ModelElemChange" @itemDataChange="itemDataChange" :isPlus="isPlus" :isPlanActive="isPlanActive" :DetailElemViewData="DetailElemViewData" />
        </el-card>
        <el-row class="elemswitch">
          <vxe-switch v-model="elemBasics" open-label="基础信息" close-label="基础信息" class="elemBasicsactive"></vxe-switch>
          <template v-if="info.userType!==2">
            <vxe-switch v-model="isPlanActive" open-label="配色" close-label="配色" class="isPlanActive"></vxe-switch>
          </template>
          <vxe-switch v-model="isPlus" open-label="深定制" close-label="轻定制" class="isPlusactive"></vxe-switch>
          <!-- <vxe-switch v-model="kuanshiactive" open-label="款式" close-label="款式" class="kuanshiactive"></vxe-switch> -->
          <!-- <vxe-switch v-model="gongyiactive" open-label="工艺" close-label="工艺" class="gongyiactive"></vxe-switch> -->
          <!-- <vxe-switch v-model="fuliaoactive" open-label="辅料" close-label="辅料" class="fuliaoactive"></vxe-switch> -->
        </el-row>
        <el-row class="isCheckedElem">
          <el-col :span="24">
            <el-button :type="SorderDetailModel.isCheckedElem?'success':'danger'" size="mini" @click="sorderDetailElemCheck">款式明细检验</el-button>
          </el-col>
          <el-col :span="24" v-if="SorderDetailModel.messageElem!==null&&SorderDetailModel.messageElem!==''">
            <el-alert :title="SorderDetailModel.messageElem" type="error">
            </el-alert>
          </el-col>
        </el-row>
        <template v-for="(baseitem,index) in DetailElemViewData">
          <div id='id_sorderdetailelem' :key="baseitem.modelElemBaseCode+index" class="modelElemBase">{{clientModelElemBaseCode(baseitem)}}</div>
          <template v-for="(item1,rowindex1) in SorderDetailElemData.filter(item=>{return item.modelElemBaseID===baseitem.modelElemBaseID})">
            <template v-if="elemBasicsShow(item1)">
              <template v-if="clientShow(item1)">
                <template v-if="isPlanShow(item1)">
                  <template v-if="isPlusShow(item1)">
                    <kuan-shi v-if="item1.modelElemTypeID==1" :sorderStore="sorderStore" v-show="kuanshiactive" :key="index+''+rowindex1" :elemItem="item1" :editAction="editProdModel" @ModelElemChange="ModelElemChange" />
                    <gong-yi v-if="item1.modelElemTypeID==2" :sorderStore="sorderStore" v-show="gongyiactive" :key="index+''+rowindex1" :elemItem="item1" :editAction="editProdModel" @ModelElemChange="ModelElemChange" />
                    <fu-liao v-if="item1.modelElemTypeID==3||item1.modelElemTypeID==4" :sorderStore="sorderStore" v-show="fuliaoactive" :key="index+''+rowindex1" :elemItem="item1" :editAction="editProdModel" @ModelElemChange="ModelElemChange" @itemDataChange="itemDataChange" />
                  </template>
                </template>
              </template>
            </template>
          </template>
        </template>
      </div>
    </div>
    <el-row :gutter="20">
      <el-col :span="4" :offset="20">
        <el-button type="success" size="mini" @click="sorderDetailElemSave()">保存</el-button>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import SorderDetailElemImage from './sorderdetailelemimage'
import KuanShi from './components/kuanshi'
import GongYi from './components/gongyi'
import FuLiao from './components/fuliao'
import { cloneDeep } from 'lodash'
import sorderEditState from './sordereditstate'
import { mapState } from 'vuex'

export default {
  name: 'OdmSorderDetailElem',
  mixins: [sorderEditState],
  components: {
    SorderDetailElemImage,
    KuanShi,
    GongYi,
    FuLiao
  },
  props: {
    SorderDetailModel: {
      type: Object,
      requited: true
    },
    sorderStore: {
      type: Object
    },
    GetDetailSizeData: {
      type: Function
    }
  },
  watch: {
    // 'SorderDetailModel': {
    //   deep: true,
    //   handler: function (newVal, oldVal) {
    //     if (newVal.modelID) {
    //       console.log(`款式明细---版型ID${newVal.modelID}发生变化`)
    //     }
    //   }
    // },

    // SorderDetailElemData: {
    //   deep: true,
    //   handler: function (newVal, oldVal) {
    //     if (newVal !== oldVal && newVal !== null) {
    //       // console.log(`款式明细---面料${newVal}`)
    //     }
    //   }
    // }
  },
  data () {
    return {
      api: {
        get: '/mtm/oDM_SorderDetailElem/get',
        rule: '/mtm/mOM_ModelElemRule/calculateElemRule',
        sorderDetailElemEdit: '/mtm/oDM_SorderDetailElem/modify',
        sorderDetailElemCheck: '/mtm/oDM_SorderDetailElem/CheckSorderDetailElem',
        itemElemItem: '/mtm/oDM_SorderDetailElem/ElemItemGet'
      },
      activeNames: ['1', '2', '3'],
      ElemList: [
        { name: '款式1' }
      ],
      kuanshiactive: true,
      gongyiactive: true,
      fuliaoactive: true,
      elemBasics: false, // 基础信息显示和隐藏,
      isPlanActive: false, // 配色
      isPlus: false, // 深定制简定制

      SorderDetailElemDataLoading: false,
      editProdModel: true,
      DetailElemViewData: [],
      SorderDetailElemData: []
    }
  },

  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
    // DetailElemViewData: {
    //   get() {
    //     if (this.SorderDetailElemData.length > 0) {
    //       var datalist = this.filterElems(this.SorderDetailElemData)
    //       return datalist
    //     }

    //   },
    //   set(val) {
    //     // this.filterElems(val)
    //   }
    // }
  },
  // watch: {
  //   SorderDetailElemData: {
  //     deep: true,
  //     handler: (newVal, oldVal) => {
  //       this.DetailElemViewData = this.filterElems(newVal)
  //     }
  //   }
  // },
  created () {
    if (this.SorderDetailModel.modelID) {
      this.get(this.SorderDetailModel.modelID, this.sorderStore.sorderTypeID, this.sorderStore.clientID, this.sorderStore.itemID, this.SorderDetailModel.id, this.SorderDetailModel.checkedModelElems)
    }
  },
  methods: {
    get (modelid, sorderTypeID = null, clientID = null, itemID = null, detailmodelId = null, checkedModelElems = []) {
      const loading = this.$loading({
        lock: true,
        text: '款式明细加载中...请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (modelid === null) {
        return
      }
      this.$api.ActionRequest(this.api.get, { modelId: modelid, sorderDetailModelID: detailmodelId, sorderTypeID: sorderTypeID, clientID: clientID, itemID: itemID, checkedModelElems: checkedModelElems }).then(result => {
        this.SorderDetailElemDataLoading = true
        // console.log('款式明细加载')
        this.SorderDetailElemData = result
        this.DetailElemViewData = this.filterElems(this.SorderDetailElemData)
        result.forEach(element => {
          if (this.SorderDetailModel && this.SorderDetailModel.id) {
            element.sorderDetailModelID = this.SorderDetailModel.id
          }
          this.activeNames.push(element.modelElemBaseCode)
        })
        this.SorderDetailElemDataLoading = false
        loading.close()
      }).then(() => {
        loading.close()
      })
    },
    // 内外账户
    clientShow (item) {
      if (this.info.userType === 1 || this.info.userType === 0) {
        return true // 管理员账号以及内部账户
      } else {
        // 外部账户
        return item.isClientShow
      }
    },
    // 深定制简定制
    isPlusShow (item) {
      if (!this.isPlus) {
        if (item.isPlus) {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },
    // 基础款式明细显示隐藏
    elemBasicsShow (item) {
      if (!item.isCustomerShow && !this.elemBasics) {
        return false
      } else {
        return true
      }
    },
    // 配色选项
    isPlanShow (item) {
      if (!this.isPlanActive) {
        return true
      } else {
        return item.isPlanShow
      }
    },
    // 内外账户
    clientModelElemBaseCode (baseitem) {
      if (this.info.userType === 1 || this.info.userType === 0) {
        return baseitem.modelElemBaseCode + ':' + baseitem.modelElemBaseName + '[' + baseitem.modelElemList.length + ']'
      } else {
        // 外部账户
        var count = baseitem.modelElemList.filter(item => { return item.isClientShow }).length
        return count === 0 ? '' : baseitem.modelElemBaseCode + ':' + baseitem.modelElemBaseName + '[' + count + ']'
      }
    },
    filterElems (list) {
      var data = cloneDeep(list)
      var bases = data.reduce((newarry, oldarry) => {
        let index = -1
        newarry.some((item, i) => {
          if (item.modelElemBaseID === oldarry.modelElemBaseID) {
            index = i
            return true
          }
        })
        if (index > -1) {
          newarry[index].modelElemList.push(oldarry)
        } else {
          newarry.push({
            modelElemBaseID: oldarry.modelElemBaseID,
            modelElemBaseCode: oldarry.modelElemBaseCode,
            modelElemBaseName: oldarry.modelElemBaseName,
            modelElemList: [oldarry]
          })
        }
        return newarry
      }, [])
      return bases
      // this.DetailElemViewData=bases;
    },
    filterModelElems (item, typearr) {
      // 过滤款式类型：款式、工艺、辅料
      if (item.modelElemTypeID !== 4) {
        if (this.activeNames.indexOf(item.modelElemTypeID + '') < 0) {
          return false
        }
      }
      if (typearr !== undefined && typearr && typearr.length > 0) {
        var iselemtype = typearr.indexOf(item.modelElemTypeID)
        if (iselemtype < 0) {
          return false
        }
      }
      // 过滤客户端显示
      if (!item.isClientShow) {
        return false
      }
      return true
    },
    // 规格检验完毕
    async sorderSizeCheck (data) {
      if (data && data.length > 0) {
        var modelelemRules = []
        for await (var item of data) {
          // await this.SizeCheck(item)
          var elem = this.SorderDetailElemData.GetFirstElement('modelElemListID', item.modelElemListID)
          if (elem != null) {
            await this.getElemRule(elem).then(list => {
              modelelemRules.push.apply(modelelemRules, list)
            })
          }
        }
        if (modelelemRules.length > 0) {
          await this.SizeCheck(modelelemRules)
        }
      }
    },
    async SizeCheck (data) {
      await this.calculateElemRuleRequest(data)
    },
    async ModelElemChange ({ modelelemid, elemlist, modelelem }) {
      var elem = this.SorderDetailElemData.GetFirstElement('modelElemListID', elemlist.modelElemListID)
      if (modelelemid !== null && modelelemid !== '') {
        elem = Object.assign(elem, modelelem)
        elem.itemImageUrl = modelelem.itemImageUrl
      } else {
        elem.modelElemID = null
        elem.qty = null
        elem.input = null
        elem.itemID = null
        elem.itemImageUrl = null
        elem.isFee = false
        elem.price = null
      }

      await this.calculateElemRule(elem)
    },

    itemDataChange (data) {
      switch (data.Type) {
        case 'Input':
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemText = data.Item.label
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemID = data.Item.value
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).input = data.Input
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).qty = data.Qty
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).IsInput = false
          break
        case 'Item':
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemID = data.Item.value
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemText = data.Item.label
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).unit = data.Item.unit
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemWidth = data.Item.itemWidth
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).qty = data.Qty
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).input = ''
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemImageUrl = data.Item.imageUrl
          this.itemElemItem(data.ModelElemID, data.Item.value)
          break
        case 'ClearItem':
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).input = ''
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemID = null
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemText = null
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemWidth = null
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).unit = ''
          this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).itemImageUrl = null
          // this.SorderDetailElemData.GetFirstElement('modelElemID', data.ModelElemID).qty = ''
          break
        default:
          break
      }
    },
    // 辅料配色方案
    itemElemItem (modelElemID, itemID) {
      this.$api.ActionRequest(this.api.itemElemItem, { ModelElemID: modelElemID, sorderTypeID: this.sorderStore.sorderTypeID, clientID: this.sorderStore.clientID, itemID: itemID }).then(res => {
        if (res.length > 0) {
          res.forEach(item => {
            if (this.SorderDetailElemData.ElementExist('modelElemID', item.modelElemID)) {
              this.SorderDetailElemData.GetFirstElement('modelElemID', item.modelElemID).itemID = item.itemID
            }
          })
        }
      })
    },
    async getElemRule (elem) {
      var p = new Promise((resolve, reject) => {
        var rule = []
        if (elem.modelElemRules.length > 0) {
          var _this = this
          var detailSizeData = this.GetDetailSizeData()
          this.$utils.arrayEach(elem.modelElemRules, (item, key) => {
            var a = { ModelElemRuleID: item.modelElemRuleID, BomRuleType: item.bomRuleType, ModelElems: [], SizeColumns: [], otherElemRules: [] }
            this.$utils.arrayEach(item.modelElemListIDs, (elem, key1) => {
              var _elem = this.SorderDetailElemData.GetFirstElement('modelElemListID', elem)
              if (_elem) {
                if (item.bomRuleType === 5) {
                  a.otherElemRules.push({ itemID: _this.sorderStore.itemID, SorderType: _this.sorderStore.sorderTypeID })
                  a.ModelElems.push({ ModelElemListID: elem, ModelElemID: _elem.modelElemID, ItemID: _elem.itemID, ItemWidth: _elem.itemWidth, Qty: _elem.qty })
                } else {
                  a.ModelElems.push({ ModelElemListID: elem, ModelElemID: _elem.modelElemID, ItemID: _elem.itemID, ItemWidth: _elem.itemWidth, Qty: _elem.qty })
                }
              }
            })
            this.$utils.arrayEach(item.sizeElemListIDs, (elem, key1) => {
              var _detialSize = detailSizeData
              var size = _detialSize.GetFirstElement('sizeColumnID', elem)
              if (size) {
                a.SizeColumns.push({ SizeColumnID: elem, Finish: size.finish })
              }
            })
            rule.push(a)
          })
        }
        resolve(rule)
      })
      return p
    },
    async calculateElemRule (elem) {
      var _this = this
      await this.getElemRule(elem).then(async list => {
        Promise.all([_this.calculateElemRuleRequest()]).then(async function (data) {
          await _this.calculateElemRuleRequest(list)
        })
      })
    },
    calculateElemRuleRequest (list) {
      return new Promise((resolve, reject) => {
        if (!list || list.length === 0) { return resolve() }
        return this.$api.ActionRequest(this.api.rule, list).then(result => {
          // var mssg = [];
          result.forEach(async item => {
            this.setElem(item).then(msg => {
              this.notifyA(msg)
            })
          })
          // this.notifyA(mssg)
          return resolve()
        })
      })
    },
    // 执行第二,三,四 次计算
    async awitcalculateElemRule (item) {
      var time = this.$utils.random(10, 100)
      setTimeout(async () => {
        await this.calculateElemRule(item)
      }, time)
    },
    async setElem (elemList) {
      var res = new Promise((resolve, reject) => {
        if (!elemList) {
          return
        }
        var msg = ''
        console.log(elemList)
        var elem = this.SorderDetailElemData.GetFirstElement('modelElemListID', elemList.modelElemListID)
        if (elem) {
          // const oldelem = elem.modelElemCode
          if (elemList.type === 1 && elemList.modelElemID === null) {
            if (elem.modelElemID !== null) {
              elem.modelElemID = null
              elem.qty = null
              elem.itemID = null
              // console.log(`款式明细计算1.1:${elem.modelElemListName}:${elem.modelElemListCode}--新:空--旧:${oldelem}`)
              msg = `款式:${elem.modelElemListCode}:${elem.modelElemListName}明细被清空`
              // this.$notify({ title: '智能计算', type: 'warning', message: `款式:${elem.modelElemListName}:${elem.modelElemListCode}明细被清除`, position: 'bottom-right' });
              this.awitcalculateElemRule(elem)
            }
          } else {
            if (elemList.type === 1) {
              if (elem.modelElem.ElementExist('modelElemID', elemList.modelElemID)) {
                if (elem.modelElemID === elemList.modelElemID) {
                  return
                }
                elem.modelElemID = elemList.modelElemID
                var selectElem = elem.modelElem.GetFirstElement('modelElemID', elemList.modelElemID)
                if (selectElem !== null) {
                  elem.isInput = selectElem.isInput
                  elem.itemID = selectElem.itemID
                  elem.isInputItem = selectElem.isInputItem
                  elem.isItemAdd = selectElem.isItemAdd
                  elem.qty = selectElem.qty
                  elem.itemImageUrl = selectElem.itemImageUrl
                  elem.isItemImageAdd = selectElem.isItemImageAdd
                }
              }
              msg += `款式:${elem.modelElemListCode}:${elem.modelElemListName} 更换:<strong style="color:red">${selectElem.modelElemCode}:${selectElem.modelElemName}</strong>`
              // this.$notify({ title: '智能计算', type: 'warning', message: `款式:${elem.modelElemListName}:${elem.modelElemListCode} 更换:${selectElem.modelElemCode}`, position: 'bottom-right' });
              // console.log(`款式明细计算1.2:${elem.modelElemListName}:${elem.modelElemListCode}--新:${selectElem.modelElemCode}--旧:${oldelem}`)
              this.awitcalculateElemRule(elem)
            }
            if (elemList.type === 2) {
              if (elem.isInputItem || elem.itemID !== null) {
                elem.qty = elemList.qty
              }
            }
            if (elemList.type === 4 || elemList.type === 5) {
              if (elem.isInputItem) {
                elem.itemID = elemList.itemID
              }
            }
          }
        }
        return resolve(msg)
      })
      return res
    },
    notifyA (msg) {
      if (msg === '' || msg === null) {
        return
      }
      var time = this.$utils.random(10, 100)
      var _this = this
      setTimeout(function () {
        _this.$notify({ dangerouslyUseHTMLString: true, title: '智能计算', type: 'warning', message: msg, position: 'top-right', offset: 100 })
      }, time)
    },
    // 保存款式明细
    async sorderDetailElemSave () {
      if (this.EditState) {
        return this.EditState
      }
      this.SorderDetailElemDataLoading = true
      var list = cloneDeep(this.SorderDetailElemData.filter(item => { return item.modelElemID !== null }))
      list.forEach(item => {
        delete item.isInput
        delete item.isInputItem
        delete item.modelElemRules
        delete item.positionIDs
        delete item.modelElem
        delete item.inputRequired
        delete item.itemRequired
        delete item.modelElemRequired
        delete item.qtyRequired
      })
      return await this.$api.ActionRequest(this.api.sorderDetailElemEdit, list).then(async res => {
        this.SorderDetailElemDataLoading = false
        return true
      }).catch(() => {
        return false
      })
    },
    async sorderDetailElemCheck () {
      var b = await this.sorderDetailElemSave()
      if (b) {
        this.$nextTick(() => {
          this.$api.ActionRequest(this.api.sorderDetailElemCheck, [this.SorderDetailModel.id]).then(res => {
            var model = res.detailModels.GetFirstElement('modelID', this.SorderDetailModel.modelID)
            if (model) {
              this.isCheckedElem = model.isChecked
              this.messageElem = model.message
              this.SorderDetailModel.isCheckedElem = model.isChecked
              this.SorderDetailModel.messageElem = model.message
              this.$notify({
                title: model.isChecked ? '成功' : '失败',
                dangerouslyUseHTMLString: true,
                message: model.isChecked ? '检验通过' : model.message,
                type: model.isChecked ? 'success' : 'error'
              })
            }
          })
        })
      }
    }
  }

}
</script>

<style lang="scss">
.sorderdeatilelem {
  padding: 10px;
  // .elemswitch {
  //   position: fixed;
  //   top: 50%;
  //   right: 20px;
  //   z-index: 999;
  // }
  // .isCheckedElem {
  //   position: absolute;
  //   bottom: 0;
  //   right: 5%;
  //   z-index: 999;
  //   width: 200px;
  // }
  .elemIsReuired {
    border: 1px solid #f56c6c;
  }
  .kuanshiactive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #5bae11;
    }
  }
  .gongyiactive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #bf971f;
    }
  }
  .fuliaoactive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #0598e1;
    }
  }
  .elemBasicsactive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #6e42b1;
    }
  }
  .isPlanActive.vxe-switch.is--on {
    .vxe-switch--button {
      background-color: #b940ff;
    }
  }
  .modeleleminput input {
    width: 120px !important;
  }
  .modelElemBase {
    font-size: 13px;
    font-weight: 600;
  }
  // min-height: 700px;
  .kuanshispan {
    display: inline-block;
    width: 150px;
    color: #5bae11;
    font-size: 13px;
  }
  // .gongyispan .gongyispan.is-checked + .el-checkbox__label{
  //   color: #e6a23c;
  // }
  .kuanshigongyi {
    padding: 2px;
    min-width: 380px;
    display: inline-block;
    margin-left: 20px;
    // font-size: 10px;
    input {
      width: 250px;
    }
  }
  .fuliao {
    padding: 2px;
    display: inline-block;
    margin-left: 20px;
    // font-size: 10px;
    input {
      width: 250px;
    }
  }
  .gongyispan {
    width: 150px;
    display: inline-block;
    color: #bf971f;
    font-size: 13px;
  }
  .fuliaospan {
    width: 150px;
    display: inline-block;
    font-size: 13px;
    color: #0598e1;
  }

  // .checkbtnclass {
  //   position: fixed;
  //   bottom: 5%;
  //   left: 88%;
  //   z-index: 999;
  // }
  .el-popover {
    opacity: 0.98;
  }

  // .testdiv {
  //   height: 500px;
  //   overflow-y: hidden;
  //   overflow-x: hidden;
  //   /* height: 500px; border: 1px solid #444; margin: 10px auto; padding: 0; overflow: hidden; */
  // }
  // .modelelem-Style {
  //   background: #f2dede;
  // }
  // .modelelem-Technology {
  //   background: oldlace;
  // }
  // .modelelem-Material {
  //   background: #f0f9eb;
  // }
  // .modelelem-Fabric {
  //   background: #9acd32;
  // }
  .item {
    margin-top: 0px;
  }
  .elemlistcode {
    display: inline-block;
    font-weight: bold;
    width: 150px;
  }

  .el-collapse-item__content {
    padding-bottom: 5px;
  }
  // .kuanshigongyi .el-input--mini .el-input__inner {
  //   width: 300px;
  //   height: 23px;
  // }

  // .fuliao .el-select.itemclass .el-input--mini .el-input__inner {
  //   width: 300px;
  //   height: 23px;
  // }

  // .fuliao .el-input--mini .el-input__inner {
  //   width: 300px;
  //   height: 23px;
  // }

  // .el-collapse-item__arrow {
  //   line-height: 30px;
  // }
  // .el-collapse-item__header {
  //   height: 30px;
  //   line-height: 30px;
  // }
  // .el-autocomplete-suggestion .el-popper {
  //   width: 30em;
  // }

  // .el-card__body {
  //   padding: 5px 5px 5px 5px;
  // }

  /**divtable**/
  .div-table {
    width: 100%;
    margin: 0.5rem auto 0;
  }
  .table-tr {
    overflow: hidden;
    /* border: 1px solid #bfbfbf; */
    border-top: 0;
    width: 700px;
  }
  .table-title {
    width: calc(100% - 17px);
    border-top: 1px solid #bfbfbf;
  }
  .v-table-title-cell > .table-title {
    border-top: 0;
  }
  .table-body {
    width: 100%;
    /* max-height: 5rem; */
    min-height: 1.5rem;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  .table-th,
  .table-td {
    /* text-align: center; */
    float: left;
    /* border-right: 1px solid #bfbfbf; */
    margin-right: -1px; /* 抵消右边框宽度 */
    min-height: 0.3rem; /* 防止内容为空时缺失 */
    line-height: 0.3rem;
    padding-bottom: 999px;
    margin-bottom: -999px;
    overflow: hidden;
  }
  .table-th span,
  .table-td span {
    /* display: block; */
    padding: 0 0.05rem;
    font-size: 13px;
  }
  // .table-td1 {
  //   width: 360px;
  // }
  // .table-td2 {
  //   width: 210px;
  // }
  // .table-td3 {
  //   width: 60px;
  // }
  // .table-td4 {
  //   width: 60px;
  // }
  // .table-td5 {
  //   width: 60px;
  // }
  // .delete-rows {
  //   background: #909399;
  // }
  // .model-select-content .el-form-item__content {
  //   width: 100%;
  // }
  // .text-align-left {
  //   text-align: left;
  // }
  // .text-align-right {
  //   text-align: right;
  // }
  // .text-italic {
  //   font-style: italic;
  // }

  // .teti {
  //   display: inline-block;
  //   margin: 5px 0 5px 20px;
  //   font-size: 8px;
  // }
  // .teti .el-input--mini .el-input__inner {
  //   width: 200px;
  //   height: 23px;
  // }
  // .bodylistcode {
  //   color: #2d698b;
  //   display: inline-block;
  //   font-weight: bold;
  //   width: 150px;
  // }
  // .text-vertical-align {
  //   vertical-align: middle !important;
  // }
}
</style>
