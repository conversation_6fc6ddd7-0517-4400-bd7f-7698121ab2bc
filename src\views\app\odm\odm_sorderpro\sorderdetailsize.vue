<template>
  <el-card shadow="always">
    <div class="sorderDetailSize" v-loading="EditState" element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)">
      <el-alert v-if="SorderDetailModel && SorderDetailModel.message != null && SorderDetailModel.message != ''" :title="SorderDetailModel.message" type="warning">
      </el-alert>
      <vxe-form>
        <vxe-form-item title="量体类型" field="sorderSizeTypeID" :item-render="{}"><template #default>
            <vxe-select v-model="SorderDetailModel.sorderSizeTypeID" placeholder="量体类型" @change="sorderSizeTypeChange">
              <vxe-option v-if="SorderDetailModel.isRuleSize" value="1" label="量体规格" disabled></vxe-option>
              <vxe-option v-if="SorderDetailModel.isRuleSize" value="2" label="成衣规格"></vxe-option>
              <!-- <vxe-option v-if="SorderDetailModel.isRuleSize" value="3" label="标准规格(按成衣算法)"></vxe-option> -->
              <vxe-option value="4" label="(旧)标准规格(无算法)"></vxe-option>
              <vxe-option value="5" label="标准规格(无算法)"></vxe-option>
            </vxe-select>
          </template></vxe-form-item>

        <vxe-form-item title="号型" field="SizeID1" :item-render="{}">
          <template #default>
            <el-select v-if="SorderDetailModel.sorderSizeTypeID !== '2' && SorderDetailModel.sorderSizeTypeID !== '1'" v-model="sizeQuery" filterable remote reserve-keyword placeholder="输入号型" :remote-method="sizeMethod" @change="sizeChange" clearable @clear="sizeClear" size="mini" class="sizeID1">
              <el-option v-for="item in sizeTableData" :key="item.id" :label="item.code" :value="item.id">
              </el-option>
            </el-select>
            <el-tag type="success">匹配号型{{ sizeIDText }}</el-tag>
          </template>
        </vxe-form-item>

        <vxe-form-item :item-render="{}"><template #default>
            <vxe-button :status="SorderDetailModel.isChecked ? 'success' : 'danger'" :loading="checkSizeLoding" content="规格检验" size='mini' @click="checkSize"></vxe-button>
            <vxe-button status="warning" content="查看规格单" size='mini' @click="selectSizeClick"></vxe-button>
          </template></vxe-form-item>
      </vxe-form>
      <finish v-if="SorderDetailModel.sorderSizeTypeID == '2'" ref="sorderDetialSizeTable" :SorderDetailModel="SorderDetailModel" :DetailSizeData="DetailSizeData" :sizeTableLoding="sizeTableLoding" />
      <measure v-if="SorderDetailModel.sorderSizeTypeID == '1'" ref="sorderDetialSizeTable" :SorderDetailModel="SorderDetailModel" :DetailSizeData="DetailSizeData" :sizeTableLoding="sizeTableLoding" />
      <new-standard-no v-if="SorderDetailModel.sorderSizeTypeID == '5'" ref="sorderDetialSizeTable" :SorderDetailModel="SorderDetailModel" :DetailSizeData="DetailSizeData" :sizeTableLoding="sizeTableLoding" />
      <standard v-if="SorderDetailModel.sorderSizeTypeID == '3'" ref="sorderDetialSizeTable" :SorderDetailModel="SorderDetailModel" :DetailSizeData="DetailSizeData" :sizeTableLoding="sizeTableLoding" />
      <standard-no v-if="SorderDetailModel.sorderSizeTypeID == '4'" ref="sorderDetialSizeTable" :SorderDetailModel="SorderDetailModel" :DetailSizeData="DetailSizeData" :sizeTableLoding="sizeTableLoding" />
      <vxe-modal v-model="selectSize" title="规格单查询" width="60%" height="50%" resize remember show-zoom>
        <template v-slot>
          <vxe-grid id="selectSizeListModal" border resizable height="auto" :loading="selectSizeLoading" :columns="(isGroupUp(SorderDetailModel.groupID))? sizeTableColumn1 : sizeTableColumn2" :toolbar="{ slots: { buttons: 'toolbar_buttons' } }" :data="sizeTableData" :seq-config="{ startIndex: (sizeListTablePage.currentPage - 1) * sizeListTablePage.pageSize }" :pager-config="sizeListTablePage" @page-change="sizeHandlePageChange" :custom-config="{ storage: true }" @cell-dblclick="selectSizeDbClick">
            <template v-slot:toolbar_buttons>
              <vxe-toolbar perfect>
                <template v-slot:tools>
                  <vxe-form ref="xForm" :data="searchSize" @submit="sizeSearchEvent()" @reset="resetEvent">
                    <vxe-form-item field="text" :item-render="{}"><template #default>
                        <vxe-input v-model.trim="searchSize.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
                      </template></vxe-form-item>
                    <vxe-form-item :item-render="{}"><template #default>
                        <vxe-button type="submit" status="success">查询</vxe-button>
                        <vxe-button type="reset">重置</vxe-button>
                      </template>
                    </vxe-form-item>
                  </vxe-form>
                </template>
              </vxe-toolbar>
            </template>
          </vxe-grid>
        </template>
      </vxe-modal>
    </div>
  </el-card>
</template>

<script>
import XEUtils from 'xe-utils'
import config from '@/config.js'
import sorderEditState from './sordereditstate'
import { mapState } from 'vuex'
import Finish from './components/sorderdetailsizetable/finish'
import Measure from './components/sorderdetailsizetable/measure'
import NewStandardNo from './components/sorderdetailsizetable/newStandardNo'
import Standard from './components/sorderdetailsizetable/standard'
import StandardNo from './components/sorderdetailsizetable/standardNo'
export default {
  name: 'SorderDetailSize',
  mixins: [config, sorderEditState],
  components: {
    Finish,
    Measure,
    NewStandardNo,
    Standard,
    StandardNo
  },
  props: {
    SorderDetailModel: {
      type: Object,
      requited: true
    },
    height: {
      type: String
    },
    sorderStore: {
      type: Object,
      default: null
    },
    sorderdetailbodySaveEven: {
      type: Function,
      requited: true
    },
    getBodysData: {
      type: Function,
      requited: true
    },
    SorderDetailSizeChecked: {
      type: Function
    }
  },
  watch: {
    SorderDetailModel: {
      deep: true,
      handler: function (newVal, oldVal) {
        console.log('顾客数据发生变化')
        // console.log(newVal)
        if (newVal.originalModelID !== null || newVal.sorderDetailModelId !== null) {
          // this.get(newVal.originalModelID, newVal.sorderDetailModelId)
        }
        if (newVal.sizeID1 !== null) {
          // this.sizeQuery = newVal.sizeID1
          this.sizeIDText = newVal.sizeIDText
          this.searchSize.id = newVal.sizeID1
          this.searchSize.modelId = newVal.modelId
          // this.sizeFindList()
        }
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      api: {
        get: '/mtm/oDM_SorderDetailSize/get',
        getsize: '/mtm/oDM_SorderDetailSize/getSize',
        edit: '/mtm/oDM_SorderDetail/modifyPro',
        checkSize: '/mtm/oDM_SorderDetailSize/checkSizePro',
        sorderDetailSizeEdit: '/mtm/oDM_SorderDetailSize/modifypro',
        sorderDetailModelEdit: '/mtm/oDM_SorderDetailModel/ModifyDetailModel'
      },
      checkSizeLoding: false,
      sizeQuery: this.SorderDetailModel.sizeID1,
      sizeIDText: this.SorderDetailModel.sizeIDText,
      selectSize: false,
      selectSizeLoading: false,
      DetailSizeData: [],
      sizeTableColumn1: [
        { field: 'groupName', title: '类别', width: '50' },
        { field: 'sizeListCode', title: '规格单编码', width: '90' },
        { field: 'sizeListName', title: '规格单名称', width: '' },
        { field: 'code', title: '规格编码', width: '80' },
        { field: 'codeName', title: '规格名称', width: '80' },
        { field: 'height', title: '身高', width: '50' },
        { field: 'bust', title: '胸围', width: '50' },
        { field: 'middleWaist', title: '中腰', width: '50' },
        { field: 'jacketHipline', title: '上衣臀围', width: '50' },
        { field: 'shoulderWidth', title: '肩宽', width: '50' },
        { field: 'sleeveWidth', title: '袖肥', width: '50' },
        { field: 'halfSleeveWidth', title: '1/2袖肥', width: '50' },
        { field: 'cuff', title: '袖口', width: '50' },
        { field: 'halfCuff', title: '1/2袖口', width: '50' },
        { field: 'backWidth', title: '背宽', width: '50' },
        { field: 'collar', title: '衬衣领围', width: '50' },
        { field: 'frontLength', title: '前衣长', width: '60' },
        { field: 'backMiddleLength', title: '后中长', width: '60' },
        { field: 'sleeveLength', title: '袖长', width: '50' }
      ],
      sizeTableColumn2: [
        { field: 'groupName', title: '类别', width: '50' },
        { field: 'sizeListCode', title: '规格单编码', width: '90' },
        { field: 'sizeListName', title: '规格单名称', width: '' },
        { field: 'code', title: '规格编码', width: '90' },
        { field: 'codeName', title: '规格名称', width: '90' },
        { field: 'height', title: '身高', width: '50' },
        { field: 'netTrouserLong', title: '净裤长', width: '60' },
        { field: 'waist', title: '腰围', width: '50' },
        { field: 'hipline', title: '臀围', width: '50' },
        { field: 'smallCrossCrotch', title: '腿围（裆下5CM', width: '80' },
        { field: 'crossCrotch', title: '腿围/横档', width: '50' },
        { field: 'halfCrossCrotch', title: '1/2横档', width: '50' },
        { field: 'kneeGirth', title: '膝围', width: '50' },
        { field: 'halfKneeGirth', title: '1/2膝围', width: '50' },
        { field: 'frontWaveLength', title: '前浪长', width: '50' },
        { field: 'backWaveLength', title: '后浪长', width: '50' },
        { field: 'trouserBottom', title: '脚口', width: '50' },
        { field: 'halfTrouserBottom', title: '1/2脚口', width: '50' },
        { field: 'throughCrotch', title: '通裆', width: '50' },
        { field: 'standCrotch', title: '立裆', width: '50' }
      ],
      sizeTableData: [],
      searchSize: {
        id: null,
        text: null,
        // modelBaseID: null,
        modelId: this.SorderDetailModel.modelId,
        maxResultCount: '10',
        // genderID: null,
        currentPage: null,
        skipCount: null
      },
      sizeListTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        align: 'right',
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
        perfect: true
      },
      sizeTableLoding: false

    }
  },
  created () {

  },
  methods: {
    clientPersonSizeChange () {
      if (this.SorderDetailModel.originalModelID !== null || this.SorderDetailModel.sorderDetailModelId !== null) {
        this.get(this.SorderDetailModel.originalModelID, this.SorderDetailModel.sorderDetailModelId)
      }
      if (this.SorderDetailModel.sizeID1 == null) {
        this.sizeQuery = null
        this.searchSize.id = null
      }
      if (this.SorderDetailModel.sizeIDText == null) {
        this.sizeIDText = null
      }
      this.searchSize.id = this.SorderDetailModel.sizeID1
      this.searchSize.text = null
      this.sizeFindList().then(() => {
        this.sizeQuery = this.SorderDetailModel.sizeID1
      })
    },
    DetailModelReloadEvent () {
      this.$emit('DetailModelReloadEvent', { data: this.SorderDetailModel })
    },
    get (modelId, detailmodelid = null) {
      return new Promise((resolve, reject) => {
        this.sizeTableLoding = true
        this.$api.ActionRequest(this.api.get, { modelId: modelId, sorderDetailModelID: detailmodelid || this.SorderDetailModel.id, sizeID1: this.SorderDetailModel.sizeID1 || this.SorderDetailModel.sizeID }).then(result => {
          this.sizeTableLoding = false
          this.DetailSizeData = result
          return resolve(true)
        }).catch(() => {
          this.sizeTableLoding = false
          return resolve(false)
        })
      })
    },
    // 规格算法切换
    async sorderSizeTypeChange () {
      this.SorderDetailModel.sizeID1 = null
      this.SorderDetailModel.sizeID = null
      this.SorderDetailModel.isChecked = false
      this.sizeIDText = null
      this.sizeQuery = null
      this.sorderDetailModelSave(this.SorderDetailModel).then(result => {
        this.DetailModelReloadEvent()
      })
      await this.setSorderDetailSizes(null)
    },
    // 规格单选择
    selectSizeClick () {
      this.searchSize.text = null
      this.searchSize.id = null
      this.selectSize = !this.selectSize
      this.sizeFindList()
    },
    async selectSizeDbClick ({ row }) {
      if (this.SorderDetailModel.sorderSizeTypeID !== '2') {
        this.sizeQuery = row.id
        this.SorderDetailModel.sizeID1 = row.id
        this.SorderDetailModel.sizeID = row.id
        this.SorderDetailModel.sizeIDText = row.code
        this.SorderDetailModel.message = null
        this.SorderDetailModel.isChecked = false
        this.selectSize = !this.selectSize
        await this.setSorderDetailSizes(row)
      }
    },
    // 选择规格后配置订单规格数据
    async setSorderDetailSizes (row) {
      this.sizeTableLoding = true
      if (row != null) {
        for (var a in row) {
          // 修长换成左袖长右袖长
          if (a === 'sleeveLength') {
            this.setSorderDetailSizesData('LeftSleeveLength', { standard1: row[a], finish: row[a], fix1: 0 })
            this.setSorderDetailSizesData('RightSleeveLength', { standard1: row[a], finish: row[a], fix1: 0 })
          } else if (a === 'netTrouserLong') { // 裤长  左裤长  右裤长
            this.setSorderDetailSizesData('LeftTrouserLong', { standard1: row[a], finish: row[a], fix1: 0 })
            this.setSorderDetailSizesData('RightTrouserLong', { standard1: row[a], finish: row[a], fix1: 0 })
          } else {
            this.setSorderDetailSizesData(a, { standard1: row[a], finish: row[a], fix1: 0 })
          }
        }
      } else {
        await this.setSorderDetailSizesData(null, { standard1: 0, finish: 0, fix1: 0, standard: 0, isManual: false })
      }
      this.sizeTableLoding = false
    },
    inputFocus (name, row) {
      var num = this.$utils.toNumber(row[name])
      if (num === 0) {
        row[name] = null
      }
    },
    async setSorderDetailSizesData (name, data) {
      this.DetailSizeData.forEach(element => {
        element.fixVariant = null
        // console.log(element.bodyValue)
        if (name == null) {
          element = Object.assign(element, data)
        } else {
          if (element.sizeColumnCode.toLowerCase() === name.toLowerCase()) {
            element = Object.assign(element, data)
          }
        }
      })
    },
    async sizeHandlePageChange ({ currentPage, pageSize }) {
      this.sizeListTablePage.currentPage = currentPage
      this.sizeListTablePage.pageSize = pageSize
      await this.sizeFindList()
    },
    sizeFindList () {
      return new Promise((resolve, reject) => {
        if (this.SorderDetailModel.modelId === null) {
          resolve(true)
          return
        }
        this.searchSize.modelId = this.SorderDetailModel.modelId
        this.selectSizeLoading = true
        this.searchSize.maxResultCount = this.sizeListTablePage.pageSize
        this.searchSize.skipCount = (this.sizeListTablePage.currentPage - 1) * this.sizeListTablePage.pageSize
        return this.$api.ActionRequest(this.api.getsize, this.searchSize).then(result => {
          this.sizeTableData = result.items
          this.sizeListTablePage.total = result.totalCount
          this.selectSizeLoading = false
          resolve(true)
        }).catch(() => {
          this.selectSizeLoading = false
          resolve(false)
        })
      })
    },
    async resetEvent () {
      this.sizeListTablePage.currentPage = 1
      await this.sizeFindList()
    },
    async sizeSearchEvent () {
      this.sizeListTablePage.currentPage = 1
      await this.sizeFindList()
    },
    async sizeMethod (query) {
      this.searchSize.text = query
      this.searchSize.id = null
      this.sizeListTablePage.currentPage = 1
      await this.sizeFindList()

      // this.$refs.xDown1.showPanel()
    },
    // 规格号型切换
    async sizeChange (val) {
      this.SorderDetailModel.isChecked = false
      this.SorderDetailModel.message = null
      if (val !== '') {
        // this.sizeQuery = val
        var size = this.sizeTableData.GetFirstElement('id', val)
        this.SorderDetailModel.sizeID1 = val
        this.SorderDetailModel.sizeIDText = size.code
        // this.sizeQuery = val

        await this.setSorderDetailSizes(size)
      } else {
        this.sizeQuery = null

        // this.SorderDetailModel.fixVariant = null
        // this.SorderDetailModel.standard = null
        await this.setSorderDetailSizes(null)
      }

      if (this.SorderDetailModel.id) {
        this.sorderDetailModelSave(this.SorderDetailModel).then(res => {
          this.DetailModelReloadEvent()
        })
      }
    },
    async sizeClear () {
      this.sizeQuery = null
      this.SorderDetailModel.isChecked = false
      this.SorderDetailModel.sizeID1 = null
      this.SorderDetailModel.sizeID = null
      this.SorderDetailModel.message = null
      this.sizeIDText = null
      // this.SorderDetailModel.fixVariant = null
      // this.SorderDetailModel.standard = null
      await this.setSorderDetailSizes(null)
    },
    fix1Blur (row) {
      if (row.fix1 === '' || row.fix1 === null) {
        row.finish = row.standard1
      } else {
        if (parseFloat(row.fix1) < row.fix1Min) {
          row.fix1 = row.fix1Min
        }
        if (parseFloat(row.fix1) > row.fix1Max) {
          row.fix1 = row.fix1Max
        }
        var count = row.standard1 == null ? 0 : parseFloat(row.standard1)
        row.finish = count + parseFloat(row.fix1)
      }
    },
    // 规格检验
    async checkSize () {
      if (this.SorderDetailModel.sorderSizeTypeID !== '2' && this.SorderDetailModel.sorderSizeTypeID !== '1') {
        if (this.SorderDetailModel.sizeID1 === null || this.SorderDetailModel.sizeID1 === '') {
          this.$XModal.message({ message: '请输入号型', status: 'error' })
          return
        }
      }
      if (this.height === null || this.height === '') {
        this.$XModal.message({ message: '请输入身高', status: 'error' })
        return
      }
      var b = await this.sorderdetailbodySaveEven()
      if (!b) {
        this.$notify.error({
          title: '错误',
          message: '特体数据保存异常,检验失败'
        })
      }
      this.sorderDetailSizeSave(this.SorderDetailModel).then(res => {
        this.checkSizeLoding = true
        const detailModel = XEUtils.clone(this.SorderDetailModel)
        detailModel.sorderDetailSize = this.DetailSizeData
        detailModel.height = this.height
        detailModel.sorderDetailBody = this.getBodysData() || []
        this.$api.ActionRequest(this.api.checkSize, detailModel).then(result => {
          this.SorderDetailSizeChecked(this.SorderDetailModel)
          this.DetailModelReloadEvent()
          this.checkSizeLoding = false
          // this.get(this.SorderDetailModel.originalModelID, this.SorderDetailModel.sorderDetailModelId).then(res => {
          //   this.checkSizeLoding = false
          //   this.$notify({
          //     title: '成功',
          //     message: '检验通过',
          //     type: 'success'
          //   })
          // })
        }).catch(() => {
          this.checkSizeLoding = false
        })
      }).catch(() => {
        this.checkSizeLoding = false
      })
    },
    async sorderDetailSizeSave () {
      return new Promise((resolve, reject) => {
        if (this.EditState) {
          return resolve(this.EditState)
        }
        var list = this.DetailSizeData
        if (list.length === 0) {
          return resolve(true)
        }
        const $table = this.$refs.sorderDetialSizeTable?.$refs.sorderDetialSizeTable
        if ($table) {
          const updateRecords = $table.getUpdateRecords()
          if (updateRecords.length === 0) {
            return resolve(true)
          }
        }
        if (this.SorderDetailModel.id) {
          // var b = this.sorderDetailModelSave(this.SorderDetailModel).then(res => {
          //   this.DetailModelReloadEvent()
          // })
          // if (!b) {
          //   return resolve(false)
          // }
        }
        // console.log('规格保存')
        this.$api.ActionRequest(this.api.sorderDetailSizeEdit, list).then(async res => {
          return resolve(true)
        }).catch(() => {
          return resolve(false)
        })
      })
    },
    async sorderDetailModelSave (list) {
      return new Promise((resolve, reject) => {
        if (list.length === 0) {
          return resolve(true)
        }
        this.sizeTableLoding = true
        return this.$api.ActionRequest(this.api.edit, list).then(async res => {
          this.sizeTableLoding = false
          return resolve(true)
        }).catch(() => {
          this.sizeTableLoding = false
          return resolve(false)
        })
      })
    },
    // 编辑事件
    editClosedEvent ({ row, column }) {

    }

  }
}
</script>

<style lang="scss">
.sorderDetailSize {
  .isRequiredRow {
    background-color: #67c23a !important;
  }

  .sizeID1 {
    input {
      width: 100px !important;
    }
  }
}
</style>
