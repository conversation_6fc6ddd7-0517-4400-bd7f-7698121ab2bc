<template>
  <d2-container class="sorderlist">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <el-button type="success" size="mini" @click="exportDBSelectEvent" v-if="menuAction.allowPrint">大宝订单导出</el-button>
          <!-- <el-button type="success" size="mini" @click="exportSuitsupplyOrderSelectEvent" v-if="menuAction.allowPrint">大宝订单导出</el-button> -->
          <el-button type="warning" size="mini" @click="exportSorderDetailSizeSelectEvent" v-if="menuAction.allowPrint">量体规格表导出</el-button>
          <el-button type="success" size="mini" @click="tableExport" v-if="menuAction.allowPrint">选中导出</el-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom class="mtmtoolbar">
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="dates">
              <template #default>
                <el-date-picker v-model="searchForm.dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="stateIDs">
              <template #default="{ data }">
                <el-select v-model.trim="data.stateIDs" placeholder="节点" multiple collapse-tags clearable size="mini">
                  <el-option v-for="item in SorderStatusComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <template v-if="!(info.userType==2)">
              <vxe-form-item field="clientID">
                <template #default="{ data }">
                  <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                    <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>
            </template>
            <vxe-form-item field="personID">
              <template #default="{ data }">
                <el-select v-model.trim="data.personID" filterable placeholder="顾客" size="mini" remote reserve-keyword :remote-method="remoteMethod5" clearable>
                  <el-option v-for="item in ClientPersonComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemID">
              <template #default="{ data }">
                <el-select v-model.trim="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
                  <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="bookNo">
              <vxe-input v-model.trim="searchForm.BookNo" placeholder="手册号" clearable style="width:110px"></vxe-input>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称/快递单号" suffix-icon="fa fa-search" clearable style="width:130px"></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomSewbase_master_table' ref='master_table' @cell-click='tableCellClick' :checkbox-config="{highlight:false}" :stripe="false" :highlight-hover-row="false" :highlight-current-row="false" :highlight-current-column="false" :row-class-name="rowClassName" :loading="tableLoading" :height="TableHeight" :data="tableData" :expand-config="{accordion: true,iconOpen: 'fa fa-minus-square', iconClose: 'fa fa-plus-square'}">
      <!-- <vxe-table-column type="expand" width="35" class-name="expandclass">
        <template v-slot:content="{ row, rowIndex }">
          <detail-list :sorder='row' :rowIndex='rowIndex' />
        </template>
      </vxe-table-column> -->
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column title="操作" width="120" v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="el-icon-location-information" @click="showOrderState(row)"></vxe-button>
        </template>
      </vxe-table-column>
      <vxe-table-column field="statusText" title="订单状态" width="100"></vxe-table-column>
      <vxe-table-column field="code" title="订单号" width="100"></vxe-table-column>
      <vxe-table-column field="clientText" title="客户" width="100"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" width="100"> </vxe-table-column>
      <vxe-table-column field="tel" title="联系电话" width="100"></vxe-table-column>
      <vxe-table-column field="address" title="地址" width="100"></vxe-table-column>
      <vxe-table-column field="detailInfo" title="明细" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="sorderTypeText" title="业务类型" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="clientPerson" title="顾客" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="itemText" title="面料" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="itemConsumption" title="面料耗量" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="itemConsumptionL" title="里布耗量" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="finalTextureText" title="纹理" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="country" title="国家编码" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="bookNo" title="备案手册号" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="数量" show-overflo width="100"></vxe-table-column>
      <!-- <vxe-table-column field="halfFitting" title="半成品试衣" :formatter="formatBool" show-overflo width="100"></vxe-table-column> -->

      <vxe-table-column field="createBy" title="创单人" show-overflo width="100"></vxe-table-column>
      <vxe-table-column field="isUrgent" title="是否加急" width="100" sortable>
        <template v-slot="{ row }">
          <span v-if="row.isUrgent" style="color:red">加急</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="issueDate" title="下单日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="deliveryDate" title="期望交期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100" sortable> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" width="100"></vxe-table-column>
      <vxe-table-column field="checkOn" title="客服审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="technicianCheckDate" title="技术审核日期" :formatter="val=>formatDate(val,'yyyy-MM-dd')" width="100"></vxe-table-column>
      <vxe-table-column field="checkBy" title="审核人" width="100"></vxe-table-column>
      <vxe-table-column field="sorderFromText" title="订单来源" width="100"></vxe-table-column>
      <vxe-table-column field="shipmentText" title="快递信息" width="100"></vxe-table-column>
      <vxe-table-column field="mesProductionPlanStateText" title="MES状态" width="100"></vxe-table-column>
      <vxe-table-column field="mEsLastFinishedProductionSchedule" title="MES最新进度" width="100">
        <template v-slot="{ row }">
          <span v-if="row.mesSchedulesDto===null||row.mesSchedulesDto.length<=0">{{row.mEsLastFinishedProductionSchedule}}</span>
          <el-popover v-else placement="top-start" title="生产进度" width="700" trigger="hover">
            <vxe-table align="center" :data="row.mesSchedulesDto">
              <vxe-table-column field="groupName" title="类别" width="80px"></vxe-table-column>
              <vxe-table-column field="serialNumber" title="流水号" width="120px"></vxe-table-column>
              <vxe-table-column field="workSecationCodeName" title="工段" width="120px"></vxe-table-column>
              <vxe-table-column field="productionStationName" title="工位" width="120px"></vxe-table-column>
              <vxe-table-column field="stateText" title="状态" width="80px"></vxe-table-column>
              <vxe-table-column field="modifyOn" title="时间" :formatter="formatDate" width="135px"></vxe-table-column>
            </vxe-table>
            <span slot="reference">{{row.mEsLastFinishedProductionSchedule}}</span>
          </el-popover>

        </template>
      </vxe-table-column>
      <vxe-table-column field="mesAsyncState" title="MES同步状态" :formatter="formatBool" width="100"></vxe-table-column>

    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="DBExportTableShow" :title="'大宝出库单导出'" width="70%" height="60%" resize destroy-on-close :show="ExportTableShow">
      <db-export-table v-if="DBExportTableShow" :selectTable="DBSelectDataTable" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import Vue from 'vue'
import masterTableMixins from '@/mixins/master_table_mixins/index'
import pluginExport from '@d2-projects/vue-table-export'
import DbExportTable from './compontents/DBExportTable.vue'
import { mapState } from 'vuex'
Vue.use(pluginExport)
// import {groupBy } from "utils"
export default {
  name: 'odm_sorderlistexport',
  mixins: [masterTableMixins],
  components: {
    DbExportTable
  },
  data () {
    return {
      DBSelectDataTable: [],
      DBExportTableShow: false,
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        sequence: 9999
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      mtmpai: process.env.VUE_APP_API,
      api: {
        get: '/mtm/odm_sorder/ListExport',
        getCadStr: '/mtm/oDM_SorderCadLayout/GetCadStr',
        delete: '/mtm/odm_sorder/deletes',
        print: '/mtm/oDM_Sorder/sorderPrint',
        sorderCadlayout: '/mtm/oDM_SorderCadLayout/get',
        sendCad: '/mtm/oDM_SorderCadLayout/sendcad',
        deepClone: '/mtm/oDM_Sorder/deepClone',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        ClientPersonComboStoreByQuery: '/mtm/comboQuery/ClientPersonComboStoreByQuery',
        SorderStatusComboStore: '/mtm/combo/sorderStatusComboStore',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        mtmpai: process.env.VUE_APP_API,
        dbSorderDetailSize: '/fs/export/sorderdetailsizedb/excel',
        suitsupplyorderexcel: '/fs/export/suitsupplydb/excel'

      },
      detailForm: {
        sorderId: null

      },
      SorderStatusComboStore: [],
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      ClientPersonComboStoreByQuery: [],
      sorderStateDrawer: false,
      footerCompanyInfo: false,
      cadLayoutShow: false,
      cadLayoutList: [],
      cadTextShow: false,
      bomTableShow: false,
      Cad: {
        cadText: null,
        sorderNum: '',
        server: '',
        fileName: ''
      },
      searchForm: {
        stateIDs: []
      },
      salesBillShow: false,
      isAutoLoding: false

    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
    //   ...mapState('d2admin/page', [
    //     'state'
    //   ])
  },
  async created () {
    await this.getCombStore()
    this.loadData()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery, { isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { isNotG: true }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.SorderStatusComboStore).then(result => {
        this.SorderStatusComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
    },

    ExportTableShow () {
      this.DBSelectDataTable = []
    },
    exportDBSelectEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要导出的数据', status: 'error' })
        return
      }
      this.DBSelectDataTable = list
      this.DBExportTableShow = true
    },
    exportSorderDetailSizeSelectEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要导出的数据', status: 'error' })
        return
      }
      this.tableLoading = true
      var ids = list.map(a => { return a.id })
      var api = this.api.mtmpai.replace('/api/', '')
      var url = api + this.api.dbSorderDetailSize
      this.$api.ActionExcelRequest(url, { IDs: ids }).then(res => {
        this.tableLoading = false
        // console.log(res)
        const url = window.URL.createObjectURL(res) // 创建一个新的 URL 对象
        // 以下代码一句话解释，在页面上生成一个a标签并指定href为上面的url,然后模拟点击，以实现自动下载
        var a = document.createElement('a')
        document.body.appendChild(a)
        a.href = url
        a.download = '量体规格表.xlsx'
        a.click()
        window.URL.revokeObjectURL(url)
      })
    },
    // 大宝订单导出
    exportSuitsupplyOrderSelectEvent () {
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要导出的数据', status: 'error' })
        return
      }
      this.tableLoading = true
      var ids = list.map(a => { return a.id })
      var api = this.api.mtmpai.replace('/api/', '')
      var url = api + this.api.suitsupplyorderexcel
      this.$api.ActionExcelRequest(url, { IDs: ids }).then(res => {
        this.tableLoading = false
        const url = window.URL.createObjectURL(res) // 创建一个新的 URL 对象
        // 以下代码一句话解释，在页面上生成一个a标签并指定href为上面的url,然后模拟点击，以实现自动下载
        var a = document.createElement('a')
        document.body.appendChild(a)
        a.href = url
        a.download = '大宝订单.xlsx'
        a.click()
        window.URL.revokeObjectURL(url)
      })
    },
    // 导出
    tableExport () {
      var time = this.$utils.toDateString(new Date(), 'yyyyMMddHHmm')
      var list = this.$refs.master_table.getCheckboxRecords()
      this.$refs.master_table.exportData({ type: 'csv', filename: '订单列表' + time, data: list })
    },

    tableCellClick ({ column, row }) {
      var xtable = this.$refs.master_table
      if (column.type === 'checkbox') {
        // var checked=xtable.isCheckedByCheckboxRow(row)
        // if (checked) {
        //   xtable.toggleCheckboxRow(row)
        // } else {
        //   xtable.setCheckboxRow(row, true)
        // }

      } else {
        xtable.toggleCheckboxRow(row)
      }
      this.selectRow = row
    },

    rowClassName ({ row, rowIndex }) {
      var stateClass = ''
      switch (row.statusID) {
        case 0: // 待定 ClientUndetermined
          stateClass = 'sorderstate-client'
          break
        case 1:// 已确认Confirmed
          stateClass = 'sorderstate-confirmed' //  background-color: #0598e1;
          break

        case 20:// 客服锁定CLock
          stateClass = 'sorderstate-clock'
          break
        case 21:// 客服审核完成 //CChecked
          stateClass = 'sorderstate-cchecked'
          break
        case 22:// 客服驳回 CReject
          stateClass = 'sorderstate-customer'
          break
        case 30:// 技术锁定 //MLock
          stateClass = 'sorderstate-mlock'
          break
        case 31:// 技术审核完成 MChecked
          stateClass = 'sorderstate-MChecked'
          break
        case 32:// 技术驳回 MReject
          stateClass = 'sorderstate-technology'
          break
        case 40:// 计划下单Planed
          stateClass = 'sorderstate-planed'
          break
        case 41:// 计划驳回 PReject
          stateClass = 'sorderstate-preject'
          break
        case 50: // 完成 Finished
          stateClass = 'sorderstate-finished'
          break
        default:
          stateClass = ''
          break
      }
      return stateClass
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    remoteMethod5 (query) {
      this.$api.ActionRequest(this.api.ClientPersonComboStoreByQuery, { text: query }).then(result => {
        this.ClientPersonComboStoreByQuery = result
      })
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      console.log(to.params)
      if (to.params.stateIDs) {
        vm.searchForm.stateIDs = to.params.stateIDs
        to.params.stateIDs = null
      }
      if (to.params.text) {
        vm.searchForm.text = to.params.text
        to.params.text = null
      }
      if (to.params.refresh) {
        vm.loadData()
      }
    })
  }
}
</script>

<style lang="scss" >
.sorderlist {
  // .mtmtoolbar {
  //   overflow-x: auto;
  //   overflow-y: hidden;
  // }
  // .el-drawer {
  //   overflow-y: auto !important;
  // }
  .expandclass {
    background-color: #e6f7ff;
  }
  .sorderstate-client {
    background-color: #909399;
    color: cornsilk;
  }
  .sorderstate-confirmed {
    background-color: #0598e1;
    color: cornsilk;
  }
  //客服驳回
  .sorderstate-customer {
    background-color: #0da468;
    color: cornsilk;
  }
  //客服锁定CLock
  .sorderstate-clock {
    background-color: #0d97a4;
    color: cornsilk;
  }
  //客服审核完成 //CChecked
  .sorderstate-cchecked {
    background-color: #0da410;
    color: cornsilk;
  }
  //技术 技术驳回
  .sorderstate-technology {
    background-color: #e6a23c;
    color: cornsilk;
  }
  //技术 技术锁定
  .sorderstate-mlock {
    background-color: #a68934;
    color: cornsilk;
  }
  //技术 技术审核完成
  .sorderstate-MChecked {
    background-color: #a47e0d;
    color: cornsilk;
  }
  //计划下单Planed
  .sorderstate-planed {
    background-color: #ea6157;
    color: cornsilk;
  }
  //计划驳回 PReject
  .sorderstate-preject {
    background-color: #de3327;
    color: cornsilk;
  }
  //完成 Finished
  .sorderstate-finished {
    background-color: #0d6aa4;
    color: cornsilk;
  }
}
</style>
