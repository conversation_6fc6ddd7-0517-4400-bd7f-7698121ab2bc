<template>
  <d2-container class="userindex">
    <el-tabs v-model="activeName" type="card" style="height:100%">
      <el-tab-pane label="个人信息" name="userfino">
        <user-info />
      </el-tab-pane>
      <el-tab-pane label="表单设置" name="vxetableset">
        <vxe-table-set />
      </el-tab-pane>
      <el-tab-pane v-if="info.userType===2" label="顾客管理" name="person">
        <client-person />
      </el-tab-pane>
      <el-tab-pane v-if="info.userType===2" label="收货地址管理" name="address">
        <client-address />
      </el-tab-pane>
    </el-tabs>
  </d2-container>
</template>
<script>
// import { mapState, mapActions } from 'vuex'
// import { mapState } from 'vuex'
// import { Loading } from 'element-ui'
import UserInfo from './userinfo'
import VxeTableSet from './vxetableset'
import { mapState } from 'vuex'
import ClientPerson from './clientperson'
import ClientAddress from './clientaddress'
export default {
  name: 'userindex',
  components: {
    UserInfo,
    ClientPerson,
    ClientAddress,
    VxeTableSet
  },
  data () {
    return {
      activeName: 'userfino'
    }
  },
  computed: {
    ...mapState('d2admin/user', ['info'])
  },
  methods: {

  },
  created () {

  }
}
</script>

<style lang="scss">
.userindex {
  .el-tabs__content {
    height: 92%;
  }
}
</style>
