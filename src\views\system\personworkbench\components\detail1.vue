<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button status="warning" @click="updatePlusEvent">一键已读</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="workbenchGroup" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.workbenchGroup" placeholder="分类" filterable clearable size="mini" disabled>
                  <el-option v-for="item in WorkbenchGroupComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="workbenchType" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.workbenchType" placeholder="类型" filterable clearable size="mini">
                  <el-option v-for="item in WorkbenchTypeComboStore" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='SymWorkbenchdetailMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="workbenchGroupText" title="分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="workbenchTypeText" title="类型" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="receiverObjectText" title="接收对象" sortable width="100"></vxe-table-column>
      <vxe-table-column field="userName" title="用户" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="message" title="内容" sortable width="100"></vxe-table-column>
      <vxe-table-column field="readReceipt" title="已读" :formatter='formatBool' sortable width="100">
        <template v-slot="{ row }">
          <span v-if="row.readReceipt" style="color:green">是</span>
          <span v-else style="color:red">否</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="toWork" title="已处理" :formatter='formatBool' sortable width="100">
        <template v-slot="{ row }">
          <span v-if="row.toWork" style="color:green">是</span>
          <span v-else style="color:red">否</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center">
        <template v-slot="{ row }">
          <vxe-button status="success" v-if="!row.readReceipt" @click="readEvent(row)">已读</vxe-button>
          <vxe-button status="primary" v-if="!row.toWork" @click="goEvent(row)">去处理</vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { mapState } from 'vuex'
import { cloneDeep } from 'lodash'
export default {
  name: 'personWorkBench',
  mixins: [detailTableMixins],
  props: {
    Group: {
      required: true
    }
  },
  components: {
  },
  data () {
    return {
      searchForm: {
        workbenchGroup: this.Group
      },
      api: {
        get: '/mtm/sym_workbenchdetail/get',
        edit: '/mtm/sym_workbenchdetail/updates',
        WorkbenchGroupComboStore: '/mtm/combo/WorkbenchGroupComboStore',
        WorkbenchTypeComboStore: '/mtm/combo/WorkbenchTypeComboStore'
      },
      WorkbenchGroupComboStore: [],
      WorkbenchTypeComboStore: []
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  async created () {
    await this.getCombStore()
    this.loadData({ userID: this.info.userid }).then(({ data }) => {
      this.userCount()
    })
  },
  methods: {
    async searchEvent () {
      this.searchForm.currentPage = 1
      this.searchForm.skipCount = 0
      await this.loadData().then(({ data }) => {
        this.userCount()
      })
    },
    // 分页发生改变时会触发该事件
    pageChange ({ currentPage, pageSize }) {
      this.searchForm.maxResultCount = pageSize
      this.searchForm.skipCount = (currentPage - 1) * pageSize
      this.searchForm.currentPage = currentPage
      this.loadData().then(({ data }) => {
        this.userCount()
      })
    },
    // 数据加载
    async loadData (form = null) {
      return new Promise((resolve, reject) => {
        this.tableLoading = true
        if (form != null) {
          this.searchForm = Object.assign(this.searchForm, form)
        }
        this.$api.ActionRequest(this.api.get, this.searchForm).then(result => {
          this.tableData = result.items
          this.searchForm.totalCount = result.totalCount
          this.searchForm.userCount = result.userCount
          this.tableLoading = false
          resolve({ data: result.items })
        }).catch(() => {
          this.tableLoading = false
        })
        // this.tableLoading = false
      })
    },
    userCount () {
      var count = this.searchForm.userCount
      var type = 1
      if (this.Group === 1) {
        type = 1
      }
      if (this.Group === 2) {
        type = 2
      }
      if (this.Group === 10) {
        type = 3
      }
      if (this.Group === 12) {
        type = 4
      }
      //   this.$emit("cuserCount", { type: type, count: count })
      this.$emit('cuserCount', { type: type, count: count })
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.WorkbenchGroupComboStore).then(result => {
        this.WorkbenchGroupComboStore = result
      })
      await this.$api.ActionRequest(this.api.WorkbenchTypeComboStore).then(result => {
        this.WorkbenchTypeComboStore = result
      })
    },
    async readEvent (row) {
      var data = cloneDeep(row)
      data.readReceipt = true
      await this.readUpdate([data])
    },
    async updatePlusEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$notify.error({
          title: '错误',
          message: '请勾选要处理的数据'
        })
        return
      }
      var data = cloneDeep(rows)
      data = data.map(item => ({
        id: item.id,
        userID: item.userID,
        workbenchID: item.workbenchID,
        roleID: item.roleID,
        userid: item.userid,
        readReceipt: true,
        toWork: item.toWork,
        message: item.message,
        url: item.url
      }))
      await this.readUpdate(data)
    },
    async readUpdate (list) {
      await this.$api.ActionRequest(this.api.edit, list).then(result => {
        this.$message({
          message: '更新成功',
          type: 'success'
        })
        this.loadData({ userID: this.info.userid }).then(({ data }) => {
        })
      })
    },
    goEvent (row) {
      var params = JSON.parse(row.parameters)
      // console.log(params)
      if (params === null) {
        return
      }
      this.$message({
        message: '正在跳转请稍后.....',
        type: 'success'
      })
      var p = {
        name: row.url,
        params: JSON.parse(row.parameters)
      }
      setTimeout(() => {
        console.log(p)
        this.$router.push(p)
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
