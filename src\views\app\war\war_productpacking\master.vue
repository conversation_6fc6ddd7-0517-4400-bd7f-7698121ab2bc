<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button status="warning" @click="printEvent" v-if="menuAction.allowPrint">UPS汇总清单</vxe-button>
          <vxe-button status="success" @click="downLoadEvent" v-if="menuAction.allowPrint">运单批量下载</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="Dates" :item-render="{}"> <template #default>
                <el-date-picker v-model="searchForm.Dates" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" size="mini">
                </el-date-picker>
              </template>
            </vxe-form-item>
            <vxe-form-item field="shipmentState" :item-render="{}"> <template #default="{ data }">
                <vxe-select v-model="data.shipmentState" placeholder="状态" clearable>
                  <vxe-option v-for="item in ShipmentStateComboStore" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarProductwarehouseMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="shipmentsNumber" title="发货单号" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="shipmentStateText" title="状态" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sorderCount" title="衣服数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="logisticsCompany" title="快递公司" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingNumber" title="快递单号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="trackingShortNumber" title="短号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="contact" title="联系人" sortable width="100"></vxe-table-column>
      <vxe-table-column field="tel" title="电话" sortable width="100"></vxe-table-column>
      <vxe-table-column field="address" title="地址" sortable width="100"></vxe-table-column>
      <vxe-table-column field="documentID" title="UPS文档ID" sortable width="100" :visible="false"></vxe-table-column>

      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="200" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button status="warning" @click="ShipmentByUpsEvent(row)" v-if="menuAction.allowEdit&&row.shipmentState===0">发货</vxe-button>
          <vxe-button status="success" @click="editEvent(row)" v-if="menuAction.allowEdit">发货信息</vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title=" '编辑&保存'" width="800" height='800' resize destroy-on-close :loading="submitLoading" show-footer>
      <vxe-form :data="formData1" :rules="formRules" title-align="left" title-width="100" @submit="submitEvent" custom-layout>
        <div style="border: 1px dashed green;padding: 10px;">
          <div style="color: red;">收货人信息</div>
          <vxe-form-item title="国家" field="shipTo.toCountry" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="国家编码" field="shipTo.country" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="州省区" field="shipTo.stateProvince" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="州省区代码" field="shipTo.stateProvinceCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="城市" field="shipTo.city" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="城市简称" field="shipTo.cityCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="联系人" field="shipTo.contactName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="店铺名称" field="shipTo.name" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="电话" field="shipTo.phone" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="邮编" field="shipTo.postalCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="地址" field="shipTo.address" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="地址1" field="shipTo.address1" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="中文国家名称" field="shipTo.countryCN" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        </div>
        <div style="width:100%; border: 1px dashed #E6A23C;padding: 10px;">
          <div style="color: red;">清关公司</div>
          <vxe-form-item title="国家" field="customs.toCountry" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="国家编码" field="customs.country" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="州省区" field="customs.stateProvince" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="州省区代码" field="customs.stateProvinceCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="城市" field="customs.city" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="城市简称" field="customs.cityCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="联系人" field="customs.contactName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="公司名称" field="customs.name" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="电话" field="customs.phone" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="邮编" field="customs.postalCode" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="地址" field="customs.address" span="12" :item-render="{name: 'input'}"></vxe-form-item>
          <vxe-form-item title="地址1" field="customs.address1" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        </div>
        <div style="width:100%; border: 1px dashed #E6A23C;padding: 10px;">
          <div style="color: red;">UPS海关</div>
          <vxe-form-item title="国家" field="upscustoms.toCountry" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="国家编码" field="upscustoms.country" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="州省区" field="upscustoms.stateProvince" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="州省区代码" field="upscustoms.stateProvinceCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="城市" field="upscustoms.city" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="城市简称" field="upscustoms.cityCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="联系人" field="upscustoms.contactName" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="公司名称" field="upscustoms.name" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="电话" field="upscustoms.phone" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="邮编" field="upscustoms.postalCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="地址" field="upscustoms.address" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="地址1" field="upscustoms.address1" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="UPS账号" field="upscustoms.upsAccount" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
        </div>
        <div style="width:100%; border: 1px dashed #F56C6C;padding: 10px;">
          <div style="color: red;">运费支付</div>
          <vxe-form-item title="国家" field="payment.toCountry" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="国家编码" field="payment.country" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="州省区" field="payment.stateProvince" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="州省区代码" field="payment.stateProvinceCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="城市" field="payment.city" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="城市简称" field="payment.cityCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="联系人" field="payment.contactName" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="公司名称" field="payment.name" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="电话" field="payment.phone" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="邮编" field="payment.postalCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="地址" field="payment.address" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="地址1" field="payment.address1" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="UPS账号" field="payment.upsAccount" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
        </div>
        <div style="width:100%; border: 1px dashed #909399;padding: 10px;">
          <div style="color: red;">发货人</div>
          <vxe-form-item title="国家" field="shopfrom.toCountry" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="国家编码" field="shopfrom.country" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="州省区" field="shopfrom.stateProvince" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="州省区代码" field="shopfrom.stateProvinceCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="城市" field="shopfrom.city" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="城市简称" field="shopfrom.cityCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="联系人" field="shopfrom.contactName" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="公司名称" field="shopfrom.name" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="电话" field="shopfrom.phone" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="邮编" field="shopfrom.postalCode" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="地址" field="shopfrom.address" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="地址1" field="shopfrom.address1" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
          <vxe-form-item title="UPS账号" field="shopfrom.upsAccount" span="12" :item-render="{name: 'input'}" folding></vxe-form-item>
        </div>
        <div style="width:100%; border: 1px dashed #909399;padding: 10px;">
          <vxe-form-item align="center" span="24" :item-render="{}" collapse-node>
            <template #default>
              <vxe-button type="submit" status="primary">保存</vxe-button>
              <vxe-button type="reset">重置</vxe-button>
            </template>
          </vxe-form-item>
        </div>
      </vxe-form>
      <!-- <template #footer>
        1231
      </template> -->
    </vxe-modal>
    <vxe-modal v-model="showUpsInfo" :title=" 'UPS信息汇总'" width="80%" height="50%" resize destroy-on-close>
      <ups-info-sum v-if="showUpsInfo" :IDs="showUpsIDs" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import UpsInfoSum from './upsinfosum.vue'
import { Download } from '@/plugin/downLoadBase64/downLoad.js'
// import Shipments from './shipments.vue'
// import ShipmentsList from './shipmentslist.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_productpacking',
  mixins: [masterTableMixins],
  components: {
    UpsInfoSum
    // Shipments,
    // ShipmentsList
  },
  data () {
    return {
      download: new Download(),
      showUpsInfo: false,
      showUpsIDs: [],
      formData: {
        shipTo: { // 收货人
          shipmentID: null,
          shipmentInfoType: 1,
          address: null,
          address1: null,
          city: null,
          cityCode: null,
          clientShopID: null,
          contactName: null,
          country: null,
          email: null,
          id: null,
          name: null,
          phone: null,
          postalCode: null,
          stateProvince: null,
          stateProvinceCode: null,
          toCountry: null,
          upsAccountConifgID: null,
          upsAccount: null
        },
        shopfrom: { // 发货人
          shipmentID: null,
          shipmentInfoType: 2,
          address: null,
          address1: null,
          city: null,
          cityCode: null,
          clientShopID: null,
          contactName: null,
          country: null,
          email: null,
          id: null,
          name: null,
          phone: null,
          postalCode: null,
          stateProvince: null,
          stateProvinceCode: null,
          toCountry: null,
          upsAccountConifgID: null,
          upsAccount: null
        },
        payment: { // 运费支付
          shipmentID: null,
          shipmentInfoType: 3,
          address: null,
          address1: null,
          city: null,
          cityCode: null,
          clientShopID: null,
          contactName: null,
          country: null,
          email: null,
          id: null,
          name: null,
          phone: null,
          postalCode: null,
          stateProvince: null,
          stateProvinceCode: null,
          toCountry: null,
          upsAccountConifgID: null,
          upsAccount: null
        },
        customs: { // 清关海关
          shipmentID: null,
          shipmentInfoType: 4,
          address: null,
          address1: null,
          city: null,
          cityCode: null,
          clientShopID: null,
          contactName: null,
          country: null,
          email: null,
          id: null,
          name: null,
          phone: null,
          postalCode: null,
          stateProvince: null,
          stateProvinceCode: null,
          toCountry: null,
          upsAccountConifgID: null,
          upsAccount: null
        },
        upscustoms: { // UPS海关
          shipmentID: null,
          shipmentInfoType: 5,
          address: null,
          address1: null,
          city: null,
          cityCode: null,
          clientShopID: null,
          contactName: null,
          country: null,
          email: null,
          id: null,
          name: null,
          phone: null,
          postalCode: null,
          stateProvince: null,
          stateProvinceCode: null,
          toCountry: null,
          upsAccountConifgID: null,
          upsAccount: null
        }
      },
      formData1: null,
      searchForm: {
        shipmentType: 5
      },

      formRules: {
        logisticsCompany: [{ required: true, message: '请输入物流公司名称' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        trackingNumber: [{ required: true, message: '请输入快递单号' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }]
      },
      api: {
        get: '/mtm/wAR_Shipment/get',
        getByShipment: '/mtm/wAR_Shipmentinfo/GetByShipment',
        // add: '/mtm/wAR_Shipment/adds',
        edit: '/mtm/wAR_Shipmentinfo/updates',
        delete: '/mtm/wAR_Shipment/deletes',
        ShipmentByUps: '/mtm/wAR_Shipment/ShipmentByUps',
        UploadPaperlessDocument: '/mtm/wAR_Shipment/UploadPaperlessDocument',
        ShipmentStateComboStore: '/mtm/combo/ShipmentStateComboStore',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery',
        getlist: '/mtm/wAR_ProductPacking/getList'
      },
      ShipmentStateComboStore: [],
      clientComboStoreByQuery: []
    }
  },
  mounted () {

  },
  beforeDestroy () {

  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ShipmentStateComboStore).then(result => {
        this.ShipmentStateComboStore = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    downLoadEvent () {
      const loading = this.$loading({
        lock: true,
        text: '下载中请稍后,请稍后.....',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要导出的数据', status: 'error' })
        return
      }
      var shipmentIDS = []
      list.forEach(item => {
        shipmentIDS.push(item.id)
      })
      // console.log(shipmentIDS.length)
      this.$api.ActionRequest(this.api.getlist, { shipmentIDS: shipmentIDS }).then(result => {
        loading.close()
        var imagelist = []
        result.forEach(item => {
          if (item.graphicImage !== null && item.trackingNumber !== null) {
            imagelist.push({ data: item.graphicImage, name: item.trackingNumber })
          }
        })
        if (imagelist.length <= 0) {
          this.$XModal.message({ message: '查询运单信息为0', status: 'error' })
          return false
        }
        this.download.handleDownloadQrIMg(imagelist)
      }).then(() => {
        loading.close()
      })
    },
    // 图片现在
    downLoad (item, downloadName) {
      const imgData = item// base64
      this.download.downloadFile(downloadName, imgData)
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    editEvent (row) {
      this.selectRow = row
      this.formData.shipTo.shipmentID = row.id
      this.formData.shopfrom.shipmentID = row.id
      this.formData.payment.shipmentID = row.id
      this.formData.customs.shipmentID = row.id
      this.formData.upscustoms.shipmentID = row.id
      this.formData1 = cloneDeep(this.formData)
      this.$api.ActionRequest(this.api.getByShipment, row).then(result => {
        result.forEach(item => {
          switch (item.shipmentInfoType) {
            case 1:
              this.formData1.shipTo = Object.assign(this.formData1.shipTo, cloneDeep(item))
              break
            case 2:
              this.formData1.shopfrom = Object.assign(this.formData1.shopfrom, cloneDeep(item))
              break
            case 3:
              this.formData1.payment = Object.assign(this.formData1.payment, cloneDeep(item))
              break
            case 4:
              this.formData1.customs = Object.assign(this.formData1.customs, cloneDeep(item))
              break
            case 5:
              this.formData1.upscustoms = Object.assign(this.formData1.upscustoms, cloneDeep(item))
              break
          }
        })
        this.showEdit = true
      })
    },
    async submitEvent () {
      var rows = []
      rows.push(this.formData1.shipTo)
      rows.push(this.formData1.shopfrom)
      rows.push(this.formData1.payment)
      rows.push(this.formData1.customs)
      rows.push(this.formData1.upscustoms)
      await this.$api.ActionRequest(this.api.edit, rows).then(result => {
        this.loadData()
        this.$XModal.message({ message: '保存成功', status: 'success' })
        this.showEdit = false
      })
    },
    async ShipmentByUpsEvent (row) {
      const loading = this.$loading({
        lock: true,
        text: '正在推送商业发票到UPS,请稍后.....',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      // await this.$api.ActionRequest(this.api.ShipmentByUps, row).then(result => {
      //   this.$XModal.message({ message: '发货成功', status: 'success' })
      //   this.showEdit = false
      //   this.loadData()
      //   loading.close()
      // }).catch(() => {
      //   loading.close()
      // })
      this.UploadPaperlessDocumentEvent(row).then(() => {
        loading.text = 'UPS发货中请稍后,请稍后.....'
        this.UpLoadShipment(row).then(() => {
          this.loadData()
          loading.close()
        }).catch(() => {
          loading.close()
        })
      }).catch(() => {
        loading.close()
      })
    },
    // UPS发货
    async UpLoadShipment (row) {
      return new Promise((resolve, reject) => {
        this.$api.ActionRequest(this.api.ShipmentByUps, row).then(result => {
          this.$XModal.message({ message: '发货成功', status: 'success' })
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    // 推送发票
    async UploadPaperlessDocumentEvent (row) {
      return new Promise((resolve, reject) => {
        this.$api.ActionRequest(this.api.UploadPaperlessDocument, row).then(result => {
          this.$XModal.message({ message: '发票推送成功', status: 'success' })
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    printEvent () {
      this.showUpsIDs = []
      const xTable = this.$refs[this.tableRef]
      var list = xTable.getCheckboxRecords()
      if (list.length <= 0) {
        this.$XModal.message({ message: '请选择要导出的数据', status: 'error' })
        return
      }
      this.showUpsIDs = list.map(item => { return item.id })
      this.showUpsInfo = true
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
