<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='SysGroupMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60" />
      <vxe-table-column type="checkbox" width="60" />
      <vxe-table-column field="code" title="编码" sortable width="100" />
      <vxe-table-column field="codeName" title="名称" sortable width="100" />
      <vxe-table-column field="remark" title="备注" sortable width="100" />
      <vxe-table-column field="sort" title="顺序" sortable width="100" />
      <vxe-table-column field="isClientShow" title="客户端显示" :formatter='formatBool' sortable width="100" />
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100" />
      <vxe-table-column field="createBy" title="创建人" sortable width="100px" />
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100" />
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" show-zoom resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}" />
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}" />
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: '$input', props: { type: 'number',clearable:true}}" />

        <vxe-form-item title="客户端显示？" field="isClientShow" span="8" :item-render="{name: '$switch'}" />
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}" />
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}" />
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>


<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
//import { cloneDeep } from 'lodash'
export default {
  name: 'sys_group',
  mixins: [masterTableMixins],
  components: {
  },
  data() {
    return {
      searchForm: {
      },
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        isClientShow: false,
        sort: 999,
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
      },
      api: {
        get: '/mtm/sys_group/get',
        add: '/mtm/sys_group/adds',
        edit: '/mtm/sys_group/updates',
        delete: '/mtm/sys_group/deletes',
        // departmentCombStore: '/mtm/combo/departmentCombStore',
        //ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      //departmentCombStore:[]
      //ModelElemListComboStoreByQuery
    }
  },
  async created() {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore() {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    },
    //remoteMethod(query,gid) {
    //return new Promise(resolve => {
    // this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query, gid: gid }).then(result => {
    // this.ModelElemListComboStoreByQuery = result
    //return resolve(true)
    // })
    // })
    //})

    //editEventThen(row) {
    //this.remoteMethod(null, row.id).then(res => { this.showEdit = true })
    //}
  }
}
</script>

<style lang="scss" scoped>
</style>
