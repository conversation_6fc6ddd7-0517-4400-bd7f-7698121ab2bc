import util from '@/libs/util.js'

export default {
  namespaced: true,
  mutations: {
    /**
     * @description 显示版本信息
     * @param {Object} state state
     */
    versionShow () {
      util.log.capsule(`${process.env.VUE_APP_Development_Company}`, `${process.env.VUE_APP_Development_Versions}`)
      console.log(`开发者：${process.env.VUE_APP_Development_Author}`)
      console.log(`联系：${process.env.VUE_APP_Development_Contact}`)
      console.log('感谢 D2 Admin  https://github.com/d2-projects/d2-admin')
      // console.log('请不要吝啬您的 star，谢谢 ~')
    }
  }
}
