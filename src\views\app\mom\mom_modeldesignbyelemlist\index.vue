<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="modelShowEvent()" v-if="menuAction.allowAdd">版型选择</vxe-button> -->
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" placeholder="品类" clearable size="mini" style="width:110px">
                  <el-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{name: '$input',props:{placeholder:'编码/名称', suffixIcon:'fa fa-search', clearable:true}}" />
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MtmModeldesignbyelemlistMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60" />
      <vxe-table-column type="checkbox" width="60" />
      <vxe-table-column field="gender" title="性别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelDesignByElemListType" title="层级" sortable width="100" />
      <vxe-table-column field="groupText" title="品类" sortable width="100" />
      <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="100" />
      <vxe-table-column field="modelElemListCodeName" title="款式名称" sortable width="100" />
      <vxe-table-column field="remark" title="备注" sortable width="100" />
      <!-- <vxe-table-column field="sort" title="层级" sortable width="100" /> -->
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100" />
      <vxe-table-column field="createBy" title="创建人" sortable width="100px" />
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100" />
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100" />
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" show-zoom resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="类别" field="groupID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.groupID" filterable placeholder="类别" size="mini">
              <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式" span="12" field="modelElemListID" :item-render="{}">
          <template #default="{ data }">
            <el-select v-model.trim="data.modelElemListID" filterable placeholder="款式" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
              <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.label+item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="层级" field="modelDesignByElemListType" span="12" :item-render="{name: '$select', options: ModelDesignByElemListTypeComboStore,props:{placeholder:'层级'}}" />
        <vxe-form-item title="性别" field="genderID" span="12" :item-render="{name: '$select', options: sexList,props:{placeholder:'性别'}}" />
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}" />
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}" />
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>

  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modeldesignbyelemlist',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      searchForm: {
      },
      showModel: false,
      formData: {
        code: '',
        codeName: '',
        remark: '',
        genderID: true,
        modelDesignByElemListType: null,
        isActive: true,
        groupID: null
      },
      formRules: {
        groupID: [{ required: true, message: '请选择品类' }],
        modelElemListID: [{ required: true, message: '请选择款式' }],
        modelDesignByElemListType: [{ required: true, message: '请选择层级' }]
      },
      api: {
        get: '/mtm/mom_modeldesignbyelemlist/get',
        add: '/mtm/mom_modeldesignbyelemlist/adds',
        edit: '/mtm/mom_modeldesignbyelemlist/updates',
        delete: '/mtm/mom_modeldesignbyelemlist/deletes',
        GroupComboStore: '/mtm/combo/groupComboStore',
        ModelDesignByElemListTypeComboStore: '/mtm/combo/ModelDesignByElemListTypeComboStore',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      GroupComboStore: [],
      ModelDesignByElemListTypeComboStore: [],
      ModelElemListComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    this.remoteMethod()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelDesignByElemListTypeComboStore).then(result => {
        this.ModelDesignByElemListTypeComboStore = result
      })
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { gid: row.modelElemListID }).then(result => {
        this.ModelElemListComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }

    // editEventThen(row) {
    // this.remoteMethod(null, row.id).then(res => { this.showEdit = true })
    // }
  }
}
</script>

<style lang="scss" scoped>
</style>
