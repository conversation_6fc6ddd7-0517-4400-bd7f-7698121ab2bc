<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" @click="insertEvent()" v-if="menuAction.allowEdit">新增</vxe-button>
          <vxe-button status="perfect" @click="saveEvent()" v-if="menuAction.allowEdit">保存</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table keep-source id="BadClientPersonDetailTable" ref="clientPersonxTable" :edit-rules="validRules" :row-class-name="rowClassName" height="auto" :data="tableData" :edit-config="{trigger: 'click', selected: true, mode: 'cell',showStatus: true, icon: 'fa fa-pencil'}" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}" :custom-config="{storage: true}">
      <vxe-table-column title="操作" width="100px" show-overflow v-if="menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa el-icon-picture" @click="imageCellClick(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="code" title="顾客编码" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="顾客姓名" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="gender" title="性别" :edit-render="{name: '$select', options: sexList}" width="100px"> </vxe-table-column>
      <vxe-table-column field="height" title="身高/cm" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100px"></vxe-table-column>
      <vxe-table-column field="weight" title="体重/kg" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100px"></vxe-table-column>
      <vxe-table-column field="tel" title="手机号" :edit-render="{name: '$input'}" width="100px"></vxe-table-column>
      <vxe-table-column field="company" title="公司" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="department" title="部门" :edit-render="{name: '$input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="position" title="职位" :edit-render="{name: '$input', }" width="100px"></vxe-table-column>
      <vxe-table-column field="birthDate" title="出生日期" :edit-render="{name: '$input', props: {type: 'date'}}" width="100px"></vxe-table-column>

      <vxe-table-column field="isActive" title="是否活动" :edit-render="{name: '$switch' }" width="100px">
        <template v-slot:edit="{ row }">
          <vxe-switch v-model="row.isActive" size="mini" open-label="是" close-label="否"></vxe-switch>
        </template>
        <template v-slot="{ row }">
          {{row.isActive?'是':'否'}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="mobile" title="座机" :edit-render="{name: '$input',props: {type: 'number'} }" width="100px"></vxe-table-column>
      <vxe-table-column field="address" title="地址" :edit-render="{name: '$input', }" width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" :edit-render="{name: '$input', }" width="100px"></vxe-table-column>

    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <el-drawer ref="elDrawer" :visible.sync="drawer" :before-close="handleClose" direction="rtl" :destroy-on-close='true' size='30%'>
      <client-person-image :form="selectRow" v-if="drawer" />
    </el-drawer>
  </d2-container>
</template>

<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
import ClientPersonImage from './clientpersonimage'
import { unionWith, cloneDeep } from 'lodash'
export default {
  name: 'BadClientPerson',
  mixins: [detailMixins],
  props: {

  },
  components: {
    ClientPersonImage
  },
  data () {
    return {
      tableRef: 'clientPersonxTable',
      sexList: [
        { value: true, label: '男' },
        { value: false, label: '女' }
      ],
      api: {
        get: '/mtm/bad_clientperson/get',
        add: '/mtm/bad_clientperson/adds',
        edit: '/mtm/bad_clientperson/updates',
        delete: '/mtm/bAD_ClientPerson/deletes'
      },
      validRules: {
        code: [
          { required: true, message: '顾客编码必须填写' }
        ],
        codeName: [
          { required: true, message: '顾客姓名必须填写' }
        ],
        gender: [
          { required: true, message: '性别必须选择' }
        ],
        height: [
          { required: true, message: '身高必须填写' }
        ],
        weight: [
          { required: true, message: '体重必须填写' }
        ]
        // tel: [
        //   { required: true, message: '手机号必须填写' },
        //   { message: '请填写正确11位手机号', type: 'string', pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/ }
        // ]
      }

    }
  },
  computed: {

  },
  created () {
    this.loadData({ id: this.form.id })
    this.tableData = []
  },
  methods: {
    async insertEvent (row) {
      const record = { clientID: this.form.id, isActive: true, weight: 0 }
      var xtable = this.$refs[this.tableRef]
      const { row: newRow } = await xtable.insertAt(record, row)
      // newRow.isActive = true
      // newRow.weight = 0
      await xtable.setActiveCell(newRow, 'code')
    },
    getTableData () {
      var xtable = this.$refs[this.tableRef]
      var insertData = xtable.getInsertRecords()
      var updateData = xtable.getUpdateRecords()
      var data = unionWith(insertData, updateData)
      return data
    },
    async removeEvent (row) {
      var xtable = this.$refs[this.tableRef]
      xtable.remove(row)
      if (row.id !== undefined) {
        await this.$api.ActionRequest(this.api.delete, [row]).then(res => {
          this.loadData({ id: this.form.id })
        })
      }
    },
    async saveEvent () {
      var b = await this.fullValidEvent()
      if (b) {
        var xtable = this.$refs[this.tableRef]
        var insertData = xtable.getInsertRecords()
        var updateData = xtable.getUpdateRecords()
        if (insertData.length !== 0) {
          await this.$api.ActionRequest(this.api.add, insertData).then(res => {
            this.loadData({ id: this.form.id })
          })
        }
        if (updateData.length !== 0) {
          await this.$api.ActionRequest(this.api.edit, updateData).then(res => {
            this.loadData({ id: this.form.id })
          })
        }
      }
    },
    async fullValidEvent () {
      var xtable = this.$refs[this.tableRef]
      const errMap = await xtable.fullValidate().catch(errMap => errMap)
      if (errMap) {
        const msgList = []
        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            const { rowIndex, column, rules } = params
            rules.forEach(rule => {
              msgList.push(`第 ${rowIndex} 行 ${column.title} 校验错误：${rule.message}`)
            })
          })
        })
        this.$XModal.message({
          status: 'error',
          message: () => {
            return [
              <div class="red" style="max-height: 400px;overflow: auto;">
                {
                  msgList.map(msg => <div>{msg}</div>)
                }
              </div>
            ]
          }
        })
        return false
      } else {
        return true
      }
    },
    imageCellClick (row) {
      this.selectRow = cloneDeep(row)
      this.drawer = true
    }
  }
}
</script>

<style>
</style>
