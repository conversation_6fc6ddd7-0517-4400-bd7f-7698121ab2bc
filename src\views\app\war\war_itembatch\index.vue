<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
          <el-button type="success" size="mini" @click="exportSelectEvent" v-if="menuAction.allowPrint">选中导出</el-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="clientID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model.trim="data.clientID" filterable placeholder="客户" size="mini" remote reserve-keyword :remote-method="remoteMethod4" clearable>
                  <el-option v-for="item in clientComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="itemID" :item-render="{}">
              <template #default="{ data }">
                <el-select v-model.trim="data.itemID" filterable placeholder="物料名称" size="mini" remote reserve-keyword :remote-method="remoteMethod" clearable>
                  <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default="{ data }">
                <vxe-input v-model.trim="data.text" placeholder="合同号/手册号" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}">
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='WarItembatchMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="code" title="海关合同号" sortable width="100"> </vxe-table-column>
      <!-- <vxe-table-column field="codeName" title="名称" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100" cell-type="string"></vxe-table-column>
      <vxe-table-column field="itemCodeName" title="物料名称" sortable width="100" cell-type="string"></vxe-table-column>
      <vxe-table-column field="originalItemNo" title="原始货号" cell-type="string" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="所属客户" sortable width="100"></vxe-table-column>
      <vxe-table-column field="qty" title="入库数量" sortable width="100"></vxe-table-column>
      <vxe-table-column field="inventoryQty" title="汇总库存" sortable width="100"></vxe-table-column>
      <vxe-table-column field="count" title="出库数量" sortable width="150">
        <template v-slot="{ row }">
          <span v-if="row.count>=row.qty" style="color:red;background-color: white;">{{row.count}}</span>
          <span v-else style="color:#0da410;background-color: white;">{{row.count}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="count1" title="当批次库存" sortable width="100">
        <template v-slot="{ row }">
          <span v-if="row.count1<=0" style="color:red;background-color: white;">{{row.count1}}</span>
          <span v-else style="color:#0da410;background-color: white;">{{row.count1}}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="eoriNo" title="手册号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="200" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button status="warning" @click="detailShowEvent(row)" v-if="menuAction.allowEdit">清单</vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="showfooterCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="海关合同号" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <!-- <vxe-form-item title="名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <vxe-form-item title="物料名称" field="itemStockID" span="24" :item-render="{}"> <el-select v-model.trim="selectRow.itemStockID" filterable placeholder="物料名称" size="mini" remote reserve-keyword :remote-method="remoteMethod1">
            <el-option v-for="item in ItemStockComboStoreByQuery" :key="item.value" :label="item.label" :value="item.id">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.clientName }} {{item.businessGroupText}}</span>
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item title="手册号" field="eoriNo" span="12" :item-render="{name: 'input'}"></vxe-form-item>

        <vxe-form-item title="入库数量" field="qty" span="12" :item-render="{name: 'input', props: { type: 'float',clearable:true}}"></vxe-form-item>

        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="detailShow" :title="selectRow.code+'物料清单'" width="85%" height="60%" resize destroy-on-close :loading="submitLoading">
      <detail :form="selectRow" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import Detail from './detail.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'war_itembatch',
  mixins: [masterTableMixins],
  components: {
    Detail
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        itemStockID: '',
        remark: '',
        isActive: true
      },
      detailShow: false,
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }],
        itemStockID: [{ required: true, message: '请选择物料' }]
      },
      api: {
        get: '/mtm/war_itembatch/get',
        add: '/mtm/war_itembatch/adds',
        edit: '/mtm/war_itembatch/updates',
        delete: '/mtm/war_itembatch/deletes',
        ItemStockComboStoreByQuery: '/mtm/comboQuery/ItemStockComboStoreByQuery',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        clientComboStoreByQuery: '/mtm/comboQuery/clientComboStoreByQuery'
      },
      ItemComboStore: [],
      clientComboStoreByQuery: [],
      ItemStockComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery).then(result => {
        this.ItemStockComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.clientComboStoreByQuery).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    detailShowEvent (row) {
      this.selectRow = row
      this.detailShow = true
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery, { text: query }).then(result => {
        this.ItemStockComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.clientComboStoreByQuery, { text: query, isNotG: true }).then(result => {
        this.clientComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      await this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery, { gid: row.itemStockID }).then(result => {
        this.ItemStockComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 编辑
    async copyRowEvent (row) {
      await this.$api.ActionRequest(this.api.ItemStockComboStoreByQuery, { gid: row.itemStockID }).then(result => {
        this.ItemStockComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.selectRow.id = null
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
