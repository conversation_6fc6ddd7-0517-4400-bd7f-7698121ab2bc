<template>
  <el-card shadow="always">
    <div class="sorderDetailSize" v-loading="EditState" element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)">
      <el-alert v-if="SorderDetailModel&&SorderDetailModel.message!=null&&SorderDetailModel.message!=''" :title="SorderDetailModel.message" type="warning">
      </el-alert>
      <vxe-form>
        <vxe-form-item title="量体类型" field="sorderSizeTypeID" :item-render="{}"> <template v-slot>
            <vxe-select v-model="SorderDetailModel.sorderSizeTypeID" placeholder="量体类型" @change="sorderSizeTypeChange">
              <!-- <vxe-option  value="1" label="量体类型"></vxe-option> -->
              <vxe-option v-if="SorderDetailModel.isRuleSize" value="2" label="成衣规格"></vxe-option>
              <vxe-option v-if="SorderDetailModel.isRuleSize" value="3" label="标准规格(按成衣算法)"></vxe-option>
              <vxe-option value="4" label="标准规格(无算法)"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="号型" field="SizeID1" :item-render="{}"> <template v-slot>
            <el-select v-if="SorderDetailModel.sorderSizeTypeID !== '2'" v-model="sizeQuery" filterable remote reserve-keyword placeholder="输入号型" :remote-method="sizeMethod" @change="sizeChange" clearable @clear="sizeClear" size="mini" class="sizeID1">
              <el-option v-for="item in sizeTableData" :key="item.id" :label="item.code" :value="item.id">
              </el-option>
            </el-select>
            <el-tag type="success">匹配号型{{sizeIDText}}</el-tag>
          </template>
        </vxe-form-item>

        <vxe-form-item :item-render="{}"> <template v-slot>
            <vxe-button :status="SorderDetailModel.isChecked?'success':'danger'" :loading="checkSizeLoding" content="规格检验" size='mini' @click="checkSize"></vxe-button>
            <vxe-button status="warning" content="查看规格单" size='mini' @click="selectSizeClick"></vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
      <vxe-table ref="sorderDetialSizeTable" :loading="sizeTableLoding" keep-source :stripe="false" :data="DetailSizeData" :mouse-config="{selected: true}" :edit-config="{trigger: 'click', mode: 'cell',showStatus: true,activeMethod: activeCellMethod}" height="800px" :row-class-name="sizeRequitedClass" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}">
        <vxe-table-column type="seq" width="40"></vxe-table-column>
        <vxe-table-column field="sizeColumnName" title="字段" width="100">
          <template v-slot="{ row }">
            <!-- 量体图片展示 -->

            <template v-if="SorderDetailModel.sorderSizeTypeID==='1'">
              <template v-if="row.imagePath1!==null&&row.imagePath1!==''">
                <el-popover placement="right-end" :title="row.sizeColumnName" width="400" trigger="hover">
                  <el-image style="width: 400px; height: 400px" :src="row.imagePath1" fit="scale-down"></el-image>
                  <div slot="reference">
                    <span v-if="row.isRequired" style="color:red">*</span> <span>{{ row.sizeColumnName}}</span><i style="color:red" class="el-icon-picture-outline"></i>
                  </div>
                </el-popover>
              </template>
              <template v-else>
                <span v-if="row.isRequired" style="color:red">*</span><span> {{row.sizeColumnName}}</span>
              </template>
            </template>
            <!-- 成衣图片显示 -->
            <template v-if="SorderDetailModel.sorderSizeTypeID!=='1'">
              <template v-if="row.imagePath!==null&&row.imagePath!==''">
                <el-popover placement="right-end" :title="row.sizeColumnName" width="400" trigger="hover">
                  <el-image style="width: 400px; height: 400px" :src="row.imagePath" fit="scale-down"></el-image>
                  <div slot="reference">
                    <span v-if="row.isRequired" style="color:red">*</span> <span>{{ row.sizeColumnName}}</span><i style="color:red" class="el-icon-picture-outline"></i>
                  </div>
                </el-popover>
              </template>
              <template v-else>
                <span v-if="row.isRequired" style="color:red">*</span> {{row.sizeColumnName}}
              </template>
            </template>
          </template>
        </vxe-table-column>
        <vxe-table-column field="pure" title="净体尺寸" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100">
          <template v-slot:edit="{ row }">
            <vxe-input v-model="row.pure" clearable slot="reference" type="float" min="0" max="300" @focus="inputFocus('pure',row)"></vxe-input>
          </template>
        </vxe-table-column>
        <vxe-table-column field="standard1" title="标准规格" width="100"></vxe-table-column>
        <vxe-table-column field="bodyValue" title="特体调整值" width="100"></vxe-table-column>
        <!-- sorderSizeTypeID==='2' 标准规格(无算法) -->
        <!-- sorderSizeTypeID==='1' 标准规格(按成衣算法) -->

        <vxe-table-column field="fix1" title="标准修正" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100">
          <template v-slot:edit="{ row }">
            <el-popover ref="popover" placement="right" width="200" trigger="focus" :content="'最大值:'+row.fix1Max+' 最小值:'+row.fix1Min">
              <vxe-input v-model="row.fix1" :placeholder="'最大值:'+row.fix1Max+'最 小值:'+row.fix1Min" type="float" :min="row.fix1Min" :max="row.fix1Max" clearable slot="reference" @blur="fix1Blur(row)" @focus="inputFocus('fix1',row)"></vxe-input>
            </el-popover>
          </template>
        </vxe-table-column>
        <!-- sorderSizeTypeID==='3' 成衣规格 -->
        <vxe-table-column field="finish" title="成衣" :edit-render="{name: '$input', props: {type: 'float', digits: 2}}" width="100">
          <template v-slot:edit="{ row }">
            <el-popover ref="popover" placement="right" width="200" trigger="focus" :content="'最大值:'+row.finishMax+' 最小值:'+row.finishMin">
              <vxe-input v-model="row.finish" :placeholder="'最大值:'+row.finishMax+' 最小值:'+row.finishMin" type="float" :min="row.finishMin" :max="row.finishMax" clearable slot="reference" @focus="inputFocus('finish',row)"></vxe-input>
            </el-popover>
          </template>
        </vxe-table-column>
        <vxe-table-column field="standard" title="参考规格" width="100"></vxe-table-column>
        <vxe-table-column field="isManual" title="手工?" :edit-render="{autofocus: '.vxe-input--inner'}" width="100">
          <template v-slot:edit="{ row }">
            <vxe-switch v-model="row.isManual" open-label="是" close-label="否"></vxe-switch>
          </template>
          <template v-slot="{ row }">{{ row.isManual?'是':'否' }}</template>
        </vxe-table-column>
        <vxe-table-column field="fixVariant" title="变体" width="200"></vxe-table-column>
      </vxe-table>
      <vxe-modal v-model="selectSize" title="规格单查询" width="60%" height="50%" resize remember>
        <template v-slot>
          <vxe-grid id="selectSizeListModal" border ref="selectSizeList" resizable height="auto" :loading="selectSizeLoading" :columns="sizeTableColumn1" :toolbar="{slots: {buttons: 'toolbar_buttons'}}" :data="sizeTableData" :seq-config="{startIndex: (sizeListTablePage.currentPage - 1) * sizeListTablePage.pageSize}" :pager-config="sizeListTablePage" @page-change="sizeHandlePageChange" :custom-config="{storage: true}" @cell-dblclick="selectSizeDbClick">
            <template v-slot:toolbar_buttons>
              <vxe-toolbar perfect>
                <template v-slot:tools>
                  <vxe-form ref="xForm" :data="searchSize" @submit="sizeSearchEvent()" @reset="resetEvent">
                    <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchSize.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
                    </vxe-form-item>
                    <vxe-form-item :item-render="{}"> <template #default>
                        <vxe-button type="submit" status="success">查询</vxe-button>
                        <vxe-button type="reset">重置</vxe-button>
                      </template>
                    </vxe-form-item>
                  </vxe-form>
                </template>
              </vxe-toolbar>
            </template>
          </vxe-grid>
        </template>
      </vxe-modal>
    </div>
  </el-card>
</template>

<script>
import XEUtils from 'xe-utils'
import config from '@/config.js'
// import { number } from 'echarts/lib/export'
import sorderEditState from './sordereditstate'
import { mapState } from 'vuex'
export default {
  name: 'SorderDetailSize',
  mixins: [config, sorderEditState],
  props: {
    SorderDetailModel: {
      type: Object,
      requited: true
    },
    height: {
      type: String
    },
    sorderStore: {
      type: Object,
      default: null
    },
    sorderdetailbodySaveEven: {
      type: Function,
      requited: true
    },
    getBodysData: {
      type: Function,
      requited: true
    }
  },
  watch: {
    'SorderDetailModel.modelID': {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.searchSize.modelId = newVal
          this.sizeQuery = null
          this.sizeIDText = null
          this.SorderDetailModel.sizeID1 = null
          this.SorderDetailModel.sizeID = null
          this.SorderDetailModel.isChecked = false
          this.SorderDetailModel.message = null
          this.get(newVal)
          // this.sizeFindList()
          // this.setSorderDetailSizes(null)
        }
      }
    },
    // //匹配的号型
    // 'SorderDetailModel.sizeID': {
    //   deep: true,
    //   handler: function (newVal, oldVal) {
    //     debugger;
    //     if (newVal !== '' && newVal !== null) {
    //       this.sizeQuery = newVal
    //       this.searchSize.id = newVal
    //       this.sizeFindList()
    //     }
    //   }
    // },
    'SorderDetailModel.sorderSizeTypeID': {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.sizeIDText = null
          this.SorderDetailModel.sizeID1 = null
          this.SorderDetailModel.sizeID = null
          this.sizeQuery = null
          this.SorderDetailModel.isChecked = false
          this.SorderDetailModel.message = null
          this.setSorderDetailSizes(null)
        }
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      api: {
        get: '/mtm/oDM_SorderDetailSize/get',
        getsize: '/mtm/oDM_SorderDetailSize/getSize',
        checkSize: '/mtm/oDM_SorderDetailSize/checkSize',
        sorderDetailSizeEdit: '/mtm/oDM_SorderDetailSize/modify',
        sorderDetailModelEdit: '/mtm/oDM_SorderDetailModel/ModifyDetailModel'
      },
      checkSizeLoding: false,
      sizeQuery: this.SorderDetailModel.sizeID1,
      sizeIDText: this.SorderDetailModel.sizeIDText,
      selectSize: false,
      selectSizeLoading: false,
      DetailSizeData: [],
      sizetableCloumns: [{ field: 'groupName', title: '类别', width: '50' },
        { field: 'sizeListCode', title: '规格单编码', width: '90' },
        { field: 'sizeListName', title: '规格单名称', width: '' },
        { field: 'code', title: '规格编码', width: '90' },
        { field: 'codeName', title: '规格名称', width: '90' },
        { field: 'height', title: '身高', width: '50' }],
      sizeTableColumn1: [
        { field: 'collar', title: '衬衣领围', width: '100' },
        { field: 'frontLength', title: '前衣长', width: '100' },
        { field: 'backMiddleLength', title: '后中长', width: '100' },
        { field: 'bust', title: '胸围', width: '150' },
        { field: 'halfBust', title: '半胸围', width: '150' },
        { field: 'middleWaist', title: '中腰', width: '100' },
        { field: 'jacketHipline', title: '上衣臀围', width: '100' },
        { field: 'halfJacketHipline', title: '半臀围', width: '100' },
        { field: 'hem', title: '下摆', width: '100' },
        { field: 'halfHem', title: '半下摆', width: '100' },
        { field: 'shoulderWidth', title: '肩宽', width: '100' },
        { field: 'smallShoulderWidth', title: '小肩宽', width: '100' },
        { field: 'sleeveLength', title: '袖长', width: '100' },
        { field: 'sleeveWidth', title: '袖肥', width: '100' },
        { field: 'halfSleeveWidth', title: '半袖肥', width: '100' },
        { field: 'cuff', title: '袖口', width: '100' },
        { field: 'halfCuff', title: '半袖口', width: '100' },
        { field: 'backWidth', title: '背宽', width: '100' },
        { field: 'halfBackWidth', title: '半背宽', width: '100' },
        { field: 'rightinSleeveLength', title: '右内袖长', width: '100' },
        { field: 'leftinSleeveLength', title: '左内袖长', width: '100' },
        { field: 'rightOutSleeveLength', title: '右外袖长', width: '100' },
        { field: 'leftOutSleeveLength', title: '左外袖长', width: '100' },
        { field: 'halfMiddleWaist', title: '半腰围(上衣)', width: '100' },
        { field: 'trouserLong', title: '毛裤长', width: '100' },
        { field: 'netTrouserLong', title: '净裤长', width: '100' },
        { field: 'waist', title: '腰围', width: '100' },
        { field: 'halfWaist', title: '半腰围(裤子)', width: '100' },
        { field: 'yaosheng', title: '腰绳', width: '100' },
        { field: 'waist1', title: '腰围（拉前）', width: '100' },
        { field: 'waist2', title: '腰围（拉后）', width: '100' },
        { field: 'hipline', title: '臀围', width: '100' },
        { field: 'halfHipline', title: '半臀围(裤子)', width: '100' },
        { field: 'crossCrotch', title: '腿围/横档', width: '100' },
        { field: 'halfCrossCrotch', title: '半横档', width: '100' },
        { field: 'smallCrossCrotch', title: '腿围（裆下5CM）' },
        { field: 'kneeGirth', title: '膝围', width: '100' },
        { field: 'halfKneeGirth', title: '半膝围', width: '100' },
        { field: 'throughCrotch', title: '通裆', width: '100' },
        { field: 'standCrotch', title: '立裆', width: '100' },
        { field: 'frontWaveLength', title: '前浪长', width: '100' },
        { field: 'backWaveLength', title: '后浪长', width: '100' },
        { field: 'trouserBottom', title: '脚口', width: '100' },
        { field: 'halfTrouserBottom', title: '半脚口', width: '100' },
        { field: 'zipperLength', title: '拉链长', width: '100' },
        { field: 'lNetInnerTrouserLong', title: '左内裤长', width: '100' },
        { field: 'rNetInnerTrouserLong', title: '右内裤长', width: '100' },
        { field: 'leftOutTrouserLong', title: '左外裤长', width: '100' },
        { field: 'rightOutTrouserLong', title: '右外裤长', width: '100' },
        { field: 'half44CrossCrotch', title: '裆下44CM', width: '100' },
        { field: 'half15CrossCrotch', title: '裆下15CM', width: '100' }
      ],

      sizeTableData: [],
      searchSize: {
        id: null,
        text: null,
        // modelBaseID: null,
        modelId: null,
        maxResultCount: '10',
        // genderID: null,
        currentPage: null,
        skipCount: null
      },
      sizeListTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        align: 'right',
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
        perfect: true
      },
      sizeTableLoding: false

    }
  },
  created () {
    if (this.SorderDetailModel.modelID) {
      //   debugger;
      this.searchSize.modelId = this.SorderDetailModel.modelID
      this.get(this.SorderDetailModel.modelID, this.SorderDetailModel.id)
    }
    var size = this.SorderDetailModel.sizeID1
    this.searchSize.id = size
    this.sizeFindList()
  },
  methods: {
    get (modelId, detailmodelid = null) {
      this.$api.ActionRequest(this.api.get, { modelId: modelId, sorderDetailModelID: detailmodelid || this.SorderDetailModel.id, sizeID1: this.SorderDetailModel.sizeID1 || this.SorderDetailModel.sizeID }).then(result => {
        this.DetailSizeData = result
        this.DetailSizeData.map(item => {
          item.sorderDetailModelID = this.SorderDetailModel.id
        })
      })
    },
    activeCellMethod ({ column, columnIndex }) {
      if (column.property === 'isManual' || column.property === 'pure') {
        return true
      }
      if (this.SorderDetailModel.sorderSizeTypeID === '3' || this.SorderDetailModel.sorderSizeTypeID === '4') {
        if (column.property === 'fix1') {
          return true
        }
      }
      if (this.SorderDetailModel.sorderSizeTypeID === '2') { // 成衣
        if (column.property === 'finish') {
          return true
        }
      }
      return false
    },
    async sorderSizeTypeChange () {
      await this.sizeTableRefresh()
      this.SorderDetailModel.sizeID1 = null
      this.SorderDetailModel.sizeID = null
      this.sizeIDText = null
      await this.setSorderDetailSizes(null)

      //   this.$refs["sorderDetialSizeTable"].reloadColumn(columns)
    },
    async sizeTableRefresh () {
      var table = this.$refs.sorderDetialSizeTable
      await table.refreshColumn()
      await table.resetColumn()
      // var rows=await table.getTableData()
      table.reloadData(this.DetailSizeData)
    },
    sizeRequitedClass ({ row }) {
      // if (row.isRequired) {
      //   return 'isRequiredRow'
      // }
    },
    selectSizeClick () {
      this.searchSize.text = null
      this.searchSize.id = null
      this.selectSize = !this.selectSize
      this.sizeFindList()
    },
    async selectSizeDbClick ({ row }) {
      if (this.SorderDetailModel.sorderSizeTypeID !== '2') {
        this.sizeQuery = row.id
        this.SorderDetailModel.sizeID1 = row.id
        this.SorderDetailModel.sizeID = row.id
        this.SorderDetailModel.sizeIDText = row.code
        this.SorderDetailModel.message = null
        this.SorderDetailModel.isChecked = false
        this.selectSize = !this.selectSize
        await this.setSorderDetailSizes(row)
      }
    },
    // 选择规格后配置订单规格数据
    async setSorderDetailSizes (row) {
      this.sizeTableLoding = true
      if (row != null) {
        for (var a in row) {
          // 修长换成左袖长右袖长
          if (a === 'sleeveLength') {
            this.setSorderDetailSizesData('LeftSleeveLength', { standard1: row[a], finish: row[a], fix1: 0 })
            this.setSorderDetailSizesData('RightSleeveLength', { standard1: row[a], finish: row[a], fix1: 0 })
          } else if (a === 'netTrouserLong') { // 裤长  左裤长  右裤长
            this.setSorderDetailSizesData('LeftTrouserLong', { standard1: row[a], finish: row[a], fix1: 0 })
            this.setSorderDetailSizesData('RightTrouserLong', { standard1: row[a], finish: row[a], fix1: 0 })
          } else {
            this.setSorderDetailSizesData(a, { standard1: row[a], finish: row[a], fix1: 0 })
          }
        }
      } else {
        await this.setSorderDetailSizesData(null, { standard1: 0, finish: 0, fix1: 0, standard: 0, isManual: false })
      }
      this.sizeTableLoding = false
    },
    inputFocus (name, row) {
      var num = this.$utils.toNumber(row[name])
      if (num === 0) {
        row[name] = null
      }
    },
    async setSorderDetailSizesData (name, data) {
      this.DetailSizeData.forEach(element => {
        element.fixVariant = null
        // console.log(element.bodyValue)
        if (name == null) {
          element = Object.assign(element, data)
        } else {
          if (element.sizeColumnCode.toLowerCase() === name.toLowerCase()) {
            element = Object.assign(element, data)
          }
        }
      })
      await this.sizeTableRefresh()
    },
    async sizeHandlePageChange ({ currentPage, pageSize }) {
      this.sizeListTablePage.currentPage = currentPage
      this.sizeListTablePage.pageSize = pageSize
      await this.sizeFindList()
    },
    async sizeFindList () {
      if (this.searchSize.modelId === null) {
        return
      }
      this.selectSizeLoading = true
      this.searchSize.maxResultCount = this.sizeListTablePage.pageSize
      this.searchSize.skipCount = (this.sizeListTablePage.currentPage - 1) * this.sizeListTablePage.pageSize
      await this.$api.ActionRequest(this.api.getsize, this.searchSize).then(async result => {
        this.sizeTableData = result.items
        this.sizeListTablePage.total = result.totalCount
        this.selectSizeLoading = false
        await this.sizeSelectTableShowCloumns()
      })
    },
    async sizeSelectTableShowCloumns () {
      var table = this.$refs.selectSizeList
      if (table === undefined) {
        return
      }
      var columns = [
      ]
      this.DetailSizeData.forEach(elem => {
        var code = elem.sizeColumnCode.toLowerCase()
        var size = this.$utils.find(this.sizeTableColumn1, item => {
          var icode = item.field.toLowerCase()
          return icode === code
        })
        if (size !== null && size !== undefined) {
          columns.push(size)
        }
        if (code === 'lefttrouserlong' || code === 'righttrouserlong') {
          if (this.$utils.findIndexOf(columns, t => t.title === '左裤长') < 0) {
            columns.push({ field: 'trouserLong', title: '左裤长', width: '100' })
          }
          if (this.$utils.findIndexOf(columns, t => t.title === '右裤长') < 0) {
            columns.push({ field: 'trouserLong', title: '右裤长', width: '100' })
          }
        }
        if (code === 'leftsleevelength' || code === 'rightsleevelength') {
          if (this.$utils.findIndexOf(columns, t => t.title === '左袖长') < 0) {
            columns.push({ field: 'sleeveLength', title: '左袖长', width: '100' })
          }
          if (this.$utils.findIndexOf(columns, t => t.title === '右袖长') < 0) {
            columns.push({ field: 'sleeveLength', title: '右袖长', width: '100' })
          }
        }
      })
      var arr = this.$utils.union(this.sizetableCloumns, columns)
      await table.loadColumn(arr)
    },
    async resetEvent () {
      this.sizeListTablePage.currentPage = 1
      await this.sizeFindList()
    },
    async sizeSearchEvent () {
      this.sizeListTablePage.currentPage = 1
      await this.sizeFindList()
    },
    async sizeMethod (query) {
      this.searchSize.text = query
      this.searchSize.id = null
      this.sizeListTablePage.currentPage = 1
      await this.sizeFindList()
      // this.$refs.xDown1.showPanel()
    },
    async sizeChange (val) {
      if (val !== '') {
        var size = this.sizeTableData.GetFirstElement('id', val)
        this.SorderDetailModel.sizeID1 = val
        await this.setSorderDetailSizes(size)
      } else {
        this.sizeQuery = null
        this.SorderDetailModel.isChecked = false
        this.SorderDetailModel.message = null
        // this.SorderDetailModel.fixVariant = null
        // this.SorderDetailModel.standard = null
        await this.setSorderDetailSizes(null)
      }
    },
    async sizeClear () {
      this.sizeQuery = null
      this.SorderDetailModel.isChecked = false
      this.SorderDetailModel.sizeID1 = null
      this.SorderDetailModel.sizeID = null
      this.SorderDetailModel.message = null
      this.sizeIDText = null
      // this.SorderDetailModel.fixVariant = null
      // this.SorderDetailModel.standard = null
      await this.setSorderDetailSizes(null)
    },
    fix1Blur (row) {
      if (row.fix1 === '' || row.fix1 === null) {
        row.finish = row.standard1
      } else {
        if (parseFloat(row.fix1) < row.fix1Min) {
          row.fix1 = row.fix1Min
        }
        if (parseFloat(row.fix1) > row.fix1Max) {
          row.fix1 = row.fix1Max
        }
        var count = row.standard1 == null ? 0 : parseFloat(row.standard1)
        row.finish = count + parseFloat(row.fix1)
      }
    },
    // 规格检验
    async checkSize () {
      if (this.SorderDetailModel.sorderSizeTypeID !== '2') {
        if (this.SorderDetailModel.sizeID1 === null || this.SorderDetailModel.sizeID1 === '') {
          this.$XModal.message({ message: '请输入号型', status: 'error' })
          return
        }
      }
      if (this.height === null || this.height === '') {
        this.$XModal.message({ message: '请输入身高', status: 'error' })
        return
      }
      var b = await this.sorderdetailbodySaveEven()
      if (!b) {
        this.$notify.error({
          title: '错误',
          message: '特体数据保存异常,检验失败'
        })
      }
      this.checkSizeLoding = true
      const detailModel = XEUtils.clone(this.SorderDetailModel)
      detailModel.sorderDetailSize = this.DetailSizeData
      detailModel.height = this.height
      detailModel.sorderDetailBody = this.getBodysData() || []
      this.$api.ActionRequest(this.api.checkSize, detailModel).then(async result => {
        this.checkSizeLoding = false
        if (result.isChecked) {
          this.$notify({
            title: '成功',
            message: '检验通过',
            type: 'success'
          })
        } else {
          this.checkSizeLoding = false
          this.$notify.error({
            title: '错误',
            message: result.message
          })
        }
        var sizes = result.sorderDetailSize
        this.SorderDetailModel.isChecked = result.isChecked
        this.SorderDetailModel.message = result.message
        this.SorderDetailModel.sizeIDText = result.sizeIDText
        this.sizeIDText = result.sizeIDText
        this.SorderDetailModel.sizeID = result.sizeID
        this.SorderDetailModel.sizeID1 = result.sizeID1
        await this.DetailSizeData.forEach(async (item) => {
          var size = sizes.GetFirstElement('sizeColumnID', item.sizeColumnID)
          if (size) {
            item.finish = size.finish
            item.fixVariant = size.fixVariant
            item.standard = size.standard
            item.bodyValue = size.bodyValue
            // item = XEUtils.assign(item, size)
          }
        })
        // // 成衣
        // if (this.SorderDetailModel.sorderSizeTypeID === '2') {
        //   this.sizeQuery = result.sizeID
        //   this.searchSize.id = result.sizeID
        //   this.sizeFindList()
        // }
      }).catch(() => {
        this.checkSizeLoding = false
      })
    },
    async sorderDetailSizeSave () {
      if (this.EditState) {
        return this.EditState
      }
      var list = this.DetailSizeData
      var b = this.sorderDetailModelSave([this.SorderDetailModel])
      if (!b) {
        return false
      }
      if (list.length === 0) {
        return true
      }
      return await this.$api.ActionRequest(this.api.sorderDetailSizeEdit, list).then(async res => {
        return true
      }).catch(() => {
        return false
      })
    },
    async sorderDetailModelSave (list) {
      if (list.length === 0) {
        return true
      }
      return await this.$api.ActionRequest(this.api.sorderDetailModelEdit, list).then(async res => {
        return true
      }).catch(() => {
        return false
      })
    }

  }
}
</script>

<style lang="scss">
.sorderDetailSize {
  .isRequiredRow {
    background-color: #67c23a !important;
  }
  .sizeID1 {
    input {
      width: 100px !important;
    }
  }
}
</style>
