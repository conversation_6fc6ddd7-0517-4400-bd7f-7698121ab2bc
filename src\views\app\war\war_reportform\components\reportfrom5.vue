<template>
  <d2-container class="portrait1">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <!-- <vxe-button status="warning" @click="printEvent('reportfrom')" icon="vxe-icon--print">打印</vxe-button> -->
          <!-- <vxe-button status="warning" @click="get() " icon="vxe-icon--print">打印</vxe-button> -->
          <vxe-button status="warning" @click="print" icon="vxe-icon--print">打印</vxe-button>
          <vxe-button :content="tablename" status="warning"  disabled></vxe-button>
        </template>
      </vxe-toolbar>

    </template>
    <div id="reportfrom" class="reportfrom">
      <div class="root">
        <pdf ref="pdf" :src="pdfurl"></pdf>
      </div>
    </div>
  </d2-container>
</template>

<script>
import reportfrommix from './reportfrom'
import pdf from 'vue-pdf'
// import ReportFormHead from './reportfromhead.vue'
export default {
  mixins: [reportfrommix],
  components: {
    pdf
    // ReportFormHead
  },

  data () {
    return {
      apiurl: '/fs/print/reportfrom5/pdf'
    }
  },
  created () { },
  methods: {

  }
}
</script>

<style  lang="scss" >
.portrait {
  @page {
    /* size: 21cm 29.7cm; */
    font-size: 14pt;
    font-family: SimSun, 宋体, serif;
    color: black;
    line-height: 1.4;
    text-align: justify;
    margin: 0;
    padding: 0;
  }
  .root {
    /*需要将预览显示的界面限定在A4大小*/
    width: 210mm;
    /*这个高度为什么不是A4的大小，是经过N次验证的方式得到的，唯一的目的就是为了保证预览和打印预览一致*/
    /*可能是我写的有一点问题，但是如果设置为297，那么显示就会出现问题*/
    height: 280;
    /*上下不要设置padding,否则打印预览下面的footer就会往上走*/
    // padding: 0 24mm 0 24mm;
    /* margin-bottom: 24mm; */
    background-color: white;
  }
  /*下面两个样式是为了保证屏幕上预览和打印预览一致*/
  @media screen {
    .content {
      width: 210mm;
      height: 340mm;
      /* padding-top:12mm; */
      display: flex;
      flex-direction: column;
    }
  }
  @media print {
    .content {
      width: 210mm;
      height: 340mm;
      padding-top: 18mm;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
