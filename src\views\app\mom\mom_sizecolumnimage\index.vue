<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="sizeColumnID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.sizeColumnID" filterable remote reserve-keyword placeholder="规格字段" :remote-method="remoteMethod1" size="mini" clearable>
                  <el-option v-for="item in SizeColumnComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelID" filterable remote reserve-keyword placeholder="版型" :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in ModelComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='MomSizecolumnimageMasterTable' :loading="tableLoading" ref='master_table' @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="sizeColumnName" title="规格字段" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modelName" title="版型" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="modelImagePath" title="图片" sortable width="100px">
        <template v-slot="{ row }">
          <el-popover placement="right-end" width="800" trigger="hover">
            <el-image :src="row.modelImagePath" fit="scale-down">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.modelImagePath" fit="scale-down" slot="reference" style="height:36px;">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>

        </template>
      </vxe-table-column>
      <vxe-table-column field="modelImage1Path" title="图片2" sortable width="100px">
        <template v-slot="{ row }">
          <el-popover placement="right-end" width="800" trigger="hover">
            <el-image :src="row.modelImage1Path" fit="scale-down">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.modelImage1Path" fit="scale-down" slot="reference" style="height:36px;">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>

        </template>
      </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="规格字段" field="sizeColumnID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.sizeColumnID" filterable remote reserve-keyword placeholder="请输入规格字段" :remote-method="remoteMethod1" clearable size="small">
              <el-option v-for="item in SizeColumnComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="版型绑定" field="modelID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelID" filterable remote reserve-keyword placeholder="请输入版型" :remote-method="remoteMethod2" clearable size="small">
              <el-option v-for="item in ModelComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片" field="modelImageID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelImageID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod" clearable @change="selectchange">
              <el-option v-for="item in ModelImageComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <el-tooltip class="item" effect="dark" :content=" item.label" placement="top-start">
                  <span style="float: left ;overflow:hidden;width:200px; text-overflow: ellipsis;    white-space: nowrap;   word-break:keep-all;" class="modelimageselect">{{ item.label }}</span>
                </el-tooltip>

                <span style="float: right; color: #8492a6; font-size: 13px">
                  <el-image :src="item.text" fit="fill" style="width: 100px; height: 100px">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片2" field="modelImageID1" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelImageID1" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod" clearable @change="selectchange1">
              <el-option v-for="item in ModelImage1ComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <el-tooltip class="item" effect="dark" :content=" item.label" placement="top-start">
                  <span style="float: left ;overflow:hidden;width:200px; text-overflow: ellipsis;    white-space: nowrap;   word-break:keep-all;" class="modelimageselect">{{ item.label }}</span>
                </el-tooltip>

                <span style="float: right; color: #8492a6; font-size: 13px">
                  <el-image :src="item.text" fit="fill" style="width: 100px; height: 100px">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{}"> <template #default>
            <vxe-switch v-model="selectRow.isActive" open-label="是" close-label="否"></vxe-switch>
          </template>
        </vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{}"> <template #default>
            <vxe-textarea v-model="selectRow.remark" placeholder="备注" show-word-count></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片" span="12" :item-render="{}"> <template #default>
            <el-image style="width: 200px; height: 200px" :src="imageurl" fit="fill">
            </el-image>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片2" span="12" :item-render="{}"> <template #default>
            <el-image style="width: 200px; height: 200px" :src="imageurl1" fit="fill">
            </el-image>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep, find } from 'lodash'
export default {
  name: 'mom_sizecolumnimage',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        sizeColumnID: null,
        modelID: null,
        modelImageID: null,
        modelImageID1: null,
        remark: '',
        isActive: true
      },
      imageurl: null,
      imageurl1: null,
      formRules: {
        sizeColumnID: [{ required: true, message: '请输入规格字段' }],
        modelImageID: [{ required: true, message: '请输入绑定图片' }]
        // codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
      },

      api: {
        get: '/mtm/mom_sizecolumnimage/get',
        add: '/mtm/mom_sizecolumnimage/adds',
        edit: '/mtm/mom_sizecolumnimage/updates',
        delete: '/mtm/mom_sizecolumnimage/deletes',
        ModelImageComboStoreByQuery: '/mtm/comboQuery/ModelImageComboStoreByQuery',
        SizeColumnComboStoreByQuery: '/mtm/comboQuery/SizeColumnComboStoreByQuery',
        ModelComboStoreByQuery: '/mtm/comboQuery/ModelComboStoreByQuery'

      },
      footerCompanyInfo: false,
      ModelImageComboStoreByQuery: [],
      ModelImage1ComboStoreByQuery: [],
      ModelComboStoreByQuery: [],
      SizeColumnComboStoreByQuery: []
    }
  },
  async created () {
    await this.getCombStore()
    this.remoteMethod()
    this.remoteMethod1()
    this.remoteMethod2()
    this.remoteMethod3()

    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    selectchange (row) {
      var itme = find(this.ModelImageComboStoreByQuery, function (i) { return i.value === row })
      if (itme) {
        this.imageurl = itme.text
      } else { this.imageurl = null }
    },
    selectchange1 (row) {
      var itme = find(this.ModelImage1ComboStoreByQuery, function (i) { return i.value === row })
      if (itme) {
        this.imageurl1 = itme.text
      } else { this.imageurl1 = null }
    },
    async getCombStore () {
      // await this.$api.ActionRequest(this.api.departmentCombStore).then(result => {
      //   this.departmentCombStore = result
      // })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { text: query }).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelComboStoreByQuery, { text: query }).then(result => {
        this.ModelComboStoreByQuery = result
      })
    },
    remoteMethod3 (query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImage1ComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent (row) {
      this.imageurl = row.modelImagePath
      this.imageurl1 = row.modelImage1Path
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
        this.SizeColumnComboStoreByQuery = result
        this.showEdit = true
      })
      await this.$api.ActionRequest(this.api.ModelComboStoreByQuery, { gid: row.modelID }).then(result => {
        this.ModelComboStoreByQuery = result
        this.showEdit = true
      })
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelID }).then(result => {
        this.ModelImage1ComboStoreByQuery = result
        this.showEdit = true
      })
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    // 复制
    async copyRowEvent (row, attributenames = [], code = false, codeName = false) {
      this.imageurl = row.modelImagePath
      this.imageurl1 = row.modelImage1Path
      await this.$api.ActionRequest(this.api.SizeColumnComboStoreByQuery, { gid: row.sizeColumnID }).then(result => {
        this.SizeColumnComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelComboStoreByQuery, { gid: row.modelID }).then(result => {
        this.ModelComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelID }).then(result => {
        this.ModelImage1ComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (!code && this.$utils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (!codeName && this.$utils.has(this.selectRow, 'codeName')) {
        this.selectRow.codeName = null
      }
      if (attributenames.length > 1) {
        attributenames.forEach(name => {
          this.selectRow[name] = null
        })
      }
      this.showEdit = true
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
