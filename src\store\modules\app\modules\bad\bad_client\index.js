// import { ActionRequest } from '@/api/modules/base/index'
export default {
  namespaced: true,
  state: {
    BAD_Client: {
      BAD_ClientPerson: [],
      BAD_ClientAddress: [],
      BAD_ClientShop: []
    }
  },
  actions: {
    async loadBadClient ({ state, dispatch }, { apiurl, formdata }) {
      state.BAD_Client = Object.assign(state.BAD_Client, formdata)
      // await ActionRequest(apiurl, { id: BAD_Client.id }).then(res => {
      //     state.BAD_Client = Object.assign(state.BAD_Client, res)
      // })
    },
    async getClientPerson ({ state, dispatch }) {}
  },
  mutations: {

  }
}
