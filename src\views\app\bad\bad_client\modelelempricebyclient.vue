<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar perfect custom>
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" @click="insertEvent()" v-if="menuAction.allowEdit">新增</vxe-button> -->
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelElemListID" :item-render="{}"> <template #default="{ data }">
                <el-select v-model="data.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id="ModelElemPriceByClientDetailTable" ref='master_table' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" keep-source :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, icon: 'fa fa-pencil-square-o'}"> >
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemListName" title="款式名称" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemCode" title="款式明细编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemName" title="款式明细名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemOriginalItemNo" title="原始物料号" sortable width="100"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column field="modelElemName" title="款式明细名称" sortable width="100"></vxe-table-column> -->
      <vxe-table-column field="price" title="价格" sortable width="100"></vxe-table-column>
      <vxe-table-column field="useClinetDiscount" title="客户折扣" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" :disabled="row.isCommon" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" :disabled="row.isCommon" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式明细" field="modelElemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.modelElemID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod" size="mini">
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="物料名称" field="itemID" span="12" :item-render="{}"> <template #default="{ data }">
            <el-select v-model.trim="data.itemID" filterable placeholder="面料" size="mini" remote reserve-keyword :remote-method="remoteMethod1" clearable>
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.label+'['+item.code+']'" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="价格" field="price" span="12" :item-render="{name: 'input', attrs: {type: 'number'},props: { type: 'float'}}"></vxe-form-item>
        <vxe-form-item title="使用客户折扣" field="useClinetDiscount" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'ClientModelElem',
  mixins: [detailMixins],
  data () {
    return {
      formData: {
        modelElemID: null,
        itemID: null,
        price: 0,
        useClinetDiscount: false,
        remark: '',
        clientID: this.form.id,
        isActive: true

      },
      formRules: {
        modelElemID: [{ required: true, message: '请选择款式明细' }],
        // itemID: [{ required: true, message: '请选择物料' }],
        price: [{ required: true, message: '请输入价格' }]
      },
      api: {
        get: '/mtm/mom_modelelemprice/get',
        add: '/mtm/mom_modelelemprice/adds',
        edit: '/mtm/mom_modelelemprice/updates',
        delete: '/mtm/mom_modelelemprice/deletes',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/modelElemListComboStoreByQuery'
      },
      ModelElemComboStoreByQuery: [],
      ItemComboStore: [],
      ModelElemListComboStoreByQuery: []

    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ ClientID: this.form.id, clientHasCommon: true })
    this.tableData = []
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    copyRowEvent (row, attributenames = [], code = false, codeName = false) {
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (this.$utils.has(this.selectRow, 'createBy')) {
        this.selectRow.createBy = null
      }
      if (this.$utils.has(this.selectRow, 'createID')) {
        this.selectRow.createID = null
      }
      if (this.$utils.has(this.selectRow, 'createOn')) {
        this.selectRow.createOn = null
      }
      if (this.$utils.has(this.selectRow, 'modifyBy')) {
        this.selectRow.modifyBy = null
      }
      if (this.$utils.has(this.selectRow, 'modifyID')) {
        this.selectRow.modifyID = null
      }
      if (this.$utils.has(this.selectRow, 'modifyOn')) {
        this.selectRow.modifyOn = null
      }
      // if (!code && XEUtils.has(this.selectRow, 'code')) {
      //   this.selectRow.code = null
      // }
      // if (!codeName && XEUtils.has(this.selectRow, 'codeName')) {
      //   this.selectRow.codeName = null
      // }
      if (attributenames.length > 1) {
        attributenames.forEach(name => {
          this.selectRow[name] = null
        })
      }
      this.selectRow.clientID = this.form.id
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      this.showEdit = true
    },
    // 编辑
    async editEvent (row) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      await this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }

  }
}
</script>

<style>
</style>
