<template>
  <d2-container class="modelbodylisttool">
    <split-pane :min-percent='30' :default-percent='50' split="vertical">
      <template slot="paneL">
        <model :toolbarShow="false" :showfooterCompanyInfo='false' :operationColumnShow='false' ref="model" />
      </template>
      <template slot="paneR">
        <body-list :toolbarShow="false" :showfooterCompanyInfo='false' :operationColumnShow='false' ref="bodylist" />
      </template>
    </split-pane>

    <template slot="footer">
      <vxe-toolbar perfect>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="fromData" @submit="submitEvent()">
            <vxe-form-item field="isCleared" title="是否清空已有项" :item-render="{}"> <template #default>
                <vxe-switch v-model="fromData.isCleared" size="size" open-label="是" close-label="否"></vxe-switch>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">确定</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
  </d2-container>
</template>

<script>
import Model from '@/views/app/mom/mom_model/master'
import BodyList from '@/views/app/mom/mom_bodylist/master'
export default {
  name: 'modelbodylisttool',
  components: {
    Model,
    BodyList
  },
  data () {
    return {
      fromData: {
        isCleared: false,
        modelIDs: [],
        bodyListIDs: []
      },
      api: {
        updatePlus: '/mtm/mOM_ModelBodyList/updatePlus'
      }
    }
  },
  methods: {
    async submitEvent () {
      var models = this.$refs.model.$refs.master_table.getCheckboxRecords()
      if (models.length === 0) {
        this.$message({ message: '请选择勾选版型', type: 'error' })
        return
      }
      var bodylists = this.$refs.bodylist.$refs.master_table.getCheckboxRecords()
      this.fromData.modelIDs = models.map(item => { return item.id })
      this.fromData.bodyListIDs = bodylists.map(item => { return item.id })
      await this.$api.ActionRequest(this.api.updatePlus, this.fromData).then(result => {
        this.$message({ message: '更新成功', type: 'success' })
      })
    }
  }

}
</script>

<style lang="scss" >
.modelbodylisttool {
  .d2-container-full__header {
    .is--perfect {
      overflow-y: auto !important;
    }
  }
}
</style>
