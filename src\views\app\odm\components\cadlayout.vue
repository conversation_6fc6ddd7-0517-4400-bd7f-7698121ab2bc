<template>
  <d2-container class="cadlayout">
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button status="success" @click="createCad">智慧分床排料</vxe-button>
          <vxe-button status="success" @click="DownloadETCad">下载ETCad格式数据</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="loadData()"></vxe-button>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id='cadlayout_master_table' ref='master_table' :row-class-name="rowClassName" :loading="tableLoading" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="65"></vxe-table-column>
      <vxe-table-column title="子订单" width="120" sortable>
        <template v-slot="{ row }">
          {{ row.sorderNum+'-'+row. lineNum}}
        </template>
      </vxe-table-column>

      <vxe-table-column field="groupName" title="类别" width="100" sortable></vxe-table-column>
      <vxe-table-column title="衣片组" width="100" sortable>
        <template v-slot="{ row }">
          {{ row.cadRuleCode!==null?row.cadRuleCode+':'+row. cadRuleName:''}}
        </template>
      </vxe-table-column>
      <vxe-table-column title="款式编码" width="100" sortable>
        <template v-slot="{ row }">
          {{ row.modelElemCode!==null?row.modelElemCode+':'+row. modelElemName:''}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="suffix" title="后缀" width="100" sortable></vxe-table-column>
      <vxe-table-column field="cadLayoutName" title="排料图" width="100" sortable></vxe-table-column>
      <vxe-table-column field="cadRuleTypeText" title="排料图类别" width="100" sortable></vxe-table-column>
      <vxe-table-column field="cadLayoutText" title="排料图代码" width="100" sortable></vxe-table-column>
      <vxe-table-column field="cadRuleOut" title="衣片组" width="100" sortable></vxe-table-column>
      <vxe-table-column title="货号" show-overflo width="100" sortable>
        <template v-slot="{ row }">
          {{ row.itemCode!==null?row.itemCode+':'+row. itemName:''}}
        </template>
      </vxe-table-column>
      <vxe-table-column field="finalFabric" title="客供货号" sortable></vxe-table-column>
      <vxe-table-column field="length" title="耗量" sortable></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
// import { mapState } from 'vuex'
// import { cloneDeep } from 'lodash'
import { mapState } from 'vuex'
export default {
  name: 'SorderCadLayOut',
  mixins: [detailTableMixins],
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  props: {
    sorderNumber: {
      required: true,
      type: String
    }
  },
  watch: {
    form: {

    }
  },
  data () {
    return {
      tableData: [],
      api: {
        createCad: '/mtm/oDM_SorderCadLayout/addSorderCadLaout',
        getCadStr: '/mtm/oDM_SorderCadLayout/GetCadStr',
        get: '/mtm/oDM_SorderCadLayout/get'
      },
      searchForm: {
        sorderID: this.form.id
      }
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ sorderID: this.form.id })
  },
  methods: {
    async getCombStore () {

    },
    get (form) {

    },
    async createCad () {
      await this.$api.ActionRequest(this.api.createCad, { sorderID: this.form.id, skipManual: true }).then(async res => {
        if (this.info.userType === 2) {
          this.$message({ type: 'success', message: '获取成功,请等待一分钟左右后刷新列表,如果未获取到请联系客服人员,请勿多次点击!', duration: 5000 })
        } else {
          this.$message({ type: 'success', message: '排料成功,请等待CAD返回耗量' })
        }
        this.loadData({ sorderID: this.form.id })
      })
    },
    // async createCad() {
    //   await this.$api.ActionRequest(this.api.createCad, { sorderID: this.form.id }).then(async res => {
    //     this.loadData();
    //   })
    // },
    async DownloadETCad () {
      await this.$api.ActionRequest(this.api.getCadStr, { SorderID: this.form.id }).then(res => {
        this.createJson(res)
      })
    },
    createJson (res) {
      // var data = res
      var data = JSON.stringify(res)
      // encodeURIComponent解决中文乱码
      const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(data)
      // 通过创建a标签实现
      const link = document.createElement('a')
      link.href = uri
      // 对下载的文件命名
      link.download = this.sorderNumber + '-ET-CAD.json'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

}
</script>

<style>
</style>
