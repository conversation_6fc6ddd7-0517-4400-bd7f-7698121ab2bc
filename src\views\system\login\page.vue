<template>
  <div class="home">
    <div class="header">
      <div class="companyname"> <span style="color:#9F0303;font-size: 20px;font-weight:600;">C2M智尚工场</span>
        <span style="margin-left:30px;font-size: 35px;color:#ffffff">全球高端服装云智造平台</span>
      </div>
      <div class="headerother">
        <el-row>
          <el-col :span="8" :offset="8">
            <el-link href="#" style="color:#ffffff">商城定制</el-link>
          </el-col>
          <el-col :span="8">
            <el-link href="#" target="_blank" style="color:#ffffff"> 公司官网</el-link>
          </el-col>
          <!-- <el-col :span="8">
            <el-link>联系我们</el-link>
          </el-col> -->
        </el-row>
      </div>
    </div>
    <div class="middle" v-loading="pageloading" element-loading-text="登录中请稍后.....">
      <div class="middle_item">
        <el-carousel indicator-position="outside" style="width:100%;height:100%;overflow: hidden;">
          <el-carousel-item style="height:100%;overflow: hidden;">
            <div class="bgimg bgimg1"></div>
          </el-carousel-item>
          <el-carousel-item style="height:100%">
            <div class="bgimg bgimg2"></div>
          </el-carousel-item>
          <el-carousel-item style="height:100%">
            <div class="bgimg bgimg3"></div>
          </el-carousel-item>
          <el-carousel-item style="height:100%">
            <div class="bgimg bgimg4"></div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="middle_item">
        <div class="design">
          <div class="designsvg">
            <!-- <el-link href="/login">
              <d2-icon-svg style="width:180px;height:180px" name="pencil-ruler" />
            </el-link> -->
            <div class="page-login--form">
              <el-card shadow="never">
                <el-form ref="loginForm" label-position="top" :rules="rules" :model="formLogin" size="default">
                  <el-form-item prop="username">
                    <el-input type="text" v-model="formLogin.username" placeholder="用户名">
                      <i slot="prepend" class="fa fa-user-circle-o"></i>
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="password">
                    <el-input type="password" v-model="formLogin.password" placeholder="密码">
                      <i slot="prepend" class="fa fa-keyboard-o"></i>
                    </el-input>
                  </el-form-item>

                </el-form>
              </el-card>
            </div>
          </div>
          <div class="designlabel">
            <el-link @click="submit">
              <p><strong style="font-size: 35px;">高级定制</strong>&nbsp;&nbsp;&nbsp;<span>中文/英文</span> </p>
              <!-- <p><strong style="font-size: 35px;">
                  <el-button @click="submit" class="button-login">登录</el-button>
                </strong> </p> -->
              <p>专属私人版型、款式设计 从这里开始</p>
            </el-link>
          </div>

        </div>
        <!-- <div class="mall" v-if="curHeight>850">
          <div class="mallsvg">
            <d2-icon-svg style="width:70%;height:70%" name="suits-shop" />
          </div>
          <div class="designlabel">
            <p> <strong style="font-size: 35px;">商城</strong></p>
          </div>
        </div> -->

      </div>

    </div>
    <div class="footer">

      <el-row>
        <el-col>

          <div class="footerCompany">
            <div class="footerone">
              <div class="footeronediv">
                <!-- <p><span class='footerh3'>邮箱</span></p> -->
                <p>商城定制</p>
                <!-- <p><a style="color: #fff;" href="http://www.cnjyy.cn/" target="_blank">官方网站</a></p>
                <span>©2020 温州金鸳鸯服装有限公司出品</span> -->
              </div>
              <div class="footeronediv">
                <p><span class='footerh3'>联系方式</span></p>
                <!-- <p>工装业务：张先生<a>13957740523</a></p> -->
                <!-- <p>商务、定制：罗先生<a>15057738088</a></p> -->
                <!-- <p>地址<a>浙江省温州经济技术开发区科技园兴国路7号</a></p> -->
              </div>
              <div class="footeronediv">
                <p> <span class='footerh3'>联系我们</span></p>
                <!-- <p>邮箱 <a><EMAIL></a></p>
                <p>浙江省温州经济技术开发区科技园兴国路7号</p> -->
              </div>
            </div>
            <!-- <p>温州市金鸳鸯服装有限公司&nbsp;{{company}}联合出品&nbsp;&nbsp;网站备案：<a target="_blank" style="color:white;" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=2020042226">浙ICP备2020042226号</a></p> -->
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { mapActions } from 'vuex'
import localeMixin from '@/locales/mixin.js'
import { uniqueId } from 'lodash'
export default {
  mixins: [localeMixin],
  data() {
    return {
      company: process.env.VUE_APP_Development_Company,
      curHeight: 0,
      timeInterval: null,
      pageloading: false,
      time: dayjs().format('HH:mm:ss'),
      // 快速选择用户
      dialogVisible: false,
      // 表单
      formLogin: {
        username: '',
        password: ''
      },
      // 表单校验
      rules: {
        username: [
          {
            required: true,
            message: '请输入用户名',
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur'
          }
        ]
      },
      api: {
        meun: '/sso/menu/getMeun'
      }

    }
  },
  mounted() {
    this.timeInterval = setInterval(() => {
      this.refreshTime()
    }, 1000)
  },
  beforeDestroy() {
    clearInterval(this.timeInterval)
  },
  created() {
    var _this = this
    document.onkeydown = function (e) {
      const key = window.event.keyCode
      if (key === 13) {
        _this.submit()
      }
    }
  },
  methods: {
    ...mapActions('d2admin/account', ['login']),
    // ...mapActions('d2admin/menu/getservermenus', ['headerSet']),
    // ...mapActions('d2admin/menu/asideSet', ['asideSet']),

    refreshTime() {
      this.time = dayjs().format('HH:mm:ss')
    },
    /**
     * @description 接收选择一个用户快速登录的事件
     * @param {Object} user 用户信息
     */
    handleUserBtnClick(user) {
      this.formLogin.username = user.username
      this.formLogin.password = user.password
      this.submit()
    },
    /**
     * @description 提交表单
     */
    // 提交登录信息
    async submit() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.pageloading = true
          // 登录
          // 注意 这里的演示没有传验证码
          // 具体需要传递的数据请自行修改代码
          this.login({
            authority: 'xiaomamtm',
            username: this.formLogin.username,
            password: this.formLogin.password
          }).then(b => {
            if (!b) {
              this.pageloading = false
            }
            setTimeout(() => {
              this.$router.replace(this.$route.query.redirect || '/')
              // this.pageloading = false;
              // 登录
              // this.getMenu();
            }, 2000)
            // this.pageloading = false;
            // 重定向对象不存在则返回顶层路径
          })
        } else {
          // 登录表单校验失败
          this.$message.error('请输入账号或密码')
          this.pageloading = false
        }
      })
    },
    async getMenu() {
      await this.$api.ActionRequest(this.api.meun, null, false, 'get').then(data => {
        // this.headerSet(data);
        // this.asideSet(data);
        // var menu = this.supplementPath(data)
        // console.log(menu)
        var menu = this.remove(data)
        console.log(menu)
        this.$store.commit('d2admin/menu/asideSet', menu)
        this.$router.replace(this.$route.query.redirect || '/')
      })
    },
    supplementPath(menu) {
      return menu.map(e => ({
        ...e,
        ...e ? (e.children ? delete e.children : e) : e,
        // e: e.children || delete e.children,
        path: e.path || uniqueId('d2-menu-empty-'),
        ...e.children ? { children: this.supplementPath(e.children) } : {}
      }))
    },
    remove(menu) {
      return menu.map(item => {
        if (item.children) {
          this.remove(item.children)
        } else {
          delete item.children
        }
        item.path = item.path || uniqueId('d2-menu-empty-')
        return item
      })
    }
  }
}
</script>

<style lang='scss'>
// @media screen and (min-height: 850px) {
//   .mall {
//     // display: none !important;
//     display: none ;
//   }
// }
.home {
  // 登录表单
  .page-login--form {
    height: 100%;
    width: 70%;
    display: flex;
    align-items: center;
    justify-content: center;
    // 卡片
    .el-card {
      // margin-bottom: 15px;
      background-color: #000000;
      background-color: rgba(0, 0, 0, 0.1);
    }
    // 登录按钮
    .button-login {
      width: 100%;
    }
    // 输入框左边的图表区域缩窄
    .el-input-group__prepend {
      padding: 0px 14px;
    }
    .login-code {
      height: 40px - 2px;
      display: block;
      margin: 0px -20px;
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }
    // 登陆选项
    .page-login--options {
      margin: 0px;
      padding: 0px;
      font-size: 14px;
      color: $color-primary;
      margin-bottom: 15px;
      font-weight: bold;
    }
    .page-login--quick {
      width: 100%;
    }
  }
  .el-carousel__container {
    height: 100%;
  }
  background: #fff;
  margin: 0 auto;
  text-align: center;
  overflow: hidden;
  * {
    // margin: 0;
    // padding: 0;
    text-align: center;
  }
  .header {
    width: 100%;
    height: 100px;
    line-height: 100px;
    display: flex;
    align-items: center;
    background-color: #333333;
    .companyname {
      flex-grow: 1;
    }
    .headerother {
      flex-grow: 0.5;
    }
  }
  .middle {
    width: 100%;
    position: absolute;
    top: 100px;
    bottom: 200px;
    background: url("./image/bg.jpg") no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    // .middle_item1 {
    //   display: flex;
    //   flex-direction: column;
    // }
    .middle_item {
      display: flex;
      flex-direction: column;
      .design {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        .designsvg {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-grow: 1;
        }
        .designlabel {
          flex-grow: 0.2;
        }
      }
      .mall {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        .mallsvg {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          flex-grow: 1;
        }
        .designlabel {
          flex-grow: 0.2;
        }
      }
    }
    .middle_item:nth-of-type(1) {
      flex-grow: 1;
      height: 80%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .middle_item:nth-of-type(2) {
      flex-grow: 0.3;
      height: 80%;
    }
  }
  .bgimg {
    width: 100%;
    height: 100%;

    // background-size: cover;
    // background-attachment: fixed;
    // position: fixed;
    // top: 0;
    // left: 0;
    // right: 0;
    // bottom: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }
  .bgimg1 {
    background-image: url("./image/ubg1.jpg");
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    z-index: -10;
    zoom: 1;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    background-position: center 0;
  }
  .bgimg2 {
    background-image: url("./image/ubg2.jpg");
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    z-index: -10;
    zoom: 1;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    background-position: center 0;
  }
  .bgimg3 {
    background-image: url("./image/ubg3.jpg");
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    z-index: -10;
    zoom: 1;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    background-position: center 0;
  }
  .bgimg4 {
    background-image: url("./image/ubg4.jpg");
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    z-index: -10;
    zoom: 1;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    background-position: center 0;
  }
  .footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
  }
  .footerone {
    font-size: 15px;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    .footeronediv {
      //   align-items: center;
      flex-grow: 1;
      // margin: 15px;
    }
  }
  .footerh3 {
    font-size: 20px;
    font-weight: 600;
  }
  .footerCompany {
    padding: 10px;
    text-align: center;
    background-color: #333333;
    color: #ffffff;
  }
}
</style>
