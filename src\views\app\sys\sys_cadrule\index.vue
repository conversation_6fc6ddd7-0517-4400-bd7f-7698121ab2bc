<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>

          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="cadRuleType">
              <template #default="{ data }">
                <el-select v-model="data.cadRuleType" filterable placeholder="版型系列" size="mini" clearable style="width:110px">
                  <el-option v-for="item in CadRuleTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID">
              <template #default="{ data }">
                <el-select v-model="data.groupID" filterable placeholder="类别" size="mini" clearable style="width:110px">
                  <el-option v-for="item in groupComboStore" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="gender">
              <template #default="{ data }">
                <el-select v-model="data.gender" filterable placeholder="性别" size="mini" clearable style="width:80px">
                  <el-option v-for="item in sexList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isPrint">
              <template #default="{ data }">
                <el-select v-model="data.isPrint" filterable placeholder="报表显示" size="mini" clearable style="width:100px">
                  <el-option v-for="item in boolList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="isExport">
              <template #default="{ data }">
                <el-select v-model="data.isExport" filterable placeholder="CAD导出" size="mini" clearable style="width:100px">
                  <el-option v-for="item in boolList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text">
              <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item>
              <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id="SysCadruleMasterTable" ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="cadRuleTypeText" title="分类" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="gender" title="性别" sortable :formatter="formatSex" width="100px"></vxe-table-column>
      <vxe-table-column field="code" title="CAD衣片组编码" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="CAD衣片组名称" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="pieces_S" title="平铺排料衣片数" sortable width="100px"></vxe-table-column> -->
      <vxe-table-column field="suffix" title="净版后缀" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="length" title="净版位数" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isPrint" title="报表显示" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isExport" title="CAD导出" :formatter='formatBool' sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="sequence" title="净版生成顺序" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="mix" title="组合数" sortable width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="gender" title="性别" sortable :formatter="formatSex" width="100px"></vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"> </vxe-table-column>
      <vxe-table-column title="操作" width="130px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="CAD衣片组编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="CAD衣片组名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <!-- <vxe-form-item title="平铺排料衣片数" field="pieces_S" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <vxe-form-item title="净版后缀" field="suffix" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="净版位数" field="length" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="净版生成顺序" field="sequence" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="报表显示" field="isPrint" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="CAD导出" field="isExport" span="12" :item-render="{name: '$switch'}"></vxe-form-item>

        <vxe-form-item title="衣片组分类" field="cadRuleType" span="12">
          <template #default="{ data }">
            <el-select v-model="data.cadRuleType" filterable placeholder="请选择" size="mini">
              <el-option v-for="item in CadRuleTypeComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>

        <vxe-form-item title="类别" field="groupID" span="12">
          <template #default="{ data }">
            <el-select v-model="data.groupID" filterable placeholder="类别" size="mini">
              <el-option v-for="item in groupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="性别" field="gender" span="12">
          <template #default="{ data }">
            <el-select v-model="data.gender" filterable placeholder="性别" size="mini">
              <el-option v-for="item in sexList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item title="深度复制" field="deepCopy" span="24" :item-render="{name: '$switch'}" v-if="deepCopy"></vxe-form-item>
        <vxe-form-item align="center" span="24">
          <template v-slot>
  <vxe-button type="submit" status="primary">保存</vxe-button>
  <vxe-button type="reset">重置</vxe-button>
</template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep, isEmpty } from 'lodash'
export default {
  name: 'sys_cadrule',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        // pieces_S: '',
        suffix: '',
        length: '',
        isPrint: '',
        isExport: '',
        sequence: '',
        gender: true,
        groupID: null,
        cadRuleType: null
      },

      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 1, max: 20, message: '长度在 1 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 1, max: 20, message: '长度在 1 到 20 个字符' }],
        cadRuleType: [{ required: true, message: '请选择衣片组分类' }]
      },
      deepCopy: false,
      api: {
        get: '/mtm/sys_cadrule/get',
        add: '/mtm/sys_cadrule/adds',
        edit: '/mtm/sys_cadrule/updates',
        clone: '/mtm/sys_cadrule/Clone',
        delete: '/mtm/sys_cadrule/deletes',
        groupComboStore: '/mtm/combo/groupComboStore',
        CadRuleTypeComboStore: '/mtm/combo/CadRuleTypeComboStore'
      },
      action: {
        get: true,
        add: true,
        edit: true,
        delete: true,
        print: true
      },

      footerCompanyInfo: false,
      groupComboStore: [],
      CadRuleTypeComboStore: []
    }
  },
  async created () {
    await this.getCombStore()
    // this.$utils.find(this.formItems, item => item.field === 'groupID').itemRender.options = this.groupComboStore
    // this.$utils.find(this.formItems, item => item.field === 'cadRuleType').itemRender.options = this.CadRuleTypeComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.groupComboStore).then(result => {
        this.groupComboStore = result
      })
      await this.$api.ActionRequest(this.api.CadRuleTypeComboStore).then(result => {
        this.CadRuleTypeComboStore = result
      })
    },
    // 新增
    insertEvent () {
      this.selectRow = cloneDeep(this.formData)
      this.showEdit = true
      this.deepCopy = false
    },
    // 编辑
    editEvent (row) {
      this.selectRow = cloneDeep(row)
      this.showEdit = true
      this.deepCopy = false
    },
    // 复制
    copyRowEvent (row) {
      // if (this.$utils.has(row, 'id')) {
      //   row.id = null
      // }
      this.deepCopy = true
      // if (this.$utils.has(row, 'code')) {
      //   row.code = null
      // }
      // if (this.$utils.has(row, 'codeName')) {
      //   row.codeName = null
      // }
      this.selectRow = cloneDeep(row)
      this.selectRow.deepCopy = true
      this.showEdit = true
    },
    // 提交
    submitEvent () {
      if (this.deepCopy) {
        this.$api.ActionRequest(this.api.clone, [this.selectRow]).then(result => {
          this.$XModal.message({ message: '深度复制成功', status: 'success' })
          this.loadData()
          this.showEdit = false
        })
      } else {
        if (isEmpty(this.selectRow.id) || this.selectRow.id === undefined || this.selectRow.id === null) {
          this.$api.ActionRequest(this.api.add, [this.selectRow]).then(result => {
            this.$XModal.message({ message: '新增成功', status: 'success' })
            this.loadData()
            this.showEdit = false
          })
        } else {
          this.$api.ActionRequest(this.api.edit, [this.selectRow]).then(result => {
            this.$XModal.message({ message: '保存成功', status: 'success' })
            this.loadData()
            this.showEdit = false
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
