
// import axios from 'axios'
export default {
  data () {
    return {
      sorderurl: '/fs/print/sorder1/pdf',
      sorderplusurl: '/fs/print/sorderplus1/pdf'
    }
  },
  methods: {
    pdf1 () {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length > 1) {
        this.$XModal.message({ message: '一次只能打印一个订单', status: 'error' })
        return
      }
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var baseUrl = this.mtmpai.replace('/api/', '')
      var data = { SorderId: [checks[0].id] }
      this.openpdf(this.sorderurl, data, baseUrl)
      // this.fetchAndOpenPdf(this.sorderurl, data, baseUrl)
    },
    pdf2 ({ DoubleFaced = false }) {
      var checks = this.$refs.master_table.getCheckboxRecords()
      if (checks.length < 1) {
        this.$XModal.message({ message: '请先勾选一个订单', status: 'error' })
        return
      }
      var ids = checks.map(row => { return row.id })
      var baseUrl = this.mtmpai.replace('/api/', '')
      var data = { SorderId: ids, doubleFaced: DoubleFaced }
      this.openpdf(this.sorderplusurl, data, baseUrl)
    },
    openpdf (api, data, baseUrl) {
      this.$api.ActionExcelRequest(api, data, false, 'post', baseUrl).then(res => {
        // console.log(res)
        this.openPdfWindow(res)
      })
    },
    openPdfInNewWindow (blob) {
      // 创建一个唯一的URL
      const url = window.URL.createObjectURL(blob)

      // 在新窗口中打开这个URL
      window.open(url, '_blank')
    },
    openPdfWindow (blob) {
      // 创建一个 blob URL
      const url = window.URL.createObjectURL(blob)

      // 打开一个新窗口
      const pdfWindow = window.open('', '_blank')

      if (pdfWindow) {
        // 保存 blob 对象，以便窗口刷新时使用
        pdfWindow.blob = blob

        // 在新窗口中写入内容
        pdfWindow.document.write(`
                <title>PDF Document</title>
                <iframe width="100%" height="100%" src="${url}"></iframe>
                <script>
                  // 以下代码处理窗口刷新逻辑
                  window.onload = function() {
                    if (window.blob) {
                      // 生成一个新的 blob URL
                      const pdfUrl = URL.createObjectURL(window.blob);
      
                      // 更新 iframe 的 src 属性
                      const iframe = document.querySelector('iframe');
                      if (iframe) {
                        iframe.src = pdfUrl;
                      }
      
                      // 当窗口关闭时，释放 blob URL
                      window.onunload = function() {
                        URL.revokeObjectURL(pdfUrl);
                      };
                    }
                  };
                </script>
              `)

        // 当窗口关闭时，释放 blob URL
        pdfWindow.onunload = function () {
          URL.revokeObjectURL(url)
        }
      } else {
        alert('Unable to open new window. Please allow pop-ups for this site.')
      }
    }
  }

}
