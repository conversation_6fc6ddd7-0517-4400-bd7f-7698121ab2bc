import { mapState, mapActions } from 'vuex'
export default {
  name: 'ActionMixins',
  computed: {
    ...mapState('d2admin/page', ['current']),
    ...mapState('d2admin/menu', ['aside'])
  },
  data () {
    return {
      menuAction: { allowAdd: false, allowDelete: false, allowEdit: false, allowPrint: false }
    }
  },

  async created () {
    // console.log(this.$route.path)
    // console.log(this.aside)
    await this.getCurrentMenu(this.$route.path)
    // console.log("当前页" + this.current);
  },
  methods: {
    ...mapActions('d2admin/menu', ['getMenu']),
    async getCurrentMenu (current) {
      this.menuAction = await this.getMenu(current)
      // console.log(this.menuAction)
    }
  }

}
