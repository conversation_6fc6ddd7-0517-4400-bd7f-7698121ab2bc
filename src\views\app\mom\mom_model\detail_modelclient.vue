<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-save" status="perfect" @click="saveEvent" v-if="menuAction.allowAdd">保存</vxe-button>
          <vxe-button status="perfect" @click="selectAll(true)" v-if="menuAction.allowAdd">全选</vxe-button>
          <vxe-button status="perfect" @click="selectAll(false)" v-if="menuAction.allowAdd">取消全选</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>

        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id='MomModel_master_table' ref='master_table' @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" :edit-config="{trigger: 'click', mode: 'cell',showStatus: true, icon: 'fa fa-pencil-square-o'}" keep-source>
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="selected" title="关联" width="150px" :edit-render="{name: '$select', options: boolOptions}"></vxe-table-column>
      <vxe-table-column field="clientCode" title="客户编码" sortable width="150px"></vxe-table-column>
      <vxe-table-column field="clientName" title="客户名称" sortable width="150px"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
// import { cloneDeep } from 'lodash'
export default {
  name: 'MomModelClientDetail',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        isActive: true,
        modelID: null,
        bodyListID: null,
        default: false,
        selected: true
      },
      boolOptions: [{ value: true, label: '是' }, { value: false, label: '否' }],
      api: {
        get: '/mtm/mOM_Model/GetModelClientByModelID',
        edit: '/mtm/mOM_Model/ModelClientUpdatesByModelID'

      },
      footerCompanyInfo: false

    }
  },
  watch: {
    'form.id': {
      deep: true,
      async handler (newVal, oldVal) {
        // console.log(`newVal:${newVal},oldVal:${oldVal}`)
        if (newVal !== oldVal) {
          await this.loadData(this.form)
        }
      }
    }
  },
  async created () {
    this.formData.modelID = this.form.id
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
  },
  methods: {
    saveEvent () {
      this.$api.ActionRequest(this.api.edit, this.tableData).then(result => {
        this.$XModal.message({ message: '保存成功', status: 'success' })
        this.loadData({ id: this.form.id }).then(({ data }) => {
          this.tableData = data
        })
      })
    },
    selectAll (b) {
      this.tableData.forEach(item => {
        item.selected = b
      })
    }
  }
}
</script>

<style lang="scss" >
.vxe-cell {
  margin: 1px !important;
  // background-color: red;
}
</style>
