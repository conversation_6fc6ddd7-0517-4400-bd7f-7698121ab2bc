@page {
    margin-top: 20px;
    margin-right: 20px;
    margin-bottom: 20px;
    margin-left: 20px;
    size: auto;
    font-size: 5px;
	/* @bottom-right{
		margin: 10pt 0 30pt 0;
		border-top: .25pt solid #666;
		content: "Page " counter(page);
		font-size: 9pt;
	} */
	
	
}
@page :right {
	 /* margin: 10pt auto 30pt auto; */
	/* border-top: .25pt solid #666;
	content: "Page " counter(page);
	font-size: 9pt; */
	/* content: "Page " counter(page); */
}
@media print {
    /* 打印时显示 */
    .printbtn ,.ym{
        display: none;
    }
	
}
/*pdfDom;width:841.89px; */
.pdfDom{width:796px;}
.pdfDom *{text-show:none;  box-sizing: border-box;  /* font-family: 黑体; font-size:13px; */ }
.pdfDom table{ border-collapse: collapse;}
.pdfDom .pdfHeader .table-thead,.pdfDom .pdfFooter .table-footer{z-index: 8 !important;}
.pdfDom .pdfFooter .table-footer div{z-index: 8 !important;}
/* @media print { */
    html,
    body {
        /* font-size: 10px;
        -webkit-text-size-adjust: none; */
        /* -webkit-transform: scale(0.8); */
        /* display: block;
        margin-top: 0; */
    }
    body {
        /* margin: 0;
        background-image: none;
        font-size: 10px; */
        /* width: 190mm; needed for Chrome   */
    }
    table {
        /* display: table; */
        /* page-break-after: auto; */
        /* border: 1px solid #000000; */
    }
    tr {
        /* page-break-inside: avoid; */
    }
    td {
        /* page-break-inside: auto;
        -webkit-text-size-adjust: none;
        font-size: 10px; */
        /* -webkit-transform: scale(0.8); */
    }
    thead {
        display: table-header-group;
        /* border-bottom: 1px solid #000000; */
    }
    /* tbody {
        display: block;
    } */
    tfoot {
        display: table-footer-group;
    }
    .divhiedden {
        display: none;
    }

    .container-body {
        box-shadow: inset 0 0 100px rgba(0, 0, 0, 0);
    }

    .table-bordered > tbody > tr > td,
    .table-bordered > tr > td,
    .table-bordered > tbody > tr > th,
    .table-bordered > tr > th,
    .table-bordered > tfoot > tr > td,
    .table-bordered > tfoot > tr > th,
    .table-bordered > thead > tr > td,
    .table-bordered > thead > tr > th {
        border: 1px solid #000000;
    }
    .table > tbody > tr > td,
    .table > tr > td,
    .table > tbody > tr > th,
    .table > tr > th,
    .table > tfoot > tr > td,
    .table > tfoot > tr > th,
    .table > thead > tr > td,
    .table > thead > tr > th {
     
        vertical-align: top;
    }
    .table {
        border: none;
      
    }
    .table > tr > th {
  
        vertical-align: top;
        border-top: 1px solid #000000;
    }
    .row-fluid [class*="span"] {
        min-height: 20px;
    }


  
    #pageFooter {
        display: table-footer-group;
    }
/* 
    #pageFooter:after {
        counter-increment: page;
        content: counter(page);
        left: 0;
        white-space: nowrap;
        z-index: 20px;
        -moz-border-radius: 5px;
        -moz-box-shadow: 0px 0px 4px #222;
        background-image: -moz-linear-gradient(top, #eeeeee, #cccccc);
        background-image: -moz-linear-gradient(top, #eeeeee, #cccccc);
    } */

    .main-picture {
        max-width: 100%;
        height: auto;
        display: inline-block;
    }
    /* 通常用h1标题来开始一本书的新章节。要强制标题总是处于页面开头，把page-break-before设置为always。 */
    .pageafter {
        page-break-after: avoid;
    }
    /* 为了避免标题后立即断页，使用page-break-after */
    .pagebreak {
        /* page-break-before: always; */
        /* page-break-after: always; */
    }
    .main-picture .center-block {
        border: none;
    }
    /* 为了避免断开图像和表格，使用page-break-inside属性 */
    /* table, figure { page-break-inside: avoid; } */
    .main-content {
        width: 100%;
    }
    
/* } */