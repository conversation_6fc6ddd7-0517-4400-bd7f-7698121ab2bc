<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar v-if="toolbarShow">
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="success" @click="adddetailEvent(form)" v-if="menuAction.allowEdit">添加明细</vxe-button>
          <vxe-button @click="deletesEvent()" status="danger" v-if="menuAction.allowDelete">批量删除</vxe-button>
          <vxe-button @click="replaceEvent()" status="warning" v-if="menuAction.allowAdd">批量替换字段(新增)</vxe-button>
          <vxe-button @click="replaceModelElemShowEvent()" status="warning" v-if="menuAction.allowAdd">批量替换款式明细(新增)</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="modelElemListID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod4" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemID" :item-render="{}">
              <template #default>
                <el-select v-model="searchForm.modelElemID" filterable placeholder="款式明细" size="mini" remote reserve-keyword plremoteaceholder="" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render="{}">
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template :item-render="{}">
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table id='SymImportsorderbaseMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60">
      </vxe-table-column>
      <vxe-table-column field="genderText" title="性别" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="groupText" title="分类" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemTypeText" title="类型" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemListText" title="款式" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="sysName" title="对应字段" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemText" title="款式明细" sortable width="100"></vxe-table-column>
      <vxe-table-column field="itemText" title="绑定物料" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sysName1" title="对应字段1" sortable width="100"></vxe-table-column>
      <vxe-table-column field="sysName2" title="对应字段2" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <vxe-table-column title="操作" width="150" align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="款式明细" field="modelElemID" span="12" :item-render="{}">
          <template #default="{data}">
            <el-select v-model="data.modelElemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式明细" :remote-method="remoteMethod1">
              <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="物料名称" field="itemID" span="12" :item-render="{}">
          <template #default="{data}">
            <el-select v-model="data.itemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="物料" :remote-method="remoteMethod" clearable>
              <el-option v-for="item in ItemComboStore" :key="item.value" :label="item.code+'【'+item.label+'】'+'规格:'+item.itemSize" :value="item.value"> </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="对应名称" field="sysName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="对应名称1" field="sysName1" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="对应名称2" field="sysName2" span="12" :item-render="{name: 'input'}"></vxe-form-item>

        <!-- <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item> -->
        <!-- <vxe-form-item title="类型" field="importSorderBaseGroup" span="12" :item-render="{name: '$select', options: ImportSorderBaseGroupComboStore}"></vxe-form-item> -->
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',},props: { type: 'float'}}">
        </vxe-form-item>
        <vxe-form-item title="系统组件" field="isSystem" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="addPlus" title="批量增加" width="80%" resize destroy-on-close>
      <vxe-form ref="xForm" :data="modelElem.searchForm" @submit="ModelElemloadData()" @reset="resetEvent">
        <vxe-form-item field="genderID" :item-render="{}"> <template #default="{ data }">
            <vxe-select v-model="data.searchForm.genderID" placeholder="性别" clearable>
              <vxe-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item> <vxe-form-item field="modelElemListID" :item-render="{}"> <el-select v-model="modelElem.searchForm.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod4" size="mini" clearable>
            <el-option v-for="item in ModelElemListComboStoreByQuery1" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model="modelElem.searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
        </vxe-form-item>

        <vxe-form-item :item-render="{}"> <vxe-button type="submit" status="success">查询</vxe-button>
          <vxe-button type="reset">重置</vxe-button>
          <vxe-button status="warning" @click="addPlush()">保存勾选</vxe-button>
        </vxe-form-item>
      </vxe-form>
      <vxe-table ref='modelElem_table' id="modelElem_table" height="500" :data="modelElemRableData" :custom-config="{storage: true}">
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <vxe-table-column field="groupText" title="类别" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="genderText" title="性别" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemTypeText" title="款式类别" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemBaseText" title="款式部位" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="modelElemListText" title="款式" sortable width="100px"></vxe-table-column>
        <vxe-table-column field="code" title="款式明细编码" sortable width="120px"></vxe-table-column>
        <vxe-table-column field="codeName" title="款式明细名称" sortable width="120px"></vxe-table-column>
      </vxe-table>
      <vxe-pager align="left" size="mini" :current-page.sync="modelElem.searchForm.currentPage" :page-size.sync="modelElem.searchForm.pageSize" :total="modelElem.searchForm.totalResult" @page-change="elemPageChange" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
      </vxe-pager>
    </vxe-modal>
    <vxe-modal v-model="replaceModelElemShow" :title="'批量替换款式明细(新增)' " width="500" resize destroy-on-close :loading="replaceModelElemSubmitLoading">
      <vxe-form :data="replaceModelElem" :rules="formRules1" title-align="right" title-width="100" @submit="replaceModelElemEvent">
        <vxe-form-item title="款式明细" field="modelElemID" span="24" :item-render="{}"> <el-select v-model="replaceModelElem.modelElemID" filterable placeholder="请选择" size="mini" remote reserve-keyword plremoteaceholder="款式明细" :remote-method="remoteMethod1">
            <el-option v-for="item in ModelElemComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'sym_importsorderModelElemDetail',
  mixins: [detailTableMixins],
  components: {
  },
  data () {
    return {
      modelElem: {
        searchForm: {
          genderID: true,
          text: null,
          modelElemListID: null,
          modelElemID: null,
          skipCount: 0,
          maxResultCount: 100,
          currentPage: 1,
          pageSize: 100,
          totalResult: 0
        }
      },
      modelElemRableData: [],
      addPlus: false,
      formData: {
        sysName: null,
        sysName1: null,
        sysName2: null,
        modelElemListID: null,
        modelElemID: null,

        itemID: null,
        isSystem: false,
        remark: null,
        sort: 1,
        importSorderModelElemID: this.form.id,
        isActive: true
      },
      formRules: {
        sysName: [{ required: true, message: '请添加对应字段名称' }, { min: 1, max: 50, message: '长度在 1 到 50 个字符' }]
      },
      formRules1: {
        modelElemID: [{ required: true, message: '请选择款式明细' }]
      },
      replaceModelElemShow: false,
      replaceModelElemSubmitLoading: false,
      replaceModelElem: {
        modelElemID: null
      },
      api: {
        get: '/mtm/sym_importsordermodelelemdetail/get',
        add: '/mtm/sym_importsordermodelelemdetail/adds',
        delete: '/mtm/sym_importsordermodelelemdetail/deletes',
        edit: '/mtm/sym_importsordermodelelemdetail/updates',

        detailGet: '/mtm/sym_importsordermodelelemdetail/get',
        detailAdd: '/mtm/sym_importsordermodelelemdetail/adds',
        modelElemGet: '/mtm/mom_modelelem/get',
        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      ModelElemListComboStoreByQuery1: [],
      ItemComboStore: [],
      ModelElemComboStoreByQuery: []
    }
  },
  async created () {
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    await this.getCombStore()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.ItemComboStore = result
      })
      await this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery).then(result => {
        this.ModelElemListComboStoreByQuery1 = result
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.ItemComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    remoteMethod4 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery1 = result
      })
    },
    async adddetailEvent (row) {
      // this.selectRow = cloneDeep(row)
      await this.ModelElemloadData()
      this.addPlus = true
    },
    rowClassName ({ row, rowIndex }) {
      if (!row.error) {
        console.log(row.error)
        return 'modelElemListError'
      }
      if (this.$utils.has(row, 'isActive') && !row.isActive) {
        return 'row-isActive-false'
      }
    },
    replaceEvent () {
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$message({ type: 'error', message: '请先勾选要替换的数据!' })
        return
      }
      this.$prompt('请输入对应字段', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        rows.forEach(item => {
          item.id = null
          item.sysName1 = value
        })
        console.log(rows)
        this.$api.ActionRequest(this.api.add, rows).then(result => {
          this.loadData()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        })
      })
    },
    replaceModelElemShowEvent () {
      this.replaceModelElem.modelElemID = null
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$message({ type: 'error', message: '请先勾选要替换的数据!' })
        return
      }
      this.replaceModelElemShow = true
    },
    replaceModelElemEvent () {
      this.replaceModelElemSubmitLoading = true
      const xTable = this.$refs[this.tableRef]
      var rows = xTable.getCheckboxRecords()
      if (rows.length <= 0) {
        this.$message({ type: 'error', message: '请先勾选要替换的数据!' })
        return
      }
      if (this.replaceModelElem.modelElemID === null || this.replaceModelElem.modelElemID === undefined) {
        this.$message({ type: 'error', message: '请选择款式明细!' })
        return
      }
      rows.forEach(item => {
        item.id = null
        item.modelElemID = this.replaceModelElem.modelElemID
      })
      this.$api.ActionRequest(this.api.add, rows).then(result => {
        this.loadData()
        this.replaceModelElemSubmitLoading = false
        this.replaceModelElemShow = false
      }).catch(() => {
        this.replaceModelElemSubmitLoading = false
      })
    },
    async ModelElemloadData () {
      await this.$api.ActionRequest(this.api.modelElemGet, this.modelElem.searchForm).then(result => {
        this.modelElemRableData = result.items
        this.modelElem.searchForm.totalResult = result.totalCount
      })
    },
    async elemPageChange (page) {
      this.modelElem.searchForm.maxResultCount = page.pageSize
      this.modelElem.searchForm.skipCount = (page.pageSize) * (page.currentPage - 1)
      await this.ModelElemloadData()
    },

    async addPlush () {
      const selectRecords = this.$refs.modelElem_table.getCheckboxRecords()
      var list = selectRecords.map(item => {
        return { modelElemListID: item.modelElemListID, modelElemID: item.id, isActive: true, isSystem: false, importSorderModelElemID: this.form.id }
      })
      // console.log(list)
      await this.$api.ActionRequest(this.api.detailAdd, list).then(result => {
        this.$XModal.message({ message: '新增成功', status: 'success' })
        this.addPlus = false
        this.loadData({ id: this.form.id })
      })
    },
    // 编辑
    editEvent (row) {
      this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
      })
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    },
    copyRowEvent (row) {
      this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.ItemComboStore = result
      })
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { gid: row.modelElemID }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      this.showEdit = true
    }
  }
}
</script>

<style lang="scss">
.modelElemListError {
  background-color: #ea6157;
  color: cornsilk;
}
</style>
