<template>
  <el-card shadow="always" style="min-height:600px;" class="sorderdetailbody">
    <vxe-form v-loading="EditState" element-loading-text="" element-loading-spinner="1" element-loading-background="rgba(0, 0, 0, 0.1)">
      <vxe-form-item v-for="(bodylist,index) in BodyListData" :key="index" :title="bodylist.bodyListCode+':'+bodylist.bodyListName" title-width="120" :class="(index+1)===BodyListData.length?'lastbodylist':''" :item-render="{}"> <template v-slot>
          <vxe-select v-model="bodylist.bodyID" placeholder="请选择" clearable @clear="bodylist.value=0" @change="({value})=>bodylistchange({bodylist,value})">
            <vxe-option v-for="item in bodylist.bodys" :key="item.id" :value="item.id" :label="item.code+':'+item.codeName">
              <div v-if="item.imagePath!==null&&item.imagePath!==''">
                <el-popover placement="right-end" :title="item.code+':'+item.codeName" width="400" trigger="hover">
                  <el-image style="width: 400px; height: 400px" :src="item.imagePath" fit="scale-down"></el-image>
                  <div class="table-td table-td1" slot="reference">
                    <span>{{ item.code }}:{{ item.codeName}}</span><i v-if="item.imagePath!==null&&item.imagePath!==''" style="color:#606266" class="el-icon-picture-outline"></i>
                  </div>
                </el-popover>
              </div>
              <div v-else>

                <div class="table-td table-td1">
                  <span>{{ item.code }}:{{ item.codeName }}</span>
                </div>
              </div>
            </vxe-option>
          </vxe-select>
          <el-popover ref="popover" placement="right-end" trigger="focus">
            <div slot style="color:red">
              <span>系统最大值:{{bodylist.max}} 系统最小值{{bodylist.min}}</span><br>
              <span>人工最大值:{{bodylist.allowMax}} 人工最小值{{bodylist.allowMin}}</span>
            </div>
            <el-input-number slot="reference" style="width:100px" v-model="bodylist.value" :precision="2" :step="0.1" :max="bodylist.allowMax" :min="bodylist.allowMin" size="mini" :disabled="bodylist.bodyID===null"></el-input-number>
          </el-popover>
        </template>
      </vxe-form-item>
    </vxe-form>
  </el-card>
</template>

<script>

import sorderEditState from './sordereditstate'
import { mapState } from 'vuex'
import { cloneDeep } from 'lodash'
export default {
  name: 'SorderDetailBody',
  mixins: [sorderEditState],
  props: {
    SorderDetailModel: {
      type: Object,
      requited: true
    },
    sorderStore: {
      type: Object,
      default: null
    }

  },
  watch: {
    'SorderDetailModel.modelID': {
      deep: true,
      handler: function (newVal, oldVal) {
        if (newVal !== oldVal) {
          // console.log(`特体---版型ID${newVal}`)
          this.get(newVal)
        }
      }
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  data () {
    return {
      allowMax: null,
      allowMin: null,
      BodyListData: [],
      api: {
        get: '/mtm/oDM_SorderDetailBody/get',
        sorderDetailBodyEdit: '/mtm/oDM_SorderDetailBody/modify'
      }
    }
  },
  created () {
    if (this.SorderDetailModel.modelID) {
      //   debugger;
      this.get(this.SorderDetailModel.modelID)
    }
  },
  methods: {
    bodyclear () {

    },
    get (modelid) {
      this.$api.ActionRequest(this.api.get, { modelId: modelid, sorderDetailModelID: this.SorderDetailModel.id }).then(result => {
        this.BodyListData = result
      })
    },
    bodylistchange ({ value, bodylist }) {
      var dto = bodylist.bodys.GetFirstElement('id', value)
      if (dto != null) {
        bodylist.allowMax = dto.allowMax
        bodylist.allowMin = dto.allowMin
        bodylist.max = dto.max
        bodylist.min = dto.min
        if (bodylist.allowMax < bodylist.value) {
          bodylist.value = bodylist.allowMax
        }
        if (bodylist.allowMin > bodylist.value) {
          bodylist.value = bodylist.allowMin
        }
      }
    },
    async sorderDetailBodySave () {
      if (this.EditState) {
        return this.EditState
      }
      var list = cloneDeep(this.BodyListData.filter(item => { return item.bodyListID !== null }))
      list.forEach(item => {
        delete item.bodys
      })
      if (list.lenght === 0) {
        return true
      }
      return await this.$api.ActionRequest(this.api.sorderDetailBodyEdit, list).then(async res => {
        return true
      }).catch(() => {
        return false
      })
    }
  }
}
</script>

<style lang="scss">
.sorderdetailbody {
  margin-bottom: 200px;
  overflow-x: auto;
  .lastbodylist {
    margin-bottom: 200px;
  }
}
</style>
