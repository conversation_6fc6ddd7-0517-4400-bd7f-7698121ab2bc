<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <!-- <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button> -->

          <vxe-button status="success" size="mini" @click="updateModelElemData(1)">新增【{{ModelElemState.insertCount}}】</vxe-button>
          <vxe-button status="warning" size="mini" @click="updateModelElemData(2)">更新【{{ModelElemState.updateCount}}】</vxe-button>
          <vxe-button status="danger" size="mini" @click="updateModelElemData(3)">删除【{{ModelElemState.deleteCount}}】</vxe-button>
          &nbsp;
          <el-badge :value="ModelElemState.modelElemCount-ModelElemState.count" :hidden="ModelElemState.modelElemCount===ModelElemState.count" class="item">
            <vxe-button icon="vxe-icon--download" status="primary" @click="modelElemDataAsync" v-if="menuAction.allowAdd">拉取数据【{{ModelElemState.modelElemCount}}】</vxe-button>
          </el-badge>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent">
          </vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="genderID" :item-render={}>
              <template #default>
                <vxe-select v-model="searchForm.genderID" placeholder="性别" clearable>
                  <vxe-option v-for="num in sexList" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="groupID" :item-render={}>
              <template #default>
                <vxe-select v-model="searchForm.groupID" placeholder="类别" clearable>
                  <vxe-option v-for="num in GroupComboStore" :key="num.value" :value="num.value" :label="num.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemBaseID" :item-render={}>
              <template #default>
                <el-select v-model="searchForm.modelElemBaseID" filterable remote reserve-keyword placeholder="款式部位" size="mini" :remote-method="remoteMethod1" clearable>
                  <el-option v-for="item in ModelElemBaseComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="modelElemListID" :item-render={}>
              <template #default>
                <el-select v-model="searchForm.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod2" size="mini" clearable>
                  <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
            <vxe-form-item field="text" :item-render={}>
              <template #default>
                <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
                </vxe-input>
              </template>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"><template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template></vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadBassictechnologyMasterTable' ref='master_table' height="auto" @cell-dblclick="cellDblClick" :row-class-name="rowClassName" :loading="tableLoading" @cell-click='tableCellClick' :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60"></vxe-table-column>
      <vxe-table-column field="genderID" title="性别" :formatter='formatSex' sortable width="100"> </vxe-table-column>
      <vxe-table-column field="groupText" title="类别" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemBaseCode" title="部位" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemBaseName" title="部位名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemBaseSort" title="部位顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemTypeName" title="工艺类别" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelELemListCode" title="款式编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemListName" title="款式名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemListSort" title="款式顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemCode" title="款式明细编码" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemName" title="款式明细名称" sortable width="100"></vxe-table-column>
      <vxe-table-column field="modelElemSort" title="款式明细顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100"></vxe-table-column>
      <!-- <vxe-table-column title="操作" width="150" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
             <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
        </template>
      </vxe-table-column> -->
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
export default {
  name: 'bad_basictechnology',
  mixins: [masterTableMixins],
  components: {
  },
  data () {
    return {
      formData: {
        // code: '',
        // codeName: '',
        // remark: '',
        isActive: true
      },
      ModelElemState: {
        modelElemCount: 0,
        sameCount: 0,
        insertCount: 0,
        updateCount: 0,
        deleteCount: 0
      },
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 20, message: '长度在 3 到 20 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符' }]
      },
      api: {
        get: '/mes/bad_basictechnology/get',
        add: '/mes/bad_basictechnology/adds',
        edit: '/mes/bad_basictechnology/updates',
        delete: '/mes/bad_basictechnology/deletes',
        modelelemdataAsync: '/mes/bAD_BasicTechnology/getmodelElemData',
        getModelElemDataState: '/mes/bAD_BasicTechnology/getModelElemDataState',
        updateModelElemData: '/mes/bAD_BasicTechnology/updateModelElemData',
        GroupComboStore: '/mtm/combo/groupComboStore',
        ModelElemBaseComboStoreByQuery: '/mtm/comboQuery/ModelElemBaseComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      GroupComboStore: [],
      ModelElemBaseComboStoreByQuery: [],
      ModelElemListComboStoreByQuery: [],
      footerCompanyInfo: false
    }
  },
  async created () {
    await this.getCombStore()
    await this.getModelElemDataState()
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    remoteMethod1 (query) {
      this.$api.ActionRequest(this.api.ModelElemBaseComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemBaseComboStoreByQuery = result
      })
    },
    remoteMethod2 (query) {
      this.$api.ActionRequest(this.api.ModelElemListComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemListComboStoreByQuery = result
      })
    },
    async getModelElemDataState () {
      await this.$api.ActionRequest(this.api.getModelElemDataState).then(result => {
        this.ModelElemState = result
      })
    },
    async updateModelElemData (state) {
      await this.$api.ActionRequest(this.api.updateModelElemData, { State: state }).then(async result => {
        this.$message.success('操作成功')
        await this.getModelElemDataState()
        this.loadData()
      })
    },
    async modelElemDataAsync () {
      await this.$api.ActionRequest(this.api.modelelemdataAsync).then(async result => {
        this.$message.success('同步成功')
        await this.getModelElemDataState()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
