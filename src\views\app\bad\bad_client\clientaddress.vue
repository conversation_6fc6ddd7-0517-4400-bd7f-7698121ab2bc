<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" @click="insertEvent()" v-if="menuAction.allowEdit">新增</vxe-button>
          <vxe-button status="perfect" @click="saveEvent()" v-if="menuAction.allowEdit">保存</vxe-button>
        </template>
        <template v-slot:tools>
          <!-- <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button> -->
        </template>
      </vxe-toolbar>
    </template>
    <vxe-table keep-source id="BadClientAddressDetailTable" :row-class-name="rowClassName" ref="clientAddressxTable" :edit-rules="validRules" :height="TableHeight" :data="tableData" :custom-config="{storage: true}" :edit-config="{trigger: 'click', selected: true, mode: 'cell',showStatus: true, icon: 'fa fa-pencil'}" :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="isDefault" title="默认" width="100px" :edit-render="{}">
        <template #edit="{ row }">
          <el-switch v-model="row.isDefault" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </template>
        <template #default="{ row }">
          {{ row.isDefault?'是':'否' }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="contact" title="联系人" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="tel" title="电话" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="address" title="地址" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="address1" title="地址1" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="contactDesc" title="联系人描述" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="fax" title="传真" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="mobile" title="座机" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="email" title="邮件" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="zip" title="邮编" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <!-- <vxe-table-column field="state" title="国家" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column> -->
      <vxe-table-column field="globalCountryID" title="国家" width="100px" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-select v-model="row.globalCountryID" placeholder="国家" @change="val=>countryChange(val,row)">
            <vxe-option v-for="num in GlobalCountryComboStore" :key="num.label" :value="num.value" :label="num.label"></vxe-option>
          </vxe-select>
        </template>
        <template #default="{ row }">
          {{ row.state }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="province" title="省" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="city" title="市" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="county" title="县" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="street" title="街道" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="port" title="港口" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="transport" title="运送方式" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column field="remark" title="备注" :edit-render="{name: 'input', immediate: true, attrs: {type: 'text'}}" width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="100px" show-overflow v-if="menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
  </d2-container>
</template>

<script>
import detailMixins from '@/mixins/detail_table_mixins/index'
import { unionWith } from 'lodash'
export default {
  name: 'BadClientAddress',
  mixins: [detailMixins],
  props: {

  },
  data () {
    return {
      tableRef: 'clientAddressxTable',
      sexList: [
        { value: true, label: '男' },
        { value: false, label: '女' }
      ],
      api: {
        get: '/mtm/bAD_ClientAddress/get',
        add: '/mtm/bAD_ClientAddress/adds',
        edit: '/mtm/bAD_ClientAddress/updates',
        delete: '/mtm/bAD_ClientAddress/deletes',
        GlobalCountryComboStore: '/mtm/combo/GlobalCountryComboStore'
      },
      validRules: {
        contact: [
          { required: true, message: '联系必须填写' }
        ],
        tel: [
          { required: true, message: '联系电话必须填写' }
        ],
        address: [
          { required: true, message: '地址必须填写' }
        ],
        globalCountryID: [
          { required: true, message: '请选择国家' }
        ]
      },
      GlobalCountryComboStore: []
    }
  },
  created () {
    this.loadData({ id: this.form.id })
    this.getCombStore()
    this.tableData = []
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GlobalCountryComboStore).then(result => {
        this.GlobalCountryComboStore = result
      })
    },
    countryChange (val, row) {
      var g = this.GlobalCountryComboStore.find(item => item.value == val.value)
      console.log(g)
      if (g) {
        row.state = g.codeName
      }
    },
    async fullValidEvent () {
      var xtable = this.$refs[this.tableRef]
      const errMap = await xtable.fullValidate().catch(errMap => errMap)
      if (errMap) {
        const msgList = []
        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            const { rowIndex, column, rules } = params
            rules.forEach(rule => {
              msgList.push(`第 ${rowIndex} 行 ${column.title} 校验错误：${rule.message}`)
            })
          })
        })
        this.$XModal.message({
          status: 'error',
          message: () => {
            return [
              <div class="red" style="max-height: 400px;overflow: auto;">
                {
                  msgList.map(msg => <div>{msg}</div>)
                }
              </div>
            ]
          }
        })
        return false
      } else {
        // this.$XModal.message({ status: 'success', message: '校验成功！' })
        return true
      }
    },
    async saveEvent () {
      var b = await this.fullValidEvent()
      if (b) {
        var xtable = this.$refs[this.tableRef]
        var insertData = xtable.getInsertRecords()
        var updateData = xtable.getUpdateRecords()
        if (insertData.length !== 0) {
          await this.$api.ActionRequest(this.api.add, insertData).then(res => {
            this.loadData({ id: this.form.id })
          })
        }
        if (updateData.length !== 0) {
          await this.$api.ActionRequest(this.api.edit, updateData).then(res => {
            this.loadData({ id: this.form.id })
          })
        }
      }
    },
    async insertEvent (row) {
      const record = { clientID: this.form.id }
      var xtable = this.$refs[this.tableRef]
      const { row: newRow } = await xtable.insertAt(record, row)
      await xtable.setActiveCell(newRow, 'code')
    },
    getTableData () {
      var xtable = this.$refs[this.tableRef]
      var insertData = xtable.getInsertRecords()
      var updateData = xtable.getUpdateRecords()
      var data = unionWith(insertData, updateData)
      return data
    },
    async removeEvent (row) {
      var xtable = this.$refs[this.tableRef]
      xtable.remove(row)
      if (row.id !== undefined) {
        await this.$api.ActionRequest(this.api.delete, [row]).then(res => {
          this.loadData({ id: this.form.id })
        })
      }
    }
  }
}
</script>

<style>
</style>
