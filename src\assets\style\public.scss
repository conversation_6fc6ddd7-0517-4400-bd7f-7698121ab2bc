@import '~@/assets/style/unit/color.scss';


// 工具类名统一前缀
$prefix: d2;

// 禁止用户选中 鼠标变为手形
%unable-select {
  user-select: none;
  cursor: pointer;
}

// 填满父元素
// 组要父元素 position: relative | absolute;
%full {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}

// flex 垂直水平居中
%flex-center-row {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}
%flex-center-col {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

// 将元素模拟成卡片外观
%card {
  border: 1px solid #dddee1;
  border-color: #e9eaec;
  background: #fff;
  border-radius: 4px;
  font-size: 14px;
  position: relative;
}
// .vxe-toolbar{
// overflow-x: auto;
// overflow-y: hidden;
// }
// .vxe-pulldown--panel {
//   z-index: 999 !important;
//   transform-origin: center top;
// }
// .my-dropdown1{
//   z-index: 999 !important;
//   transform-origin: center top;
// }