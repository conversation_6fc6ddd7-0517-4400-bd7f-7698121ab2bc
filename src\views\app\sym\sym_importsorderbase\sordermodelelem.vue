<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <!-- <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button> -->
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增 </vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable>
              </vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>

    </template>

    <vxe-table id='SymImportsordermodelelemDetailTable' ref='master_table' :loading="tableLoading" height="auto" @cell-click='tableCellClick' :row-class-name="rowClassName" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="checkbox" width="60"></vxe-table-column>
      <!-- <vxe-table-column type="expand" width="35" class-name="expandclass">
        <template v-slot:content="{ row, rowIndex }">
          <sorder-model-elem-detail :form="row" :key="rowIndex" />
        </template>
      </vxe-table-column> -->
      <vxe-table-column field="code" title="对应编码" width="100"> </vxe-table-column>
      <vxe-table-column field="codeName" title="中文解释" width="100"> </vxe-table-column>
      <vxe-table-column field="isDefault" title="默认项?" :formatter='formatBool' sortable width="100"> </vxe-table-column>
      <vxe-table-column field="isSystem" title="系统字段?" :formatter='formatBool' sortable width="100"> </vxe-table-column>
      <vxe-table-column field="detailCount" title="明细数量" width="100"> </vxe-table-column>
      <!-- <vxe-table-column field="modelElemListCode" title="款式编码" sortable width="100"> </vxe-table-column>
      <vxe-table-column field="modelElemListCodeName" title="款式名称" sortable width="100"> </vxe-table-column> -->
      <vxe-table-column field="remark" title="备注" width="100"> </vxe-table-column>
      <vxe-table-column field="sort" title="顺序" sortable width="100"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" width="100px"> </vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100">
      </vxe-table-column>
      <!-- <vxe-table-column title="添加" width="100" align="center">
        <template v-slot="{ row }">
          <vxe-button icon="fa fa-plus" status="success" @click="adddetailEvent(row)" v-if="menuAction.allowEdit">添加明细</vxe-button>
        </template>
      </vxe-table-column> -->
      <vxe-table-column title="操作" width="180" align="center" v-if="operationColumnShow&&(menuAction.allowEdit||menuAction.allowDelete)">
        <template v-slot="{ row }">
          <vxe-button @click="selectRowShowEvent(row)" status="success">查看明细</vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="对应编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="中文解释" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <!-- <vxe-form-item field="modelElemListID" :item-render="{}" span="24">
          <template #title>
            <el-tooltip class="item" effect="dark" content="只用于订单导出Excel模板中需要绑定款式,其他类型模板不需要绑定!" placement="top-start">
              <span icon='el-icon-info'>款式<i class="el-icon-info"></i></span>
            </el-tooltip>
          </template>
          <template #default>
            <el-select v-model="selectRow.modelElemListID" filterable remote reserve-keyword placeholder="款式" :remote-method="remoteMethod2" size="mini" clearable>
              <el-option v-for="item in ModelElemListComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item> -->
        <vxe-form-item title="系统字段?" field="isSystem" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="系统默认?" field="isDefault" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="顺序" field="sort" span="12" :item-render="{name: 'input', attrs: {type: 'number',}}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="8" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template v-slot>
            <vxe-button type="submit" status="primary">保存</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>

    <vxe-modal v-model="selectRowShow" title="明细数据" width="80%" height="50%" resize show-zoom destroy-on-close>
      <sorder-model-elem-detail v-if="selectRowShow" :form="selectRow" />
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import SorderModelElemDetail from './sordermodelelemdetail.vue'
// import { cloneDeep } from 'lodash'
export default {
  name: 'SymImportSorderModelElem',
  mixins: [detailTableMixins],
  components: {
    SorderModelElemDetail
  },
  data () {
    return {

      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        importSorderBaseID: this.form.id,
        isSystem: false,
        sort: 1,
        isDefault: false
      },
      selectRowShow: false,
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 50, message: '长度在 2 到 50 个字符' }]
      },
      api: {
        get: '/mtm/sym_importsordermodelelem/get',
        add: '/mtm/sym_importsordermodelelem/adds',
        edit: '/mtm/sym_importsordermodelelem/updates',
        delete: '/mtm/sym_importsordermodelelem/deletes',

        ModelElemComboStoreByQuery: '/mtm/comboQuery/ModelElemComboStoreByQuery',
        ModelElemListComboStoreByQuery: '/mtm/comboQuery/ModelElemListComboStoreByQuery'
      },
      ModelElemComboStoreByQuery: []

    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    loadChildrenMethod ({ row }) {
      // 异步加载子节点
      return new Promise(resolve => {
        setTimeout(() => {
          const childs = [
            { id: row.id + 100000, name: row.name + 'Test45', type: 'mp4', size: null, date: '2021-10-03', hasChild: true },
            { id: row.id + 150000, name: row.name + 'Test56', type: 'mp3', size: null, date: '2021-07-09', hasChild: false }
          ]
          resolve(childs)
        }, 500)
      })
    },
    selectRowShowEvent (row) {
      this.selectRow = row
      this.selectRowShow = true
    },
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    },
    // tableCellClick({ row, rowIndex, column, columnIndex }) {
    //   if (column.title === '操作') { return }
    //   this.selectRow = cloneDeep(row)
    //   const xTable = this.$refs[this.tableRef]
    //   xTable.setTreeExpand(row, true)
    // },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ModelElemComboStoreByQuery, { text: query }).then(result => {
        this.ModelElemComboStoreByQuery = result
      })
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
