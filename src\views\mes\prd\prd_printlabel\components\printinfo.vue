<template>
  <d2-container>

    <div id="myPrint" class="myPrint">
      <hr style="border:1px dashed #000" />
      <table style="width:100%">
        <tr>
          <td class="firsttd">
            <div class="printsize ">系统编号:<span><strong style="font-size: 16px;"> {{formdata.sorderNum}}</strong></span></div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize">客户:{{formdata.clientName}}</div>
          </td>
        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize">
              <span class="firstspan"> 货号:{{formdata.clientPersonName}} </span>
              <span class="firstspan"> {{formdata.workStecationName}} </span>
            </div>
          </td>

        </tr>
        <tr>
          <td class="firsttd">
            <div class="printsize">版号:{{formdata.modelCode}}</div>
          </td>
        </tr>

        <template v-for="(item1,index1) in elemsData">
          <tr :key="index1">
            <td class="firsttd">
              <div class="printsize">
                <template v-for="(item2,index2) in item1">
                  <span :key="index2" class="firstspan"> {{item2.label}}:{{item2.value}}</span>
                </template>
                <!-- <span> {{formdata.elems[0].label}}:{{formdata.elems[0].value}} </span> -->
              </div>
            </td>
          </tr>
        </template>
        <template v-for="(item1,index1) in sizesData">
          <tr :key="index1+':'+item1.label">
            <td class="firsttd">
              <div class="printsize">
                <template v-for="(item2,index2) in item1">
                  <span :key="index2+':'+index1" class="firstspan"> {{item2.label}}:{{item2.value}}</span>
                </template>
                <!-- <span> {{formdata.elems[0].label}}:{{formdata.elems[0].value}} </span> -->
              </div>
            </td>
          </tr>
        </template>

        <!-- <tr>
          <td class="firsttd">
            <div class="printsize"> <span class="firstspan">下摆:</span><span>袖肥:</span> </div>
          </td>
        </tr> -->
        <tr>
          <td style="text-align: center;">
            <img id="barcode" style="width:90%" />
          </td>
        </tr>
        <tr>
          <td>
            <div style="text-align: center;"><strong>{{formdata.serialNumber}}</strong></div>
          </td>
        </tr>
      </table>
      <hr style="border:1px dashed #000" />
    </div>
    <div id='printimage' style="display:none">
      <img :src="imgUrl" style="width:200px; " />
    </div>
    <template slot="footer">
      <!-- <vxe-button status="info" content="打印" @click="printEvent1"></vxe-button> -->
      <vxe-button status="info" content="打印" @click="toImage"></vxe-button>
      <!-- <vxe-button status="info" content="打印2" @click="print"></vxe-button> -->
    </template>
  </d2-container>
</template>

<script>
// import html2canvas from 'html2canvas'
import html2canvas from './html2canvas.min.js'
import JsBarcode from 'jsbarcode'
export default {
  name: 'print',
  props: {
    selectRow: {
      type: Object,
      default: null
    }
  },
  watch: {
    'formdata.elems': {
      deep: true,
      handler: function (newval, oldval) {
        if (newval.length > 0) {
          var list = []
          list.push({ label: '规格', value: this.formdata.sizeCode })
          list = this.$utils.union(list, newval)
          this.elemsData = this.$utils.chunk(list, 2)
        }
      }
    },
    'formdata.sizes': {
      deep: true,
      handler: function (newval, oldval) {
        if (newval.length > 0) {
          this.sizesData = this.$utils.chunk(newval, 2)
        }
      }
    }
  },
  data () {
    return {
      num: '201200020002',
      imgUrl: null,
      api: {
        get: '/mes/pRD_ProductionPlanDetail/getPrintLabelInfo'
      },
      formdata: {
        clientName: null,
        clientPersonName: null,
        elems: [],
        groupName: null,
        modelName: null,
        serialNumber: null,
        sizeCode: null,
        sorderNum: null,
        sizes: []
      },
      elemsData: [],
      sizesData: []

    }
  },
  created () {
    // console.clear()
    this.get(this.selectRow.id)
    // 不要在create时调用jsbarcode库，此时相关DOM还未加载。
  },
  mounted () {

  },
  methods: {
    toImage () {
      this.imgUrl = null
      var shareContent = document.getElementById('myPrint')// 需要截图的包裹的（原生的）DOM 对象
      var self = this
      html2canvas(shareContent, {
        background: '#ffffff', // 一定要添加背景颜色，否则出来的图片，背景全部都是透明的
        dpi: 1800,
        onrendered: function (image) {
          const url = image.toDataURL()
          self.imgUrl = url
          self.$nextTick(() => {
            const divEl = document.getElementById('printimage')
            self.$XPrint({
              sheetName: '票头',
              content: divEl.innerHTML
            })
            // self.$parent.updateState();
            self.$emit('updateState')
          })
        }
      })
    },

    printEvent1 () {
      const printStyle = `
              table {
                display: inline-block;
                font-size: 8x;
                -webkit-transform-origin-x: 0;
                font-weight:500;
              }
              .firstspan {
                width: 60%; display: inline-block;
                }
              .printsize {
                width: 100%;
                font-size: 8px;
                -webkit-transform: scale(0.8);
                -webkit-transform-origin-x: 0;
                padding-left: 20px;
            }
              `
      const divEl = document.getElementById('printimage')
      this.$XPrint({
        sheetName: '打印票头',
        style: printStyle,
        content: divEl.innerHTML
      })
    },
    printEvent () {
      const printStyle = `
              table {
                display: inline-block;
                font-size: 8x;
                -webkit-transform-origin-x: 0;
                font-weight:500;
                color: #000000; // 修改表格默认颜色
                font-family: "Microsoft YaHei",微软雅黑,"MicrosoftJhengHei",华文细黑,STHeiti,MingLiu; // 修改表格默认字体
              }
              .firstspan {
                width: 60%; display: inline-block;
                }
              .printsize {
                width: 100%;
                font-size: 8px;
                -webkit-transform: scale(0.8);
                -webkit-transform-origin-x: 0;
                padding-left: 15px;
            }
              `
      const divEl = document.getElementById('myPrint')
      this.$XPrint({
        sheetName: '打印票头',
        style: printStyle,
        content: divEl.innerHTML
      })
    },
    print () {
      var url = this.mesapi.replace('/api/', '')
      window.open(`${url}/fs/print/productionplanlabel/pdf?num=111`, '_blank')
    },
    createBarCode () {
      this.$nextTick(() => {
        // 生成条形码
        JsBarcode(
          '#barcode',
          this.formdata.serialNumber,
          {
            displayValue: false, // 是否在条形码下方显示文字
            // format: "pharmacode",  //条形码的格式
            // lineColor: "#0aa",  //线条颜色
            //  width: 2, //线宽
            height: 60 // 条码高度
            // margin:15,
            // fontSize:20
            // displayValue: false //是否显示文字信息
          }
        )
      })
    },
    async get (id) {
      await this.$api.ActionRequest(this.api.get, { id: id }).then(result => {
        this.formdata = result
        this.createBarCode()
      })
    },
    printClick () {

    }

  }
}
</script>

<style lang="scss">
.myPrint {
  width: 220px;
  color: black;
  font-weight: 900 !important;
  //   border: 1px solid red;
  //   font-size: 8px;
  //   font-size: 12px;
  table {
    display: inline-block;
    font-size: 8x;
    -webkit-transform-origin-x: 0;
    font-weight: 500;
  }
  .firstspan {
    width: 47%;
    display: inline-block;
  }
  .printsize {
    width: 100%;
    font-size: 12px;
    // -webkit-transform: scale(0.9);
    -webkit-transform-origin-x: 0;
    padding-left: 10px;
  }
  .printimage {
    display: none !important;
  }
}
</style>
