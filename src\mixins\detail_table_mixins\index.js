
import baseTableMixins from '../base_table_mixins/index'
export default {
  mixins: [baseTableMixins],
  props: {
    form: { type: Object, default: null },
    masterSeach: { type: Object, default: null }
  },
  data () {
    return {
      footerCompanyInfo: this.showfooterCompanyInfo
    }
  },
  watch: {
    // "form.id":{
    //   deep:true,
    //   async handler(newVal,oldVal){
    //     console.log(`newVal:${newVal},oldVal:${oldVal}`)
    //     console.log(this.api.get)
    //     if(newVal!==oldVal){
    //       await this.loadData(this.form)
    //     }
    //   }
    // }
  },

  created () {

  },
  methods: {
    // 数据加载
    async loadData (form = null) {
      return new Promise((resolve, reject) => {
        this.submitLoading = true
        this.tableLoading = true
        if (form != null) {
          this.searchForm = Object.assign(this.searchForm, form)
        }
        this.$api.ActionRequest(this.api.get, this.searchForm).then(result => {
          this.tableData = result.items
          this.searchForm.totalCount = result.totalCount
          this.submitLoading = false
          this.tableLoading = false
          resolve({ data: result.items })
        }).catch(() => {
          this.tableLoading = false
        })
        this.submitLoading = false
      })
    },
    goback () {
      this.$emit('nextpage', { pagename: 'master', data: {}, masterSeach: this.masterSeach, keepalive: true })
    }
  }
}
