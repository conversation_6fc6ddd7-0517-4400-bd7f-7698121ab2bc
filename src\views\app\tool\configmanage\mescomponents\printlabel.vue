<template>
  <vxe-form title-align="right">
    <template v-for="(item,index) in data">
      <vxe-form-item :title="item.codeName" field="qty" :key="index" span="24" :item-render="{}">
        <template #default>
          <vxe-input v-model="item.qty" placeholder="打印数量" type="number"></vxe-input>
        </template>
      </vxe-form-item>
    </template>
    <vxe-form-item align="center" span="24" :item-render="{}">
      <template #default>
        <vxe-button status="primary" @click="submitEvent">保存</vxe-button>
      </template>
    </vxe-form-item>
  </vxe-form>
</template>

<script>
export default {
  name: 'PrintLabel', // 票头打印
  props: {
    success: {
      type: Function
    }
  },
  data () {
    return {
      api: {
        get: '/mes/conifgService/getPrintLabelPrintSetting',
        edit: '/mes/conifgService/updatePrintLabelPrintSetting',
        GroupComboStore: '/mtm/combo/groupComboStore'
      },
      data: []
    }
  },
  created () {
    this.get()
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    async get () {
      this.data = []
      await this.$api.ActionRequest(this.api.GroupComboStore).then(groups => {
        this.$api.ActionRequest(this.api.get).then(result => {
          console.log(groups)
          groups.forEach(element => {
            var code = element.label.split(':')[0]
            var codeName = element.label.split(':')[1]
            var dto = result.GetFirstElement('groupID', element.value)
            var obj = {}
            if (dto == null) {
              obj.codeName = codeName
              obj.code = code
              obj.groupID = element.value
              obj.qty = 1
            } else {
              obj.codeName = codeName
              obj.code = code
              obj.groupID = element.value
              obj.qty = dto.qty
            }
            this.data.push(obj)
          })
        })
      })
    },
    submitEvent () {
      this.$api.ActionRequest(this.api.edit, this.data).then(result => {
        this.$XModal.message({ message: '修改成功', status: 'success' })
        this.success()
      })
    }
  }
}
</script>

<style>
</style>
