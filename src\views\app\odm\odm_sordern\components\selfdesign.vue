<template>
  <d2-container type="card">
    <template slot="header">
      <el-radio-group v-model="GroupID" @change="groupChange" size="mini">
        <el-radio-button v-for="(item,index) in GroupComboStore" :label="item.value" :key="index">{{item.label}}</el-radio-button>
      </el-radio-group>
      <el-button style="float: right;" type="primary" :loading="loading" @click="groupChange(GroupID)" size="mini">重置</el-button>
    </template>
    <el-card class="box-card selfdesign" shadow="always">
      <div>
        <el-row>
          <el-col :md="6" :lg="4" :xl="3">
            <el-row>
              <el-col>
                <el-steps :active="modelElemListActive" finish-status="success" direction="vertical">
                  <template v-for="(elemlist,index) in (ModelElemListData)">
                    <el-step :key="index+1">
                      <template v-slot:title>
                        <el-button type="text" @click="modelElemTitleClick(index+1)"> {{elemlist.modelElemListCode+':'+elemlist.modelElemListName}}</el-button><span style="color:red">&nbsp;&nbsp;&nbsp;{{elemlist.modelElem.length}}</span>
                      </template>
                    </el-step>
                  </template>
                </el-steps>
              </el-col>
              <el-col>
                <div class="block">
                  <!-- <span class="demonstration">{{ModelData==null?"":ModelData.code+':'+ModelData.codeName}}</span> -->
                  <el-image style="width: 150px; height: 150px" fit="fill" class="modelElemImage" :src="ModelData==null?null:ModelData.imagePath" lazy slot="reference">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :md="18" :lg="20" :xl="21">
            <template v-for="(elemlist,index) in (ModelElemListData)">
              <el-row :key="index" v-if='((index+1)==modelElemListActive)'>
                <el-radio-group v-model="elemlist.modelElemID" @change="val=>elemChange(true,val,elemlist)">
                  <el-radio v-for="(elem) in elemlist.modelElem" :label="elem.modelElemID" :key="elem.modelElemID">
                    <el-popover placement="right" width="400" trigger="hover" :open-delay="1000">
                      <el-image style="width: 400px; height: 400px" fit="fill" class="modelElemImage" :src="elem.imagePath">
                      </el-image>
                      <el-image style="width: 150px; height: 150px" fit="fill" class="modelElemImage" :src="elem.imagePath" lazy slot="reference">
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </el-popover>
                    <el-tooltip class="item" effect="dark" :content="elem.modelElemCode+':'+elem.modelElemName" placement="right">
                      <span class="modelelemtext">{{elem.modelElemName}}</span>
                    </el-tooltip>
                  </el-radio>
                </el-radio-group>
              </el-row>
            </template>
          </el-col>

        </el-row>

      </div>
    </el-card>
    <template slot="footer">
      <!-- <el-button @click="next('previous')" type="warning">上一步</el-button>
      <el-button @click="next('next')" type="success">下一步</el-button> -->
      <span>{{ModelData==null?"":ModelData.code+':'+ModelData.codeName}}</span>
      <vxe-button status="success" style="float:right" content="成功颜色" size="mini" @click="setModel">确定</vxe-button>
    </template>
  </d2-container>
</template>

<script>
export default {
  name: 'SelfDesign', // 自主设计
  props: {
    ClientID: {
      type: String
    }
  },
  data () {
    return {
      GroupComboStore: [],
      ModelElemListData: [],
      api: {
        get: '/mtm/oDM_Sorder/selfDesign',
        getmodel: '/mtm/oDM_Sorder/selfDesignModel',
        groupComboStore: '/mtm/combo/groupComboStore'
      },
      modelElemListActive: 1,
      //   activeNames: [],
      GroupID: null,
      ModelData: null,
      loading: true
    }
  },
  async created () {
    await this.getCombStore()
  },
  methods: {
    async modelElemTitleClick (key) {
      console.log(key)
      if ((key - this.modelElemListActive) > 1) {
        this.$notify({
          title: '错误',
          message: '不能越级选择',
          type: 'error'
        })
        return
      }
      var modelElemList = this.ModelElemListData[this.modelElemListActive - 1]
      if (modelElemList.modelElemID == null && this.modelElemListActive < key) {
        this.$notify({
          title: '错误',
          message: `${modelElemList.modelElemListCode}:${modelElemList.modelElemListName}未选择,不能越级选择`,
          type: 'error'
        })
        return
      }
      this.modelElemListActive = key
      await this.elemChange(false)
    },
    next () {
      if (this.modelElemListActive++ >= this.ModelElemListData.length) {
        this.modelElemListActive = this.ModelElemListData.length
      }
    },

    async getCombStore () {
      this.loading = true
      await this.$api.ActionRequest(this.api.groupComboStore).then(async result => {
        this.GroupComboStore = result
        this.GroupID = result[0].value
        await this.groupChange(result[0].value)
        this.loading = false
      })
    },
    async groupChange (groupId = null) {
      this.modelElemListActive = 0
      this.ModelElemListData = []
      await this.elemChange()
      if (groupId != null) {
        this.GroupID = groupId
      }
    },
    async elemChange (gonext = true, modelelem, modelElemList) {
      // console.log('modelElemListActive--' + this.modelElemListActive)
      for (let index = 0; index < this.ModelElemListData.length; index++) {
        // const element = this.ModelElemListData[index]
        if (gonext) {
          if ((index) > this.modelElemListActive) {
            this.ModelElemListData[index].modelElemID = null
          }
        } else {
          if (index > this.modelElemListActive - 2) {
            this.ModelElemListData[index].modelElemID = null
          }
        }
      }
      // this.ModelElemListData.forEach(item => {
      //   if (item.marketSeq > this.modelElemListActive && gonext) {
      //     item.modelElemID = null
      //   }
      //   if (!gonext && item.marketSeq >= this.modelElemListActive) {
      //     item.modelElemID = null
      //   }
      // })
      this.loading = true
      var data = this.ModelElemListData.filter(a => { return a.modelElemID != null })
      await this.$api.ActionRequest(this.api.get, { GroupID: this.GroupID, ModelElemList: data, ClientID: this.ClientID }).then(async result => {
        this.ModelElemListData = result
        this.loading = false
      })
      if (gonext) {
        // 推断版型
        await this.getModel()
        this.next()
      }
    },
    async getModel () {
      var data = this.ModelElemListData.filter(a => { return a.modelElemID != null })
      if (data.length === 0) {
        return
      }
      this.loading = true
      await this.$api.ActionRequest(this.api.getmodel, { GroupID: this.GroupID, ModelElem: data }).then(async result => {
        this.ModelData = result
        this.$notify({
          title: '成功',
          message: `版型${result.codeName}`,
          type: 'success'
        })
        this.loading = false
      }).catch(() => {
        this.ModelData = null
        this.loading = false
      })
    },

    setModel () {
      if (this.ModelData === null) {
        this.$notify({
          title: '失败',
          message: '未推算出任何版型,请重新选择！',
          type: 'error'
        })
        return
      }
      var checkedModelElems = this.ModelElemListData.filter(item => { return item.modelElemID != null }).map(item => { return item.modelElemID })
      this.ModelData.checkedModelElems = checkedModelElems
      this.$emit('selectModel', this.ModelData)
      this.$emit('selfDesignShowEvent')
    }

  }
}
</script>

<style lang="scss">
.selfdesign {
  .el-tabs--left .el-tabs__item.is-left {
    text-align: center;
  }
  .modelElemImage {
    // border: 1px solid red;
    margin-bottom: 20px;
  }
  .el-radio {
    margin-bottom: 10px;
  }
  .el-radio__inner {
    position: relative;
    left: 90px;
  }
  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 30px;
  }
  .modelelemtext {
    display: block;
    width: 150px;
    // position: absolute;
    bottom: 2px;
    // left: 50%;
    // right: 50%;
    white-space: normal;
    margin-left: 25px;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
