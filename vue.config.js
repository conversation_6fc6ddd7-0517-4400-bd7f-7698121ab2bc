const argv = process.argv
const modeIndex = argv.indexOf('--mode')
const mode = modeIndex > -1 ? argv[modeIndex + 1] : 'development'

console.log('Current NODE_ENV:', process.env.NODE_ENV)
console.log('Detected mode from argv:', mode)

const CompressionWebpackPlugin = require('compression-webpack-plugin')
const VueFilenameInjector = require('@d2-projects/vue-filename-injector')
const ThemeColorReplacer = require('webpack-theme-color-replacer')
const forElementUI = require('webpack-theme-color-replacer/forElementUI')
const cdnDependencies = require('./dependencies-cdn')
const { chain, set, each } = require('lodash')

// 拼接路径
const resolve = dir => require('path').join(__dirname, dir)

// 增加环境变量
process.env.VUE_APP_VERSION = require('./package.json').version
process.env.VUE_APP_BUILD_TIME = require('dayjs')().format('YYYY-M-D HH:mm:ss')

// 基础路径 注意发布之前要先修改这里
const publicPath = process.env.VUE_APP_PUBLIC_PATH || '/'

// 设置不参与构建的库
const externals = {}
cdnDependencies.forEach(pkg => { externals[pkg.name] = pkg.library })

// 引入文件的 cdn 链接
const cdn = {
  css: cdnDependencies.map(e => e.css).filter(e => e),
  js: cdnDependencies.map(e => e.js).filter(e => e)
}

// 多页配置，默认未开启，如需要请参考 https://cli.vuejs.org/zh/config/#pages
const pages = undefined
// const pages = {
//   index: './src/main.js',
//   subpage: './src/subpage.js'
// }
const SSOApi = process.env.VUE_APP_USER_API
const MTMApi = process.env.VUE_APP_API
const MESApi = process.env.VUE_APP_MESAPI

module.exports = {
  // 根据你的实际情况更改这里
  publicPath,
  lintOnSave: true,
  devServer: {
    port: 8080,
    open: true,
    publicPath, // 和 publicPath 保持一致
    // disableHostCheck: process.env.NODE_ENV === 'development', // 关闭 host check，方便使用 ngrok 之类的内网转发工具
    proxy: {
      '/api/sso': {
        target: SSOApi,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/api/sso': '/api/sso'
        },
        // 此处开启 https
        https: true
      },
      '/api/mes': {
        target: MESApi,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/api/mes': '/api/mes'
        },
        // 此处开启 https
        https: true
      },
      '/api/mtm': {
        target: MTMApi,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/api/mtm': '/api/mtm'
        },
        // 此处开启 https
        https: true
      }
    }

  },

  css: {
    loaderOptions: {
      // 设置 scss 公用变量文件
      sass: {
        prependData: '@import \'~@/assets/style/public.scss\';'
        // silenceDeprecations: ['legacy-js-api', 'color-functions'],
      }
    }
  },
  transpileDependencies: ['json-editor-vue'],
  pages,
  configureWebpack: config => {
    const plugins = [
      // gzip
      new CompressionWebpackPlugin({
        filename: '[path].gz[query]',
        test: new RegExp('\\.(' + ['js', 'css'].join('|') + ')$'),
        threshold: 10240,
        minRatio: 0.8,
        deleteOriginalAssets: false
      })
    ]
    if (['production', 'staging', 'preview'].includes(mode))
    // if (['production', 'staging', 'preview'].includes(process.env.VUE_APP_ELEMENT_COLOR))
    {
      return {
        plugins,
        externals: externals
      }
    } else {
      return {
        plugins
      }
    }
  },
  // 默认设置: https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-service/lib/config/base.js
  chainWebpack: config => {
    /**
     * 添加 CDN 参数到 htmlWebpackPlugin 配置中
     * 已适配多页
     */
    const htmlPluginNames = chain(pages).keys().map(page => 'html-' + page).value()
    const targetHtmlPluginNames = htmlPluginNames.length ? htmlPluginNames : ['html']
    each(targetHtmlPluginNames, name => {
      config.plugin(name).tap(options => {
        // set(options, '[0].cdn', process.env.NODE_ENV === 'production' ? cdn : [])
        // set(options, '[0].cdn', process.env.NODE_ENV === 'production' ? cdn : [])
        set(options, '[0].cdn', cdn)
        return options
      })
    })

    /**
     * 删除懒加载模块的 prefetch preload，降低带宽压力
     * https://cli.vuejs.org/zh/guide/html-and-static-assets.html#prefetch
     * https://cli.vuejs.org/zh/guide/html-and-static-assets.html#preload
     * 而且预渲染时生成的 prefetch 标签是 modern 版本的，低版本浏览器是不需要的
     */
    config.plugins
      .delete('prefetch')
      .delete('preload')
    // 解决 cli3 热更新失效 https://github.com/vuejs/vue-cli/issues/1559
    config.resolve
      .symlinks(true)
    config
      .plugin('theme-color-replacer')
      .use(ThemeColorReplacer, [{
        fileName: 'css/theme-colors.[contenthash:8].css',
        matchColors: [
          ...forElementUI.getElementUISeries(process.env.VUE_APP_ELEMENT_COLOR) // Element-ui主色系列
        ],
        externalCssFiles: ['./node_modules/element-ui/lib/theme-chalk/index.css'], // optional, String or string array. Set external css files (such as cdn css) to extract colors.
        changeSelector: forElementUI.changeSelector
      }])
    config
      // 开发环境 sourcemap 不包含列信息
      .when(process.env.NODE_ENV === 'development',
        config => config.devtool('cheap-source-map')
      )
      // 预览环境构建 vue-loader 添加 filename
      .when(
        process.env.VUE_APP_SCOURCE_LINK === 'TRUE',
        config => VueFilenameInjector(config, {
          propName: process.env.VUE_APP_SOURCE_VIEWER_PROP_NAME
        })
      )
    // split chunks
    config.when(['production', 'staging', 'preview'].includes(mode),
      // config.when(['production', 'staging', 'preview'].includes(process.env.NODE_ENV),
      config => {
        config.optimization.splitChunks({
          chunks: 'all',
          maxSize: 250000, // 250kb
          minSize: 100000, // 100kb
          cacheGroups: {
            libs: {
              name: 'chunk-libs',
              test: /[\/]node_modules[\/]/,
              priority: 10,
              chunks: 'initial' // only package third parties that are initially dependent
            },
            elementUI: {
              name: 'chunk-elementUI', // split elementUI into a single package
              priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs
              test: /[\/]node_modules[\/]_?element-ui(.*)/ // in order to adapt to cnpm
            },
            vxeTable: {
              name: 'chunk-vxeTable',
              priority: 20,
              test: /[\/]node_modules[\/]_?vxe-table(.*)/
            },
            echarts: {
              name: 'chunk-echarts',
              priority: 20,
              test: /[\/]node_modules[\/]_?(echarts|zrender)(.*)/
            },
            exceljs: {
              name: 'chunk-exceljs',
              priority: 20,
              test: /[\/]node_modules[\/]_?exceljs(.*)/
            }
          }
        })
        config.optimization.runtimeChunk('single')
      })
    // markdown
    config.module
      .rule('md')
      .test(/\.md$/)
      .use('text-loader')
      .loader('text-loader')
      .end()
    // svg
    const svgRule = config.module.rule('svg')
    svgRule.uses.clear()
    svgRule
      .include
      .add(resolve('src/assets/svg-icons/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'd2-[name]'
      })
      .end()
    // image exclude
    const imagesRule = config.module.rule('images')
    imagesRule
      .test(/\.(png|jpe?g|gif|webp|svg)(\?.*)?$/)
      .exclude
      .add(resolve('src/assets/svg-icons/icons'))
      .end()
    // 重新设置 alias
    config.resolve.alias
      .set('@api', resolve('src/api'))
    // 分析工具
    if (process.env.npm_config_report) {
      config
        .plugin('webpack-bundle-analyzer')
        .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
    }
  },
  // 不输出 map 文件
  productionSourceMap: false,
  // i18n
  pluginOptions: {
    i18n: {
      locale: 'zh-chs',
      fallbackLocale: 'en',
      localeDir: 'locales',
      enableInSFC: true
    }
  }
}
