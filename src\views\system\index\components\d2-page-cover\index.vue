<template>
  <div class="d2-page-cover">
    <div class="mainclass">
      <div class="itemclass2">
        <sorder-chart2 />
      </div>
      <div class="itemclass">
        <sorder-Chart />
      </div>

      <div class="itemclass">
        <raddar-chart />
      </div>
    </div>

    <div class="d2-page-cover__logo">
      <slot />
    </div>
    <p class="d2-page-cover__title"></p>
    <slot name="footer" />
  </div>
</template>

<script>
import RaddarChart from './componments/RaddarChart'
import sorderChart from './componments/sorder<PERSON>hart'
import sorderChart2 from './componments/sorderChart2'
export default {
  name: 'PageCouverIndex',
  components: {
    Raddar<PERSON>hart,
    // Pie<PERSON><PERSON>,
    // Bar<PERSON><PERSON>,
    sorder<PERSON>hart,
    sorderChart2
    // sorderModelChart
    // PanelGroup
  }
}
</script>
<style lang="scss" scoped>
.d2-page-cover {
  @extend %full;
  @extend %unable-select;
  display: flex;
  flex-flow: column nowrap;
  justify-content: flex-start;
  align-items: center;
  .mainclass {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-align-content: center;
    align-content: flex-start;
    justify-content: flex-start;
    .itemclass {
      width: 600px;
      height: 500px;
      margin: 5px;
    }
    .itemclass2 {
      width: 800px;
      height: 500px;
      margin: 5px;
    }
  }
  .d2-page-cover__logo {
    img {
      width: 200px;
    }
  }
  .d2-page-cover__title {
    margin: 0px;
    margin-bottom: 20px;
    font-weight: bold;
    color: $color-text-main;
  }
  .d2-page-cover__sub-title {
    margin: 0px;
    margin-bottom: 5px;
    color: $color-text-normal;
  }
  .d2-page-cover__build-time {
    margin: 0px;
    margin-bottom: 10px;
    font-size: 12px;
    color: $color-text-placehoder;
  }
}
</style>
