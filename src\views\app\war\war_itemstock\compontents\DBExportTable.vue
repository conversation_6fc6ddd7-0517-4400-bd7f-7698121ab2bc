<template>
  <d2-container>
    <vxe-table border resizable show-overflow :data="tableData" ref="xTable1" :edit-config="{trigger: 'click', mode: 'cell'}">
      <vxe-table-column type="seq" title="No." width="60"></vxe-table-column>
      <vxe-table-column field="originalItemNo" cell-type="string" title="Supplier fabric code" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="collection" title="Collection" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="jYYReferenceCode" title="JYY reference code" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="inventoryQty" title="In stock qty (m)" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="supplierItemName" title="Supplier name" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="restockQty" title="Restock qty (m)" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="restockWeek" title="Restock Week" :edit-render="{name: '$input', props: {type: 'date'}, autoselect: true}"></vxe-table-column>
      <vxe-table-column field="borrowed" title="Borrowed" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="retailPrice" title="Unit price (Euro)" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="countValue" title="Value (Euro)" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="JYYcode" title="JYY code" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
      <vxe-table-column field="remark" title="Remark" :edit-render="{name: 'input', autoselect: true}"></vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-button status="warning" @click="exportDataEvent()">导出数据</vxe-button>
    </template>
  </d2-container>
</template>

<script>
export default {
  name: 'DBExportTable',
  props: {
    selectTable: {
      require: true,
      type: Array
    }
  },
  data () {
    return {
      tableData: [

      ]
    }
  },
  created () {
    this.tableData = this.selectTable
  },
  methods: {
    exportDataEvent () {
      const xTable = this.$refs.xTable1
      const exportConfig = {
        filename: '大宝面料库存导出-' + this.$utils.toDateString(new Date(), 'yyyyMMddHHmmss'),
        sheetName: 'Sheet1',
        isColgroup: false,
        isFooter: false,
        type: 'xlsx'
      }

      xTable.exportData(exportConfig)
    }
  }
}
</script>

<style>
</style>
