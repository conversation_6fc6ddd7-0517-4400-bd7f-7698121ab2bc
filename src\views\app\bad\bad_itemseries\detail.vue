<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="vxe-icon--arrow-left" status="perfect" @click="goback">返回</vxe-button>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowEdit">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='BadItemseries_detail_table' ref='master_table' column-key auto-resize resizable border highlight-hover-row highlight-current-row highlight-hover-column highlight-current-column :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="sizeCode" title="尺码" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="itemCode" title="物料编码" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="itemCodeName" title="物料" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"> </vxe-table-column>
      <vxe-table-column title="操作" width="100px" :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete">
          </vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo v-if="footerCompanyInfo" />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="尺码" field="sizeCode" span="12" :item-render="{}"> <vxe-input v-model="selectRow.sizeCode" placeholder="请输入名称" clearable></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="物料" field="itemID" span="24" :item-render="{}"> <template #default="{ data }">
            <el-select v-model="data.itemID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod" :loading="loading" clearable>
              <el-option v-for="item in itemoptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{}"> <template #default>
            <vxe-textarea v-model="selectRow.remark" placeholder="请输入备注" clearable></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"> <template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import detailTableMixins from '@/mixins/detail_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'BadItemseriesDetail',
  mixins: [detailTableMixins],

  data () {
    return {
      formData: {
        sizeCode: '',
        remark: '',
        isActive: true,
        itemID: null,
        itemSeriesID: null
      },
      formRules: {
        sizeCode: [{ required: true, message: '请输入尺码' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'sizeCode', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'itemID', title: '物料', span: 12, itemRender: { name: '$input' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/bad_itemseriesitem/get',
        add: '/mtm/bad_itemseriesitem/adds',
        edit: '/mtm/bad_itemseriesitem/updates',
        delete: '/mtm/bad_itemseriesitem/deletes',
        ItemComboStore: '/mtm/comboQuery/itemComboStoreByQuery'
      },
      itemoptions: [],
      loading: false
    }
  },
  async created () {
    await this.getCombStore()
    this.loadData({ id: this.form.id }).then(({ data }) => {
    })
    this.formData.itemSeriesID = this.form.id
    // this.$utils.find(this.formItems, item => item.field === 'sewBaseID').itemRender.options = this.SewBaseComboStore
  },
  methods: {
    async getCombStore () {
      await this.$api.ActionRequest(this.api.ItemComboStore).then(result => {
        this.itemoptions = result
      })
    },
    remoteMethod (query) {
      this.$api.ActionRequest(this.api.ItemComboStore, { text: query }).then(result => {
        this.itemoptions = result
      })
    },
    // 编辑
    editEvent (row) {
      this.$api.ActionRequest(this.api.ItemComboStore, { gid: row.itemID }).then(result => {
        this.itemoptions = result
        this.selectRow = cloneDeep(row)
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
