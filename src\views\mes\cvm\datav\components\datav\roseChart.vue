<template>
  <div id="rose-chart">
    <div class="rose-chart-title">小组占压</div>
    <dv-charts :option="option" ref="charts" />
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  data () {
    return {
      data: [],
      api: {
        get: '/mes/boardChart/ProductionTeamStepUp'
      },
      option: {
        series: [
          {
            type: 'pie',
            radius: '50%',
            roseSort: false,
            data: [
              { name: '路基', value: '1' }

            ],
            insideLabel: {
              show: false
            },
            outsideLabel: {
              formatter: '{name} {percent}%',
              labelLineEndLength: 20,
              style: {
                fill: '#fff'
              },
              labelLineStyle: {
                stroke: '#fff'
              }
            },
            roseType: true
          }
        ],
        color: ['#da2f00', '#fa3600', '#ff4411', '#ff724c', '#541200', '#801b00', '#a02200', '#5d1400', '#b72700']
      }
    }
  },
  created () {
    // this.get()
  },
  methods: {
    async get () {
      await this.$api.ActionRequest(this.api.get).then(result => {
        this.data = result.map(item => { return { name: item.label, value: item.count } })
        // this.option.series[0].data = this.data ;
        // console.log(  this.$refs.charts.$options)
        // this.$refs.charts.option=(this.option)
        this.createData(this.data)
      })
    },
    async createData (rows) {
      this.option = {
        series: [
          {
            type: 'pie',
            radius: '45%',
            roseSort: false,
            data: rows,
            insideLabel: {
              show: false
            },
            activeTimeGap: 2000,
            outsideLabel: {
              formatter: '{name} {percent}%',
              labelLineEndLength: 20,
              style: {
                fill: '#fff'
              },
              labelLineStyle: {
                stroke: '#fff'
              }
            },
            roseType: true
          }
        ],
        color: ['#da2f00', '#fa3600', '#ff4411', '#ff724c', '#541200', '#801b00', '#a02200', '#5d1400', '#b72700', '#b72710', '#b72701', '#b72702']
      }
      // this.option.series[0].data = this.data
    },
    randomExtend (minNum, maxNum) {
      if (arguments.length === 1) {
        return parseInt(Math.random() * minNum + 1, 10)
      } else {
        return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10)
      }
    }
  },
  mounted () {
    const { get } = this
    get()
    setInterval(get, 1000 * 60)
  }
}
</script>

<style lang="scss">
#rose-chart {
  width: 30%;
  height: 100%;
  background-color: rgba(6, 30, 93, 0.5);
  border-top: 2px solid rgba(1, 153, 209, 0.5);
  box-sizing: border-box;

  .rose-chart-title {
    height: 50px;
    font-weight: bold;
    text-indent: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
  }

  .dv-charts-container {
    height: 100%;
  }
}
</style>
