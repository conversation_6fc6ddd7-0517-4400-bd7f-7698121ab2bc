<template>
  <div class="showpageheader">
    <el-card class="box-card">
      <vxe-form title-align="right" title-width="100" :size='size'>
        <vxe-form-item title="系统编号:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.sorderNum}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="客户编号:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.clientName}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款号/姓名:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.clientPersonName}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="流水号:" :item-render="{}">
          <template #default>
            <span class="formitemspan"><strong>{{data.serialNumber}}</strong></span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="是否加急:" :item-render="{}">
          <template #default>
            <span class="formitemspan isUrgent">{{data.isUrgent?'是':'否'}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="下单数量:" :item-render="{}">
          <template #default>
            <span class="formitemspan"></span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="面料号:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.itemName}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="成分:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.itemComp}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="纹理:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.finalTextureText}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="幅宽:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.finalWidth}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="长度:" :item-render="{}">
          <template #default>
            <span class="formitemspan"></span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="洗水唛号型:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.customerSize}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="下单日期:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{formatDate(data.issueDate,'yyyy-MM-dd')}}</span>
          </template>
        </vxe-form-item>

        <vxe-form-item title="规格号型:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{data.sizeCode}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="交货日期:" :item-render="{}">
          <template #default>
            <span class="formitemspan">{{formatDate(data.deliveryDate,'yyyy-MM-dd')}}</span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="版型编码:" :item-render="{}">
          <template #default>
            <span class="formitemspan1" :item-render="{}"> {{data.modelCode}} </span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="款式描述:" :item-render="{}">
          <template #default>
            <span class="formitemspan1"> {{data.modelName}} </span>
          </template>
        </vxe-form-item>
        <vxe-form-item title="特体:" span="18" :item-render="{}">
          <template #default> <span></span></template>
        </vxe-form-item>
        <vxe-form-item title="备注:" span="12" :item-render="{}">
          <template #default>
            <span style="color:red;    font-weight: 900;">{{data.remark}}</span>
          </template>
        </vxe-form-item>
      </vxe-form>
    </el-card>
  </div>
</template>

<script>
import XEUtils from 'xe-utils'
import defaultset from './index'
export default {
  name: 'ShowHeader',
  mixins: [defaultset],
  props: {
    form: {
      type: Object
    }
  },
  watch: {
    form: {
      deep: true,
      handler (newval, oldval) {
        if (newval !== null) {
          this.data = newval
        }
      }
    }
  },
  data () {
    return {
      data: {
        sizeCode: null,
        sorderNum: null,
        clientName: null,
        itemName: null,
        modelName: null,
        modelCode: null,
        deliveryDate: null,
        clientPersonName: null,
        serialNumber: null,
        isUrgent: null,
        finalTextureText: null,
        finalWidth: null,
        customerSize: null,
        issueDate: null,
        remark: null
      }
    }
  },
  created () {
    if (this.form != null) {
      this.data = this.form
    }
  },
  methods: {
    formatDate (cellValue, format) {
      if (cellValue === undefined || cellValue === null) {
        return null
      }
      return XEUtils.toDateString(this.formatLongDate(cellValue), format || 'yyyy-MM-dd HH:mm:ss')
    },
    formatLongDate (date) {
      var dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    }
  }
}
</script>

<style lang='scss'>
.showpageheader {
  .formitemspan {
    width: 100px;
    display: inline-block;
    font-weight: 600;
  }
  .isUrgent {
    color: red;
  }
  .formitemspan1 {
    width: 180px;
    display: inline-block;
    font-weight: 500;
  }
  .vxe-form .vxe-form--item {
    padding: 0px !important;
    color: black;
  }
  .el-card__body {
    padding: 0 !important;
  }
  .vxe-form--item-inner {
    min-height: 20px !important;
  }
}
</style>
