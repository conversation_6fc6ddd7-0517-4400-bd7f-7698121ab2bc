<template>
  <d2-container>
    <template slot="header">
      <vxe-toolbar>
        <template v-slot:buttons>
          <vxe-button icon="fa fa-plus" status="perfect" @click="insertEvent" v-if="menuAction.allowAdd">新增</vxe-button>
        </template>
        <template v-slot:tools>
          <vxe-button type="text" icon="vxe-icon--question" class="tool-btn"></vxe-button>
          <vxe-button type="text" icon="vxe-icon--refresh" class="tool-btn" @click="handleCleanCacheAndRefreshCurrent"></vxe-button>
        </template>
      </vxe-toolbar>
      <vxe-toolbar perfect custom>
        <template v-slot:tools>
          <vxe-form ref="xForm" :data="searchForm" @submit="searchEvent()" @reset="resetEvent">
            <vxe-form-item field="text" :item-render="{}"> <vxe-input v-model.trim="searchForm.text" placeholder="编码/名称" suffix-icon="fa fa-search" clearable></vxe-input>
            </vxe-form-item>
            <vxe-form-item :item-render="{}"> <template #default>
                <vxe-button type="submit" status="success">查询</vxe-button>
                <vxe-button type="reset">重置</vxe-button>
              </template>
            </vxe-form-item>
          </vxe-form>
        </template>
      </vxe-toolbar>
    </template>

    <vxe-table id='Mom_modelType_master_table' ref='master_table' :loading="tableLoading" @cell-click='tableCellClick' :row-class-name="rowClassName" :height="TableHeight" :data="tableData" :custom-config="{storage: true}">
      <vxe-table-column type="seq" width="60px"></vxe-table-column>
      <vxe-table-column field="code" title="版型类别编码" width="100px"></vxe-table-column>
      <vxe-table-column field="codeName" title="版型类别名称" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="imagePath" title="图片" sortable> <template v-slot="{ row }">
          <el-popover placement="right-end" width="200" trigger="hover">
            <el-image :src="row.imagePath" fit="fill">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <el-image :src="row.imagePath" fit="fill" slot="reference">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </el-popover>
        </template>
      </vxe-table-column>
      <vxe-table-column field="remark" title="备注" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="isActive" title="活动" :formatter='formatBool' sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createBy" title="创建人" sortable width="100px"></vxe-table-column>
      <vxe-table-column field="createOn" title="创建时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyOn" title="修改时间" :formatter="val=>formatDate(val)" sortable width="100px"> </vxe-table-column>
      <vxe-table-column field="modifyBy" title="修改人" sortable width="100px"></vxe-table-column>
      <vxe-table-column title="操作" width="150px" show-overflow :fixed='tableOptFixed' v-if="menuAction.allowEdit||menuAction.allowDelete">
        <template v-slot="{ row }">
          <vxe-button type="text" icon="fa fa-files-o" @click="copyRowEvent(row)" v-if="menuAction.allowAdd"></vxe-button>
          <vxe-button type="text" icon="fa fa-edit" @click="editEvent(row)" v-if="menuAction.allowEdit"></vxe-button>
          <vxe-button type="text" icon="fa fa-trash-o" @click="removeEvent(row)" v-if="menuAction.allowDelete"></vxe-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <template slot="footer">
      <vxe-pager @page-change='pageChange' :current-page.sync="searchForm.currentPage" :page-size.sync="searchForm.pageSize" :total="searchForm.totalCount" :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']">
        <template v-slot:right>
          <mtm-footer-companyinfo />
        </template>
      </vxe-pager>
    </template>
    <vxe-modal v-model="showEdit" :title="selectRow.id!=null? '编辑&保存' : '新增&保存'" width="800" resize destroy-on-close :loading="submitLoading">
      <vxe-form :data="selectRow" :rules="formRules" title-align="right" title-width="100" @submit="submitEvent">
        <vxe-form-item title="编码" field="code" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="编码名称" field="codeName" span="12" :item-render="{name: 'input'}"></vxe-form-item>
        <vxe-form-item title="版型分类" field="actionTypeID" span="12">
          <template #default>
            <el-select v-model="selectRow.groupID" filterable placeholder="请选择类型" size="mini">
              <el-option v-for="item in GroupComboStore" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="图片" field="modelImageID" span="12"><template #default>
            <el-select v-model="selectRow.modelImageID" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="ModelImageComboStoreByQueryMethod" clearable @change="selectchange">
              <el-option v-for="item in ModelImageComboStoreByQuery" :key="item.value" :label="item.label" :value="item.value">
                <el-tooltip class="item" effect="dark" :content=" item.label" placement="top-start">
                  <span style="float: left ;overflow:hidden;width:250px; text-overflow: ellipsis;    white-space: nowrap;   word-break:keep-all;" class="modelimageselect">{{ item.label }}</span>
                </el-tooltip>
                <span style="width:60px;">{{item.position}}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  <el-image :src="item.text" fit="fill" style="width: 100px; height: 100px">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="活动" field="isActive" span="12" :item-render="{name: '$switch'}"></vxe-form-item>
        <vxe-form-item title="备注" field="remark" span="24" :item-render="{name: '$textarea'}"></vxe-form-item>
        <vxe-form-item title="图片" span="24" :item-render="{}"><template #default>
            <el-image style="width: 200px; height: 200px" :src="imageurl" fit="fill">
            </el-image>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center" span="24" :item-render="{}"><template #default>
            <vxe-button type="submit" status="primary">提交</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </d2-container>
</template>

<script>
import masterTableMixins from '@/mixins/master_table_mixins/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'mom_modeltype', // 版型类别
  mixins: [masterTableMixins],
  components: {
  },
  data() {
    return {
      formData: {
        code: '',
        codeName: '',
        remark: '',
        isActive: true,
        groupID: '',
        modelImageID: ''
      },
      imageurl: null,
      formRules: {
        code: [{ required: true, message: '请输入编码' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }],
        codeName: [{ required: true, message: '请输入编码名称' }, { min: 2, max: 12, message: '长度在 2 到 12 个字符' }]
      },
      formItems: [
        { field: 'groupID', title: '版型分类', span: 24, itemRender: { name: '$select', options: [] } },
        { field: 'code', title: '编码', span: 12, itemRender: { name: '$input' } },
        { field: 'codeName', title: '编码名称', span: 12, itemRender: { name: '$input' } },
        // { field: 'modelImageID', title: '选择图片', span: 12, itemRender: { name: '$input' } },
        // { field: 'modelImageID', title: '图片路径', span: 12, itemRender: { name: '$input' } },
        { field: 'isActive', title: '活动', span: 12, itemRender: { name: '$switch' } },
        // { field: 'isActive', title: '图片', span: 12, itemRender: { name: '$switch' } },
        { field: 'remark', title: '备注', span: 12, itemRender: { name: '$textarea' } },
        { align: 'center', span: 24, titleAlign: 'left', itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '提交', status: 'primary' } }, { props: { type: 'reset', content: '重置' } }] } }
      ],
      api: {
        get: '/mtm/mom_modeltype/get',
        add: '/mtm/mom_modeltype/adds',
        edit: '/mtm/mom_modeltype/updates',
        delete: '/mtm/mom_modeltype/deletes',
        GroupComboStore: '/mtm/comboQuery/groupComboStoreByQuery',
        ModelImageComboStoreByQuery: '/jyymtm/comboQuery/ModelImageComboStoreByQuery'
      },
      action: {
        get: true,
        add: true,
        edit: true,
        delete: true,
        print: true
      },
      ModelImageComboStoreByQuery: [],
      GroupComboStore: [],
      footerCompanyInfo: false

    }
  },
  async created() {
    await this.getCombStore()
    this.$utils.find(this.formItems, item => item.field === 'groupID').itemRender.options = this.GroupComboStore
  },
  methods: {
    selectchange(row) {
      if (row) {
        var itme = this.ModelImageComboStoreByQuery.GetFirstElement('value', row)
        if (itme) {
          console.log(itme)
          this.imageurl = itme.text
        } else { this.imageurl = null }
      } else {
        this.imageurl = null
      }
      // var itme = find(this.ModelImageComboStoreByQuery, function (i) { return i.value === row })

    },
    async getCombStore() {
      await this.$api.ActionRequest(this.api.GroupComboStore).then(result => {
        this.GroupComboStore = result
      })
    },
    ModelImageComboStoreByQueryMethod(query) {
      this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { text: query }).then(result => {
        this.ModelImageComboStoreByQuery = result
      })
    },
    // 编辑
    async editEvent(row) {
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
        this.selectRow = cloneDeep(row)
        this.imageurl = row.imagePath
        this.showEdit = true
      })
      // await this.$api.ActionRequest(this.api.ModelBaseComboStoreByQuery, { gid: row.modelBaseID }).then(result => {
      //   this.ModelBaseComboStoreByQuery = result

      // })
    },
    // 复制
    async copyRowEvent(row) {
      this.selectRow = cloneDeep(row)
      if (this.$utils.has(this.selectRow, 'id')) {
        this.selectRow.id = null
      }
      if (this.$utils.has(this.selectRow, 'code')) {
        this.selectRow.code = null
      }
      if (this.$utils.has(this.selectRow, 'codeName')) {
        this.selectRow.codeName = null
      }
      await this.$api.ActionRequest(this.api.ModelImageComboStoreByQuery, { gid: row.modelImageID }).then(result => {
        this.ModelImageComboStoreByQuery = result
        this.imageurl = row.imagePath
        this.showEdit = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
